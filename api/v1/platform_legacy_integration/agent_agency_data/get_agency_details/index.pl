#!/usr/bin/perl

use strict;
use warnings;
use CGI qw(:standard);
use CGI::Carp qw(carpout fatalsToBrowser);
use JSON;
use Try::Tiny;

##########################################
######### ACCEPTABLE PARAMETERS ##########
##########################################
#BASIC AGENCY INFO REQUESTS:
# agency - Returns data for a specific agency
#
#BASIC AGENCY INFO REQUESTS & VALID AGENT LIST:
# agency 
# policy_State -
# effective_date - 
# line_of_business -
#
#
##########################################

BEGIN
{
    # Turn on the buffer flushing to help make sure all I/O gets to files/screens
    $| = 1;
  
        (my $base_dir = File::Spec->rel2abs(__FILE__)) =~ s|/imtonline.*|/imtonline/|;
        my @incs = qw(. Common platform_legacy_integration );

        push @INC, map {$base_dir . $_} @incs;
    
}

use api::v1::platform_legacy_integration::jsonResponse qw (json_response json_error_response);
use api::v1::platform_legacy_integration::commonAuth qw (authenticate);
use api::v1::platform_legacy_integration::agent_agency_data::include_methods qw(
    get_agency_address    
    get_agency_data
    get_agency_fax 
    get_agency_phone
    get_agent_list 
);
use Common::loggerConfigs::loggerCommon qw(init_logger);

my $LOGGER = init_logger( { caller_path => File::Spec->rel2abs( __FILE__ ) } );

local $SIG{__DIE__} = sub {
    if($^S) { return; }
    $Log::Log4perl::caller_depth++;
    $LOGGER->logdie(@_);
};

my $agency_number = undef;
my $include = undef;
my $agent_list = undef;
my $effective_date = undef;
my $policy_state = undef;
my $line_of_business = undef;
my $response = undef;


main();

sub main
{
    my $cgi = CGI->new();

    ##AUTHENTICATION - THIS WILL THROW A 401 IF USER IS NOT AUTHENTICATED
    authenticate({'CGI' => $cgi, key => $cgi->http('IMTAuthorization')});

    try{

        if ($cgi->request_method() ne 'GET')
        {
            json_error_response({
                                            logger => $LOGGER,                                         
                                            message => 'Invalid GET request',
                                            status_code => 400
                           }); 
        }       


	    $agency_number = $cgi->param('agency') || undef;  
        if(!$agency_number || length($agency_number) != 6) 
        {   
            json_error_response({
                                            logger => $LOGGER,                                         
                                            message => 'Invalid agency number',
                                            status_code => 400
                           }); 
        }
        
        #IF ADDITIONAL DATA WAS REQUESTED VIA "INCLUDE",  WE NEED TO ENSURE THE APPROPRIATE DATA POINTS EXIST
        $include = $cgi->param('include') || undef; 

        my @include_params = split(/,/, $include); 
        foreach (@include_params)
        {
            if($_ eq "valid_agent_list"){
                $effective_date = $cgi->param('eff_date') || undef;
                $policy_state = $cgi->param('policy_state') || undef;
                $line_of_business = $cgi->param('line_of_business') || undef; 

                #IF WE ARE ASKED TO INCLUDE THE VALID AGENTS,  THE FOLLOWING DATA POINTS ARE REQUIRED.
                if(!$effective_date && !$policy_state && !$line_of_business){
                    json_error_response({
                                            logger => $LOGGER, 
                                            message => 'Include requires additional data points.  Please refer to documentation.',
                                            status_code => 400
                           }); 
                 }    

                $agent_list=1;          
            }
            else{ 
                        json_error_response({
                                            logger => $LOGGER,
                                            message => 'Invalid Parameters- please review the documentation',
                                            status_code => 400
                           }); 
            }
        } 
	}
    catch{
         my $error = $_;

            json_error_response({
                                            logger => $LOGGER,        
                                            message => 'Sorry, our hamsters went on strike.  We\'re in negotiations now,  won\'t be long!',
                                            status_code => 500
                           });    
    };

    my $response = get_base_agency_data();
       
    if($agent_list)
    { 
        my $agents = get_agent_list(
                    {logger => $LOGGER, 
                     agency_number => $agency_number, 
                     effective_date=>$effective_date,
                     policy_state=>$policy_state,
                     line_of_business=>$line_of_business});
                            
        for(my $i = 0; $i < scalar(@$agents); $i++){  
           
            $agents->[$i]->{'agent_number'} = $agents->[$i]->{'AGENT_NUMBER'};
            $agents->[$i]->{'soliciting_number'} = substr($agents->[$i]->{'AGENT_NUMBER'},6,3); 
            $agents->[$i]->{'district'} = $agents->[$i]->{'DISTRICT'};
            $agents->[$i]->{'name'} = $agents->[$i]->{'NAME'};

            delete($agents->[$i]->{'AGENT_NUMBER'});
            delete($agents->[$i]->{'DISTRICT'});
            delete($agents->[$i]->{'NAME'});
        }  
        $response->{'agent_list'} = $agents;

    }

    return json_response({code => '200', message => 'Success', data=> $response}); ## Success endpoint is closed   
}

sub get_base_agency_data
{            
    my $agency_data = get_agency_data({logger => $LOGGER, 
         agency_number => $agency_number});
 
    if(scalar@{$agency_data} < 1)
    {
         return json_response({code => '404', message => 'No records found' }); ## Success endpoint is closed
    }  
     
    #GRAB AGENCY PHONE NUMBER
    my $phone = api::v1::platform_legacy_integration::agent_agency_data::include_methods::get_agency_phone(
        {logger => $LOGGER, agency_number => $agency_number});

    if($phone)
    {
        $agency_data->[0]->{'misc'}->{'phone_number'} = $phone;
    }

    #GRAB AGENCY WEBSITE
    my $website = api::v1::platform_legacy_integration::agent_agency_data::include_methods::get_agency_website(
        {logger => $LOGGER, agency_number => $agency_number});

    if($website)
    {
        $agency_data->[0]->{'misc'}->{'website'} = $website;
    }

    my $address = get_agency_address(
        {   logger        => $LOGGER,
            agency_number => $agency_number
        }
    );

    if ($address) {
        $agency_data->[0]{'misc'}{'mailing_address'} = $address;
    }

    my $fax = get_agency_fax(
        {   logger        => $LOGGER,
            agency_number => $agency_number
        }
    );

    if ($fax) {
        $agency_data->[0]{'misc'}{'fax_number'} = $fax;
    }
    
    $response->{'agency_data'} = $agency_data->[0];
}

1;
