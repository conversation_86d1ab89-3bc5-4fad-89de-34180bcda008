#!/usr/local/bin/perl
package api::v1::platform_legacy_integration::agent_agency_data::include_methods;

require 5.000;
use strict;
use vars qw($VERSION @ISA @EXPORT @EXPORT_OK);

use Exporter;
use IMT::ValidAgents qw(ValidAgents);
use Data::Dumper;
use Common::Connect qw(DBChop);

use api::v1::platform_legacy_integration::jsonResponse qw (json_response json_error_response);

@ISA = qw(Exporter);
@EXPORT = qw();
@EXPORT_OK = qw(
   get_agency_address 
   get_agency_data 
   get_agency_fax
   get_agency_phone 
   get_agent_list 
   get_agent_details 
   is_transferred_agent
);

$VERSION = '0.01';

sub get_agency_address {
    my ($params) = @_;

    my $agency_number = $params->{agency_number} || undef;
    my $LOGGER        = $params->{logger};

    my $query = <<SQL;
        SELECT 
            ADD_LINE1,
            ADD_LINE2,
            ADD_CITY,
            ADD_STATE,
            ADD_ZIP
        FROM GENSUPDB.USER_INFO UI
            JOIN CLIENTDB.CLIENT C
                ON C.USER_KEY = UI.USER_KEY
            JOIN CLIENTDB.CLIENT_HAS_ADDRESS CHA
                ON CHA.CLIENT_ID = C.CLIENT_ID
            JOIN CLIENTDB.ADDRESS A
                ON A.ADDRESS_ID = CHA.ADDRESS_ID
        WHERE 
            UI.USER_ID = ?
            AND A.ADD_TYPE IN ('P', 'E');
SQL

    my $dbh = DBChop();

    my $address_sth = $dbh->prepare($query) || json_error_response(
        {   logger      => $LOGGER,
            message     => 'Could not prepare query ' . $dbh->errstr,
            status_code => 500
        }
    );

    $address_sth->execute($agency_number) || json_error_response(
        {   logger      => $LOGGER,
            message     => 'Could not execute query ' . $dbh->errstr,
            status_code => 500
        }
    );

    my $results = $address_sth->fetchall_arrayref( {} );

    my $response_hash = {
        'street_line_1' => $results->[0]->{'ADD_LINE1'},
        'street_line_2' => $results->[0]->{'ADD_LINE2'},
        'city'          => $results->[0]->{'ADD_CITY'},
        'state'         => $results->[0]->{'STATE'},
        'zipcode'       => $results->[0]->{'ADD_ZIP'},
    };

    return $response_hash;
}

sub get_agency_data
{
    my ($params) =  @_;
    
    my $agency_number = $params->{agency_number} || undef;   
    my $LOGGER = $params->{logger};
    my $select;
    my $query_param = undef;
   
    my $dbh = DBChop();

    $select = $dbh->prepare("SELECT AGENCYNO, DISTRICT, NAME, MANSYSTEM AS MANAGEMENT_SYSTEM,
                             LOCATE_AGENT, IVANS_ACCOUNT,
                             IVANS_USERID, MANSYSTEM_VER AS MANAGEMENT_SYSTEM_VERSION, BOP_DOWNLOAD,  
                             print_agency, PRINT_NEWBIZ AS PRINT_NEW_BUSINESS, PRINT_BILLING, MUTUAL_AFFILIATED,
                             DOWNLOAD_STATUS, WAP_ALT_GUIDELINES, COMMISSION_DOWNLOAD,
                             APPS, CLAIMS_NOTIFY, COMM_UNDERWRITING, NO_PHOTO_AGENCY, MON_OPEN, MON_CLOSE	TUE_OPEN,TUE_CLOSE,WED_OPEN	WED_CLOSE,THU_OPEN, THU_CLOSE, FRI_OPEN, FRI_CLOSE	SAT_OPEN,SAT_CLOSE,SUN_OPEN,SUN_CLOSE                                  
                             FROM AGENTDB.AGENCY
                             WHERE AGENCYNO = ?") ||  json_error_response({
                                                                           logger => $LOGGER,
                                                                           message => 'Could not prepare query '.$dbh->errstr,
                                                                           status_code => 500
                                                            });                                                                
                       
    $select->execute($agency_number) || json_error_response({
                                                                           logger => $LOGGER,  
                                                                           message => 'Could not execute query '.$dbh->errstr,
                                                                           status_code => 500
                                                            });
    my $results=$select->fetchall_arrayref({});  

    $select->finish();

    $dbh->disconnect();

    #CLEANUP PRIMARY DATA POINTS
    $results->[0]->{'agency_number'} = $results->[0]->{'AGENCYNO'} || "";
    delete($results->[0]->{'AGENCYNO'});

    $results->[0]->{'name'} = $results->[0]->{'NAME'} || "";
    delete($results->[0]->{'NAME'});

    #CLEANUP PRINT RELATED DATA
    $results->[0]->{'print_settings'}->{'print_agency'} = $results->[0]->{'PRINT_AGENCY'} || "";
    delete($results->[0]->{'PRINT_AGENCY'});
   
    $results->[0]->{'print_settings'}->{'print_new_business'} = $results->[0]->{'PRINT_NEW_BUSINESS'} || "";
    delete($results->[0]->{'PRINT_NEW_BUSINESS'});

    $results->[0]->{'print_settings'}->{'print_billing'} = $results->[0]->{'PRINT_BILLING'} || "";
    delete($results->[0]->{'PRINT_BILLING'});
   
    #MISC DATA ELEMENTS 
    $results->[0]->{'misc'}->{'apps'} = $results->[0]->{'APPS'};
    delete($results->[0]->{'APPS'});

    $results->[0]->{'misc'}->{'district'} = $results->[0]->{'DISTRICT'};
    delete($results->[0]->{'DISTRICT'});

    $results->[0]->{'misc'}->{'district'} = $results->[0]->{'CLAIMS_NOTIFY'};
    delete($results->[0]->{'CLAIMS_NOTIFY'});

    $results->[0]->{'misc'}->{'mutual_affiliated'} = $results->[0]->{'MUTUAL_AFFILIATED'};
    delete($results->[0]->{'MUTUAL_AFFILIATED'});

    $results->[0]->{'misc'}->{'location_agent'} = $results->[0]->{'LOCATE_AGENT'};
    delete($results->[0]->{'LOCATE_AGENT'});
   
    #CLEANUP DOWNLOAD RELATED DATA 
    if($results->[0]->{'MANAGEMENT_SYSTEM'})
    {       
       $results->[0]->{'download_data'}->{'management_system'}->{'system_name'} = $results->[0]->{'MANAGEMENT_SYSTEM'};
       delete($results->[0]->{'MANAGEMENT_SYSTEM'});

       $results->[0]->{'download_data'}->{'download_status'} = $results->[0]->{'DOWNLOAD_STATUS'} || "";
       delete($results->[0]->{'DOWNLOAD_STATUS'});

       $results->[0]->{'download_data'}->{'management_system'}->{'management_system_version'} = $results->[0]->{'MANAGEMENT_SYSTEM_VERSION'} || "";
       delete($results->[0]->{'MANAGEMENT_SYSTEM_VERSION'});

       $results->[0]->{'download_data'}->{'ivans_data'}->{'ivans_account'} = $results->[0]->{'IVANS_ACCOUNT'} || "";
       delete($results->[0]->{'IVANS_ACCOUNT'});

       $results->[0]->{'download_data'}->{'ivans_data'}->{'ivans_userid'} = $results->[0]->{'IVANS_USERID'} || "";
       delete($results->[0]->{'IVANS_USERID'});

       $results->[0]->{'download_data'}->{'bop_download'} = $results->[0]->{'BOP_DOWNLOAD'};   
       delete($results->[0]->{'BOP_DOWNLOAD'});

      $results->[0]->{'download_data'}->{'commission_download'} = $results->[0]->{'COMMISSION_DOWNLOAD'} || "";
       delete($results->[0]->{'COMMISSION_DOWNLOAD'});

    }
    else{
      $results->[0]->{'download_data'} = "NO DOWNLOAD DETAILS";
    }

    #BUILD OUT STORE HOURS
    $results->[0]->{'office_hours'}->{'monday_open'} = $results->[0]->{'MON_OPEN'};
    delete($results->[0]->{'MON_OPEN'});
    $results->[0]->{'office_hours'}->{'monday_close'} = $results->[0]->{'MON_CLOSE'};
    delete($results->[0]->{'MON_CLOSE'});
    $results->[0]->{'office_hours'}->{'tuesday_open'} = $results->[0]->{'TUE_OPEN'};
    delete($results->[0]->{'TUE_OPEN'});
    $results->[0]->{'office_hours'}->{'tuesday_close'} = $results->[0]->{'TUE_CLOSE'};
    delete($results->[0]->{'TUE_CLOSE'});
    $results->[0]->{'office_hours'}->{'wednesday_open'} = $results->[0]->{'WED_OPEN'};
    delete($results->[0]->{'WED_OPEN'});
    $results->[0]->{'office_hours'}->{'wednesday_close'} = $results->[0]->{'WED_CLOSE'};
    delete($results->[0]->{'WED_CLOSE'});
    $results->[0]->{'office_hours'}->{'thursday_open'} = $results->[0]->{'THU_OPEN'};
    delete($results->[0]->{'THU_OPEN'});
    $results->[0]->{'office_hours'}->{'thursday_close'} = $results->[0]->{'THU_CLOSE'};
    delete($results->[0]->{'THU_CLOSE'});
    $results->[0]->{'office_hours'}->{'friday_open'} = $results->[0]->{'FRI_OPEN'};
    delete($results->[0]->{'FRI_OPEN'});
    $results->[0]->{'office_hours'}->{'friday_close'} = $results->[0]->{'FRI_CLOSE'};
    delete($results->[0]->{'FRI_CLOSE'});
    $results->[0]->{'office_hours'}->{'SAT_OPEN'} = $results->[0]->{'SAT_OPEN'};
    delete($results->[0]->{'SAT_OPEN'});
    $results->[0]->{'office_hours'}->{'SAT_CLOSE'} = $results->[0]->{'SAT_CLOSE'};
    delete($results->[0]->{'SAT_CLOSE'});
    $results->[0]->{'office_hours'}->{'SUN_OPEN'} = $results->[0]->{'SUN_OPEN'};
    delete($results->[0]->{'SUN_OPEN'});
    $results->[0]->{'office_hours'}->{'SUN_CLOSE'} = $results->[0]->{'SUN_CLOSE'};
    delete($results->[0]->{'SUN_CLOSE'});

   foreach my $day (keys %{ $results->[0]{'office_hours'} }) {
    my $value = $results->[0]{'office_hours'}{$day};
        if ($value =~ /^(\d{2})(\d{2})$/) {
            my ($hour, $minute) = ($1, $2);
            my $period = $hour < 12 ? 'AM' : 'PM';
            $hour = $hour % 12;
            $hour = 12 if $hour == 0;
            my $converted_hours = sprintf("%d:%s %s", $hour, $minute, $period);
            $results->[0]->{'office_hours'}->{$day} = $converted_hours;
        }
        else{
            $value = ucfirst(lc($value));
            $value = "By Appointment" if($value eq "Appt");
            $results->[0]->{'office_hours'}->{$day} = $value;
        }
    }



    #BUILD OUT THE GRADING AND CLEAN UP ORIGINAL RESULTS
    if($results->[0]->{'COMM_UNDERWRITING'})
    {
         if($results->[0]->{'COMM_UNDERWRITING'} eq ''){     
            $results->[0]->{'grading'}->{'commercial_underwriting'} = 'Regular';
         }
         elsif($results->[0]->{'COMM_UNDERWRITING'} eq 'P'){
            $results->[0]->{'grading'}->{'commercial_underwriting'} = 'Preferred';
         }
         elsif($results->[0]->{'COMM_UNDERWRITING'} eq 'R'){
            $results->[0]->{'grading'}->{'commercial_underwriting'} = 'Restricted';
         }
        delete($results->[0]->{'COMM_UNDERWRITING'});
    }
    if($results->[0]->{'NO_PHOTO_AGENCY'})
    {
         if($results->[0]->{'NO_PHOTO_AGENCY'} eq 'Y')
         {
            $results->[0]->{'grading'}->{'personal_underwriting'} = 'No Photo Agency';
         }
         else{$results->[0]->{'grading'}->{'personal_underwriting'} = '';
         }
         delete($results->[0]->{'NO_PHOTO_AGENCY'});
    }
 

    #GET THE ASSOCIATED AGENCY DATA POINTS OFF THE AGENTS_INFO TABLE AND COMBINE THEM WITH THE PRIMARY RESULTS FROM ABOVE
    my $misc = get_agency_misc({agency_number=> $agency_number});

    $results->[0]->{'agency_status'}->{'transfer_date_new_business'} = $misc->[0]->{'TRANSFER_DATE_NEW'};
    $results->[0]->{'agency_status'}->{'transfer_date_amendment'} = $misc->[0]->{'TRANSFER_DATE_AMEND'};

    $results->[0]->{'agency_status'}->{'wap_alternate_guidelines'} = $results->[0]->{'WAP_ALT_GUIDELINES'};
    delete($results->[0]->{'WAP_ALT_GUIDELINES'});

    #BUILD OUT STATUS
    if($misc->[0]->{'CANCEL_DATE_NEW'} && $misc->[0]->{'INC_EXCL_IND'} ){   
        if($misc->[0]->{'INC_EXCL_IND'} eq 'R' && $misc->[0]->{'CANCEL_DATE_NEW'} ne '' ){
          $results->[0]->{'agency_status'}->{'status'} = 'RUNOFF';
          $results->[0]->{'agency_status'}->{'runoff_date'} = $misc->[0]->{'CANCEL_DATE_NEW'};
        }
        else { $results->[0]->{'agency_status'}->{'status'} = 'ACTIVE';} 
    }
    else { $results->[0]->{'agency_status'}->{'status'} = 'ACTIVE';}

   
    return $results;
}

sub get_agency_fax {
    my ($params) = @_;

    my $dbh           = DBChop();
    my $agency_number = $params->{agency_number} || undef;
    my $LOGGER        = $params->{'logger'};

    my $fax_query = <<SQL;
        SELECT 
            CO.CONTACT_INFO,
            CO.PRECEDENCE 
        FROM 
            GENSUPDB.USER_INFO AS U
            INNER JOIN CLIENTDB.CLIENT AS C 
                ON C.USER_KEY = U.USER_KEY
            INNER JOIN CLIENTDB.CLIENT_HAS_CONTACT AS CC
                ON C.CLIENT_ID = CC.CLIENT_ID
            INNER JOIN CLIENTDB.CONTACT AS CO 
                ON CO.CONTACT_ID = CC.CONTACT_ID
        WHERE 
            U.USER_ID = ? 
            AND CO.TYPE='FX'
SQL

    my $fax_sth = $dbh->prepare($fax_query) || json_error_response(
        {   logger      => $LOGGER,
            message     => 'Could not prepare query ' . $dbh->errstr,
            status_code => 500
        }
    );

    $fax_sth->execute($agency_number) || json_error_response(
        {   logger      => $LOGGER,
            message     => 'Could not execute query ' . $dbh->errstr,
            status_code => 500
        }
    );

    my $result = $fax_sth->fetchall_arrayref( {} );

    my @sorted = sort( { $a->{'PRECEDENCE'} <=> $b->{'PRECEDENCE'} } @$result );
    if ( scalar(@sorted) ) {
        return $sorted[0]->{'CONTACT_INFO'};
    }
    else {
        return 0;
    }

    return;
}

sub get_agency_misc
{
   my ($params) =  @_;
    
    my $agency_number = $params->{agency_number}."000" || undef;   
    my $LOGGER = $params->{'logger'};
    
    my $select;
    my $query_param = undef;
   
    my $dbh = DBChop();

    $select = $dbh->prepare("SELECT  AGENT_NUMBER, NAME, INC_EXCL_IND, APPOINT_DATE AS DATE_APPOINTED,
                             TRANSFER_DATE_NEW, TRANS_DATE_AMEND, CANCEL_DATE_NEW, CANCEL_DATE_AMEND, 
                             SOLD_TO_AGENT,  USER_TYPE                
                             FROM AGENTDB.AGENTS_INFO 
                             WHERE AGENT_NUMBER = ?") || json_error_response({
                                                                           logger => $LOGGER, 
                                                                           message => 'Could not prepare query '.$dbh->errstr,
                                                                           status_code => 500
                                                            });

    $select->execute($agency_number) || json_error_response({
                                                                           logger => $LOGGER,  
                                                                           message => 'Could not execute query '.$dbh->errstr,
                                                                           status_code => 500
                                                            });
    my $results=$select->fetchall_arrayref({});  

    $select->finish();

    $dbh->disconnect();
   
    return $results;
}

sub get_agency_website
{
    my ($params) =  @_;
    
    my $dbh =  DBChop();
    my $agency_number = $params->{agency_number} || undef;
    my $LOGGER = $params->{'logger'};

    my $query = $dbh->prepare("SELECT CO.CONTACT_INFO, CO.PRECEDENCE FROM GENSUPDB.USER_INFO AS U
                                INNER JOIN CLIENTDB.CLIENT AS C ON C.USER_KEY = U.USER_KEY
                                INNER JOIN CLIENTDB.CLIENT_HAS_CONTACT AS CC ON C.CLIENT_ID = CC.CLIENT_ID
                                INNER JOIN CLIENTDB.CONTACT AS CO ON CO.CONTACT_ID = CC.CONTACT_ID
                                WHERE U.USER_ID = ? AND CO.TYPE='UR'") || json_error_response({
                                                                           logger => $LOGGER, 
                                                                           message => 'Could not prepare query '.$dbh->errstr,
                                                                           status_code => 500
                                                            });

       $query->execute($agency_number) || json_error_response({
                                                                           logger => $LOGGER,
                                                                           message => 'Could not execute query '.$dbh->errstr,
                                                                           status_code => 500
                                                            }); 

    my $result = $query->fetchall_arrayref({});

    $dbh->disconnect();

    my @sorted = sort({$a->{'PRECEDENCE'} <=> $b->{'PRECEDENCE'}} @$result);
    if(scalar(@sorted))
    {
       return $sorted[0]->{'CONTACT_INFO'};
    }
    else{
       return 0;
    }
}


sub get_agency_phone
{
    my ($params) =  @_;
    
    my $dbh =  DBChop();
    my $agency_number = $params->{agency_number} || undef;
    my $LOGGER = $params->{'logger'};

    my $query = $dbh->prepare("SELECT CO.CONTACT_INFO, CO.PRECEDENCE FROM GENSUPDB.USER_INFO AS U
                                INNER JOIN CLIENTDB.CLIENT AS C ON C.USER_KEY = U.USER_KEY
                                INNER JOIN CLIENTDB.CLIENT_HAS_CONTACT AS CC ON C.CLIENT_ID = CC.CLIENT_ID
                                INNER JOIN CLIENTDB.CONTACT AS CO ON CO.CONTACT_ID = CC.CONTACT_ID
                                WHERE U.USER_ID = ? AND CO.TYPE='PW'") || json_error_response({
                                                                           logger => $LOGGER, 
                                                                           message => 'Could not prepare query '.$dbh->errstr,
                                                                           status_code => 500
                                                            });

       $query->execute($agency_number) || json_error_response({
                                                                           logger => $LOGGER,
                                                                           message => 'Could not execute query '.$dbh->errstr,
                                                                           status_code => 500
                                                            }); 

    my $result = $query->fetchall_arrayref({});

    $dbh->disconnect();

    my @sorted = sort({$a->{'PRECEDENCE'} <=> $b->{'PRECEDENCE'}} @$result);
    if(scalar(@sorted))
    {
       return $sorted[0]->{'CONTACT_INFO'};
    }
    else{
       return 0;
    }
}

#PROVIDES A VALID SOLICITING AGENTS FOR A GIVEN AGENCY, POLICY STATE AND LOB
sub get_agent_list
{
     my ($params) =  @_;
  
     my $agents = ValidAgents($params->{agency_number},$params->{effective_date},$params->{policy_state},$params->{line_of_business});

     return $agents;

}

sub get_agent_details
{
    my ($params) =  @_;
    my $agent_number = $params->{agent_number};
    my $LOGGER = $params->{logger};
  
    my $dbh = DBChop();
    my $query = $dbh->prepare("SELECT INC_EXCL_IND,
                                       AGENT_NUMBER,
                                       TRANSFER_DATE_NEW,
                                       TRANS_DATE_AMEND AS TRANSFER_DATE_AMEND,
                                       CANCEL_DATE_AMEND,
                                       NAME,
                                       SOLD_TO_AGENT,
                                       CANCEL_DATE_NEW,
                                       APPOINT_DATE,
                                       IMT_MUTUAL,
                                       WRITE_IA,
                                       WRITE_IL,
                                       WRITE_NE,
                                       WRITE_SD,
                                       WRITE_WI,
                                       WRITE_MN,
                                       WRITE_AZ
		                         FROM AGENTDB.AGENTS_INFO
                               WHERE AGENT_NUMBER = ?") ||  json_error_response({
                                                                           logger => $LOGGER, 
                                                                           message => 'Could not prepare query '.$dbh->errstr,
                                                                           status_code => 500
                                                            });                            
                           
    $query->execute($agent_number) || json_error_response({
                                                               logger => $LOGGER,
                                                               message => 'Could not execute query '.$dbh->errstr,
                                                               status_code => 500
                                                          });      
      
    my $result = $query->fetchall_arrayref({});
    $query->finish();
    $dbh->disconnect();
   
    return $result;
}

sub is_transferred_agent
{
    my ($params) =  @_;

    my $agent_9_digit = $params->{agent_number} || undef;

    my $dbh = DBChop();

    # Pull info for the given agent number
    my $sth = $dbh->prepare
            ("SELECT A.AGENT_NUMBER as ORIGINAL_AGENT,
                     A.NAME AS ORIGINAL_AGENT_NAME,
                     A.TRANSFER_DATE_NEW,
                     A.SOLD_TO_AGENT as TRANSFERRED_TO_AGENT,
                     B.NAME AS TRANSFERRED_AGENT_NAME
              FROM AGENTDB.AGENTS_INFO AS A
              JOIN AGENTDB.AGENTS_INFO AS B
              ON A.SOLD_TO_AGENT = B.AGENT_NUMBER
              WHERE A.AGENT_NUMBER = ? AND
                    A.SOLD_TO_AGENT IS NOT NULL" ) || json_error_response({code => '500', message => 'Could not prepare query'.$dbh->errstr});

        $sth->execute($agent_9_digit) || json_error_response({code => '500', message => 'Could not execute query'.$dbh->errstr});

    my $agentData = $sth->fetchall_arrayref({}) || json_error_response({code => '500', message => 'Could not fetch results'});
    
    return $agentData;
 
}


