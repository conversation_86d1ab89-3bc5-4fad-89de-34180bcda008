stages:
  - test
  - stage
  - longtests
  - analyze
  - publish


run_all_utilities:
  stage: analyze
  image: alpine:latest
  script:
    - chmod +x strategic_utilities/run_all_analyses.sh
    - strategic_utilities/run_all_analyses.sh
  artifacts:
    paths:
      - strategic_utilities/output/
    expire_in: 1 week
  rules:
    - if: $CI_COMMIT_BRANCH == "jh-strategic-utils"
      when: manual
  tags:
    - docker-tests
publish_to_wiki:
  stage: publish
  image: alpine:latest
  variables:
    GIT_STRATEGY: clone
  script:
    # Install required tools
    - apk add --no-cache curl jq
    
    # Create a directory for processed reports
    - mkdir -p processed_reports
    
    # Process each report file
    - |
      for file in strategic_utilities/output/*.md; do
        filename=$(basename "$file")
        # Add metadata header
        echo "# $(basename "$filename" .md)" > "processed_reports/$filename"
        echo "Generated on $(date '+%Y-%m-%d')" >> "processed_reports/$filename"
        echo "Commit: ${CI_COMMIT_SHORT_SHA}" >> "processed_reports/$filename"
        echo "" >> "processed_reports/$filename"
        cat "$file" >> "processed_reports/$filename"
        
        # Upload directly to GitLab as a wiki page using the API
        curl --request POST \
          --header "PRIVATE-TOKEN: ${CI_JOB_TOKEN}" \
          --form "title=$(basename "$filename" .md)" \
          --form "content=@processed_reports/$filename" \
          "https://gitlab.imtins.com/api/v4/projects/${CI_PROJECT_ID}/wikis"
      done
    
    # Create an index page
    - |
      echo "# Utility Reports Index" > index.md
      echo "Last updated: $(date '+%Y-%m-%d %H:%M:%S')" >> index.md
      echo "" >> index.md
      for file in strategic_utilities/output/*.md; do
        name=$(basename "$file" .md)
        echo "* [${name}](${name})" >> index.md
      done
      
      # Upload the index
      curl --request POST \
        --header "PRIVATE-TOKEN: ${CI_JOB_TOKEN}" \
        --form "title=Utility Reports" \
        --form "content=@index.md" \
        "https://gitlab.imtins.com/api/v4/projects/${CI_PROJECT_ID}/wikis"
  artifacts:
    paths:
      - processed_reports/
    expire_in: 1 week
  rules:
    - if: $CI_COMMIT_BRANCH == "jh-strategic-utils"
      when: manual
  dependencies:
    - run_all_utilities
  tags:
    - docker-tests


