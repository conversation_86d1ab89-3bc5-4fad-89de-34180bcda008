
#!/usr/local/bin/perl
package Claims_Monetary_MiscEdits;

require 5.000;


use strict;
use vars qw($VERSION @ISA @EXPORT_OK);


use Exporter;

@ISA = qw(Exporter);
@EXPORT_OK = qw(closeClaimEdits paymentEdits editLimit completeAdjEdits reserveEdits);

$VERSION = '0.01';

use Claims_Error qw(error);
use Claims_Constants qw(getIMTLineCodeScreen getInlandMarineClassCode);
use Claims_ReservesTotals qw(CalculateReserves);
use date_routines qw(getcurrentdate juliandate);
use Claims_Misc qw(getCoveredItemDescQuery getCoveredItemDesc);
use Math::Round qw(nearest);
#sjs add for 110485
use Claims_TrackerAPI qw(:NTF_TYPES :MSG_IDS close_notification insert_notification);
use Common::Platform::Users::usersCommon qw(fetch_user_key_data);

sub closeClaimEdits
{
           my $ENGINE = shift;

           my $error = $ENGINE->{'error'};
           my @closeEditsErrors = ();

    #decide which function to use for editing this claim
    my %IMTLineCodeScreen = getIMTLineCodeScreen();
    my $screen = $IMTLineCodeScreen{$ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'}};

    allLinesCloseEdits($ENGINE);

    if($screen eq 'VEH')
      {
              vehicleCloseEdits($ENGINE);
      }
    elsif($screen eq 'PROP')
      {
          propertyCloseEdits($ENGINE);
      }
    elsif($screen eq 'WC')
      {
          workCompCloseEdits($ENGINE);
      }

    my $closeErrsLoop = $ENGINE->{'close_errors'};
    for my $ce (@$closeErrsLoop)
    {
        push (@closeEditsErrors, $ce);
    }

    return (\@closeEditsErrors);
}

sub allLinesCloseEdits
{
    my $ENGINE = shift;
    my $error = $ENGINE->{'error'};
           my $claimid = $ENGINE->{'claimGeneral'}->{'CLAIM_ID'};
           my @closeEditsErrors = ();
           my $claimStatus = $ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'};
          my $userID = $ENGINE->{'AUTH'}->{'user_key'};


    my $taskListQuery = $ENGINE->{'DBH'}->prepare(
    "SELECT TL.CLM_TASK_ID, TL.TASKS_ID, TL.TASK_COMPLETE, TL.TASK_NA, TL.TASK_USER_DONE, T.TASK_DESCRIPTION
        FROM CLAIMDB.CLM_TASK_LIST AS TL
        INNER JOIN CLAIMDB.TASKS AS T
          ON T.TASKS_ID = TL.TASKS_ID
      WHERE CLAIM_ID = ?
        AND DATE_DELETED = '9999-01-01-01.00.00.000000' AND
        T.PRIVATE_OR_PUBLIC <> '' ")
                || $error->($ENGINE,'CLM_Task_List query prepare failed: '.$ENGINE->{'DBH'}->errstr);

    $taskListQuery->execute($claimid)
        || $error->($ENGINE,'Task List query execute failed: '.$ENGINE->{'DBH'}->errstr);

    my $taskListResults = $taskListQuery->fetchall_arrayref({});

    #changed if statement to not error Glass Claims in Error status if
    #they have no tasks.  107225   2/13/2012  akc.
#    if((scalar(@$taskListResults) < 1
#            && $claimStatus ne 'ER'))
#    {
##        my $errorMessage = 'Not all Tasks have been completed. Please go to File Activity screen and make sure each Task has been indicated as Completed or Not Applicable.';
##        push (@closeEditsErrors, $errorMessage);
#        my %errHash = ('screen'=>'Claims_FileActivity',
#                              'field'=>'ALGComp'.$taskListResults->[0]->{'CLM_TASK_ID'},
#                              'msg'=>'Not all Tasks have been completed. Please go to File Activity screen and make sure each Task has been indicated as Completed or Not Applicable.');
#        #put reference to work hash on array of hash references
#        push (@closeEditsErrors, \%errHash);
#    }
    my $taskNeedCompletion = 'N';
    for my $t (@$taskListResults)
    {
        #This if is checking for the Quarterly Report and Work Comp Memo task.  It will skip thes because there are not
        #check boxes to complete these tasks.
        if($t->{'TASKS_ID'} == 13 || $t->{'TASKS_ID'} == 19)
        { next; }
#        if($t->{'TASKS_ID'} == 1)
#        {
#            if($t->{'TASK_COMPLETE'} eq '9999-01-01 01:00:00.000000' && $t->{'TASK_NA'} eq '9999-01-01 01:00:00.000000')
#            {
#                 my $errorMessage = 'First Contact Insured task is not completed on the File Activity screen.';
#                 push (@closeEditsErrors, $errorMessage);
#            }
#            if($t->{'TASK_COMPLETE'} eq '9999-01-01 01:00:00.000000' && $t->{'TASK_NA'} eq '9999-01-01 01:00:00.000000')
#            {
#                if($t->{'TASK_USER_DONE'} eq '9999-01-01 01:00:00.000000')
#                {
#                     my $errorMessage = 'First Contact Insured Date Contact Made is not completed on the File Activity screen.';
#                     push (@closeEditsErrors, $errorMessage);
#                }
#            }
#            elsif($t->{'TASK_COMPLETE'} ne '9999-01-01 01:00:00.000000')
#            {
#                if($t->{'TASK_USER_DONE'} eq '9999-01-01 01:00:00.000000')
#                {
#                     my $errorMessage = 'First Contact Insured Date Contact Made is not completed on the File Activity screen.';
#                     push (@closeEditsErrors, $errorMessage);
#                }
#            }
#        }
#        if($t->{'TASKS_ID'} == 2)
#        {
#            if($t->{'TASK_COMPLETE'} eq '9999-01-01 01:00:00.000000' && $t->{'TASK_NA'} eq '9999-01-01 01:00:00.000000')
#            {
#                 my $errorMessage = 'First Contact Claimant task is not completed on the File Activity screen.';
#                 push (@closeEditsErrors, $errorMessage);
#            }
#            if($t->{'TASK_COMPLETE'} eq '9999-01-01 01:00:00.000000' && $t->{'TASK_NA'} eq '9999-01-01 01:00:00.000000')
#            {
#                if($t->{'TASK_USER_DONE'} eq '9999-01-01 01:00:00.000000')
#                {
#                     my $errorMessage = 'First Contact Claimant Date Contact Made is not completed on the File Activity screen.';
#                     push (@closeEditsErrors, $errorMessage);
#                }
#            }
#            elsif($t->{'TASK_COMPLETE'} ne '9999-01-01 01:00:00.000000')
#            {
#                if($t->{'TASK_USER_DONE'} eq '9999-01-01 01:00:00.000000')
#                {
#                     my $errorMessage = 'First Contact Claimant Date Contact Made is not completed on the File Activity screen.';
#                     push (@closeEditsErrors, $errorMessage);
#                }
#            }
#        }
#        else
#        {
#            if($t->{'TASK_COMPLETE'} eq '9999-01-01 01:00:00.000000' && $t->{'TASK_NA'} eq '9999-01-01 01:00:00.000000')
#            {
#                 my $taskDescription = $t->{'TASK_DESCRIPTION'};
#                 my $errorMessage = $taskDescription.' task is not completed on the File Activity screen.';
#                 push (@closeEditsErrors, $errorMessage);
#            }
#        }
        if($t->{'TASK_COMPLETE'} eq '9999-01-01 01:00:00.000000' && $t->{'TASK_NA'} eq '9999-01-01 01:00:00.000000')
        {
            $taskNeedCompletion = 'Y';
        }
    }
    #changed if statement to not error Glass Claims in Error status if
    #they have no tasks.  107225   2/13/2012  akc.
    if($taskNeedCompletion eq 'Y'
            && $claimStatus ne 'ER')
    {
#        my $errorMessage = 'Not all Tasks have been completed. Please go to File Activity screen and make sure each Task has been indicated as Completed or Not Applicable.';
#        push (@closeEditsErrors, $errorMessage);
        my %errHash = ('screen'=>'Claims_FileActivity',
                              'field'=>'ALGComp'.$taskListResults->[0]->{'CLM_TASK_ID'},
                              'msg'=>'Not all Tasks have been completed. Please go to File Activity screen and make sure each Task has been indicated as Completed or Not Applicable.');
        #put reference to work hash on array of hash references
        push (@closeEditsErrors, \%errHash);
    }

           my ($curDay, $curMonth, $curYear) = (localtime)[3,4,5];
           $curYear = $curYear+1900;
           $curDay = length($curDay)<2 ? '0'.$curDay : $curDay;
           $curMonth = $curMonth+1;
           $curMonth = length($curMonth)<2 ? '0'.$curMonth : $curMonth;
           my $currentDate = $curYear.'-'.$curMonth.'-'.$curDay;
#           $currentDate = "'$currentDate'";

#    my $alertsQuery = $ENGINE->{'DBH'}->prepare(
#    "SELECT *
#        FROM CLAIMDB.CLM_ALERTS
#      WHERE CLAIM_ID = ?
#        AND COMPLETE = 'N'
#        AND ALERTS_ID IN (5,7,9,10)
#        AND DUE_DATE <= ?
#        AND DATE_DELETED = \'9999-01-01 01:00:00.000000\'")
#                || $error->($ENGINE,'CLM_ALERTS query prepare failed: '.$ENGINE->{'DBH'}->errstr);

#    $alertsQuery->execute($claimid,$currentDate)
#        || $error->($ENGINE,'Alerts query execute failed: '.$ENGINE->{'DBH'}->errstr);

#    my $alertsResults = $alertsQuery->fetchall_arrayref({});

#    my $notificationQuery = $ENGINE->{'DBH'}->prepare(
#    "SELECT DISTINCT CLAIM_ID, COMMENT, MESSAGE_ID
#        FROM CLAIMDB.CLM_NOTIFICATION
#      WHERE CLAIM_ID = ?
#        AND MESSAGE_ID IN (5,7,9,10)
#        AND COMPLETION_DATE IS NULL")
#                || $error->($ENGINE,'CLM_NOTIFICATION query prepare failed: '.$ENGINE->{'DBH'}->errstr);



#    $notificationQuery->execute($claimid)
#        || $error->($ENGINE,'NOTIFICATION query execute failed: '.$ENGINE->{'DBH'}->errstr);

#    my $notificationResults = $notificationQuery->fetchall_arrayref({});
# 107485 sjs tracker has generated, but the day tracker is due is not yet, but they still want to close the claim
# Alert query
    my $alertQuery = $ENGINE->{'DBH'}->prepare('SELECT DUE_DATE, HOW_OFTEN, USER_KEY, CLM_ALERT_ID, ALERT_TYPE, ALERTS_ID FROM CLAIMDB.CLM_ALERTS WHERE CLAIM_ID = ? AND COMPLETE = \'N\' AND DATE_DELETED = \'9999-01-01 01:00:00.000000\'') || $error->($ENGINE,'Alert query prepare failed: '.$ENGINE->{'DBH'}->errstr);
    $alertQuery->execute($claimid) || $error->($ENGINE,'Alert query execute failed: '.$ENGINE->{'DBH'}->errstr);
    my $alertResults = $alertQuery->fetchall_arrayref({});
    my @sortedAlertResults = sort({$a->{'DUE_DATE'} cmp $b->{'DUE_DATE'}} @$alertResults);
    my $userName = '';
    for my $a (@sortedAlertResults)
    {
        my $userKey = $a->{'USER_KEY'};

        my $user_key_data = fetch_user_key_data({
          authorization => $ENGINE->{'AUTH'}->{'platform_access_token'},
          user_key => $userKey,
          max_attempts => 2
        });

        my $hold_first_name = uc($user_key_data->{content}->{data}->[0]->{attributes}->{first_name});
        my $hold_last_Name = uc($user_key_data->{content}->{data}->[0]->{attributes}->{last_name});

        if(defined($hold_first_name) && $hold_first_name gt '' && defined($hold_last_Name) && $hold_last_Name gt '')
        {
            $userName = $hold_first_name.' '.$hold_last_Name;
        }
        else
        {
            $userName = 'Unknown';
        }
                if((defined($a->{'ALERTS_ID'}) && $a->{'ALERTS_ID'} == 5 )
                          && $currentDate ge $a->{'DUE_DATE'})
                    {
                        my %errHash = ('screen'=>'Claims_FileActivity',
                                              'field'=>'claimCovEntry',
                                              'msg'=>'Please satisfy the claim update for '.$userName.'.');
                        #put reference to work hash on array of hash references
                        push (@closeEditsErrors, \%errHash);
                 }
                else
                 {
# 110485 sjs add check to see if tracker notifications need to be closed.
                    if (defined($a->{'ALERTS_ID'}) && $a->{'ALERTS_ID'} == 5 )
                        {
                           close_notification($ENGINE,{'type'=>ALERT_NTF_TYPE,
                          'msg_id'=>COVERAGE_UPDATE_DUE,
                          'claim_id'=>$claimid});
                        }
                  }
                if((defined($a->{'ALERTS_ID'}) && $a->{'ALERTS_ID'} == 7 )
                          && $currentDate ge $a->{'DUE_DATE'})
                    {
                        my %errHash = ('screen'=>'Claims_FileActivity',
                                              'field'=>'',
                                              'msg'=>'Please satisfy the quarterly report for '.$userName.'.');
                        #put reference to work hash on array of hash references
                        push (@closeEditsErrors, \%errHash);
                 }
                else
                 {
# 110485 sjs add check to see if tracker notifications need to be closed.
                    if (defined($a->{'ALERTS_ID'}) && $a->{'ALERTS_ID'} == 7 )
                        {
                           close_notification($ENGINE,{'type'=>ALERT_NTF_TYPE,
                          'msg_id'=>QUARTERLY_REPORT_DUE,
                          'claim_id'=>$claimid});
                        }
                  }
                if((defined($a->{'ALERTS_ID'}) && $a->{'ALERTS_ID'} == 9 )
                          && $currentDate ge $a->{'DUE_DATE'})
                    {
                        my %errHash = ('screen'=>'Claims_FileActivity',
                                              'field'=>'',
                                              'msg'=>'Please satisfy work comp medical memo for '.$userName.'.');
                        #put reference to work hash on array of hash references
                        push (@closeEditsErrors, \%errHash);
                 }
                else
                 {
# 110485 sjs add check to see if tracker notifications need to be closed.
                    if (defined($a->{'ALERTS_ID'}) && $a->{'ALERTS_ID'} == 9 )
                        {
                           close_notification($ENGINE,{'type'=>ALERT_NTF_TYPE,
                          'msg_id'=>WC_MEDICAL_MEMO,
                          'claim_id'=>$claimid});
                        }
                  }
                if((defined($a->{'ALERTS_ID'}) && $a->{'ALERTS_ID'} == 10 )
                          && $currentDate ge $a->{'DUE_DATE'})
                     {
                        my %errHash = ('screen'=>'Claims_FileActivity',
                                              'field'=>'',
                                              'msg'=>'Please satisfy work comp disability memo for '.$userName.'.');
                        #put reference to work hash on array of hash references
                        push (@closeEditsErrors, \%errHash);
                 }
                else
                 {
# 110485 sjs add check to see if tracker notifications need to be closed.
                    if (defined($a->{'ALERTS_ID'}) && $a->{'ALERTS_ID'} == 10 )
                        {
                           close_notification($ENGINE,{'type'=>ALERT_NTF_TYPE,
                          'msg_id'=>WC_DISABILITY_MEMO,
                          'claim_id'=>$claimid});
                        }
                  }

    }

#    if (scalar(@$alertsResults) > 0)
#    {
#        my $errorMessage = 'Can not close claim.  There are some alerts that need to be taken care of.';
#        push (@closeEditsErrors, $errorMessage);
#    }

    #Check to see if Possible wind/hail has been set.
    if($ENGINE->{'claimGeneral'}->{'CLM_STORM_ID'} == 99999)
    {
            my %errHash = ('screen'=>'Claims_Monetary',
                                  'field'=>'',
                                  'msg'=>'Storm code 99999 Possible wind/hail cannot be set on a closed claim.  Please remove the code, press Save, and add the correct code if necessary.');
            #put reference to work hash on array of hash references
            push (@closeEditsErrors, \%errHash);
    }

    #Edits for Flagship fields
    my $partiesQuery = $ENGINE->{'DBH'}->prepare('SELECT P.PARTY_ID, FIRST_NAME, LAST_NAME, ORM_TERM_DATE, TPOC_SETTLE_DATE, TPOC_SETTLE_AMT, FS_READY_REPORT, FS_MEDICAL_READY, MEDICARE_ELIGIBLE, P.BODILY_INJURY
    FROM CLAIMDB.CLM_PARTIES P
    LEFT OUTER JOIN CLAIMDB.CLM_INJ_DEF I
    ON I.PARTY_ID = P.PARTY_ID
    WHERE P.CLAIM_ID = ? AND P.DATE_DELETED = \'9999-01-01 01:00:00.000000\'') || $error->($ENGINE,'Parties query prepare failed: '.$ENGINE->{'DBH'}->errstr);
    $partiesQuery->execute($claimid) || $error->($ENGINE,'Parties query execute failed: '.$ENGINE->{'DBH'}->errstr);
    my $partiesResults = $partiesQuery->fetchall_arrayref({});

    my $cashQuery = $ENGINE->{'DBH'}->prepare('SELECT C.CLM_CASH_ID, PAYMENT_AMT
    FROM CLAIMDB.CLM_PARTY_CASH P
    INNER JOIN CLAIMDB.CLM_CASH C
    ON C.CLM_CASH_ID = P.CLM_CASH_ID
    WHERE P.PARTY_ID = ? AND C.DATE_DELETED = \'9999-01-01 01:00:00.000000\'') || $error->($ENGINE,'Cash query prepare failed: '.$ENGINE->{'DBH'}->errstr);

    for my $p (@$partiesResults)
    {
        if(defined($p->{'FS_MEDICAL_READY'}) && $p->{'FS_MEDICAL_READY'} eq 'Y' &&
           defined($p->{'ORM_TERM_DATE'}) && $p->{'ORM_TERM_DATE'} eq '9999-01-01'
            && !($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /600|605/))
        {
            my %errHash = ('screen'=>'Claims_Details',
                                  'field'=>'ormtermdate'.$p->{'PARTY_ID'},
                                  'msg'=>'ORM Termination Date is missing for  '.$p->{'FIRST_NAME'}.' '.$p->{'LAST_NAME'});
            #put reference to work hash on array of hash references
            push (@closeEditsErrors, \%errHash);
        }
        if(defined($p->{'FS_READY_REPORT'}) && $p->{'FS_READY_REPORT'} eq 'Y' &&
           defined($p->{'TPOC_SETTLE_DATE'}) && $p->{'TPOC_SETTLE_DATE'} eq '9999-01-01')
        {
            my %errHash = ('screen'=>'Claims_Details',
                                  'field'=>'tpocsettledate'.$p->{'PARTY_ID'},
                                  'msg'=>'TPOC Settlement Date is missing for  '.$p->{'FIRST_NAME'}.' '.$p->{'LAST_NAME'});
            #put reference to work hash on array of hash references
            push (@closeEditsErrors, \%errHash);
        }
        if(defined($p->{'FS_READY_REPORT'}) && $p->{'FS_READY_REPORT'} eq 'Y' &&
           defined($p->{'TPOC_SETTLE_AMT'}) && $p->{'TPOC_SETTLE_AMT'} eq '0.00')
        {
            my %errHash = ('screen'=>'Claims_Details',
                                  'field'=>'tpocsettleamt'.$p->{'PARTY_ID'},
                                  'msg'=>'TPOC Settlement Amount is missing for  '.$p->{'FIRST_NAME'}.' '.$p->{'LAST_NAME'});
            #put reference to work hash on array of hash references
            push (@closeEditsErrors, \%errHash);
        }

        $cashQuery->execute($p->{'PARTY_ID'}) || $error->($ENGINE,'Cash query execute failed: '.$ENGINE->{'DBH'}->errstr);
        my $cashResults = $cashQuery->fetchall_arrayref({});

        if (scalar(@$cashResults) > 0)
        {
            if(defined($p->{'MEDICARE_ELIGIBLE'}) && $p->{'MEDICARE_ELIGIBLE'} eq 'Y' &&
               defined($p->{'BODILY_INJURY'}) && $p->{'BODILY_INJURY'} eq 'Y')
            {

                my $paymentTotal = 0;
                for my $c (@$cashResults)
                {
                    $paymentTotal = $paymentTotal + $c->{'PAYMENT_AMT'}
                }

                if($paymentTotal > 0)
                {
	                if(defined($p->{'FS_MEDICAL_READY'}) && $p->{'FS_MEDICAL_READY'} eq 'N' &&
	                   defined($p->{'FS_READY_REPORT'}) && $p->{'FS_READY_REPORT'} eq 'N')
	                {
	                    my %errHash = ('screen'=>'Claims_Details',
	                                          'field'=>'FSmedical'.$p->{'PARTY_ID'},
	                                          'msg'=>$p->{'FIRST_NAME'}.' '.$p->{'LAST_NAME'}.' is Medicare eligible with payments. Please mark them for referral in the Details screen. If the party is not injured, please change the injury status to No in the Details screen.');
	                    #put reference to work hash on array of hash references
	                    push (@closeEditsErrors, \%errHash);
	                }
                }
            }
        }
    }

    $ENGINE->{'close_errors'} = \@closeEditsErrors;
}

sub vehicleCloseEdits
{
    my $ENGINE = shift;
           my $claimid = $ENGINE->{'claimGeneral'}->{'CLAIM_ID'};
           my $error = $ENGINE->{'error'};

    my $holdErrors = $ENGINE->{'close_errors'};  #reference to array
    #dereference array so we can push more data onto it
    my @closeEditsErrors = ();
    if(scalar @$holdErrors)
    { @closeEditsErrors = @$holdErrors; }

    my $vehQuery = $ENGINE->{'DBH'}->prepare(
    "SELECT CLM_COMMON_STAT_ID, PARTY_ID, YEAR, MODEL, VEH_INTERIOR_UPKEEP, TIRE_TREAD, DRIVING_DIRECTION, ROAD_TYPE, WEATHER
        FROM CLAIMDB.CLM_VEHICLE
      WHERE CLAIM_ID = ?
        AND DATE_DELETED = \'9999-01-01 01:00:00.000000\'")
                || $error->($ENGINE,'CLM_Vehicle query prepare failed: '.$ENGINE->{'DBH'}->errstr);

    $vehQuery->execute($claimid)
        || $error->($ENGINE,'Vehicle query execute failed: '.$ENGINE->{'DBH'}->errstr);

    my $vehResults = $vehQuery->fetchall_arrayref({});

    if (scalar(@$vehResults) > 0)
    {
        if ($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} !~ /052|030|051|804|814|816/ && $ENGINE->{'claimGeneral'}->{'MANUAL_OR_WHAT'} ne 'G')
        {
            for my $x (@$vehResults)
            {
                if ($x->{'DRIVING_DIRECTION'} eq '')
                {
#                    my $errorMessage = 'Please complete Vehicle Direction for '.$x->{'YEAR'}.' '.$x->{'MODEL'}.' on the Details screen.';
#                    push (@closeEditsErrors, $errorMessage);
                        my %errHash = ('screen'=>'Claims_Details',
                                              'field'=>'direction'.$x->{'CLM_COMMON_STAT_ID'},
                                              'msg'=>'Please complete Vehicle Direction for '.$x->{'YEAR'}.' '.$x->{'MODEL'}.' on the Details screen.');
                        #put reference to work hash on array of hash references
                        push (@closeEditsErrors, \%errHash);
                }
                if ($x->{'ROAD_TYPE'} eq '')
                {
#                    my $errorMessage = 'Please complete Type of Road for '.$x->{'YEAR'}.' '.$x->{'MODEL'}.' on the Details screen.';
#                    push (@closeEditsErrors, $errorMessage);
                        my %errHash = ('screen'=>'Claims_Details',
                                              'field'=>'roadType'.$x->{'CLM_COMMON_STAT_ID'},
                                              'msg'=>'Please complete Type of Road for '.$x->{'YEAR'}.' '.$x->{'MODEL'}.' on the Details screen.');
                        #put reference to work hash on array of hash references
                        push (@closeEditsErrors, \%errHash);
                }
            }
        }
        if ($vehResults->[0]->{'WEATHER'} eq '' && $ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} !~ /030|051|804|814|816/ && $ENGINE->{'claimGeneral'}->{'MANUAL_OR_WHAT'} ne 'G')
        {
#            my $errorMessage = 'Please complete Weather Conditions on the Details screen.';
#            push (@closeEditsErrors, $errorMessage);
            my %errHash = ('screen'=>'Claims_Details',
                                  'field'=>'weather',
                                  'msg'=>'Please complete Weather Conditions on the Details screen.');
            #put reference to work hash on array of hash references
            push (@closeEditsErrors, \%errHash);
        }

        for my $x (@$vehResults)
        {
            if ($x->{'PARTY_ID'} == 0)
            {
                my %errHash = ('screen'=>'Claims_Details',
                                      'field'=>'driverSel'.$x->{'CLM_COMMON_STAT_ID'},
                                      'msg'=>'Please select a driver for '.$x->{'YEAR'}.' '.$x->{'MODEL'}.' on the Details screen.');
                #put reference to work hash on array of hash references
                push (@closeEditsErrors, \%errHash);
            }
        }
    }
    if($ENGINE->{'claimGeneral'}->{'LOSS_TIME_GENERIC'} eq '' && $ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} !~ /030|051|804|814|816/ && $ENGINE->{'claimGeneral'}->{'MANUAL_OR_WHAT'} ne 'G')
    {
#        my $errorMessage = 'Please complete Time of Day Loss Occurred on the Details screen.';
#        push (@closeEditsErrors, $errorMessage);
        my %errHash = ('screen'=>'Claims_Details',
                              'field'=>'lossTimeGeneric',
                              'msg'=>'Please complete Time of Day Loss Occurred on the Details screen.');
        #put reference to work hash on array of hash references
        push (@closeEditsErrors, \%errHash);
    }

   # REQUIRE SDIP ON PERSONAL VEHICLE CLAIMS (Personal Auto, Motorcycle, Recreational Vehicle, Boat)
   if ($ENGINE->{'Monetary'}->{'SDIPERR'} eq 'Y' )
   {
            my $numOfSDIPS = @{$ENGINE->{'Monetary'}->{'CLM_SDIPS'}};
            my $errSDIPS = 0;
            for my $a (@{$ENGINE->{'Monetary'}->{'CLM_SDIPS'}})
            {
                for my $t (keys %$a)
                  {
                      if($t eq 'DELETE' && $a->{$t} eq 'Y')
                      { $errSDIPS = $errSDIPS + 1; }
                  }
            }
            if($numOfSDIPS == $errSDIPS)
            {
                 my %errHash = ('screen'=>'Claims_Monetary',
                                       'field'=>'',
                                       'msg'=>'An SDIP is required for all personal vehicle claims.');
                 #put reference to work hash on array of hash references
                 push (@closeEditsErrors, \%errHash);
            }
   }
   elsif ($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /010|011|030|031|051|052/ && $ENGINE->{'claimGeneral'}->{'MANUAL_OR_WHAT'} ne 'G')
   {
      my $sdipQuery = $ENGINE->{'DBH'}->prepare(
         "SELECT SDIP_CODE
            FROM CLAIMDB.CLM_SDIPS
           WHERE CLAIM_ID = ?
             AND DATE_DELETED = \'9999-01-01 01:00:00.000000\'") || $error->($ENGINE,'SDIP query prepare failed: '.$ENGINE->{'DBH'}->errstr);

      $sdipQuery->execute($claimid) || $error->($ENGINE,'SDIP query execute failed: '.$ENGINE->{'DBH'}->errstr);

      my $sdipResults = $sdipQuery->fetchall_arrayref({});

      if (scalar(@$sdipResults) < 1)
      {
#         my $errorMessage = 'An SDIP is required for all personal vehicle claims.';
#         push (@closeEditsErrors, $errorMessage);
         my %errHash = ('screen'=>'Claims_Monetary',
                               'field'=>'',
                               'msg'=>'An SDIP is required for all personal vehicle claims.');
         #put reference to work hash on array of hash references
         push (@closeEditsErrors, \%errHash);
      }
   }
    $ENGINE->{'close_errors'} = \@closeEditsErrors;
}

sub propertyCloseEdits
{
    my $ENGINE = shift;
           my $claimid = $ENGINE->{'claimGeneral'}->{'CLAIM_ID'};
           my $error = $ENGINE->{'error'};

    my $holdErrors = $ENGINE->{'close_errors'};  #reference to array
    #dereference array so we can push more data onto it
    my @closeEditsErrors = ();
    if(scalar @$holdErrors)
    { @closeEditsErrors = @$holdErrors; }

    my $propertyQuery = $ENGINE->{'DBH'}->prepare(
    "SELECT PS.CLM_COMMON_STAT_ID, PS.INTERIOR_UPKEEP, PS.EXTERIOR_UPKEEP, PS.YARD_UPKEEP, PS.AUTO_OUTSIDE_LIGHT, CS.LOCATION_NO
        FROM CLAIMDB.CLM_COMMON_STAT AS CS
        INNER JOIN CLAIMDB.CLM_PROPERTY_STAT AS PS
          ON CS.CLM_COMMON_STAT_ID = PS.CLM_COMMON_STAT_ID AND PS.DATE_DELETED = '9999-01-01 01:00:00.000000'
      WHERE CS.CLAIM_ID = ? AND CS.STAT_TYPE <> 'I'
        AND CS.DATE_DELETED = \'9999-01-01 01:00:00.000000\'")
                || $error->($ENGINE,'CLM_Property Stat query prepare failed: '.$ENGINE->{'DBH'}->errstr);

    $propertyQuery->execute($claimid)
        || $error->($ENGINE,'Property Stat query execute failed: '.$ENGINE->{'DBH'}->errstr);

    my $propertyResults = $propertyQuery->fetchall_arrayref({});

    my $locationNums = '';
    my $statIds = '';

    if ($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /100|110|111|112|113|120/)
    {
        for my $p (@$propertyResults)
        {
            if($locationNums ne '')
            {$locationNums .= ',';}
            $locationNums .= $p->{'LOCATION_NO'};
            if($statIds ne '')
            {$statIds .= ',';}
            $statIds .= $p->{'CLM_COMMON_STAT_ID'};
            if ($p->{'INTERIOR_UPKEEP'} eq '')
            {
#                my $errorMessage = 'Please complete Home Interior Upkeep on the Details screen.';
#                push (@closeEditsErrors, $errorMessage);
                    my %errHash = ('screen'=>'Claims_Details',
                                          'field'=>'iupkeep'.$p->{'CLM_COMMON_STAT_ID'},
                                          'msg'=>'Please complete Home Interior Upkeep on the Details screen.');
                    #put reference to work hash on array of hash references
                    push (@closeEditsErrors, \%errHash);
            }
            if ($p->{'EXTERIOR_UPKEEP'} eq '')
            {
#                my $errorMessage = 'Please complete Home Exterior Upkeep on the Details screen.';
#                push (@closeEditsErrors, $errorMessage);
                    my %errHash = ('screen'=>'Claims_Details',
                                          'field'=>'eupkeep'.$p->{'CLM_COMMON_STAT_ID'},
                                          'msg'=>'Please complete Home Exterior Upkeep on the Details screen.');
                    #put reference to work hash on array of hash references
                    push (@closeEditsErrors, \%errHash);
            }
            if ($p->{'YARD_UPKEEP'} eq '')
            {
#                my $errorMessage = 'Please complete Yard Upkeep on the Details screen.';
#                push (@closeEditsErrors, $errorMessage);
                    my %errHash = ('screen'=>'Claims_Details',
                                          'field'=>'yupkeep'.$p->{'CLM_COMMON_STAT_ID'},
                                          'msg'=>'Please complete Yard Upkeep on the Details screen.');
                    #put reference to work hash on array of hash references
                    push (@closeEditsErrors, \%errHash);
            }
        }
    }
    else
    {
        for my $p (@$propertyResults)
        {
            if($locationNums ne '')
            {$locationNums .= ',';}
            $locationNums .= $p->{'LOCATION_NO'};
            if($statIds ne '')
            {$statIds .= ',';}
            $statIds .= $p->{'CLM_COMMON_STAT_ID'};
        }
    }

    if($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /100|105|112|113|575/ 
       && $ENGINE->{'claimGeneral'}->{'PROP_OR_LIAB'} =~ /P|B/
       && $ENGINE->{'claimGeneral'}->{'CLOSE_RECOVERABLE'} eq 'P'
       && (!defined($ENGINE->{'CGI'}->param('recovPend')) || $ENGINE->{'CGI'}->param('recovPend') ne 'on'))
    {
        my $recovReserveQuery = $ENGINE->{'DBH'}->prepare
        ("select distinct l.roof_replaced,l.year_roof_replaced,l.depreciate_amount,l.clm_common_stat_id,ce.location_no,r.recoverable
            from claimdb.clm_location l
            join claimdb.clm_common_stat cs
            on cs.clm_common_stat_id = l.clm_common_stat_id
            join claimdb.clm_covs_endorses ce
            on ce.location_no = cs.location_no
            join claimdb.clm_reserves r
            on r.coverage_id = ce.coverage_id
            where r.claim_id = ?
            and r.recoverable = 'X'
            and ce.location_no in ($locationNums)
            and l.clm_common_stat_id in ($statIds)
            and l.loc_type = 'AC'
            for fetch only;")
        || error($ENGINE,'Recovery reserve prepare failed: '.$ENGINE->{'DBH'}->errstr);
        $recovReserveQuery->execute($claimid)
        || error($ENGINE,'Recovery reserve query execute failed: '.$ENGINE->{'DBH'}->errstr);
        my $recovReserveResults = $recovReserveQuery->fetchall_arrayref({})
        || error($ENGINE,'Recovery reserve query fetch failed: '.$ENGINE->{'DBH'}->errstr);

        if(scalar @$recovReserveResults > 0)
        {
            for my $loc (@$recovReserveResults)
            {
                if(!defined($loc->{'ROOF_REPLACED'}) || $loc->{'ROOF_REPLACED'} eq '')
                {
                    my %errHash = ('screen'=>'Claims_Details',
                                          'field'=>'roofReplaced'.$loc->{'CLM_COMMON_STAT_ID'},
                                          'msg'=>'Please complete Has the Roof Been Replaced on the Details screen.');
                    push (@closeEditsErrors, \%errHash);
                }
                elsif($loc->{'ROOF_REPLACED'} eq 'Y')
                {
                    if(!defined($loc->{'YEAR_ROOF_REPLACED'}) || $loc->{'YEAR_ROOF_REPLACED'} eq '')
                    {
                        my %errHash = ('screen'=>'Claims_Details',
                                          'field'=>'yearRoofReplaced'.$loc->{'CLM_COMMON_STAT_ID'},
                                          'msg'=>'Please complete Year Roof Replaced on the Details screen.');
                        push (@closeEditsErrors, \%errHash);
                    }
                    if(!defined($loc->{'DEPRECIATE_AMOUNT'}) || $loc->{'DEPRECIATE_AMOUNT'} eq '')
                    {
                        my %errHash = ('screen'=>'Claims_Details',
                                          'field'=>'depreciationAmt'.$loc->{'CLM_COMMON_STAT_ID'},
                                          'msg'=>'Please complete Nonrecoverable Depreciation Amount on the Details screen.');
                        push (@closeEditsErrors, \%errHash);
                    }
                }
            }
        }
    }

    $ENGINE->{'close_errors'} = \@closeEditsErrors;
}

sub workCompCloseEdits
{
    my $ENGINE = shift;
    my $claimid = $ENGINE->{'claimGeneral'}->{'CLAIM_ID'};
    my $error = $ENGINE->{'error'};

    my $holdErrors = $ENGINE->{'close_errors'};  #reference to array
    #dereference array so we can push more data onto it
    my @closeEditsErrors = ();
    if(scalar @$holdErrors)
    { @closeEditsErrors = @$holdErrors; }

    my $cashQuery = $ENGINE->{'DBH'}->prepare(
    "SELECT NCCI_BEN_TYPE_CODE, LOSS_CODE
        FROM CLAIMDB.CLM_CASH
      WHERE CLAIM_ID = ?
        AND DATE_DELETED = \'9999-01-01 01:00:00.000000\'")
                || $error->($ENGINE,'CLM_Vehicle query prepare failed: '.$ENGINE->{'DBH'}->errstr);

    $cashQuery->execute($claimid)
        || $error->($ENGINE,'Vehicle query execute failed: '.$ENGINE->{'DBH'}->errstr);

    my $cashResults = $cashQuery->fetchall_arrayref({});

    my $wcStatQuery = $ENGINE->{'DBH'}->prepare(
    "SELECT NCCI_MAX_MED_IMP_DATE, NCCI_DIS_LOEC_PERCT, LUMP_SUM_IND, NCCI_MED_EXTING_IND, BENEFIT_JURISDICT_ST
        FROM CLAIMDB.CLM_WC_STAT
      WHERE CLAIM_ID = ?
        AND DATE_DELETED = \'9999-01-01 01:00:00.000000\'")
                || $error->($ENGINE,'CLM_Vehicle query prepare failed: '.$ENGINE->{'DBH'}->errstr);

    $wcStatQuery->execute($claimid)
        || $error->($ENGINE,'Vehicle query execute failed: '.$ENGINE->{'DBH'}->errstr);

    my $wcStatResults = $wcStatQuery->fetchall_arrayref({});

#    my $benTypeCodeSw = 'N';
#    for my $x (@$cashResults)
#    {
#        if (defined($x->{'NCCI_BEN_TYPE_CODE'}) && $x->{'NCCI_BEN_TYPE_CODE'} gt '')
#        {
#            $benTypeCodeSw = 'Y';
#            last;
#        }
#    }

#    #Close edits for MMI date when Benefit Type Code is set
#    if($benTypeCodeSw eq 'Y')
#    {
#        for my $w (@$wcStatResults)
#        {
#            if (defined($w->{'NCCI_MAX_MED_IMP_DATE'}) && $w->{'NCCI_MAX_MED_IMP_DATE'} eq '9999-01-01')
#            {
#                my %errHash = ('screen'=>'Claims_Details',
#                                      'field'=>'mmiDate',
#                                      'msg'=>'Please complete Maximum Medical Improvement(MMI) Date on the Details screen in the Show/Hide NCCI Reporting Stat section.');
#                #put reference to work hash on array of hash references
#                push (@closeEditsErrors, \%errHash);
#            }
#        }
#    }

    #Close edits for Disability payments
    for my $c (@$cashResults)
    {
        if (defined($c->{'LOSS_CODE'}) && $c->{'LOSS_CODE'} eq '01' && defined($c->{'NCCI_BEN_TYPE_CODE'}) && $c->{'NCCI_BEN_TYPE_CODE'} =~ /02|03|04/)
        {
	        for my $w (@$wcStatResults)
	        {
                my $jurisdictionState = $w->{'BENEFIT_JURISDICT_ST'};
                if (defined($w->{'NCCI_MAX_MED_IMP_DATE'}) && $w->{'NCCI_MAX_MED_IMP_DATE'} eq '9999-01-01' ||
                    !defined($w->{'NCCI_MAX_MED_IMP_DATE'}))
                {
                    my %errHash = ('screen'=>'Claims_Details',
                                          'field'=>'mmiDate',
                                          'msg'=>'Please complete Maximum Medical Improvement(MMI) Date on the Details screen in the Show/Hide NCCI Reporting Stat section.');
                    #put reference to work hash on array of hash references
                    push (@closeEditsErrors, \%errHash);
                }
	            if (defined($w->{'NCCI_DIS_LOEC_PERCT'}) && $w->{'NCCI_DIS_LOEC_PERCT'} eq '' ||
	                !defined($w->{'NCCI_DIS_LOEC_PERCT'}))
	            {
                    if(defined($jurisdictionState) && $jurisdictionState =~ /AL|AZ|AR|CO|CT|DC|HI|ID|IL|IA|KS|KY|ME|MD|MS|MO|MT|NE|NH|NM|NC|OK|OR|RI|SC|SC|TN|VA|WI/)
                    {
	                    my %errHash = ('screen'=>'Claims_Details',
	                                          'field'=>'disLOECPct',
	                                          'msg'=>'Please complete Disability/Loss of Earning Capacity (LOEC) Percentage on the Details screen in the Show/Hide NCCI Reporting Stat section.');
	                    #put reference to work hash on array of hash references
	                    push (@closeEditsErrors, \%errHash);
	                }
	            }
	        }
	    }
    }

    #Fields required when closed.
    for my $w (@$wcStatResults)
    {
        if (defined($w->{'LUMP_SUM_IND'}) && $w->{'LUMP_SUM_IND'} eq '' ||
            !defined($w->{'LUMP_SUM_IND'}))
        {
            my %errHash = ('screen'=>'Claims_Details',
                                  'field'=>'lumpsumind',
                                  'msg'=>'Please complete Lump-Sum Indicator on the Details screen in the Show/Hide NCCI Reporting Stat section.');
            #put reference to work hash on array of hash references
            push (@closeEditsErrors, \%errHash);
        }
        if (defined($w->{'NCCI_MED_EXTING_IND'}) && $w->{'NCCI_MED_EXTING_IND'} eq 'U' || $w->{'NCCI_MED_EXTING_IND'} eq '' ||
            !defined($w->{'NCCI_MED_EXTING_IND'}))
        {
            my %errHash = ('screen'=>'Claims_Details',
                                  'field'=>'medExtinInd',
                                  'msg'=>'Please complete Medical Extinguishment Indicator on the Details screen in the Show/Hide NCCI Reporting Stat section.');
            #put reference to work hash on array of hash references
            push (@closeEditsErrors, \%errHash);
        }
    }

    $ENGINE->{'close_errors'} = \@closeEditsErrors;
}

sub paymentEdits
{
        my $ENGINE = shift;

    my $hasMedical = 'N';
    my $hasBIUMUIM = 'N';
    my $hasBIMedical = 'N';
    my $missingNCCI = 'N';
        my $paymentError = 'N';
        my $hasPartyMissingData = 'N';
        my @paymentErrors = ();
        my @wcpaymentErrors = ();
        my %partyHash = ();
        my $line = $ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'};
    my $policyState = $ENGINE->{'claimGeneral'}->{'POLICY_STATE'};
    my $claimId = $ENGINE->{'claimGeneral'}->{'CLAIM_ID'};
    my $lossDate = substr($ENGINE->{'claimGeneral'}->{'LOSS_DATE_TIME'},0,10);
    my $accidentState = $ENGINE->{'claimGeneral'}->{'ACCIDENT_STATE'};
    my $error = $ENGINE->{'error'};

    for my $q (@{$ENGINE->{'Monetary'}->{'CLM_CASH'}})
    {
            my $lossType = '';
            my $lossCause = '';
            my $partyID = 0;
            my $cov = '';
            my $partyMissingData = 'N';
        my $claimantName = '';
        my $partyMissingSex = '';
        my $partyMissingMarital = '';
        my $partyMissingSS = '';
        my $partyMissingBirth = '';
        my $coverageID = 0;
        my $ncci_Jurisdiction_st_Missing = 'N';
        my $ncci_Tran_From_Date_Missing = 'N';
        my $ncci_Tran_To_Date_Missing = 'N';
        my $ncci_Pre_Inj_wkly_wage_Missing = 'N';
        my $ncci_Weekly_Ben_Amt = 'N';
        my $ncci_Employment_status = 'N';
        my $ncci_impairment_perct = 'N';
        my $ncci_impairment_basis = 'N';
        my $ncci_ben_off_code = 'N';
        my $ncci_ben_off_amt = 'N';
        my $ncci_pre_ex_dis_perct = 'N';
        my $wc_act = 'N';
        my $wcHireDate = 'N';
        my $accidentSt = 'N';
        my $attorneySw = 'N';

        #skip editing payments with 0
        if (defined $q->{'PAYMENT_AMT'}
                && $q->{'PAYMENT_AMT'} == 0)
        {
                 next;
        }
        if (!defined $q->{'PAYMENT_AMT'})
        {
                 next;
        }

        #find the matching reserve to get the loss code and
        #cause of loss code and the party_id
            for my $r (@{$ENGINE->{'Monetary'}->{'OLD_CLM_RESERVES'}})
            {
                 if ($q->{'CLM_RESERVES_ID'} == $r->{'CLM_RESERVES_ID'})
                 {
                     $lossType = $r->{'LOSS_CODE'};
                     #not all lines have a cause-of-loss so we can space it
                     #here if needed
                            $lossCause = $r->{'IMT_CAUSE_OF_LOSS'} || '';
                            $partyID = $r->{'PARTY_ID'} || 0;
                            $coverageID = $q->{'COVERAGE_ID'} || 0;
                            last;
                 }
            }
            if($lossType eq '')
            {
                $lossType = $q->{'LOSS_CODE'};
                $lossCause = $q->{'IMT_CAUSE_OF_LOSS'} || '';
                $partyID = $q->{'PARTY_ID'} || 0;
                $coverageID = $q->{'COVERAGE_ID'} || 0;
            }

        if ($coverageID)
        {
                #get the coverage code too, in case we need it
                for my $c (@{$ENGINE->{'Monetary'}->{'OLD_CLM_COVS_ENDORSES'}})
                {
                     if ($c->{'COVERAGE_ID'} == $coverageID)
                     {
                        $cov = $c->{'IMT_COVERAGE'};
                           }
                  }
        }

        #now find the party and see if he is missing his HCIN/SS#, birthdate,
        #marital or sex information
        if ($partyID)
        {
                for my $r (@{$ENGINE->{'Monetary'}->{'OLD_CLM_PARTIES'}})
                {
                     if ($partyID == $r->{'PARTY_ID'} && $r->{'BUSINESS_NAME'} eq '')
                     {
                             #check to see if he has sex
                    if (!$r->{'SEX'} || $r->{'SEX'} eq '')
                    {
                             $partyMissingData = 'Y';
                             $partyMissingSex = 'Gender';
                    }
                    if($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /600|605/ && $ENGINE->{'claimGeneral'}->{'ACCIDENT_STATE'} ne 'IA')
                    {

                    }
                    else
                    {
                            if (!$r->{'MARITAL'} || $r->{'MARITAL'} eq '')
                            {
                                $partyMissingData = 'Y';
                                $partyMissingMarital = 'Marital Status';
                            }
                    }
                    if ($r->{'BIRTHDATE'} eq '9999-01-01')
                    {
                             $partyMissingData = 'Y';
                             $partyMissingBirth = 'Birthdate';
                    }

                    if ((!$r->{'ID_TYPE'} || $r->{'ID_TYPE'} eq '')
                            && (!$r->{'ID_NO'} || $r->{'ID_NO'} eq '')
                            && (!$r->{'HCIN_NO'} || $r->{'HCIN_NO'} eq ''))
                    {
                             $partyMissingData = 'Y';
                             $partyMissingSS = 'SSN or HCIN';
                    }

                    $claimantName =  $r->{'FIRST_NAME'} . ' ' . $r->{'LAST_NAME'};
                     }
                }
            }

        if($line =~ /600|605/ && $lossType eq '01')
        {
            my $wcStatGet = $ENGINE->{'DBH'}->prepare
                ("SELECT NCCI_TRAN_FROM_DATE, NCCI_TRAN_TO_DATE, BENEFIT_JURISDICT_ST, NCCI_PRE_INJ_WKLY_WAGE, WC_EMPLOYMENT_STATUS, NCCI_IMPAIRMENT_PERCT, NCCI_IMPAIR_PCT_BASIS,
                         NCCI_BEN_OFF_CODE, NCCI_BEN_OFF_AMT, NCCI_PRE_EX_DIS_PERCT, WC_ACT, WC_HIRE_DATE, NCCI_WEEKLY_BEN_AMT
                 from CLAIMDB.CLM_WC_STAT
                    WHERE
                    CLAIM_ID = ?
                    and DATE_DELETED = '9999-01-01-01.00.00.000000'")
                        || $error->($ENGINE);
            #retrieve the wc_stat
            $wcStatGet->execute($claimId)
                || $error->($ENGINE);

            my $wcResult = $wcStatGet->fetchall_arrayref({})
                || $error->($ENGINE);

            #Check Benefit Type Code
            for my $s (@$wcResult)
            {
                if ($q->{'NCCI_BEN_TYPE_CODE'} =~ /01|02|03|04|05|11|61/ &&
                    $s->{'NCCI_TRAN_FROM_DATE'} eq '9999-01-01')
                {
                    $ncci_Tran_From_Date_Missing = 'Y';
                    $missingNCCI = 'Y';
                }
                if ($q->{'NCCI_BEN_TYPE_CODE'} =~ /01|02|03|04|05|11|61/ &&
                    $s->{'NCCI_TRAN_TO_DATE'} eq '9999-01-01')
                {
                    $ncci_Tran_To_Date_Missing = 'Y';
                    $missingNCCI = 'Y';
                }
                if ($q->{'NCCI_BEN_TYPE_CODE'} eq '03' &&
                    $s->{'BENEFIT_JURISDICT_ST'} =~ /AK|KY|MN|MT|OR|TN/ && $lossDate ge '2014-07-01')
                {
                    $ncci_Jurisdiction_st_Missing = 'L';
                    $missingNCCI = 'Y';
                }
                if ($q->{'NCCI_BEN_TYPE_CODE'} eq '03' &&
                    $s->{'BENEFIT_JURISDICT_ST'} =~ /VT/)
                {
                    $ncci_Jurisdiction_st_Missing = 'V';
                    $missingNCCI = 'Y';
                }
                if ($q->{'NCCI_BEN_TYPE_CODE'} eq '04' &&
                    $s->{'BENEFIT_JURISDICT_ST'} =~ /FL/)
                {
                    $ncci_Jurisdiction_st_Missing = 'F';
                    $missingNCCI = 'Y';
                }
                if ($q->{'NCCI_BEN_TYPE_CODE'} eq '09' &&
                    $s->{'BENEFIT_JURISDICT_ST'} =~ /AK|FL|GA|ID|KS|KY|ME|MI|MN|NE|NJ|NV|OR|TX|UT|VT|WV/)
                {
                    $ncci_Jurisdiction_st_Missing = 'A';
                    $missingNCCI = 'Y';
                }
                if ($q->{'NCCI_BEN_TYPE_CODE'} eq '11' &&
                    $s->{'BENEFIT_JURISDICT_ST'} =~ /KY|LA/)
                {
                    $ncci_Jurisdiction_st_Missing = 'K';
                    $missingNCCI = 'Y';
                }
                if ($q->{'NCCI_BEN_TYPE_CODE'} eq '15' &&
                    $s->{'BENEFIT_JURISDICT_ST'} !~ /LA|SD/)
                {
                    $ncci_Jurisdiction_st_Missing = 'S';
                    $missingNCCI = 'Y';
                }
                if ($q->{'NCCI_BEN_TYPE_CODE'} eq '50' &&
                    $s->{'BENEFIT_JURISDICT_ST'} !~ /CT|GA|LA|RI/)
                {
                    $ncci_Jurisdiction_st_Missing = 'C';
                    $missingNCCI = 'Y';
                }
#                if(defined($q->{'LOSS_CODE'}) && $q->{'LOSS_CODE'} eq '01')
#                {
                    if ((defined($s->{'NCCI_PRE_INJ_WKLY_WAGE'}) && $s->{'NCCI_PRE_INJ_WKLY_WAGE'} eq '') ||
                        !defined($s->{'NCCI_PRE_INJ_WKLY_WAGE'}))
                    {
                        $ncci_Pre_Inj_wkly_wage_Missing = 'Y';
                        $missingNCCI = 'Y';
                    }
                    if ((defined($s->{'NCCI_WEEKLY_BEN_AMT'}) && $s->{'NCCI_WEEKLY_BEN_AMT'} eq '0.00') ||
                        !defined($s->{'NCCI_WEEKLY_BEN_AMT'}))
                    {
                        $ncci_Weekly_Ben_Amt = 'Y';
                        $missingNCCI = 'Y';
                    }
                    if ((defined($s->{'WC_EMPLOYMENT_STATUS'}) && $s->{'WC_EMPLOYMENT_STATUS'} eq '') ||
                        !defined($s->{'WC_EMPLOYMENT_STATUS'}))
                    {
                        $ncci_Employment_status = 'Y';
                        $missingNCCI = 'Y';
                    }
                    if ((defined($s->{'NCCI_IMPAIRMENT_PERCT'}) && $s->{'NCCI_IMPAIRMENT_PERCT'} eq '') ||
                        !defined($s->{'NCCI_IMPAIRMENT_PERCT'}))
                    {
                        $ncci_impairment_perct = 'Y';
                        $missingNCCI = 'Y';
                    }
                    if ((defined($s->{'NCCI_IMPAIR_PCT_BASIS'}) && $s->{'NCCI_IMPAIR_PCT_BASIS'} eq '') ||
                        !defined($s->{'NCCI_IMPAIR_PCT_BASIS'}))
                    {
                        $ncci_impairment_basis = 'Y';
                        $missingNCCI = 'Y';
                    }
	                if ((defined($s->{'NCCI_BEN_OFF_CODE'}) && $s->{'NCCI_BEN_OFF_CODE'} eq '') ||
	                    !defined($s->{'NCCI_BEN_OFF_CODE'}))
	                {
	                    $ncci_ben_off_code = 'Y';
	                    $missingNCCI = 'Y';
	                }
	                if ((defined($s->{'NCCI_BEN_OFF_AMT'}) && $s->{'NCCI_BEN_OFF_AMT'} eq '') ||
	                    !defined($s->{'NCCI_BEN_OFF_AMT'}))
	                {
	                    $ncci_ben_off_amt = 'Y';
	                    $missingNCCI = 'Y';
	                }
	                if ((defined($s->{'NCCI_PRE_EX_DIS_PERCT'}) && $s->{'NCCI_PRE_EX_DIS_PERCT'} eq '') ||
	                    !defined($s->{'NCCI_PRE_EX_DIS_PERCT'}))
	                {
	                    $ncci_pre_ex_dis_perct = 'Y';
	                    $missingNCCI = 'Y';
	                }
	                if ((defined($s->{'WC_ACT'}) && $s->{'WC_ACT'} eq '') ||
	                    !defined($s->{'WC_ACT'}))
	                {
	                    $wc_act = 'Y';
	                    $missingNCCI = 'Y';
	                }
	                if ((defined($s->{'WC_HIRE_DATE'}) && $s->{'WC_HIRE_DATE'} eq '9999-01-01') ||
	                    !defined($s->{'WC_HIRE_DATE'}))
	                {
	                    $wcHireDate = 'Y';
	                    $missingNCCI = 'Y';
	                }
#                }
            }
#            my $partyGet = $ENGINE->{'DBH'}->prepare
#                ("SELECT P.PARTY_ID
#                 from CLAIMDB.CLM_PARTIES P
#                 INNER JOIN CLAIMDB.CLM_PARTY_ROLES PR
#                 ON PR.PARTY_ID = P.PARTY_ID
#                    WHERE
#                    P.CLAIM_ID = ?
#                    AND PR.ROLE = 'EM'
#                    and P.DATE_DELETED = '9999-01-01-01.00.00.000000'")
#                        || $error->($ENGINE);
#            #retrieve the wc_stat
#            $partyGet->execute($claimId)
#                || $error->($ENGINE);

#            my $partyResult = $partyGet->fetchall_arrayref({})
#                || $error->($ENGINE);
#            my $party_id = $partyResult->[0]->{'PARTY_ID'};

#            my $inj_defGet = $ENGINE->{'DBH'}->prepare
#                ("SELECT INJ_ATTORNEY
#                 from CLAIMDB.CLM_INJ_DEF
#                    WHERE
#                    CLAIM_ID = ?
#                    AND PARTY_ID = ?
#                    and DATE_DELETED = '9999-01-01-01.00.00.000000'")
#                        || $error->($ENGINE);
#            #retrieve the wc_stat
#            $inj_defGet->execute($claimId,$party_id)
#                || $error->($ENGINE);

#            my $inj_defResult = $inj_defGet->fetchall_arrayref({})
#                || $error->($ENGINE);
#            my $attorneyInd = $inj_defResult->[0]->{'INJ_ATTORNEY'};

#            if ((defined($attorneyInd) && $attorneyInd eq 'N') ||
#                !defined($attorneyInd))
#            {
#                $attorneySw = 'Y';
#                $missingNCCI = 'Y';
#            }


            if ((defined($accidentState) && $accidentState eq '') ||
                !defined($accidentState))
            {
                $accidentSt = 'Y';
                $missingNCCI = 'Y';
            }
        }

        if ($partyMissingData eq 'Y')
        {
                ####This section is stolen from claims_fileActivity.pm
                ####function BIMEDTotals
                if($line =~ /010|011|012|030|031|051/)
                {
                    if($lossType eq '01')
                    {
                        if($lossCause =~ /001|058|059|060|061|062|063|064|065|067/)
                        {
                            $hasMedical = 'Y';
                        }
                    }
                    if($lossType eq '03')
                    {
                        if($lossCause =~ /099|058|059|060|061|062|063|064|065|067/)
                        {
                            $hasMedical = 'Y'
                        }
                    }
                    if($lossType eq '06')
                    {
                        if($lossCause =~ /099|058|059|060|061|062|063|064|065/)
                        {
                            $hasMedical = 'Y'
                        }
                    }
                    if($lossType =~ /09|10/)
                    {
                        if($lossCause =~ /099|058|059|060|064|065/)
                        {
                            $hasMedical = 'Y'
                        }
                    }
                    if($lossType =~ /L1/)
                    {
                        if($lossCause =~ /0L1/)
                        {
                            $hasMedical = 'Y'
                        }
                    }
                    if($lossType =~ /L2/)
                    {
                        if($lossCause =~ /0L2/)
                        {
                            $hasMedical = 'Y'
                        }
                    }
                }
                elsif($line =~ /112|113|120|110|111/)
                {
                    if($lossType eq '58'
                        || $lossType eq '73')
                    {
                        if($lossCause =~ /^070$|^124$|^125$|^126$|^127$|^128$|^129$|^130$|^131$|^132$|^134$|^199$/)
                        {
                            $hasBIMedical = 'Y'
                        }
                    }
                }
                elsif($line =~ /015|016/)
                {
                    if($lossType =~ /01|03|09|10|L3|L4/)
                    {
                        $hasBIMedical = 'Y'
                    }
                    if($cov eq '03')
                    {
                        if($lossType =~ /58|59|60|61|62|63|64|65|67/)
                        {
                            $hasBIMedical = 'Y'
                        }
                    }
                    if($cov eq '06')
                    {
                        if($lossType =~ /58|59|60|61|62|63|64|65/)
                        {
                            $hasBIMedical = 'Y'
                        }
                    }
                    if($cov =~ /09|10/)
                    {
                        if($lossType =~ /58|59|60|64|65/)
                        {
                            $hasBIMedical = 'Y'
                        }
                    }
                }
                elsif($line =~ /052/)
                {
                    if($lossType =~ /70|73/)
                    {
                        $hasBIMedical = 'Y'
                    }
                }
                elsif($line =~ /100/)
                {
                    if($lossType =~ /28|54|56|70|73/)
                    {
                        $hasBIMedical = 'Y'
                    }
                }
                elsif($line =~ /575|580/)
                {
                    if($lossType =~ /26|28|29|30|53|54|55|56/)
                    {
                        $hasBIMedical = 'Y'
                    }
                }
                elsif($line =~ /804/)
                {
                    if($lossType =~ /01|03|09|10|13|L3|L4/)
                    {
                        $hasBIMedical = 'Y'
                    }
                }
                elsif($line =~ /810|811|812|814|815|816/)
                {
                    if($lossType =~ /01|03|28|29|53|54|55|56/)
                    {
                        $hasBIMedical = 'Y'
                    }
                }
                elsif($line =~ /300|301|302|330|331|332/)
                {
                    if($lossType =~ /28|53|54|55|56|57|62|63|64|67|70|73|74|75|76/)
                    {
                        $hasBIMedical = 'Y'
                    }
                }
                elsif($line =~ /350|360/)
                {
                    if($lossType =~ /70/)
                    {
                        $hasBIMedical = 'Y'
                    }
                }


                #this section is stolen from function BIUMUIMTotals
                if($line =~ /010|011|012|030|031|051/)
            {
                if($lossType eq '01')
                {
                    if($lossCause =~ /001|058|059|060|061|062|063|064|065|067/)
                    {
                        $hasBIUMUIM = 'Y';
                    }
                }
                if($lossType =~ /09|10/)
                {
                    if($lossCause =~ /099|058|059|060|064|065/)
                    {
                        $hasBIUMUIM = 'Y';
                    }
                }
                if($lossType =~ /L1/)
                {
                    if($lossCause =~ /0L1/)
                    {
                        $hasBIUMUIM = 'Y';
                    }
                }
                if($lossType =~ /L2/)
                {
                    if($lossCause =~ /0L2/)
                    {
                        $hasBIUMUIM = 'Y';
                    }
                }
            }
            elsif($line =~ /112|113|120|110|111/)
            {
                if($lossType eq '58')
                    {
                        if($lossCause =~ /^070$|^124$|^125$|^126$|^127$|^128$|^129$|^130$|^131$|^132$|^134$|^199$/)
                        {
                        $hasBIUMUIM = 'Y';
                    }
                }
            }
            elsif($line =~ /015|016/)
            {
                if($lossType =~ /01|09|10/)
                {
                    $hasBIUMUIM = 'Y';
                }
                if($cov =~ /09|10/)
                {
                    if($lossType =~ /58|59|60|64|65/)
                    {
                        $hasBIUMUIM = 'Y';
                    }
                }
            }
            elsif($line =~ /052/)
            {
                if($lossType =~ /70|72/)
                {
                    $hasBIUMUIM = 'Y';
                }
            }
            elsif($line =~ /100/)
            {
                if($lossType =~ /28|54|70/)
                {
                    $hasBIUMUIM = 'Y';
                }
            }
            elsif($line =~ /575|580/)
            {
                if ($lossType =~ /26|28|29|53|54/)
                {
                    $hasBIUMUIM = 'Y';
                }
            }
            elsif($line =~ /804/)
            {
                if($lossType =~ /01|09|10|L3|L4/)
                {
                    $hasBIUMUIM = 'Y';
                }
            }
            elsif($line =~ /810|811|812|814|815|816/)
            {
                if($lossType =~ /01|28|29|53|54/)
                {
                    $hasBIUMUIM = 'Y';
                }
            }
            elsif($line =~ /300|301|302|330|331|332/)
            {
                if($lossType =~ /28|53|54|57|62|64|67|70|74/)
                {
                    $hasBIUMUIM = 'Y';
                }
            }
            elsif($line =~ /350|360/)
            {
                if($lossType =~ /70/)
                {
                    $hasBIUMUIM = 'Y';
                }
            }

                         #this section stolen from function medicaltotals
                         if($line =~ /010|011|012|030|031|051/)
                {
                  if($lossType eq '03')
                  {
                      if($lossCause =~ /099|058|059|060|061|062|063|064|065|067/)
                      {
                          $hasMedical = 'Y';
                      }
                  }
                }
                elsif($line =~ /112|113|120|110|111/)
                {
              if($lossType eq '73')
                  {
                          if($lossCause =~ /^124$|^125$|^126$|^127$|^128$|^129$|^130$|^131$|^132$|^199$/)
                    {
                        $hasMedical = 'Y';
                }
              }
                }
                elsif($line =~ /015|016/)
                {
                  if($lossType =~ /03/)
                  {
                      $hasMedical = 'Y';
                  }
                  if($cov eq '03')
                  {
                      if($lossType =~ /58|59|60|61|62|63|64|65|67/)
                      {
                          $hasMedical = 'Y';
                      }
                  }
                }
                elsif($line =~ /052/)
                {
                  if($lossType =~ /73/)
                  {
                      $hasMedical = 'Y';
                  }
                }
                elsif($line =~ /100/)
                {
                  if($lossType =~ /56|73/)
                  {
                      $hasMedical = 'Y';
                  }
                }
                elsif($line =~ /575|580/)
                {
                  if($lossType =~ /30|55|56/)
                  {
                      $hasMedical = 'Y';
                  }
                }
                elsif($line =~ /804/)
                {
                  if($lossType =~ /03|13/)
                  {
                      $hasMedical = 'Y';
                  }
                }
                elsif($line =~ /810|811|812|814|815|816/)
                {
                  if($lossType =~ /03|55|56/)
                  {
                      $hasMedical = 'Y';
                  }
                }
                elsif($line =~ /300|301|302|330|331|332/)
                {
                  if($lossType =~ /55|56|63|73|75|76/)
                  {
                      $hasMedical = 'Y';
                  }
                }

                #all workers comp must require this information for the
                #Employee Claimant
                if($line =~ /600|605/)
                {
                        ###################################################
                #check to see if the reserve is set on the Employee or not
                ###################################################
                    #read the roles of the party in question
                    #sql prepare to retrieve clm_cash_id rows for backoff
                    my $partyRolesGet = $ENGINE->{'DBH'}->prepare
                        ("SELECT PR.ROLE from
                            CLAIMDB.CLM_PARTY_ROLES PR
                            WHERE
                            PR.PARTY_ID = ?
                            and PR.DATE_DELETED = '9999-01-01-01.00.00.000000'")
                                || $error->($ENGINE);
                    #retrieve the role rows
                    $partyRolesGet->execute($partyID)
                        || $error->($ENGINE);

                    my $result = $partyRolesGet->fetchall_arrayref({})
                        || $error->($ENGINE);

                    #find Employee role
                    for my $c (@$result)
                    {
                        if ($c->{'ROLE'} eq 'EM')
                        {
                            $hasMedical = 'Y';
                        }
                    }
                }
        }

        my $errorMessage = '';
        if($line =~ /600|605/ && $missingNCCI eq 'Y')
        {
            if($ncci_Tran_From_Date_Missing eq 'Y')
            { $errorMessage .= '<p>Certain Benefit Types requires Transaction From Date to also be completed.  Go to "Details Screen" and under "Show/Hide NCCI" to add the Transaction From Date.</p>'; }
            if($ncci_Tran_To_Date_Missing eq 'Y')
            { $errorMessage .= '<p>Certain Benefit Types requires Transaction To Date to also be completed.  Go to "Details Screen" and under "Show/Hide NCCI" to add the Transaction To Date.</p>'; }
            if($ncci_Jurisdiction_st_Missing eq 'L')
            { $errorMessage .= '<p>Do not allow Benefit Type Scheduled Permanent Partial Disability when Jurisdiction State is AK, KY, MN, MT, OR, TN and loss date is on or greater than 7/1/2014.  Go to "Details Screen" and under "Show/Hide NCCI" to change Jurisdiction State or change Benefit Type Code.</p>'; }
            if($ncci_Jurisdiction_st_Missing eq 'V')
            { $errorMessage .= '<p>Do not allow Benefit Type Scheduled Permanent Partial Disability when Jurisdiction State is VT.  Go to "Details Screen" and under "Show/Hide NCCI" to change Jurisdiction State or change Benefit Type Code.</p>'; }
            if($ncci_Jurisdiction_st_Missing eq 'F')
            { $errorMessage .= '<p>Do not allowed Benefit Type Unscheduled Permanent Partial Disability Benefits when Jurisdiction State is FL.  Go to "Details Screen" and under "Show/Hide NCCI" to change Jurisdiction State or change Benefit Type Code.</p>'; }
            if($ncci_Jurisdiction_st_Missing eq 'A')
            { $errorMessage .= '<p>Do not allowed Benefit Type Disfigurement Benefits when Jurisdiction State is AK, FL, GA,ID, KS,KY, ME, MI, MN, NE, NJ, NV, OR, TX, UT, VT, or WV.  Go to "Details Screen" and under "Show/Hide NCCI" to change Jurisdiction State or change Benefit Type Code.</p>'; }
            if($ncci_Jurisdiction_st_Missing eq 'K')
            { $errorMessage .= '<p>Do not allowed Benefit Type Temporary Partial Disability Benefits when Jurisdiction State is KY or LA.  Go to "Details Screen" and under "Show/Hide NCCI" to change Jurisdiction State or change Benefit Type Code.</p>'; }
            if($ncci_Jurisdiction_st_Missing eq 'S')
            { $errorMessage .= '<p>Do not allowed Benefit Type Supplemental Benefits when Jurisdiction State is anything other than LA or SD.  Go to "Details Screen" and under "Show/Hide NCCI" to change Jurisdiction State or change Benefit Type Code.</p>'; }
            if($ncci_Jurisdiction_st_Missing eq 'C')
            { $errorMessage .= '<p>Do not allowed Benefit Type Specified Indemnity Benefits when Jurisdiction State is anything other than CT, GA, LA or RI.  Go to "Details Screen" and under "Show/Hide NCCI" to change Jurisdiction State or change Benefit Type Code.</p>'; }
#            if($ncci_Pre_Inj_wkly_wage_Missing eq 'Y')
#            { $errorMessage .= '<p>Average Weekly Wage(Pre-Injur) is missing.  Go to "Details Screen" and under "Show/Hide NCCI" to change Average Weekly Wage(Pre-Injur).</p>'; }
#            if($ncci_Employment_status eq 'Y')
#            { $errorMessage .= '<p>Employment Status is missing.  Go to "Details Screen" and under "Show/Hide NCCI" to change Employment Status.</p>'; }
#            if($ncci_impairment_perct eq 'Y')
#            { $errorMessage .= '<p>Impairment Percentage is missing.  Go to "Details Screen" and under "Show/Hide NCCI" to change Impairment Percentage.</p>'; }
#            if($ncci_impairment_basis eq 'Y')
#            { $errorMessage .= '<p>Impairment Percentage Basis is missing.  Go to "Details Screen" and under "Show/Hide NCCI" to change Impairment Percentage Basis.</p>'; }
#            if($ncci_ben_off_code eq 'Y')
#            { $errorMessage .= '<p>Benefit Offset Code is missing.  Go to "Details Screen" and under "Show/Hide NCCI" to change Benefit Offset Code.</p>'; }
#            if($ncci_ben_off_amt eq 'Y')
#            { $errorMessage .= '<p>Benefit Offset Amount is missing.  Go to "Details Screen" and under "Show/Hide NCCI" to change Benefit Offset Amount.</p>'; }
#            if($ncci_pre_ex_dis_perct eq 'Y')
#            { $errorMessage .= '<p>Pre-existing Disability Percentage is missing.  Go to "Details Screen" and under "Show/Hide NCCI" to change Pre-existing Disability Percentage.</p>'; }
#            if($wc_act eq 'Y')
#            { $errorMessage .= '<p>Act-Loss Condition Code is missing.  Go to "Details Screen" and under "Show/Hide NCCI" to change Act-Loss Condition Code.</p>'; }
#            if($accidentSt eq 'Y')
#            { $errorMessage .= '<p>State Loss Applies is missing.  Go to "Details Screen" and under "Insured Information" to change State Loss Applies.</p>'; }
#            if($attorneySw eq 'Y')
#            { $errorMessage .= '<p>Attorney is missing.  Go to "File Activity Screen" and under "Work Comp Memorandum" to add an Attorney.</p>'; }
#            if($wcHireDate eq 'Y')
#            { $errorMessage .= '<p>Date of Hire is missing.  Go to "Details Screen" and under "Employee Information" to change Date of Hire.</p>'; }
                if($ncci_Pre_Inj_wkly_wage_Missing eq 'Y')
                {
	                my %errHash = ('screen'=>'Claims_Details',
	                                      'field'=>'preInjAverageWeeklyWage',
	                                      'msg'=>'Average Weekly Wage(Pre-injury) is missing on the Details screen.');
	                #put reference to work hash on array of hash references
	                push (@wcpaymentErrors, \%errHash);
                }
                if($ncci_Weekly_Ben_Amt eq 'Y')
                {
	                my %errHash = ('screen'=>'Claims_Details',
	                                      'field'=>'weeklyBenAmt',
	                                      'msg'=>'Entry must be greater than $0 for Weekly Benefit Amount.');
	                #put reference to work hash on array of hash references
	                push (@wcpaymentErrors, \%errHash);
                }
                if($ncci_Employment_status eq 'Y')
                {
	                my %errHash = ('screen'=>'Claims_Details',
	                                      'field'=>'employmentStatus',
	                                      'msg'=>'Employment Status is missing on the Details screen.');
	                #put reference to work hash on array of hash references
	                push (@wcpaymentErrors, \%errHash);
                }
                if($ncci_impairment_perct eq 'Y')
                {
                    my %errHash = ('screen'=>'Claims_Details',
                                          'field'=>'impairPerct',
                                          'msg'=>'Impairment Percentage is missing on the Details screen.');
                    #put reference to work hash on array of hash references
                    push (@wcpaymentErrors, \%errHash);
                }
                if($ncci_impairment_basis eq 'Y')
                {
                    my %errHash = ('screen'=>'Claims_Details',
                                          'field'=>'impairPerctBasis',
                                          'msg'=>'Impairment Percentage Basis is missing on the Details screen.');
                    #put reference to work hash on array of hash references
                    push (@wcpaymentErrors, \%errHash);
                }
                if($ncci_ben_off_code eq 'Y')
                {
	                my %errHash = ('screen'=>'Claims_Details',
	                                      'field'=>'benOffSetcode',
	                                      'msg'=>'Benefit Offset Code is missing on the Details screen.');
	                #put reference to work hash on array of hash references
	                push (@wcpaymentErrors, \%errHash);
                }
                if($ncci_ben_off_amt eq 'Y')
                {
	                my %errHash = ('screen'=>'Claims_Details',
	                                      'field'=>'benOffSetAmt',
	                                      'msg'=>'Benefit Offset Amount is missing on the Details screen.');
	                #put reference to work hash on array of hash references
	                push (@wcpaymentErrors, \%errHash);
                }
                if($ncci_pre_ex_dis_perct eq 'Y')
                {
	                my %errHash = ('screen'=>'Claims_Details',
	                                      'field'=>'preExtDisPerct',
	                                      'msg'=>'Pre-existing Disability Percentage is missing on the Details screen.');
	                #put reference to work hash on array of hash references
	                push (@wcpaymentErrors, \%errHash);
                }
                if($wc_act eq 'Y')
                {
	                my %errHash = ('screen'=>'Claims_Details',
	                                      'field'=>'actLossCode',
	                                      'msg'=>'Act-Loss Condition Code is missing on the Details screen.');
	                #put reference to work hash on array of hash references
	                push (@wcpaymentErrors, \%errHash);
                }
                if($accidentSt eq 'Y')
                {
	                my %errHash = ('screen'=>'Claims_Details',
	                                      'field'=>'stateLossApplies',
	                                      'msg'=>'State Loss Applies is missing on the Details screen.');
	                #put reference to work hash on array of hash references
	                push (@wcpaymentErrors, \%errHash);
                }
                if($wcHireDate eq 'Y')
                {
	                my %errHash = ('screen'=>'Claims_Details',
	                                      'field'=>'employeeHireDate',
	                                      'msg'=>'Date of Hire is missing on the Details screen.');
	                #put reference to work hash on array of hash references
	                push (@wcpaymentErrors, \%errHash);
                }
            push (@paymentErrors, $errorMessage);
        }

        if (($hasMedical eq 'Y' || $hasBIUMUIM  eq 'Y' || $hasBIMedical eq 'Y')
                && $partyMissingData eq 'Y')
                {

                my $missingFields = '';
                my $errorCntr = 0;
                #determine the proper field names that are missing and
                #string them together for the error
            if ($partyMissingSex gt '')
            {
                    $missingFields = $partyMissingSex;
                    $errorCntr++;
            }
            if ($partyMissingMarital gt '' && $policyState eq 'IA')
            {
                    if ($errorCntr gt 0)
                    {
                        $missingFields .= (', ' . $partyMissingMarital);
                        $errorCntr++;
                    }
                    else
                    {
                         $missingFields = $partyMissingMarital;
                         $errorCntr++;
                    }
            }
            if ($partyMissingSS gt '')
            {
                    if ($errorCntr gt 0)
                    {
                        $missingFields .= (', ' . $partyMissingSS);
                        $errorCntr++;
                    }
                    else
                    {
                         $missingFields = $partyMissingSS;
                         $errorCntr++;
                    }
            }
            if ($partyMissingBirth gt '')
            {
                    if ($errorCntr gt 0)
                    {
                        $missingFields .= (', ' . $partyMissingBirth);
                        $errorCntr++;
                    }
                    else
                    {
                         $missingFields = $partyMissingBirth;
                         $errorCntr++;
                    }
            }
            if ($errorCntr > 0)
            {
                $errorMessage = 'Claimant ' . $claimantName . ' requires ' . $missingFields . ' before making this type of payment. ';
                if($line =~ /600|605/)
                {
                    $errorMessage .= 'Enter this information on the Details tab for the Employee. ';
                }
                else
                {
#sjs 09/28/2012 107196 change error message
                    $errorMessage .= 'Choose the "Select" option in the "What do you want to do?" dropdown on this screen.  Next, go to the "Details Screen" and under "Insureds and Claimants" click "Show/Hide" for the claimant needing the additional information.';
                }
                push (@paymentErrors, $errorMessage);
                     #$paymentError = 'Y';
            }
        }
    }

    $ENGINE->{'wc_reserve_errors'} = \@wcpaymentErrors;

    return (\@paymentErrors);
        #return ($paymentError, $claimantName);

}

sub reserveEdits
{
    my $ENGINE = shift;

    my $line = $ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'};
    my $policyState = $ENGINE->{'claimGeneral'}->{'POLICY_STATE'};
    my $claimId = $ENGINE->{'claimGeneral'}->{'CLAIM_ID'};
    my $lossDate = substr($ENGINE->{'claimGeneral'}->{'LOSS_DATE_TIME'},0,10);
    my $accidentState = $ENGINE->{'claimGeneral'}->{'ACCIDENT_STATE'};
    my $error = $ENGINE->{'error'};

    my @reserveErrors = ();
    my @wcreserveErrors = ();
    my $missingNCCI = 'N';
    my $ncci_Pre_Inj_wkly_wage_Missing = 'N';
    my $ncci_Weekly_Ben_Amt = 'N';
    my $ncci_Employment_status = 'N';
    my $ncci_impairment_perct = 'N';
    my $ncci_impairment_basis = 'N';
    my $ncci_ben_off_code = 'N';
    my $ncci_ben_off_amt = 'N';
    my $ncci_pre_ex_dis_perct = 'N';
    my $wc_act = 'N';
    my $wcHireDate = 'N';
    my $accidentSt = 'N';
    my $attorneySw = 'N';
    my $partyMissingData = 'N';
    my $partyMissingSex = '';
    my $partyMissingBirth = '';
    my $claimantName = '';
    if($line =~ /600|605/)
    {
	    for my $r (@{$ENGINE->{'Monetary'}->{'CLM_RESERVES'}})
	    {
	        if(defined ($r->{'LOSS_CODE'}) && $r->{'LOSS_CODE'} eq '01')
	        {
                my $partyID = $r->{'PARTY_ID'} || 0;
	            #now find the party and see if he is missing his HCIN/SS#, birthdate,
	            #marital or sex information
	            if ($partyID)
	            {
	                for my $or (@{$ENGINE->{'Monetary'}->{'OLD_CLM_PARTIES'}})
	                {
	                     if ($partyID == $or->{'PARTY_ID'} && $or->{'BUSINESS_NAME'} eq '')
	                     {
	                             #check to see if he has sex
	                        if (!$or->{'SEX'} || $or->{'SEX'} eq '')
	                        {
	                             $partyMissingData = 'Y';
	                             $partyMissingSex = 'Gender';
	                        }
	                        if ($or->{'BIRTHDATE'} eq '9999-01-01')
	                        {
	                             $partyMissingData = 'Y';
	                             $partyMissingBirth = 'Birthdate';
	                        }
	                        $claimantName =  $or->{'FIRST_NAME'} . ' ' . $or->{'LAST_NAME'};
	                     }
	                }
	            }

	            my $wcStatGet = $ENGINE->{'DBH'}->prepare
	                ("SELECT NCCI_PRE_INJ_WKLY_WAGE, WC_EMPLOYMENT_STATUS, NCCI_IMPAIRMENT_PERCT, NCCI_IMPAIR_PCT_BASIS, NCCI_BEN_OFF_CODE,
	                         NCCI_BEN_OFF_AMT, NCCI_PRE_EX_DIS_PERCT, WC_ACT, WC_HIRE_DATE, NCCI_WEEKLY_BEN_AMT
	                 from CLAIMDB.CLM_WC_STAT
	                    WHERE
	                    CLAIM_ID = ?
	                    and DATE_DELETED = '9999-01-01-01.00.00.000000'")
	                        || $error->($ENGINE);
	            #retrieve the wc_stat
	            $wcStatGet->execute($claimId)
	                || $error->($ENGINE);

	            my $wcResult = $wcStatGet->fetchall_arrayref({})
	                || $error->($ENGINE);

	            for my $s (@$wcResult)
	            {
	                if ((defined($s->{'NCCI_PRE_INJ_WKLY_WAGE'}) && $s->{'NCCI_PRE_INJ_WKLY_WAGE'} eq '') ||
	                    !defined($s->{'NCCI_PRE_INJ_WKLY_WAGE'}))
	                {
	                    $ncci_Pre_Inj_wkly_wage_Missing = 'Y';
	                    $missingNCCI = 'Y';
	                }
	                if ((defined($s->{'NCCI_WEEKLY_BEN_AMT'}) && $s->{'NCCI_WEEKLY_BEN_AMT'} eq '') ||
	                    !defined($s->{'NCCI_WEEKLY_BEN_AMT'}))
	                {
	                    $ncci_Weekly_Ben_Amt = 'Y';
	                    $missingNCCI = 'Y';
	                }
	                if ((defined($s->{'WC_EMPLOYMENT_STATUS'}) && $s->{'WC_EMPLOYMENT_STATUS'} eq '') ||
	                    !defined($s->{'WC_EMPLOYMENT_STATUS'}))
	                {
	                    $ncci_Employment_status = 'Y';
	                    $missingNCCI = 'Y';
	                }
	                if ((defined($s->{'NCCI_IMPAIRMENT_PERCT'}) && $s->{'NCCI_IMPAIRMENT_PERCT'} eq '') ||
	                    !defined($s->{'NCCI_IMPAIRMENT_PERCT'}))
	                {
	                    $ncci_impairment_perct = 'Y';
	                    $missingNCCI = 'Y';
	                }
	                if ((defined($s->{'NCCI_IMPAIR_PCT_BASIS'}) && $s->{'NCCI_IMPAIR_PCT_BASIS'} eq '') ||
	                    !defined($s->{'NCCI_IMPAIR_PCT_BASIS'}))
	                {
	                    $ncci_impairment_basis = 'Y';
	                    $missingNCCI = 'Y';
	                }
	                if ((defined($s->{'NCCI_BEN_OFF_CODE'}) && $s->{'NCCI_BEN_OFF_CODE'} eq '') ||
	                    !defined($s->{'NCCI_BEN_OFF_CODE'}))
	                {
	                    $ncci_ben_off_code = 'Y';
	                    $missingNCCI = 'Y';
	                }
	                if ((defined($s->{'NCCI_BEN_OFF_AMT'}) && $s->{'NCCI_BEN_OFF_AMT'} eq '') ||
	                    !defined($s->{'NCCI_BEN_OFF_AMT'}))
	                {
	                    $ncci_ben_off_amt = 'Y';
	                    $missingNCCI = 'Y';
	                }
	                if ((defined($s->{'NCCI_PRE_EX_DIS_PERCT'}) && $s->{'NCCI_PRE_EX_DIS_PERCT'} eq '') ||
	                    !defined($s->{'NCCI_PRE_EX_DIS_PERCT'}))
	                {
	                    $ncci_pre_ex_dis_perct = 'Y';
	                    $missingNCCI = 'Y';
	                }
	                if ((defined($s->{'WC_ACT'}) && $s->{'WC_ACT'} eq '') ||
	                    !defined($s->{'WC_ACT'}))
	                {
	                    $wc_act = 'Y';
	                    $missingNCCI = 'Y';
	                }
	                if ((defined($s->{'WC_HIRE_DATE'}) && $s->{'WC_HIRE_DATE'} eq '9999-01-01') ||
	                    !defined($s->{'WC_HIRE_DATE'}))
	                {
	                    $wcHireDate = 'Y';
	                    $missingNCCI = 'Y';
	                }
	            }
	        }

#            my $partyGet = $ENGINE->{'DBH'}->prepare
#                ("SELECT P.PARTY_ID
#                 from CLAIMDB.CLM_PARTIES P
#                 INNER JOIN CLAIMDB.CLM_PARTY_ROLES PR
#                 ON PR.PARTY_ID = P.PARTY_ID
#                    WHERE
#                    P.CLAIM_ID = ?
#                    AND PR.ROLE = 'EM'
#                    and P.DATE_DELETED = '9999-01-01-01.00.00.000000'")
#                        || $error->($ENGINE);
#            #retrieve the wc_stat
#            $partyGet->execute($claimId)
#                || $error->($ENGINE);

#            my $partyResult = $partyGet->fetchall_arrayref({})
#                || $error->($ENGINE);
#            my $party_id = $partyResult->[0]->{'PARTY_ID'};

#            my $inj_defGet = $ENGINE->{'DBH'}->prepare
#                ("SELECT INJ_ATTORNEY
#                 from CLAIMDB.CLM_INJ_DEF
#                    WHERE
#                    CLAIM_ID = ?
#                    AND PARTY_ID = ?
#                    and DATE_DELETED = '9999-01-01-01.00.00.000000'")
#                        || $error->($ENGINE);
#            #retrieve the wc_stat
#            $inj_defGet->execute($claimId,$party_id)
#                || $error->($ENGINE);

#            my $inj_defResult = $inj_defGet->fetchall_arrayref({})
#                || $error->($ENGINE);
#            my $attorneyInd = $inj_defResult->[0]->{'INJ_ATTORNEY'};

#            if ((defined($attorneyInd) && $attorneyInd eq 'N') ||
#                !defined($attorneyInd))
#            {
#                $attorneySw = 'Y';
#                $missingNCCI = 'Y';
#            }

	        if ((defined($accidentState) && $accidentState eq '') ||
	            !defined($accidentState))
	        {
	            $accidentSt = 'Y';
	            $missingNCCI = 'Y';
	        }

	        my $errorMessage = '';
	        if($line =~ /600|605/ && $missingNCCI eq 'Y')
	        {
#                if($attorneySw eq 'Y')
#                { $errorMessage .= '<p>Attorney is missing.  Go to "File Activity Screen" and under "Work Comp Memorandum" to add an Attorney.</p>'; }
                if($ncci_Pre_Inj_wkly_wage_Missing eq 'Y')
                {
	                my %errHash = ('screen'=>'Claims_Details',
	                                      'field'=>'preInjAverageWeeklyWage',
	                                      'msg'=>'Average Weekly Wage(Pre-injury) is missing on the Details screen.');
	                #put reference to work hash on array of hash references
	                push (@wcreserveErrors, \%errHash);
                }
                if($ncci_Weekly_Ben_Amt eq 'Y')
                {
	                my %errHash = ('screen'=>'Claims_Details',
	                                      'field'=>'weeklyBenAmt',
	                                      'msg'=>'Weekly Benefit Amount is missing on the Details screen.');
	                #put reference to work hash on array of hash references
	                push (@wcreserveErrors, \%errHash);
                }
                if($ncci_Employment_status eq 'Y')
                {
	                my %errHash = ('screen'=>'Claims_Details',
	                                      'field'=>'employmentStatus',
	                                      'msg'=>'Employment Status is missing on the Details screen.');
	                #put reference to work hash on array of hash references
	                push (@wcreserveErrors, \%errHash);
                }
                if($ncci_impairment_perct eq 'Y')
                {
                    my %errHash = ('screen'=>'Claims_Details',
                                          'field'=>'impairPerct',
                                          'msg'=>'Impairment Percentage is missing on the Details screen.');
                    #put reference to work hash on array of hash references
                    push (@wcreserveErrors, \%errHash);
                }
                if($ncci_impairment_basis eq 'Y')
                {
                    my %errHash = ('screen'=>'Claims_Details',
                                          'field'=>'impairPerctBasis',
                                          'msg'=>'Impairment Percentage Basis is missing on the Details screen.');
                    #put reference to work hash on array of hash references
                    push (@wcreserveErrors, \%errHash);
                }
                if($ncci_ben_off_code eq 'Y')
                {
	                my %errHash = ('screen'=>'Claims_Details',
	                                      'field'=>'benOffSetcode',
	                                      'msg'=>'Benefit Offset Code is missing on the Details screen.');
	                #put reference to work hash on array of hash references
	                push (@wcreserveErrors, \%errHash);
                }
                if($ncci_ben_off_amt eq 'Y')
                {
	                my %errHash = ('screen'=>'Claims_Details',
	                                      'field'=>'benOffSetAmt',
	                                      'msg'=>'Benefit Offset Amount is missing on the Details screen.');
	                #put reference to work hash on array of hash references
	                push (@wcreserveErrors, \%errHash);
                }
                if($ncci_pre_ex_dis_perct eq 'Y')
                {
	                my %errHash = ('screen'=>'Claims_Details',
	                                      'field'=>'preExtDisPerct',
	                                      'msg'=>'Pre-existing Disability Percentage is missing on the Details screen.');
	                #put reference to work hash on array of hash references
	                push (@wcreserveErrors, \%errHash);
                }
                if($wc_act eq 'Y')
                {
	                my %errHash = ('screen'=>'Claims_Details',
	                                      'field'=>'actLossCode',
	                                      'msg'=>'Act-Loss Condition Code is missing on the Details screen.');
	                #put reference to work hash on array of hash references
	                push (@wcreserveErrors, \%errHash);
                }
                if($accidentSt eq 'Y')
                {
	                my %errHash = ('screen'=>'Claims_Details',
	                                      'field'=>'stateLossApplies',
	                                      'msg'=>'State Loss Applies is missing on the Details screen.');
	                #put reference to work hash on array of hash references
	                push (@wcreserveErrors, \%errHash);
                }
                if($wcHireDate eq 'Y')
                {
	                my %errHash = ('screen'=>'Claims_Details',
	                                      'field'=>'employeeHireDate',
	                                      'msg'=>'Date of Hire is missing on the Details screen.');
	                #put reference to work hash on array of hash references
	                push (@wcreserveErrors, \%errHash);
                }
#	            push (@reserveErrors, $errorMessage);
	        }

	        if($partyMissingData eq 'Y')
	        {
                my $missingFields = '';
                my $errorCntr = 0;
                #determine the proper field names that are missing and
                #string them together for the error
                if ($partyMissingSex gt '')
                {
                    $missingFields = $partyMissingSex;
                    $errorCntr++;
                }
                if ($partyMissingBirth gt '')
                {
                    if ($errorCntr gt 0)
                    {
                        $missingFields .= (', ' . $partyMissingBirth);
                        $errorCntr++;
                    }
                    else
                    {
                         $missingFields = $partyMissingBirth;
                         $errorCntr++;
                    }
                }
	            if ($errorCntr > 0)
	            {
	                $errorMessage = 'Claimant ' . $claimantName . ' requires ' . $missingFields . ' before making this type of payment. ';
	                $errorMessage .= 'Enter this information on the Details tab for the Employee. ';
	                push (@reserveErrors, $errorMessage);
	            }
	        }
	    }
    }

    $ENGINE->{'wc_reserve_errors'} = \@wcreserveErrors;

    return (\@wcreserveErrors);

}

sub editLimit
{
    my $ENGINE = shift;
    my $error = $ENGINE->{'error'};
           my $claimid = $ENGINE->{'claimGeneral'}->{'CLAIM_ID'};
           my $claimStatus = $ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'};
           my $line = $ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'};
          my $userID = $ENGINE->{'AUTH'}->{'user_key'};

    #variables used to retreive the current system date
    my $date1;
    my $date2;
    my $date3;
    my $time1;
    my $time2;
    my $day;
    my $month;
    my $year;
    ($month, $day, $year, $date1, $date2, $date3, $time1, $time2 ) = getcurrentdate();
    my $currentDateTime = $year.'-'.$month.'-'.$day.' '.substr($time2,0,2).':'.substr($time2,2,2).':'.substr($time2,4,2).'.000000';
#    print ' currdatetime '.$currentDateTime;

    my $covsEndorsesGet = $ENGINE->{'DBH'}->prepare
        ("SELECT * from
            CLAIMDB.CLM_COVS_ENDORSES
            WHERE
            COVERAGE_ID = ?
            and DATE_DELETED = '9999-01-01-01.00.00.000000'")
                || $error->($ENGINE);

    my $reserveGet = $ENGINE->{'DBH'}->prepare
        ("SELECT * from
            CLAIMDB.CLM_RESERVES
            WHERE
            CLM_RESERVES_ID = ?
            and DATE_DELETED = '9999-01-01-01.00.00.000000'")
                || $error->($ENGINE);

    for my $a (@{$ENGINE->{'Monetary'}->{'CLM_RESERVES'}})
    {  # print '<br>here1';
#        print '<b>**** NEW Reserve record here!: </b>';
            #retrieve the coverage
        if(defined($a->{'CLM_RESERVES_ID'}) && $a->{'CLM_RESERVES_ID'} > 0)
        {
                $reserveGet->execute($a->{'CLM_RESERVES_ID'})
                    || $error->($ENGINE);
                my $reservesResult = $reserveGet->fetchall_arrayref({})
                    || $error->($ENGINE);
                $a->{'LOSS_CODE'} = $reservesResult->[0]->{'LOSS_CODE'};
                $a->{'IMT_CAUSE_OF_LOSS'} = $reservesResult->[0]->{'IMT_CAUSE_OF_LOSS'};
                $a->{'COVERAGE_ID'} = $reservesResult->[0]->{'COVERAGE_ID'};
        }
            $covsEndorsesGet->execute($a->{'COVERAGE_ID'})
                || $error->($ENGINE);
            my $covsEndorsesResult = $covsEndorsesGet->fetchall_arrayref({})
                || $error->($ENGINE);
        $a->{'LOCATION_NO'} = $covsEndorsesResult->[0]->{'LOCATION_NO'};
        $a->{'UNIT_NO'} = $covsEndorsesResult->[0]->{'UNIT_NO'};
        $a->{'IMT_COVERAGE'} = $covsEndorsesResult->[0]->{'IMT_COVERAGE'};
        $a->{'COVERAGE'} = $covsEndorsesResult->[0]->{'COVERAGE'};
        if(defined($a->{'NEWROW'}) && $a->{'NEWROW'} eq 'Y')
        { $a->{'TRANSACTION_DATE'} = $currentDateTime; }
#        for my $t (keys %$a)
#          {
#              print '<br>' . $t .'=>' . ($a->{$t} || '****NONE') .'; ';
#          }
    }

    for my $a (@{$ENGINE->{'Monetary'}->{'CLM_CASH'}})
    {  # print '<br>here1';
#        print '<b>**** NEW Cash record here!: </b>';
        #retrieve the coverage
        $covsEndorsesGet->execute($a->{'COVERAGE_ID'})
            || $error->($ENGINE);
        my $covsEndorsesResult = $covsEndorsesGet->fetchall_arrayref({})
            || $error->($ENGINE);
        if(defined($a->{'CLM_RESERVES_ID'}) && $a->{'CLM_RESERVES_ID'} > 0)
        {
                $reserveGet->execute($a->{'CLM_RESERVES_ID'})
                    || $error->($ENGINE);
                my $reservesResult = $reserveGet->fetchall_arrayref({})
                    || $error->($ENGINE);
                $a->{'LOSS_CODE'} = $reservesResult->[0]->{'LOSS_CODE'};
                $a->{'IMT_CAUSE_OF_LOSS'} = $reservesResult->[0]->{'IMT_CAUSE_OF_LOSS'};
        }
        $a->{'LOCATION_NO'} = $covsEndorsesResult->[0]->{'LOCATION_NO'};
        $a->{'UNIT_NO'} = $covsEndorsesResult->[0]->{'UNIT_NO'};
        $a->{'IMT_COVERAGE'} = $covsEndorsesResult->[0]->{'IMT_COVERAGE'};
        $a->{'COVERAGE'} = $covsEndorsesResult->[0]->{'COVERAGE'};
        if(defined($a->{'NEWROW'}) && $a->{'NEWROW'} eq 'Y')
        { $a->{'TRANSACTION_DATE'} = $currentDateTime; }
#        for my $t (keys %$a)
#          {
#              print '<br>' . $t .'=>' . ($a->{$t} || '****NONE') .'; ';
#          }
    }

    $ENGINE->{'CHECH_LIMITS'} ='Y';

    my $currentReservesRef = CalculateReserves($ENGINE);
#    use Data::Dumper;
#    die Dumper($currentReservesRef);
    my $limitErrors = incurredTotals($currentReservesRef,$line,$ENGINE);

    undef ($ENGINE->{'CHECH_LIMITS'});

    if(defined $ENGINE->{'NewDraft'}->{'insurpayDocName'} && scalar @{$limitErrors} > 0)   #$limitErrors is an array, was coded as a hash with keys.  5/15/2020 akc #202428
    {
        my @draft = grep($_->{'NEWROW'} eq 'Y', @{$ENGINE->{'Monetary'}->{'CLM_CASH'}});
        my ($insurpay_ok,$insurpay_errs,$draft_num,$draftData) = Claims_Insurpay_Transmit::Insurpay_Transmit($ENGINE,\@draft,1);
        delete $ENGINE->{'NewDraft'}->{'insurpayDocName'};
    }

    return ($limitErrors);
}

sub incurredTotals
{
    my $currentReservesRef = shift;
    my $line = shift;
    my $ENGINE = shift;
    my $error = $ENGINE->{'error'};
    my %currentReserves = %$currentReservesRef;
    my $miscPropReserve = 0;
    my %BIReserve = ();
    my %MedPayReserve = ();
    my $PDReserve = 0;
    my $CSLReserve = 0;
    my %UMReserve = ();
    my %UIMReserve = ();
    my $boatUMUIMReserve = 0;
    my %TowingReserve = ();
    my %RentRemReserve = ();
    my %guestBIReserve = ();
    my $liabilityReserve = 0;
    my %liabilityReserveBOGL = ();
    my %propertyReserve = ();
    my $addResPremReserve = 0;
    my $fireLegLiabReserve = 0;
    my %medPayEmpReserve = ();
    my %medPayNameReserve = ();
    my $miscPropPayment = 0;
    my %BIPayment = ();
    my %MedPayPayment = ();
    my $PDPayment = 0;
    my $CSLPayment = 0;
    my %UMPayment = ();
    my %UIMPayment = ();
    my $boatUMUIMPayment = 0;
    my %TowingPayment = ();
    my %RentRemPayment = ();
    my %guestBIPayment = ();
    my $liabilityPayment = 0;
    my %liabilityPaymentBOGL = ();
    my %propertyPayment = ();
    my $addResPremPayment = 0;
    my $fireLegLiabPayment = 0;
    my %medPayEmpPayment = ();
    my %medPayNamePayment = ();
    my @limitErrors = ();

    for my $cov (sort keys %currentReserves)
    {
        for my $lossType (sort keys %{$currentReserves{$cov}})
        {
            for my $lossCause (sort keys %{$currentReserves{$cov}->{$lossType}})
            {
                for my $partyID (sort keys %{$currentReserves{$cov}->{$lossType}->{$lossCause}})
                {
                    for my $type (sort keys %{$currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}})
                    {
                        for my $unit (sort keys %{$currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}})
                        {
                            if(defined($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit})
                                && defined($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'RESERVE'}))
                              {
#                                if ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'RESERVE'} != 0)
#                                {
                                    if($line =~ /010|011|012|030|031|051/)
                                    {
                                        if($lossType eq '01')
                                        {
                                            $BIReserve{$partyID} += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'RESERVE'} / 100);
                                        }
                                        if($lossType eq '03')
                                        {
                                            $MedPayReserve{$partyID} += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'RESERVE'} / 100);
                                        }
                                        if($lossType eq '04')
                                        {
                                            $PDReserve += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'RESERVE'} / 100);
                                        }
                                        if($lossType eq '09')
                                        {
                                            $UMReserve{$partyID} += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'RESERVE'} / 100);
                                        }
                                        if($lossType eq '10')
                                        {
                                            $UIMReserve{$partyID} += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'RESERVE'} / 100);
                                        }
                                        if($lossType eq '16')
                                        {
                                            $TowingReserve{$unit} += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'RESERVE'} / 100);
                                        }
                                        if($lossType eq '48')
                                        {
                                            $RentRemReserve{$unit} += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'RESERVE'} / 100);
                                        }
                                        if($line eq '030' && $lossType eq '08')
                                        {
                                            $guestBIReserve{$partyID} += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'RESERVE'} / 100);
                                        }
                                        if($line eq '030' && $lossType eq '51')
                                        {
                                            $MedPayReserve{$partyID} += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'RESERVE'} / 100);
                                        }
                                    }
                                    elsif($line =~ /112|113|120|110|111/)
                                    {
                                        if($lossType eq '58')
                                        {
                                            $liabilityReserve += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'RESERVE'} / 100);
                                        }
                                        if($lossType eq '73')
                                        {
                                            $MedPayReserve{$partyID} += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'RESERVE'} / 100);
                                        }
                                    }
                                    elsif($line =~ /015|016|804/)
                                    {
                                        if($cov eq '03')
                                        {
                                            $MedPayReserve{$partyID} += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'RESERVE'} / 100);
                                        }
                                        if($cov =~ /06/)
                                        {
                                            $CSLReserve += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'RESERVE'} / 100);
                                        }
#                                        if($cov eq '09')
#                                        {
#                                            $UMReserve{$partyID} += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'RESERVE'} / 100);
#                                        }
#                                        if($cov eq '10')
#                                        {
#                                            $UIMReserve{$partyID} += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'RESERVE'} / 100);
#                                        }
                                        if($cov eq '16')
                                        {
                                            $TowingReserve{$unit} += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'RESERVE'} / 100);
                                        }
                                        if($cov eq '48')
                                        {
                                            $RentRemReserve{$unit} += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'RESERVE'} / 100);
                                        }
                                    }
                                    elsif($line =~ /052/)
                                    {
                                        if($cov eq '02')
                                        {
                                            $miscPropReserve += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'RESERVE'} / 100);
                                        }
                                        if($cov eq '06')
                                        {
                                            if($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'covcode'} eq 'CSL')
                                            { $CSLReserve += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'RESERVE'} / 100); }
                                        }
                                        if($cov eq '09')
                                        {
                                            $boatUMUIMReserve += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'RESERVE'} / 100);
                                        }
                                    }
                                    elsif($line =~ /100/)
                                    {
                                        if($cov =~ /06/)
                                        {
                                            $liabilityReserve += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'RESERVE'} / 100);
                                        }
                                        if($cov =~ /09/)
                                        {
                                            $MedPayReserve{$partyID} += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'RESERVE'} / 100);
                                        }
                                    }
                                    elsif($line =~ /200|205/)
                                    {
                                        if(substr($cov,0,2) =~ /07/)
                                        {
                                            $propertyReserve{$cov} += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'RESERVE'} / 100);
                                        }
                                    }
                                    elsif($line =~ /575|580/)
                                    {
                                        if($lossType =~ /26|27|29|53|54|58|59/)
                                        {
                                            $liabilityReserveBOGL{$partyID} += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'RESERVE'} / 100);
                                        }
                                        if($lossType =~ /30|55|56/)
                                        {
                                            $MedPayReserve{$partyID} += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'RESERVE'} / 100);
                                        }
                                        if($lossType =~ /28/)
                                        {
                                            $fireLegLiabReserve += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'RESERVE'} / 100);
                                        }
                                    }
#                                    elsif($line =~ /804/)
#                                    {
#                                        if($lossType =~ /01|09|10|L3|L4/)
#                                        {
#                                            $BIUMUIMTotal += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'RESERVE'} / 100);
#                                        }
#                                    }
                                    elsif($line =~ /810|811|812|814|815|816/)
                                    {
                                        if(substr($cov,0,2) =~ /06/)
                                        {
                                            if($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'covcode'} eq 'CSL')
                                            {
                                                $liabilityReserveBOGL{$partyID} += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'RESERVE'} / 100);
                                            }
                                        }
                                        if(substr($cov,0,2) =~ /06/)
                                        {
                                            if($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'covcode'} eq 'MEDEX')
                                            {
                                                $MedPayReserve{$partyID} += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'RESERVE'} / 100);
                                            }
                                        }
                                        if(substr($cov,0,2) =~ /06/)
                                        {
                                            if($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'covcode'} eq 'FDLL')
                                            {
                                                $fireLegLiabReserve += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'RESERVE'} / 100);
                                            }
                                        }
                                        if(substr($cov,0,2) =~ /06/)
                                        {
                                            if($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'covcode'} eq 'PIADV')
                                            {
                                                $miscPropReserve += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'RESERVE'} / 100);
                                            }
                                        }
                                        if(substr($cov,0,2) =~ /06/)
                                        {
                                            if($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'covcode'} eq 'PCO')
                                            {
                                                $PDReserve += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'RESERVE'} / 100);
                                            }
                                        }
                                    }
                                    elsif($line =~ /300|301|302|330|331|332/)
                                    {
#                                        print ' cov '.$cov;
                                        if($cov =~ /06/)
                                        {
#                                            print ' cov1 '.$currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'covcode'};
                                            if($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'covcode'} eq 'LIAB')
                                            {
                                                $CSLReserve += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'RESERVE'} / 100);
                                            }
                                            if($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'covcode'} eq 'ARESP')
                                            {
                                                $addResPremReserve += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'RESERVE'} / 100);
                                            }
                                        }
                                        if($cov =~ /28/)
                                        {
                                            $fireLegLiabReserve += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'RESERVE'} / 100);
                                        }
                                        if($cov =~ /73/)
                                        {
                                            $MedPayReserve{$partyID} += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'RESERVE'} / 100);
                                        }
                                        if($cov =~ /74/)
                                        {
                                            $liabilityReserve += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'RESERVE'} / 100);
                                        }
                                        if($cov =~ /75/)
                                        {
                                            $medPayEmpReserve{$partyID} += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'RESERVE'} / 100);
                                        }
                                        if($cov =~ /76/)
                                        {
                                            $medPayNameReserve{$partyID} += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'RESERVE'} / 100);
                                        }
                                    }
                                    elsif($line =~ /350|360/)
                                    {
                                        if($lossType =~ /70/)
                                        {
                                            $liabilityReserve += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'RESERVE'} / 100);
                                        }
                                    }
#                                    $resCloseMessageTotal += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'RESERVE'} / 100);
#                                }
                              }
                            if(defined($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit})
                                && defined($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'PAYMENT'}))
                              {
#                                  if ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'PAYMENT'} != 0)
#                                  {
                                    if($line =~ /010|011|012|030|031|051/)
                                    {
                                        if($lossType eq '01')
                                        {
                                            $BIPayment{$partyID} += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'PAYMENT'} / 100);
                                        }
                                        if($lossType eq '03')
                                        {
                                            $MedPayPayment{$partyID} += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'PAYMENT'} / 100);
                                        }
                                        if($lossType eq '04')
                                        {
                                            $PDPayment += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'PAYMENT'} / 100);
                                        }
                                        if($lossType eq '09')
                                        {
                                            $UMPayment{$partyID} += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'PAYMENT'} / 100);
                                        }
                                        if($lossType eq '10')
                                        {
                                            $UIMPayment{$partyID} += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'PAYMENT'} / 100);
                                        }
                                        if($lossType eq '16')
                                        {
                                            $TowingPayment{$unit} += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'PAYMENT'} / 100);
                                        }
                                        if($lossType eq '48')
                                        {
                                            $RentRemPayment{$unit} += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'PAYMENT'} / 100);
                                        }
                                        if($line eq '030' && $lossType eq '08')
                                        {
                                            $guestBIPayment{$partyID} += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'PAYMENT'} / 100);
                                        }
                                        if($line eq '030' && $lossType eq '51')
                                        {
                                            $MedPayPayment{$partyID} += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'PAYMENT'} / 100);
                                        }
                                    }
                                    elsif($line =~ /052/)
                                    {
                                        if($cov eq '02')
                                        {
                                            $miscPropPayment += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'PAYMENT'} / 100);
                                        }
                                        if($cov eq '06')
                                        {
                                            if($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'covcode'} eq 'CSL')
                                            { $CSLPayment += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'PAYMENT'} / 100); }
                                        }
                                        if($cov eq '09')
                                        {
                                            $boatUMUIMPayment += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'PAYMENT'} / 100);
                                        }
                                    }
                                    elsif($line =~ /112|113|120|110|111/)
                                    {
                                        if($lossType eq '58')
                                        {
                                            $liabilityPayment += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'PAYMENT'} / 100);
                                        }
                                        if($lossType eq '73')
                                        {
                                            $MedPayPayment{$partyID} += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'PAYMENT'} / 100);
                                        }
                                    }
                                    elsif($line =~ /015|016|804/)
                                    {
                                        if($cov eq '03')
                                        {
                                            $MedPayPayment{$partyID} += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'PAYMENT'} / 100);
                                        }
                                        if($cov =~ /06/)
                                        {
                                            $CSLPayment += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'PAYMENT'} / 100);
                                        }
#                                        if($cov eq '09')
#                                        {
#                                            $UMPayment{$partyID} += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'PAYMENT'} / 100);
#                                        }
#                                        if($cov eq '10')
#                                        {
#                                            $UIMPayment{$partyID} += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'PAYMENT'} / 100);
#                                        }
                                        if($cov eq '16')
                                        {
                                            $TowingPayment{$unit} += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'PAYMENT'} / 100);
                                        }
                                        if($cov eq '48')
                                        {
                                            $RentRemPayment{$unit} += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'PAYMENT'} / 100);
                                        }
                                    }
                                    elsif($line =~ /100/)
                                    {
                                        if($cov =~ /06/)
                                        {
                                            $liabilityPayment += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'PAYMENT'} / 100);
                                        }
                                        if($cov =~ /09/)
                                        {
                                            $MedPayPayment{$partyID} += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'PAYMENT'} / 100);
                                        }
                                    }
                                    elsif($line =~ /200|205/)
                                    {
                                        if(substr($cov,0,2) =~ /07/)
                                        {
                                            $propertyPayment{$cov} += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'PAYMENT'} / 100);
                                        }
                                    }
                                    elsif($line =~ /350|360/)
                                    {
                                        if($lossType =~ /70/)
                                        {
                                            $liabilityPayment += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'PAYMENT'} / 100);
                                        }
                                    }
                                    elsif($line =~ /300|301|302|330|331|332/)
                                    {
                                        if($cov =~ /06/)
                                        {
                                            if($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'covcode'} eq 'LIAB')
                                            {
                                                $CSLPayment += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'PAYMENT'} / 100);
                                            }
                                            if($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'covcode'} eq 'ARESP')
                                            {
                                                $addResPremPayment += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'PAYMENT'} / 100);
                                            }
                                        }
                                        if($cov =~ /28/)
                                        {
                                            $fireLegLiabPayment += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'PAYMENT'} / 100);
                                        }
                                        if($cov =~ /73/)
                                        {
                                            $MedPayPayment{$partyID} += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'PAYMENT'} / 100);;
                                        }
                                        if($cov =~ /74/)
                                        {
                                            $liabilityPayment += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'PAYMENT'} / 100);
                                        }
                                        if($cov =~ /75/)
                                        {
                                            $medPayEmpPayment{$partyID} += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'PAYMENT'} / 100);
                                        }
                                        if($cov =~ /76/)
                                        {
                                            $medPayNamePayment{$partyID} += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'PAYMENT'} / 100);
                                        }
                                    }
                                    elsif($line =~ /575|580/)
                                    {
                                        if($lossType =~ /26|27|29|53|54|58|59/)
                                        {
                                            $liabilityPaymentBOGL{$partyID} += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'PAYMENT'} / 100);
                                        }
                                        if($lossType =~ /30|55|56/)
                                        {
                                            $MedPayPayment{$partyID} += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'PAYMENT'} / 100);
                                        }
                                        if($lossType =~ /28/)
                                        {
                                            $fireLegLiabPayment += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'PAYMENT'} / 100);
                                        }
                                    }
                                    elsif($line =~ /810|811|812|814|815|816/)
                                    {
                                        if(substr($cov,0,2) =~ /06/)
                                        {
                                            if($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'covcode'} eq 'CSL')
                                            {
                                                $liabilityPaymentBOGL{$partyID} += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'PAYMENT'} / 100);
                                            }
                                        }
                                        if(substr($cov,0,2) =~ /06/)
                                        {
                                            if($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'covcode'} eq 'MEDEX')
                                            {
                                                $MedPayPayment{$partyID} += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'PAYMENT'} / 100);
                                            }
                                        }
                                        if(substr($cov,0,2) =~ /06/)
                                        {
                                            if($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'covcode'} eq 'FDLL')
                                            {
                                                $fireLegLiabPayment += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'PAYMENT'} / 100);
                                            }
                                        }
                                        if(substr($cov,0,2) =~ /06/)
                                        {
                                            if($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'covcode'} eq 'PIADV')
                                            {
                                                $miscPropPayment += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'PAYMENT'} / 100);
                                            }
                                        }
                                        if(substr($cov,0,2) =~ /06/)
                                        {
                                            if($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'covcode'} eq 'PCO')
                                            {
                                                $PDPayment += ($currentReserves{$cov}->{$lossType}->{$lossCause}->{$partyID}->{$type}->{$unit}->{'PAYMENT'} / 100);
                                            }
                                        }
                                    }
#                                  }
                              }
                        }
                    }
                }
            }
        }
    }

    my $covsEndorsesGet = $ENGINE->{'DBH'}->prepare
        ("SELECT * from
            CLAIMDB.CLM_COVS_ENDORSES
            WHERE
            CLAIM_ID = ?
            AND COV_OR_ENDORSE = 'C'
            and DATE_DELETED = '9999-01-01-01.00.00.000000'
            ORDER BY IMT_COVERAGE, COVERAGE")
                || $error->($ENGINE);

    $covsEndorsesGet->execute($ENGINE->{'claimGeneral'}->{'CLAIM_ID'})
        || $error->($ENGINE);
    my $covsEndorsesResult = $covsEndorsesGet->fetchall_arrayref({})
        || $error->($ENGINE);

    my $partyGet = $ENGINE->{'DBH'}->prepare
        ("SELECT * from
            CLAIMDB.CLM_PARTIES
            WHERE
            PARTY_ID = ?
            and DATE_DELETED = '9999-01-01-01.00.00.000000'")
                || $error->($ENGINE);

        my %inlandMarineClassCodes = getInlandMarineClassCode();

    my $errorMessage = '';
    my $totalAgg = 0;
    if($line =~ /010|011|012|030|031|051/)
    {
        my $BITotalClaim = 0;
            for my $key (keys %BIReserve)
            {
            my $BITotal = 0;
#            if($BIPayment{$key} > $BIReserve{$key})
#            {
#                $BITotal = $BIPayment{$key};
#            }
#            else
#            {
                $BITotal = $BIReserve{$key} + $BIPayment{$key};
#            }
                $BITotalClaim += $BITotal;
                for my $c (@$covsEndorsesResult)
                {
                    if($c->{'IMT_COVERAGE'} eq '01')
                    {
                    $BITotal = nearest(.01, $BITotal);
                    $BIPayment{$key} = nearest(.01, $BIPayment{$key});
                    $c->{'LIMIT1'} = nearest(.01, $c->{'LIMIT1'});
                    if($BITotal > $c->{'LIMIT1'} || $BIPayment{$key} > $c->{'LIMIT1'})
                    {
                            $partyGet->execute($key)
                                || $error->($ENGINE);
                            my $partyResult = $partyGet->fetchall_arrayref({})
                                || $error->($ENGINE);
                        my $name = '';
                            if(defined($partyResult->[0]->{'FIRST_NAME'}) && $partyResult->[0]->{'FIRST_NAME'} gt '')
                            {
                            $name = $partyResult->[0]->{'FIRST_NAME'}.' '.$partyResult->[0]->{'LAST_NAME'};
                            }
                            else
                            {
                            $name = $partyResult->[0]->{'BUSINESS_NAME'};
                            }
                        $errorMessage = 'Bodily Injury amount for '.$name.' is more than the limit amount.';
                        push (@limitErrors, $errorMessage);
                    }
                    }
                }
            }
        #this if statement is in here to by pass the med pay limit edit  for claim 2012B5226. 110027
        if($ENGINE->{'claimGeneral'}->{'IMT_CLAIM_NO'} !~ /2012B5226|2012B8671|2013C9179|2019A1018/)
        {
            for my $key (keys %MedPayReserve)
            {
            my $MedicalPayTotal = 0;
#            if($MedPayPayment{$key} > $MedPayReserve{$key})
#            {
#                $MedicalPayTotal = $MedPayPayment{$key};
#            }
#            else
#            {
                $MedicalPayTotal = $MedPayReserve{$key} + $MedPayPayment{$key};
#            }
                for my $c (@$covsEndorsesResult)
                {
                    if($c->{'IMT_COVERAGE'} eq '03' || ($line eq '030' && $c->{'IMT_COVERAGE'} eq '52'))
                    {
                    $MedicalPayTotal = nearest(.01, $MedicalPayTotal);
                    $MedPayPayment{$key} = nearest(.01, $MedPayPayment{$key});
                    $c->{'LIMIT1'} = nearest(.01, $c->{'LIMIT1'});
                    if($MedicalPayTotal > $c->{'LIMIT1'} || $MedPayPayment{$key} > $c->{'LIMIT1'})
                    {
                            $partyGet->execute($key)
                                || $error->($ENGINE);
                            my $partyResult = $partyGet->fetchall_arrayref({})
                                || $error->($ENGINE);
                        my $name = '';
                            if(defined($partyResult->[0]->{'FIRST_NAME'}) && $partyResult->[0]->{'FIRST_NAME'} gt '')
                            {
                            $name = $partyResult->[0]->{'FIRST_NAME'}.' '.$partyResult->[0]->{'LAST_NAME'};
                            }
                            else
                            {
                            $name = $partyResult->[0]->{'BUSINESS_NAME'};
                            }
                        $errorMessage = 'Medical Payments amount for '.$name.' is more than the limit amount.';
                        push (@limitErrors, $errorMessage);
                    }
                    }
                }
            }
            }
        my $UMTotalClaim = 0;
            for my $key (keys %UMReserve)
            {
            my $UMTotal = 0;
#            if($UMUIMPayment{$key} > $UMUIMReserve{$key})
#            {
#                $UMUIMTotal = $UMUIMPayment{$key};
#            }
#            else
#            {
                $UMTotal = $UMReserve{$key} + $UMPayment{$key};
#            }
                $UMTotalClaim += $UMTotal;
                for my $c (@$covsEndorsesResult)
                {
                    if($c->{'IMT_COVERAGE'} =~ /09/)
                    {
                    $UMTotal = nearest(.01, $UMTotal);
                    $UMPayment{$key} = nearest(.01, $UMPayment{$key});
                    $c->{'LIMIT1'} = nearest(.01, $c->{'LIMIT1'});
                    if($UMTotal > $c->{'LIMIT1'} || $UMPayment{$key} > $c->{'LIMIT1'})
                    {
                            $partyGet->execute($key)
                                || $error->($ENGINE);
                            my $partyResult = $partyGet->fetchall_arrayref({})
                                || $error->($ENGINE);
                        my $name = '';
                            if(defined($partyResult->[0]->{'FIRST_NAME'}) && $partyResult->[0]->{'FIRST_NAME'} gt '')
                            {
                            $name = $partyResult->[0]->{'FIRST_NAME'}.' '.$partyResult->[0]->{'LAST_NAME'};
                            }
                            else
                            {
                            $name = $partyResult->[0]->{'BUSINESS_NAME'};
                            }
                        $errorMessage = 'UM amount for '.$name.' is more than the limit amount.';
                        push (@limitErrors, $errorMessage);
                    }
                    }
                }
            }
        my $UIMTotalClaim = 0;
            for my $key (keys %UIMReserve)
            {
            my $UIMTotal = 0;
#            if($UMUIMPayment{$key} > $UMUIMReserve{$key})
#            {
#                $UMUIMTotal = $UMUIMPayment{$key};
#            }
#            else
#            {
                $UIMTotal = $UIMReserve{$key} + $UIMPayment{$key};
#            }
                $UIMTotalClaim += $UIMTotal;
                for my $c (@$covsEndorsesResult)
                {
                    if($c->{'IMT_COVERAGE'} =~ /10/)
                    {
                    $UIMTotal = nearest(.01, $UIMTotal);
                    $UIMPayment{$key} = nearest(.01, $UIMPayment{$key});
                    $c->{'LIMIT1'} = nearest(.01, $c->{'LIMIT1'});
                    if($UIMTotal > $c->{'LIMIT1'} || $UIMPayment{$key} > $c->{'LIMIT1'})
                    {
                            $partyGet->execute($key)
                                || $error->($ENGINE);
                            my $partyResult = $partyGet->fetchall_arrayref({})
                                || $error->($ENGINE);
                        my $name = '';
                            if(defined($partyResult->[0]->{'FIRST_NAME'}) && $partyResult->[0]->{'FIRST_NAME'} gt '')
                            {
                            $name = $partyResult->[0]->{'FIRST_NAME'}.' '.$partyResult->[0]->{'LAST_NAME'};
                            }
                            else
                            {
                            $name = $partyResult->[0]->{'BUSINESS_NAME'};
                            }
                        $errorMessage = 'UIM amount for '.$name.' is more than the limit amount.';
                        push (@limitErrors, $errorMessage);
                    }
                    }
                }
            }
        my $itemDescQuery = getCoveredItemDescQuery($ENGINE);
            my %itemDescription = ();
            for my $key (keys %TowingReserve)
            {
            my $towingTotal = 0;
#            if($TowingPayment{$key} > $TowingReserve{$key})
#            {
#                $towingTotal = $TowingPayment{$key};
#            }
#            else
#            {
                $towingTotal = $TowingReserve{$key} + $TowingPayment{$key};
#            }
                for my $c (@$covsEndorsesResult)
                {
                    my $unitDesc = '';
                    my $lossCode = '';

                    if(!defined($itemDescription{$c->{'LOCATION_NO'}.'_'.$c->{'UNIT_NO'}}))
                      { $itemDescription{$c->{'LOCATION_NO'}.'_'.$c->{'UNIT_NO'}} = getCoveredItemDesc($ENGINE,$c->{'LOCATION_NO'},$c->{'UNIT_NO'},$itemDescQuery); }

                    if($itemDescription{$c->{'LOCATION_NO'}.'_'.$c->{'UNIT_NO'}})
                      { $unitDesc = '<td>'.$itemDescription{$c->{'LOCATION_NO'}.'_'.$c->{'UNIT_NO'}}.'</td>'; }
                    else
                      { $unitDesc = '<td>Unknown</td>'; }
                    if($c->{'IMT_COVERAGE'} eq '16' && $unitDesc eq $key)
                    {
                    $towingTotal = nearest(.01, $towingTotal);
                    $TowingPayment{$key} = nearest(.01, $TowingPayment{$key});
                    $c->{'LIMIT1'} = nearest(.01, $c->{'LIMIT1'});
                    if($towingTotal > $c->{'LIMIT1'} || $TowingPayment{$key} > $c->{'LIMIT1'})
                    {
                        $errorMessage = 'Towing amount for '.$key.' is more than the limit amount.';
                        push (@limitErrors, $errorMessage);
                    }
                    }
                }
            }
        my $rentRemTotalClaim = 0;
        if($ENGINE->{'claimGeneral'}->{'IMT_CLAIM_NO'} !~ /2016N0004|2019A6784|2021E6573|2021G3577|2022J9199|2022K5723/)
        {
	        for my $key (keys %RentRemReserve)
	        {
	            my $rentRemTotal = 0;
	#            if($RentRemPayment{$key} > $RentRemReserve{$key})
	#            {
	#                $rentRemTotal = $RentRemPayment{$key};
	#            }
	#            else
	#            {
	                $rentRemTotal = $RentRemReserve{$key} + $RentRemPayment{$key};
	#            }
	            $rentRemTotalClaim += $rentRemTotal;
	            for my $c (@$covsEndorsesResult)
	            {
	                    my $unitDesc = '';
	                    my $lossCode = '';

	                    if(!defined($itemDescription{$c->{'LOCATION_NO'}.'_'.$c->{'UNIT_NO'}}))
	                      { $itemDescription{$c->{'LOCATION_NO'}.'_'.$c->{'UNIT_NO'}} = getCoveredItemDesc($ENGINE,$c->{'LOCATION_NO'},$c->{'UNIT_NO'},$itemDescQuery); }

	                    if($itemDescription{$c->{'LOCATION_NO'}.'_'.$c->{'UNIT_NO'}})
	                      { $unitDesc = '<td>'.$itemDescription{$c->{'LOCATION_NO'}.'_'.$c->{'UNIT_NO'}}.'</td>'; }
	                    else
	                      { $unitDesc = '<td>Unknown</td>'; }
	                if($c->{'IMT_COVERAGE'} eq '48' && $unitDesc eq $key)
	                {
	                    $rentRemTotal = nearest(.01, $rentRemTotal);
	                    $RentRemPayment{$key} = nearest(.01, $RentRemPayment{$key});
	                    $c->{'LIMIT2'} = nearest(.01, $c->{'LIMIT2'});
	                    if($rentRemTotal > $c->{'LIMIT2'} || $RentRemPayment{$key} > $c->{'LIMIT2'})
	                    {
	                        $errorMessage = 'Rental Reimbursement amount for '.$key.' is more than the limit amount.';
	                        push (@limitErrors, $errorMessage);
	                    }
	                }
	            }
	        }
        }
        my $guestBITotalClaim = 0;
            if($line eq '030')
            {
                for my $key (keys %guestBIReserve)
                {
                my $guestBITotal = 0;
#                if($guestBIPayment{$key} > $guestBIReserve{$key})
#                {
#                    $guestBITotal = $guestBIPayment{$key};
#                }
#                else
#                {
                    $guestBITotal = $guestBIReserve{$key} + $guestBIPayment{$key};
#                }
                    $guestBITotalClaim += $guestBITotal;
                    for my $c (@$covsEndorsesResult)
                    {
                        if($c->{'IMT_COVERAGE'} eq '01')
                        {
                        $guestBITotal = nearest(.01, $guestBITotal);
                        $guestBIPayment{$key} = nearest(.01, $guestBIPayment{$key});
                        $c->{'LIMIT1'} = nearest(.01, $c->{'LIMIT1'});
                            if($guestBITotal > $c->{'LIMIT1'} || $guestBIPayment{$key} > $c->{'LIMIT1'})
                            {
                                $partyGet->execute($key)
                                    || $error->($ENGINE);
                                my $partyResult = $partyGet->fetchall_arrayref({})
                                    || $error->($ENGINE);
                                my $name = '';
                                if(defined($partyResult->[0]->{'FIRST_NAME'}) && $partyResult->[0]->{'FIRST_NAME'} gt '')
                                {
                                    $name = $partyResult->[0]->{'FIRST_NAME'}.' '.$partyResult->[0]->{'LAST_NAME'};
                                }
                                else
                                {
                                    $name = $partyResult->[0]->{'BUSINESS_NAME'};
                                }
                                $errorMessage = 'Guest Bodily Injury amount for '.$name.' is more than the limit amount.';
                                push (@limitErrors, $errorMessage);
                            }
                        }
                    }
                }
            }
            for my $c (@$covsEndorsesResult)
        {
            if($c->{'IMT_COVERAGE'} eq '01')
            {
                $BITotalClaim = nearest(.01, $BITotalClaim);
                $c->{'LIMIT2'} = nearest(.01, $c->{'LIMIT2'});
                if($BITotalClaim > $c->{'LIMIT2'})
                {
                    $errorMessage = 'Bodily Injury per claim amount is more than the limit amount.';
                    push (@limitErrors, $errorMessage);
                }
            }
            if($c->{'IMT_COVERAGE'} eq '04')
            {
                $PDReserve = nearest(.01, $PDReserve);
                $PDPayment = nearest(.01, $PDPayment);
                $c->{'LIMIT1'} = nearest(.01, $c->{'LIMIT1'});
                if($PDReserve + $PDPayment > $c->{'LIMIT1'} || $PDPayment > $c->{'LIMIT1'})
                {
                    $errorMessage = 'Property Damage per claim amount is more than the limit amount.';
                    push (@limitErrors, $errorMessage);
                }
            }
            if($c->{'IMT_COVERAGE'} =~ /09/)
            {
                $UMTotalClaim = nearest(.01, $UMTotalClaim);
                $c->{'LIMIT2'} = nearest(.01, $c->{'LIMIT2'});
                if($UMTotalClaim > $c->{'LIMIT2'})
                {
                    $errorMessage = 'UM per claim amount is more than the limit amount.';
                    push (@limitErrors, $errorMessage);
                }
            }
            if($c->{'IMT_COVERAGE'} =~ /10/)
            {
                $UIMTotalClaim = nearest(.01, $UIMTotalClaim);
                $c->{'LIMIT2'} = nearest(.01, $c->{'LIMIT2'});
                if($UIMTotalClaim > $c->{'LIMIT2'})
                {
                    $errorMessage = 'UIM per claim amount is more than the limit amount.';
                    push (@limitErrors, $errorMessage);
                }
            }
#            if($c->{'IMT_COVERAGE'} eq '48')
#            {
#                if($rentRemTotalClaim > $c->{'LIMIT2'})
#                {
#                    $errorMessage = 'Rental Reimbursement per claim amount is more than the limit amount.';
#                    push (@limitErrors, $errorMessage);
#                }
#            }
            if($line eq '030' && $c->{'IMT_COVERAGE'} eq '01')
            {
                $guestBITotalClaim = nearest(.01, $guestBITotalClaim);
                $c->{'LIMIT2'} = nearest(.01, $c->{'LIMIT2'});
                if($guestBITotalClaim > $c->{'LIMIT2'})
                {
                    $errorMessage = 'Guest Bodily Injury per claim amount is more than the limit amount.';
                    push (@limitErrors, $errorMessage);
                }
            }
        }
    }
    elsif($line =~ /052/)
    {
            for my $c (@$covsEndorsesResult)
        {
            if($c->{'IMT_COVERAGE'} eq '02')
            {
                $miscPropReserve = nearest(.01, $miscPropReserve);
                $miscPropPayment = nearest(.01, $miscPropPayment);
                $c->{'LIMIT1'} = nearest(.01, $c->{'LIMIT1'});
                if($miscPropReserve + $miscPropPayment > $c->{'LIMIT1'} || $miscPropPayment > $c->{'LIMIT1'})
                {
                    $errorMessage = 'Misc Property per claim amount is more than the limit amount.';
                    push (@limitErrors, $errorMessage);
                }
            }
            if($c->{'COVERAGE'} eq 'CSL')
            {
                $CSLReserve = nearest(.01, $CSLReserve);
                $CSLPayment = nearest(.01, $CSLPayment);
                $c->{'LIMIT1'} = nearest(.01, $c->{'LIMIT1'});
                if($CSLReserve + $CSLPayment > $c->{'LIMIT1'} || $CSLPayment > $c->{'LIMIT1'})
                {
                    $errorMessage = 'Combined Single Limit per claim amount is more than the limit amount.';
                    push (@limitErrors, $errorMessage);
                }
            }
            if($c->{'IMT_COVERAGE'} eq '09')
            {
                $boatUMUIMReserve = nearest(.01, $boatUMUIMReserve);
                $boatUMUIMPayment = nearest(.01, $boatUMUIMPayment);
                $c->{'LIMIT1'} = nearest(.01, $c->{'LIMIT1'});
                if($boatUMUIMReserve + $boatUMUIMPayment > $c->{'LIMIT1'} || $boatUMUIMPayment > $c->{'LIMIT1'})
                {
                    $errorMessage = 'UM/UIM per claim amount is more than the limit amount.';
                    push (@limitErrors, $errorMessage);
                }
            }
        }
    }
    elsif($line =~ /112|113|120|110|111/)
    {
        if($ENGINE->{'claimGeneral'}->{'IMT_CLAIM_NO'} !~ /2013C1399|2018R5728/)
        {
            for my $key (keys %MedPayReserve)
            {
            my $MedicalPayTotal = 0;
#            if($MedPayPayment{$key} > $MedPayReserve{$key})
#            {
#                $MedicalPayTotal = $MedPayPayment{$key};
#            }
#            else
#            {
                $MedicalPayTotal = $MedPayReserve{$key} + $MedPayPayment{$key};
#            }
                for my $c (@$covsEndorsesResult)
                {
                    if($c->{'COVERAGE'} eq 'MEDPM')
                    {
                    $MedicalPayTotal = nearest(.01, $MedicalPayTotal);
                    $MedPayPayment{$key} = nearest(.01, $MedPayPayment{$key});
                    $c->{'LIMIT1'} = nearest(.01, $c->{'LIMIT1'});
                    if($MedicalPayTotal > $c->{'LIMIT1'} || $MedPayPayment{$key} > $c->{'LIMIT1'})
                    {
                            $partyGet->execute($key)
                                || $error->($ENGINE);
                            my $partyResult = $partyGet->fetchall_arrayref({})
                                || $error->($ENGINE);
                        my $name = '';
                            if(defined($partyResult->[0]->{'FIRST_NAME'}) && $partyResult->[0]->{'FIRST_NAME'} gt '')
                            {
                            $name = $partyResult->[0]->{'FIRST_NAME'}.' '.$partyResult->[0]->{'LAST_NAME'};
                            }
                            else
                            {
                            $name = $partyResult->[0]->{'BUSINESS_NAME'};
                            }
                        $errorMessage = 'Medical Payments amount for '.$name.' is more than the limit amount.';
                        push (@limitErrors, $errorMessage);
                    }
                    }
                }
            }
            }
        for my $c (@$covsEndorsesResult)
        {
            if($c->{'COVERAGE'} eq 'PL')
            {
                $liabilityReserve = nearest(.01, $liabilityReserve);
                $liabilityPayment = nearest(.01, $liabilityPayment);
                $c->{'LIMIT1'} = nearest(.01, $c->{'LIMIT1'});
                if($liabilityReserve + $liabilityPayment > $c->{'LIMIT1'} || $liabilityPayment > $c->{'LIMIT1'})
                {
                    $errorMessage = 'Liability per claim amount is more than the limit amount.';
                    push (@limitErrors, $errorMessage);
                }
            }
        }
    }
    elsif($line =~ /015|016|804/)
    {
            for my $key (keys %MedPayReserve)
            {
            my $MedicalPayTotal = 0;
#            if($MedPayPayment{$key} > $MedPayReserve{$key})
#            {
#                $MedicalPayTotal = $MedPayPayment{$key};
#            }
#            else
#            {
                $MedicalPayTotal = $MedPayReserve{$key} + $MedPayPayment{$key};
#            }
                for my $c (@$covsEndorsesResult)
                {
                    if($c->{'IMT_COVERAGE'} eq '03')
                    {
                    $MedicalPayTotal = nearest(.01, $MedicalPayTotal);
                    $MedPayPayment{$key} = nearest(.01, $MedPayPayment{$key});
                    $c->{'LIMIT1'} = nearest(.01, $c->{'LIMIT1'});
                    if($MedicalPayTotal > $c->{'LIMIT1'} || $MedPayPayment{$key} > $c->{'LIMIT1'})
                    {
                            $partyGet->execute($key)
                                || $error->($ENGINE);
                            my $partyResult = $partyGet->fetchall_arrayref({})
                                || $error->($ENGINE);
                        my $name = '';
                            if(defined($partyResult->[0]->{'FIRST_NAME'}) && $partyResult->[0]->{'FIRST_NAME'} gt '')
                            {
                            $name = $partyResult->[0]->{'FIRST_NAME'}.' '.$partyResult->[0]->{'LAST_NAME'};
                            }
                            else
                            {
                            $name = $partyResult->[0]->{'BUSINESS_NAME'};
                            }
                        $errorMessage = 'Medical Payments amount for '.$name.' is more than the limit amount.';
                        push (@limitErrors, $errorMessage);
                    }
                    }
                }
            }
#        my $UMTotalClaim = 0;
#        for my $key (keys %UMReserve)
#        {
#            my $UMTotal = 0;
##            if($UMUIMPayment{$key} > $UMUIMReserve{$key})
##            {
##                $UMUIMTotal = $UMUIMPayment{$key};
##            }
##            else
##            {
#                $UMTotal = $UMReserve{$key} + $UMPayment{$key};
##            }
#            $UMTotalClaim += $UMTotal;
#            for my $c (@$covsEndorsesResult)
#            {
#                if($c->{'IMT_COVERAGE'} =~ /09/)
#                {
#                    $UMTotal = nearest(.01, $UMTotal);
#                    $UMPayment{$key} = nearest(.01, $UMPayment{$key});
#                    $c->{'LIMIT1'} = nearest(.01, $c->{'LIMIT1'});
#                    if($UMTotal > $c->{'LIMIT1'} || $UMPayment{$key} > $c->{'LIMIT1'})
#                    {
#                        $partyGet->execute($key)
#                            || $error->($ENGINE);
#                        my $partyResult = $partyGet->fetchall_arrayref({})
#                            || $error->($ENGINE);
#                        my $name = '';
#                        if(defined($partyResult->[0]->{'FIRST_NAME'}) && $partyResult->[0]->{'FIRST_NAME'} gt '')
#                        {
#                            $name = $partyResult->[0]->{'FIRST_NAME'}.' '.$partyResult->[0]->{'LAST_NAME'};
#                        }
#                        else
#                        {
#                            $name = $partyResult->[0]->{'BUSINESS_NAME'};
#                        }
#                        $errorMessage = 'UM amount for '.$name.' is more than the limit amount.';
#                        push (@limitErrors, $errorMessage);
#                    }
#                }
#            }
#        }
#        my $UIMTotalClaim = 0;
#        for my $key (keys %UIMReserve)
#        {
#            my $UIMTotal = 0;
##            if($UMUIMPayment{$key} > $UMUIMReserve{$key})
##            {
##                $UMUIMTotal = $UMUIMPayment{$key};
##            }
##            else
##            {
#                $UIMTotal = $UIMReserve{$key} + $UIMPayment{$key};
##            }
#            $UIMTotalClaim += $UIMTotal;
#            for my $c (@$covsEndorsesResult)
#            {
#                if($c->{'IMT_COVERAGE'} =~ /10/)
#                {
#                    $UIMTotal = nearest(.01, $UIMTotal);
#                    $UIMPayment{$key} = nearest(.01, $UIMPayment{$key});
#                    $c->{'LIMIT1'} = nearest(.01, $c->{'LIMIT1'});
#                    if($UIMTotal > $c->{'LIMIT1'} || $UIMPayment{$key} > $c->{'LIMIT1'})
#                    {
#                        $partyGet->execute($key)
#                            || $error->($ENGINE);
#                        my $partyResult = $partyGet->fetchall_arrayref({})
#                            || $error->($ENGINE);
#                        my $name = '';
#                        if(defined($partyResult->[0]->{'FIRST_NAME'}) && $partyResult->[0]->{'FIRST_NAME'} gt '')
#                        {
#                            $name = $partyResult->[0]->{'FIRST_NAME'}.' '.$partyResult->[0]->{'LAST_NAME'};
#                        }
#                        else
#                        {
#                            $name = $partyResult->[0]->{'BUSINESS_NAME'};
#                        }
#                        $errorMessage = 'UIM amount for '.$name.' is more than the limit amount.';
#                        push (@limitErrors, $errorMessage);
#                    }
#                }
#            }
#        }
        my $itemDescQuery = getCoveredItemDescQuery($ENGINE);
            my %itemDescription = ();
            for my $key (keys %TowingReserve)
            {
            my $towingTotal = 0;
#            if($TowingPayment{$key} > $TowingReserve{$key})
#            {
#                $towingTotal = $TowingPayment{$key};
#            }
#            else
#            {
                $towingTotal = $TowingReserve{$key} + $TowingPayment{$key};
#            }
                for my $c (@$covsEndorsesResult)
                {
                    my $unitDesc = '';
                    my $lossCode = '';

                    if(!defined($itemDescription{$c->{'LOCATION_NO'}.'_'.$c->{'UNIT_NO'}}))
                      { $itemDescription{$c->{'LOCATION_NO'}.'_'.$c->{'UNIT_NO'}} = getCoveredItemDesc($ENGINE,$c->{'LOCATION_NO'},$c->{'UNIT_NO'},$itemDescQuery); }

                    if($itemDescription{$c->{'LOCATION_NO'}.'_'.$c->{'UNIT_NO'}})
                      { $unitDesc = '<td>'.$itemDescription{$c->{'LOCATION_NO'}.'_'.$c->{'UNIT_NO'}}.'</td>'; }
                    else
                      { $unitDesc = '<td>Unknown</td>'; }
                    if($c->{'IMT_COVERAGE'} eq '16' && $unitDesc eq $key)
                    {
                    $towingTotal = nearest(.01, $towingTotal);
                    $TowingPayment{$key} = nearest(.01, $TowingPayment{$key});
                    $c->{'LIMIT1'} = nearest(.01, $c->{'LIMIT1'});
                    if($towingTotal > $c->{'LIMIT1'} || $TowingPayment{$key} > $c->{'LIMIT1'})
                    {
                        $errorMessage = 'Towing amount for '.$key.' is more than the limit amount.';
                        push (@limitErrors, $errorMessage);
                    }
                    }
                }
            }
        my $rentRemTotalClaim = 0;
        for my $key (keys %RentRemReserve)
        {
            my $rentRemTotal = 0;
#            if($RentRemPayment{$key} > $RentRemReserve{$key})
#            {
#                $rentRemTotal = $RentRemPayment{$key};
#            }
#            else
#            {
                $rentRemTotal = $RentRemReserve{$key} + $RentRemPayment{$key};
#            }
            $rentRemTotalClaim += $rentRemTotal;
            for my $c (@$covsEndorsesResult)
            {
                my $unitDesc = '';
                my $lossCode = '';

                if(!defined($itemDescription{$c->{'LOCATION_NO'}.'_'.$c->{'UNIT_NO'}}))
                  { $itemDescription{$c->{'LOCATION_NO'}.'_'.$c->{'UNIT_NO'}} = getCoveredItemDesc($ENGINE,$c->{'LOCATION_NO'},$c->{'UNIT_NO'},$itemDescQuery); }

                if($itemDescription{$c->{'LOCATION_NO'}.'_'.$c->{'UNIT_NO'}})
                  { $unitDesc = '<td>'.$itemDescription{$c->{'LOCATION_NO'}.'_'.$c->{'UNIT_NO'}}.'</td>'; }
                else
                  { $unitDesc = '<td>Unknown</td>'; }
                if($c->{'IMT_COVERAGE'} eq '48' && $unitDesc eq $key)
                {
                    $rentRemTotal = nearest(.01, $rentRemTotal);
                    $RentRemPayment{$key} = nearest(.01, $RentRemPayment{$key});
                    $c->{'LIMIT2'} = nearest(.01, $c->{'LIMIT2'});
                    if($rentRemTotal > $c->{'LIMIT2'} || $RentRemPayment{$key} > $c->{'LIMIT2'})
                    {
                        $errorMessage = 'Rental Reimbursement amount for '.$key.' is more than the limit amount.';
                        push (@limitErrors, $errorMessage);
                    }
                }
            }
        }
        my $CSLFound = 'N';
            for my $c (@$covsEndorsesResult)
        {
            if($c->{'IMT_COVERAGE'} eq '06' && $CSLFound eq 'N')
            {
                $CSLFound = 'Y';
                $CSLReserve = nearest(.01, $CSLReserve);
                $CSLPayment = nearest(.01, $CSLPayment);
                $c->{'LIMIT1'} = nearest(.01, $c->{'LIMIT1'});
                if($CSLReserve + $CSLPayment > $c->{'LIMIT1'} || $CSLPayment > $c->{'LIMIT1'})
                {
                    $errorMessage = 'Combined Single Limit per claim amount is more than the limit amount.';
                    push (@limitErrors, $errorMessage);
                }
            }
#            if($c->{'IMT_COVERAGE'} =~ /09/)
#            {
#                $UMTotalClaim = nearest(.01, $UMTotalClaim);
#                $c->{'LIMIT2'} = nearest(.01, $c->{'LIMIT2'});
#                if($UMTotalClaim > $c->{'LIMIT2'})
#                {
#                    $errorMessage = 'UM per claim amount is more than the limit amount.';
#                    push (@limitErrors, $errorMessage);
#                }
#            }
#            if($c->{'IMT_COVERAGE'} =~ /10/)
#            {
#                $UIMTotalClaim = nearest(.01, $UIMTotalClaim);
#                $c->{'LIMIT2'} = nearest(.01, $c->{'LIMIT2'});
#                if($UIMTotalClaim > $c->{'LIMIT2'})
#                {
#                    $errorMessage = 'UIM per claim amount is more than the limit amount.';
#                    push (@limitErrors, $errorMessage);
#                }
#            }
#            if($c->{'IMT_COVERAGE'} eq '48')
#            {
#                if($rentRemTotalClaim > $c->{'LIMIT2'})
#                {
#                    $errorMessage = 'Rental Reimbursement per claim amount is more than the limit amount.';
#                    push (@limitErrors, $errorMessage);
#                }
#            }
        }
    }
    elsif($line =~ /100/)
    {
            for my $key (keys %MedPayReserve)
            {
            my $MedicalPayTotal = 0;
#            if($MedPayPayment{$key} > $MedPayReserve{$key})
#            {
#                $MedicalPayTotal = $MedPayPayment{$key};
#            }
#            else
#            {
                $MedicalPayTotal = $MedPayReserve{$key} + $MedPayPayment{$key};
#            }
                for my $c (@$covsEndorsesResult)
                {
                    if($c->{'COVERAGE'} eq 'MEDPM')
                    {
                    $MedicalPayTotal = nearest(.01, $MedicalPayTotal);
                    $MedPayPayment{$key} = nearest(.01, $MedPayPayment{$key});
                    $c->{'LIMIT1'} = nearest(.01, $c->{'LIMIT1'});
                    if($MedicalPayTotal > $c->{'LIMIT1'} || $MedPayPayment{$key} > $c->{'LIMIT1'})
                    {
                            $partyGet->execute($key)
                                || $error->($ENGINE);
                            my $partyResult = $partyGet->fetchall_arrayref({})
                                || $error->($ENGINE);
                        my $name = '';
                            if(defined($partyResult->[0]->{'FIRST_NAME'}) && $partyResult->[0]->{'FIRST_NAME'} gt '')
                            {
                            $name = $partyResult->[0]->{'FIRST_NAME'}.' '.$partyResult->[0]->{'LAST_NAME'};
                            }
                            else
                            {
                            $name = $partyResult->[0]->{'BUSINESS_NAME'};
                            }
                        $errorMessage = 'Medical Payments amount for '.$name.' is more than the limit amount.';
                        push (@limitErrors, $errorMessage);
                    }
                    }
                }
            }
            for my $c (@$covsEndorsesResult)
        {
            if($c->{'COVERAGE'} eq 'PL')
            {
                $liabilityReserve = nearest(.01, $liabilityReserve);
                $liabilityPayment = nearest(.01, $liabilityPayment);
                $c->{'LIMIT1'} = nearest(.01, $c->{'LIMIT1'});
                if($liabilityReserve + $liabilityPayment > $c->{'LIMIT1'} || $liabilityPayment > $c->{'LIMIT1'})
                {
                    $errorMessage = 'Liability per claim amount is more than the limit amount.';
                    push (@limitErrors, $errorMessage);
                }
            }
        }
    }
    elsif($line =~ /200|205/)
    {
        if($ENGINE->{'claimGeneral'}->{'IMT_CLAIM_NO'} !~ /2015H6877/)
        {
            for my $key (keys %propertyReserve)
            {
            my $propertyTotal = 0;
#            if($propertyPayment{$key} > $propertyReserve{$key})
#            {
#                $propertyTotal = $propertyPayment{$key};
#            }
#            else
#            {
                $propertyTotal = $propertyReserve{$key} + $propertyPayment{$key};
#            }
                for my $c (@$covsEndorsesResult)
                {
                    if($c->{'COVERAGE_ID'} eq substr($key,2))
                    {
                    $propertyTotal = nearest(.01, $propertyTotal);
                    $propertyPayment{$key} = nearest(.01, $propertyPayment{$key});
                    $c->{'LIMIT1'} = nearest(.01, $c->{'LIMIT1'});
                    if($propertyTotal > $c->{'LIMIT1'} || $propertyPayment{$key} > $c->{'LIMIT1'})
                    {
                        $errorMessage = 'Property amount for class '.$c->{'CLASS'}.' '.substr($inlandMarineClassCodes{$c->{'CLASS'}},0,70).' is more than the limit amount.';
                        push (@limitErrors, $errorMessage);
                    }
                    }
                }
            }
        }
    }
    elsif($line =~ /350|360/)
    {
            for my $c (@$covsEndorsesResult)
        {
            if($c->{'IMT_COVERAGE'} eq '70')
            {
                $liabilityReserve = nearest(.01, $liabilityReserve);
                $liabilityPayment = nearest(.01, $liabilityPayment);
                $c->{'LIMIT1'} = nearest(.01, $c->{'LIMIT1'});
                if($liabilityReserve + $liabilityPayment > $c->{'LIMIT1'} || $liabilityPayment > $c->{'LIMIT1'})
                {
                    $errorMessage = 'Liability per claim amount is more than the limit amount.';
                    push (@limitErrors, $errorMessage);
                }
            }
        }
    }
    elsif($line =~ /300|301|302|330|331|332/)
    {
        my $medicalPayTotalClaim = 0;
            for my $key (keys %MedPayReserve)
            {
            my $MedicalPayTotal = 0;
#            if($MedPayPayment{$key} > $MedPayReserve{$key})
#            {
#                $MedicalPayTotal = $MedPayPayment{$key};
#            }
#            else
#            {
                $MedicalPayTotal = $MedPayReserve{$key} + $MedPayPayment{$key};
#            }
                $medicalPayTotalClaim += $MedicalPayTotal;
                for my $c (@$covsEndorsesResult)
                {
                    if($c->{'IMT_COVERAGE'} eq '73')
                    {
                    $MedicalPayTotal = nearest(.01, $MedicalPayTotal);
                    $MedPayPayment{$key} = nearest(.01, $MedPayPayment{$key});
                    $c->{'LIMIT1'} = nearest(.01, $c->{'LIMIT1'});
                    if($MedicalPayTotal > $c->{'LIMIT1'} || $MedPayPayment{$key} > $c->{'LIMIT1'})
                    {
                            $partyGet->execute($key)
                                || $error->($ENGINE);
                            my $partyResult = $partyGet->fetchall_arrayref({})
                                || $error->($ENGINE);
                        my $name = '';
                            if(defined($partyResult->[0]->{'FIRST_NAME'}) && $partyResult->[0]->{'FIRST_NAME'} gt '')
                            {
                            $name = $partyResult->[0]->{'FIRST_NAME'}.' '.$partyResult->[0]->{'LAST_NAME'};
                            }
                            else
                            {
                            $name = $partyResult->[0]->{'BUSINESS_NAME'};
                            }
                        $errorMessage = 'Medical Payments amount for '.$name.' is more than the limit amount.';
                        push (@limitErrors, $errorMessage);
                    }
                    }
                }
            }
        my $medPayEmpTotalClaim = 0;
            for my $key (keys %medPayEmpReserve)
            {
            my $medPayEmpTotal = 0;
#            if($medPayEmpPayment{$key} > $medPayEmpReserve{$key})
#            {
#                $medPayEmpTotal = $medPayEmpPayment{$key};
#            }
#            else
#            {
                $medPayEmpTotal = $medPayEmpReserve{$key} + $medPayEmpPayment{$key};
#            }
                $medPayEmpTotalClaim += $medPayEmpTotal;
                for my $c (@$covsEndorsesResult)
                {
                    if($c->{'IMT_COVERAGE'} eq '75')
                    {
                    $medPayEmpTotal = nearest(.01, $medPayEmpTotal);
                    $medPayEmpPayment{$key} = nearest(.01, $medPayEmpPayment{$key});
                    $c->{'LIMIT1'} = nearest(.01, $c->{'LIMIT1'});
                    if($medPayEmpTotal > $c->{'LIMIT1'} || $medPayEmpPayment{$key} > $c->{'LIMIT1'})
                    {
                            $partyGet->execute($key)
                                || $error->($ENGINE);
                            my $partyResult = $partyGet->fetchall_arrayref({})
                                || $error->($ENGINE);
                        my $name = '';
                            if(defined($partyResult->[0]->{'FIRST_NAME'}) && $partyResult->[0]->{'FIRST_NAME'} gt '')
                            {
                            $name = $partyResult->[0]->{'FIRST_NAME'}.' '.$partyResult->[0]->{'LAST_NAME'};
                            }
                            else
                            {
                            $name = $partyResult->[0]->{'BUSINESS_NAME'};
                            }
                        $errorMessage = 'Medical Payments Employees and Exchage Labor amount for '.$name.' is more than the limit amount.';
                        push (@limitErrors, $errorMessage);
                    }
                    }
                }
            }
            for my $key (keys %medPayNameReserve)
            {
            my $medPayNameTotal = 0;
#            if($medPayNamePayment{$key} > $medPayNameReserve{$key})
#            {
#                $medPayNameTotal = $medPayNamePayment{$key};
#            }
#            else
#            {
                $medPayNameTotal = $medPayNameReserve{$key} + $medPayNamePayment{$key};
#            }
                for my $c (@$covsEndorsesResult)
                {
                    if($c->{'IMT_COVERAGE'} eq '76')
                    {
                    $medPayNameTotal = nearest(.01, $medPayNameTotal);
                    $medPayNamePayment{$key} = nearest(.01, $medPayNamePayment{$key});
                    $c->{'LIMIT1'} = nearest(.01, $c->{'LIMIT1'});
                    if($medPayNameTotal > $c->{'LIMIT1'} || $medPayNamePayment{$key} > $c->{'LIMIT1'})
                    {
                            $partyGet->execute($key)
                                || $error->($ENGINE);
                            my $partyResult = $partyGet->fetchall_arrayref({})
                                || $error->($ENGINE);
                        my $name = '';
                            if(defined($partyResult->[0]->{'FIRST_NAME'}) && $partyResult->[0]->{'FIRST_NAME'} gt '')
                            {
                            $name = $partyResult->[0]->{'FIRST_NAME'}.' '.$partyResult->[0]->{'LAST_NAME'};
                            }
                            else
                            {
                            $name = $partyResult->[0]->{'BUSINESS_NAME'};
                            }
                        $errorMessage = 'Medical Payments Named Person amount for '.$name.' is more than the limit amount.';
                        push (@limitErrors, $errorMessage);
                    }
                    }
                }
            }
            my $CSLLimit1 = 0;
            for my $c (@$covsEndorsesResult)
        {
            if($c->{'IMT_COVERAGE'} eq '06' && $c->{'COVERAGE'} eq 'LIAB')
            {
                if($ENGINE->{'claimGeneral'}->{'IMT_CLAIM_NO'} !~ /2015K6178/)
                {
	                $CSLLimit1 = $c->{'LIMIT1'};
	                $CSLReserve = nearest(.01, $CSLReserve);
	                $CSLPayment = nearest(.01, $CSLPayment);
	                $c->{'LIMIT1'} = nearest(.01, $c->{'LIMIT1'});
	                if($CSLReserve + $CSLPayment > $c->{'LIMIT1'} || $CSLPayment > $c->{'LIMIT1'})
	                {
	                    $errorMessage = 'Combined Single Limit per claim amount is more than the limit amount.';
	                    push (@limitErrors, $errorMessage);
	                }
                }
            }
            if($c->{'IMT_COVERAGE'} eq '06'  && $c->{'COVERAGE'} eq 'ARESP')
            {
                $addResPremReserve = nearest(.01, $addResPremReserve);
                $addResPremPayment = nearest(.01, $addResPremPayment);
                $c->{'LIMIT1'} = nearest(.01, $c->{'LIMIT1'});
                if($addResPremReserve + $addResPremPayment > $c->{'LIMIT1'} || $addResPremPayment > $c->{'LIMIT1'})
                {
                    $errorMessage = 'Additional Residence Premises per claim amount is more than the limit amount.';
                    push (@limitErrors, $errorMessage);
                }
            }
#            if($c->{'IMT_COVERAGE'} eq '73')
#            {
#                if($medicalPayTotalClaim > $c->{'LIMIT2'})
#                {
#                    $errorMessage = 'Medical Payments Premises per claim amount is more than the limit amount.';
#                    push (@limitErrors, $errorMessage);
#                }
#            }
            if($c->{'IMT_COVERAGE'} eq '28')
            {
                #Adding Fire Legal and the CSLReserve together because fire legal is included in the CSL.
                $CSLReserve = $CSLReserve + $fireLegLiabReserve;
                $CSLPayment = $CSLPayment + $fireLegLiabPayment;
                $CSLReserve = nearest(.01, $CSLReserve);
                $CSLPayment = nearest(.01, $CSLPayment);
                $c->{'LIMIT1'} = nearest(.01, $c->{'LIMIT1'});
                if($fireLegLiabReserve + $fireLegLiabPayment > 0)
                {
                        if($CSLReserve + $CSLPayment > $CSLLimit1 || $CSLPayment > $CSLLimit1)
                        {
                            $errorMessage = 'Fire Legal Liability per claim amount is more than the limit amount.';
                            push (@limitErrors, $errorMessage);
                        }
                }
            }
            if($c->{'IMT_COVERAGE'} eq '74')
            {
                $liabilityReserve = nearest(.01, $liabilityReserve);
                $liabilityPayment = nearest(.01, $liabilityPayment);
                $c->{'LIMIT1'} = nearest(.01, $c->{'LIMIT1'});
                if($liabilityReserve + $liabilityPayment > $c->{'LIMIT1'} || $liabilityPayment > $c->{'LIMIT1'})
                {
                    $errorMessage = 'Employers Liability per claim amount is more than the limit amount.';
                    push (@limitErrors, $errorMessage);
                }
            }
#            if($c->{'IMT_COVERAGE'} eq '75')
#            {
#                if($medPayEmpTotalClaim > $c->{'LIMIT2'})
#                {
#                    $errorMessage = 'Medical Payments Employees and Excange Labor per claim amount is more than the limit amount.';
#                    push (@limitErrors, $errorMessage);
#                }
#            }
        }
    }
    elsif($line =~ /575|580/)
    {
        my $CSLTotalClaim = 0;
            for my $key (keys %liabilityReserveBOGL)
            {
            my $CSLTotal = 0;
#            if($liabilityPaymentBOGL{$key} > $liabilityReserveBOGL{$key})
#            {
#                $CSLTotal = $liabilityPaymentBOGL{$key};
#            }
#            else
#            {
                $CSLTotal = $liabilityReserveBOGL{$key} + $liabilityPaymentBOGL{$key};
#            }
                $CSLTotalClaim += $CSLTotal;
                for my $c (@$covsEndorsesResult)
                {
                    if($c->{'COVERAGE'} =~ /CSL|EAOCC/)
                    {
                    $CSLTotal = nearest(.01, $CSLTotal);
                    $liabilityPaymentBOGL{$key} = nearest(.01, $liabilityPaymentBOGL{$key});
                    $c->{'LIMIT1'} = nearest(.01, $c->{'LIMIT1'});
                    if($CSLTotal > $c->{'LIMIT1'} || $liabilityPaymentBOGL{$key} > $c->{'LIMIT1'})
                    {
                            $partyGet->execute($key)
                                || $error->($ENGINE);
                            my $partyResult = $partyGet->fetchall_arrayref({})
                                || $error->($ENGINE);
                        my $name = '';
                            if(defined($partyResult->[0]->{'FIRST_NAME'}) && $partyResult->[0]->{'FIRST_NAME'} gt '')
                            {
                            $name = $partyResult->[0]->{'FIRST_NAME'}.' '.$partyResult->[0]->{'LAST_NAME'};
                            }
                            else
                            {
                            $name = $partyResult->[0]->{'BUSINESS_NAME'};
                            }
                        $errorMessage = 'Liability amount for '.$name.' is more than the limit amount.';
                        push (@limitErrors, $errorMessage);
                    }
                    }
                }
            }
            for my $key (keys %MedPayReserve)
            {
            my $MedicalPayTotal = 0;
#            if($MedPayPayment{$key} > $MedPayReserve{$key})
#            {
#                $MedicalPayTotal = $MedPayPayment{$key};
#            }
#            else
#            {
                $MedicalPayTotal = $MedPayReserve{$key} + $MedPayPayment{$key};
#            }
                for my $c (@$covsEndorsesResult)
                {
                    if($c->{'COVERAGE'} =~ /MEDPM|LBMED/)
                    {
                    $MedicalPayTotal = nearest(.01, $MedicalPayTotal);
                    $MedPayPayment{$key} = nearest(.01, $MedPayPayment{$key});
                    $c->{'LIMIT1'} = nearest(.01, $c->{'LIMIT1'});
                    if($MedicalPayTotal > $c->{'LIMIT1'} || $MedPayPayment{$key} > $c->{'LIMIT1'})
                    {
                            $partyGet->execute($key)
                                || $error->($ENGINE);
                            my $partyResult = $partyGet->fetchall_arrayref({})
                                || $error->($ENGINE);
                        my $name = '';
                            if(defined($partyResult->[0]->{'FIRST_NAME'}) && $partyResult->[0]->{'FIRST_NAME'} gt '')
                            {
                            $name = $partyResult->[0]->{'FIRST_NAME'}.' '.$partyResult->[0]->{'LAST_NAME'};
                            }
                            else
                            {
                            $name = $partyResult->[0]->{'BUSINESS_NAME'};
                            }
                        $errorMessage = 'Medical Payments amount for '.$name.' is more than the limit amount.';
                        push (@limitErrors, $errorMessage);
                    }
                    }
                }
            }
            for my $c (@$covsEndorsesResult)
        {
            if($c->{'COVERAGE'} =~ /CSL|EAOCC/)
            {
                $CSLTotalClaim = nearest(.01, $CSLTotalClaim);
                $c->{'LIMIT2'} = nearest(.01, $c->{'LIMIT2'});
                if($CSLTotalClaim > $c->{'LIMIT2'})
                {
                    $errorMessage = 'Liability limit per claim amount is more than the limit amount.';
                    push (@limitErrors, $errorMessage);
                }
            }
            if($c->{'COVERAGE'} eq 'FLLEX')
            {
                if($line =~ /580/)
                {
                    $fireLegLiabReserve = nearest(.01, $fireLegLiabReserve);
                    $fireLegLiabPayment = nearest(.01, $fireLegLiabPayment);
                    $c->{'LIMIT2'} = nearest(.01, $c->{'LIMIT2'});
                        if($fireLegLiabReserve + $fireLegLiabPayment > $c->{'LIMIT2'} || $fireLegLiabPayment > $c->{'LIMIT2'})
                        {
                            $errorMessage = 'Fire Legal per claim amount is more than the limit amount.';
                            push (@limitErrors, $errorMessage);
                        }
                }
                else
                {
                    $fireLegLiabReserve = nearest(.01, $fireLegLiabReserve);
                    $fireLegLiabPayment = nearest(.01, $fireLegLiabPayment);
                    $c->{'LIMIT1'} = nearest(.01, $c->{'LIMIT1'});
                        if($fireLegLiabReserve + $fireLegLiabPayment > $c->{'LIMIT1'} || $fireLegLiabPayment > $c->{'LIMIT1'})
                        {
                            $errorMessage = 'Fire Legal per claim amount is more than the limit amount.';
                            push (@limitErrors, $errorMessage);
                        }
                }
            }
            if($c->{'COVERAGE'} eq 'DAMPRMRENT')
            {
                if($line =~ /575/)
                {
                    $fireLegLiabReserve = nearest(.01, $fireLegLiabReserve);
                    $fireLegLiabPayment = nearest(.01, $fireLegLiabPayment);
                    $c->{'LIMIT1'} = nearest(.01, $c->{'LIMIT1'});
                        if($fireLegLiabReserve + $fireLegLiabPayment > $c->{'LIMIT1'} || $fireLegLiabPayment > $c->{'LIMIT1'})
                        {
                            $errorMessage = 'Damage to Premises Rented to You per claim amount is more than the limit amount.';
                            push (@limitErrors, $errorMessage);
                        }
                }
            }
        }
    }
    elsif($line =~ /810|811|812|814|815|816/)
    {
        my $CSLTotalClaim = 0;
            for my $key (keys %liabilityReserveBOGL)
            {
            my $CSLTotal = 0;
#            if($liabilityPaymentBOGL{$key} > $liabilityReserveBOGL{$key})
#            {
#                $CSLTotal = $liabilityPaymentBOGL{$key};
#            }
#            else
#            {
                $CSLTotal = $liabilityReserveBOGL{$key} + $liabilityPaymentBOGL{$key};
#            }
                $CSLTotalClaim += $CSLTotal;
                for my $c (@$covsEndorsesResult)
                {
                    if($c->{'COVERAGE'} eq 'CSL')
                    {
                    $CSLTotal = nearest(.01, $CSLTotal);
                    $liabilityPaymentBOGL{$key} = nearest(.01, $liabilityPaymentBOGL{$key});
                    $c->{'LIMIT1'} = nearest(.01, $c->{'LIMIT1'});
                    if($CSLTotal > $c->{'LIMIT1'} || $liabilityPaymentBOGL{$key} > $c->{'LIMIT1'})
                    {
                            $partyGet->execute($key)
                                || $error->($ENGINE);
                            my $partyResult = $partyGet->fetchall_arrayref({})
                                || $error->($ENGINE);
                        my $name = '';
                            if(defined($partyResult->[0]->{'FIRST_NAME'}) && $partyResult->[0]->{'FIRST_NAME'} gt '')
                            {
                            $name = $partyResult->[0]->{'FIRST_NAME'}.' '.$partyResult->[0]->{'LAST_NAME'};
                            }
                            else
                            {
                            $name = $partyResult->[0]->{'BUSINESS_NAME'};
                            }
                        $errorMessage = 'Liability amount for '.$name.' is more than the limit amount.';
                        push (@limitErrors, $errorMessage);
                    }
                    }
                }
            }
            for my $key (keys %MedPayReserve)
            {
            my $MedicalPayTotal = 0;
#            if($MedPayPayment{$key} > $MedPayReserve{$key})
#            {
#                $MedicalPayTotal = $MedPayPayment{$key};
#            }
#            else
#            {
                $MedicalPayTotal = $MedPayReserve{$key} + $MedPayPayment{$key};
#            }
                for my $c (@$covsEndorsesResult)
                {
                    if($c->{'COVERAGE'} eq 'MEDEX')
                    {
                    $MedicalPayTotal = nearest(.01, $MedicalPayTotal);
                    $MedPayPayment{$key} = nearest(.01, $MedPayPayment{$key});
                    $c->{'LIMIT1'} = nearest(.01, $c->{'LIMIT1'});
                    if($MedicalPayTotal > $c->{'LIMIT1'} || $MedPayPayment{$key} > $c->{'LIMIT1'})
                    {
                            $partyGet->execute($key)
                                || $error->($ENGINE);
                            my $partyResult = $partyGet->fetchall_arrayref({})
                                || $error->($ENGINE);
                        my $name = '';
                            if(defined($partyResult->[0]->{'FIRST_NAME'}) && $partyResult->[0]->{'FIRST_NAME'} gt '')
                            {
                            $name = $partyResult->[0]->{'FIRST_NAME'}.' '.$partyResult->[0]->{'LAST_NAME'};
                            }
                            else
                            {
                            $name = $partyResult->[0]->{'BUSINESS_NAME'};
                            }
                        $errorMessage = 'Medical Payments amount for '.$name.' is more than the limit amount.';
                        push (@limitErrors, $errorMessage);
                    }
                    }
                }
            }
            for my $c (@$covsEndorsesResult)
        {
            if($c->{'COVERAGE'} eq 'CSL')
            {
                $CSLTotalClaim = nearest(.01, $CSLTotalClaim);
                $c->{'LIMIT2'} = nearest(.01, $c->{'LIMIT2'});
                $totalAgg = $c->{'LIMIT2'};
                if($CSLTotalClaim > $c->{'LIMIT2'})
                {
                    $errorMessage = 'Liability limit per claim amount is more than the limit amount.';
                    push (@limitErrors, $errorMessage);
                }
            }
            if($c->{'COVERAGE'} eq 'FDLL')
            {
                $fireLegLiabReserve = nearest(.01, $fireLegLiabReserve);
                $fireLegLiabPayment = nearest(.01, $fireLegLiabPayment);
                $c->{'LIMIT1'} = nearest(.01, $c->{'LIMIT1'});
                if($fireLegLiabReserve + $fireLegLiabPayment > $c->{'LIMIT1'} || $fireLegLiabPayment > $c->{'LIMIT1'})
                {
                    $errorMessage = 'Fire Legal per claim amount is more than the limit amount.';
                    push (@limitErrors, $errorMessage);
                }
            }
            if($c->{'COVERAGE'} eq 'PIADV')
            {
                $miscPropReserve = nearest(.01, $miscPropReserve);
                $miscPropPayment = nearest(.01, $miscPropPayment);
                $c->{'LIMIT1'} = nearest(.01, $c->{'LIMIT1'});
                if($miscPropReserve + $miscPropPayment > $c->{'LIMIT1'} || $miscPropPayment > $c->{'LIMIT1'})
                {
                    $errorMessage = 'Personal Advertising Injury claim amount is more than the limit amount.';
                    push (@limitErrors, $errorMessage);
                }
            }
            if($c->{'COVERAGE'} eq 'PCO')
            {
                if($line =~ /810|814|816/ && $ENGINE->{'claimGeneral'}->{'SYSTEM_IND'} eq 'N' && $c->{'LIMIT1'} == 0 && $c->{'LIMIT2'} == 0)
                {
                   $PDReserve = nearest(.01, $PDReserve);
                   $PDPayment = nearest(.01, $PDPayment);
                   $CSLTotalClaim = nearest(.01, $CSLTotalClaim);
                   if($PDReserve + $PDPayment + $CSLTotalClaim > $totalAgg || $PDPayment > $totalAgg)
                   {
                       $errorMessage = 'Products and Completed Operations and CSL claim amount is more than the limit amount.';
                       push (@limitErrors, $errorMessage);
                   }
                }
                else
                {
                   $PDReserve = nearest(.01, $PDReserve);
                   $PDPayment = nearest(.01, $PDPayment);
                   $c->{'LIMIT1'} = nearest(.01, $c->{'LIMIT1'});
                   if($PDReserve + $PDPayment > $c->{'LIMIT1'} || $PDPayment > $c->{'LIMIT1'})
                   {
                       $errorMessage = 'Products and Completed Operations claim amount is more than the limit amount.';
                       push (@limitErrors, $errorMessage);
                   }
                }
            }
        }
    }
#    for my $key (keys %BIReserve)
#    {
#        my $BITotal = $BIReserve{$key} + $BIPayment{$key};
#        $BITotalClaim += $BITotal;
#        print ' BItotal_perPerson '.$BITotal;
#    }
#    print ' bitotalclaim '.$BITotalClaim;
#    for my $key (keys %MedPayReserve)
#    {
#        my $medPayTotal = $MedPayReserve{$key} + $MedPayPayment{$key};
#        print ' medpaytotal_perPerson '.$medPayTotal;
#    }
#    my $medPayTotal = $MedPayReserve + $MedPayPayment;
#    print ' medpaytotal '.$medPayTotal;
#    print ' csltotal '.$CSLTotal;
    return (\@limitErrors);
}

sub completeAdjEdits
{
    my $ENGINE = shift;
    my $error = $ENGINE->{'error'};
    my $claimid = $ENGINE->{'claimGeneral'}->{'CLAIM_ID'};
    my @completeEditsErrors = ();
    my $userID = $ENGINE->{'CGI'}->param('adjusterToComplete');


     my $user_key_data = fetch_user_key_data({
       authorization => $ENGINE->{'AUTH'}->{'platform_access_token'},
       user_key => $userID,
       max_attempts => 2
     });

     my $hold_first_name = uc($user_key_data->{content}->{data}->[0]->{attributes}->{first_name});
     my $hold_last_Name = uc($user_key_data->{content}->{data}->[0]->{attributes}->{last_name});

     my ($curDay, $curMonth, $curYear) = (localtime)[3,4,5];
        $curYear = $curYear+1900;
        $curDay = length($curDay)<2 ? '0'.$curDay : $curDay;
        $curMonth = $curMonth+1;
        $curMonth = length($curMonth)<2 ? '0'.$curMonth : $curMonth;
     my $currentDate = $curYear.'-'.$curMonth.'-'.$curDay;

# Alert query
    my $alertQuery = $ENGINE->{'DBH'}->prepare('
        SELECT DUE_DATE, HOW_OFTEN, USER_KEY, CLM_ALERT_ID, ALERT_TYPE, ALERTS_ID
        FROM CLAIMDB.CLM_ALERTS
        WHERE CLAIM_ID = ?
            and user_key = ?
            AND COMPLETE = \'N\'
            AND end_date = \'9999-01-01\'') || $error->($ENGINE,'Alert query prepare failed: '.$ENGINE->{'DBH'}->errstr);
    $alertQuery->execute($claimid, $userID) || $error->($ENGINE,'Alert query execute failed: '.$ENGINE->{'DBH'}->errstr);
    my $alertResults = $alertQuery->fetchall_arrayref({});

    my @sortedAlertResults = sort({$a->{'DUE_DATE'} cmp $b->{'DUE_DATE'}} @$alertResults);

    my $userName = $hold_first_name.' '.$hold_last_Name;
    for my $a (@sortedAlertResults)
    {
        my $alert_num = defined($a->{'ALERTS_ID'}) ? $a->{'ALERTS_ID'} : 0;
        my %alert_info = (
            5 => {'name'=>'claim update','msg_id'=>COVERAGE_UPDATE_DUE,'field'=>'claimCovEntry'},
            7 => {'name'=>'quarterly report','msg_id'=>QUARTERLY_REPORT_DUE,'field'=>''},
            9 => {'name'=>'work comp medical memo','msg_id'=>WC_MEDICAL_MEMO,'field'=>''},
            10 => {'name'=>'work comp disability memo','msg_id'=>WC_DISABILITY_MEMO,'field'=>''}
        );
        if($alert_num > 0)
        {
            if($currentDate ge $a->{'DUE_DATE'} && defined $alert_info{$alert_num})
            {
                my %errHash = ('screen'=>'Claims_FileActivity',
                                         'field'=>$alert_info{$alert_num}{'field'},
                                         'msg'=>'Please satisfy the '.$alert_info{$alert_num}{'name'}.' for '.$userName.'.');
                   #put reference to work hash on array of hash references
                   push (@completeEditsErrors, \%errHash);
            }
#            else
#            {   #move alert notification to 'completed' basket
#                my $notification_query = $ENGINE->{'DBH'}->prepare('
#                    SELECT N.NOTIFICATION_ID
#                    FROM CLAIMDB.CLM_NOTIFICATION AS N
#                        JOIN CLAIMDB.CLM_NTF_HAS_BASKET AS NB ON N.NOTIFICATION_ID = NB.NOTIFICATION_ID
#                        JOIN CLAIMDB.CLM_BASKET AS B ON B.BASKET_ID = NB.BASKET_ID
#                    WHERE b.user_key = ?
#                        and n.claim_id = ?
#                        and b.name <> \'COMPLETED\'') || $error->($ENGINE,'Alert query prepare failed: '.$ENGINE->{'DBH'}->errstr);
#                $notification_query->execute($a->{'USER_KEY'}, $claimid) || $error->($ENGINE,'Alert query execute failed: '.$ENGINE->{'DBH'}->errstr);
#                my $notification_results = $notification_query->fetchall_arrayref({});
#                close_notification($ENGINE,{'type'=>ALERT_NTF_TYPE,
#                     'msg_id'=>$alert_info{$alert_num}{'msg_id'},
#                     'claim_id'=>$claimid});
#            }
        }
    }
    $ENGINE->{'complete_errors'} = \@completeEditsErrors;
}


1;
