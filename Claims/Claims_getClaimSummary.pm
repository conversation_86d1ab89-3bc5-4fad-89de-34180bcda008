#!/usr/local/bin/perl
package Claims_getClaimSummary;

require 5.000;
use strict;
use vars qw($VERSION @ISA @EXPORT_OK);

use Exporter;

@ISA = qw(Exporter);
@EXPORT_OK = qw(getClaimSummaryTable);

$VERSION = '0.01';

use IMT::CommaFormatted qw(CommaFormatted);
use IMT::Access_Constants qw(getLineCodesByAccess);
use Claims_Error qw (error);


sub getClaimSummaryTable
{
    my $ENGINE=shift;
    my $client_id;
    my $x;
    my $y;
    my $z;
    my $col=0;
    my $gray;
    my $class;
    my @agencies = ();
    my @where = ();

    my $output =
               '<table class="footable table toggle-square toggle-medium all_claims">'.
               '<thead><tr>'.
               '<th>Policy</th><th>Claim</th><th data-sort-initial="descending">Loss Date</th><th>Paid</th><th>Reserve</th><th data-hide="phone,tablet">SDIP</th><th style="width:50%" data-hide="phone,tablet">Description</th>'.
               #'<th onclick="tableSort(this,\'claimSummary\','.($col++).',\'a\')">Policy</th><th onclick="tableSort(this,\'claimSummary\','.($col++).',\'a\')">Claim</th><th onclick="tableSort(this,\'claimSummary\','.($col++).',\'d\')">Loss Date</th><th class="numeric" onclick="tableSort(this,\'claimSummary\','.($col++).',\'n\')">Paid</th><th class="numeric" onclick="tableSort(this,\'claimSummary\','.($col++).',\'n\')">Reserve</th><th onclick="tableSort(this,\'claimSummary\','.($col++).',\'a\')">SDIP</th><th onclick="tableSort(this,\'claimSummary\','.($col++).',\'a\')" style="width:50%">Description</th>'.
               '</tr></thead>'.
               '<tbody id="claimSummary">';

    my @policies = ();
    my $policy;

    my $helpCall = '';
    if(defined($ENGINE->{'claimGeneral'}))
      { $helpCall = ' onclick="help(this.title);"'; }

    if($ENGINE->{'CGI'} && defined($ENGINE->{'CGI'}->param('client_id')))
    {
        $client_id = $ENGINE->{'CGI'}->param('client_id');
        my $sql = '
          select  ID_NUMBER,POLICY_STATE,LOB
          from    CLIENTDB.CLIENT_HAS_ITEM c
          join    CLIENTDB.ITEM i
          on      i.ITEM_ID=c.ITEM_ID
          join    CLIENTDB.VERSION v
          on      v.ITEM_ID=i.ITEM_ID
          where   CLIENT_ID = ? and v.VERSION_TYPE = \'P\' and v.STATUS <> \'Z\' and ';

        if($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} ne 'Internal')
        {
            my $sql .= ' (';
            my $accessLineCodes = getLineCodesByAccess();

            foreach my $key (keys %{$ENGINE->{'AUTH'}->{'AGENCY_ACCESS'}})
            {
                $sql.='\''.$key.'\',';
                my @agency_access = ();

                for my $access (keys %{$accessLineCodes})
                {
                    if($ENGINE->{'AUTH'}->{'AGENCY_ACCESS'}->{$key}->{$access})
                      { push(@agency_access,@{$accessLineCodes->{$access}}); }
                }

                if(scalar(@agency_access) > 0)
                {
                    if($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'Agent')
                    {
                        push(@agencies, '(AGENCY = \''.$key.'\' )');
                    }
                    else
                    {
                        push(@agencies, '(ID_NUMBER like \''.substr($key,2,2).'%\' or ID_NUMBER like \'LB%\'');
                    }
                }
            }
            chop($sql);
            $sql .= ')';

            if(scalar(@agencies) > 0)
            {
                $sql .= '('.join(' OR ',@agencies).')';
            }
            else
            {
               $sql .= ' 1=0 ';
            }
#            $sql.='AGENCY = \''.$ENGINE->{'AUTH'}->{'id'}.'\' and ';
        }
#        elsif($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'Mutual') {
#            $sql.='(ID_NUMBER like \''.substr($ENGINE->{'AUTH'}->{'id'},2,2).'%\' or ID_NUMBER like \'LB%\') and ';
#        }

        if(rindex($sql,'and ')) {
        $sql = substr($sql,0,-4);
        }

        my $sth = $ENGINE->{'DBH'}->prepare($sql) || error($ENGINE->{'DBH'}->errstr());
        $sth->execute($client_id) || error($ENGINE->{'DBH'}->errstr());
        $policy = $sth->fetchall_arrayref({});
    }
    elsif(defined($ENGINE->{'claimGeneral'}->{'ITEM_ID'}))
    {
        my $STH = $ENGINE->{'DBH'}->prepare('SELECT CLIENT_ID FROM CLIENTDB.CLIENT_HAS_ITEM WHERE ITEM_ID = '.$ENGINE->{'claimGeneral'}->{'ITEM_ID'}) ||
              error('General query prepare failed: '.$ENGINE->{'DBH'}->errstr);
        $STH->execute() || error('General query execute failed: '.$ENGINE->{'DBH'}->errstr);
        my $result = $STH->fetchall_arrayref({});

        if(scalar(@$result) > 0)
        {
            for my $r (@$result)
            {
                my $client_id = $r->{'CLIENT_ID'};

                my $sql = '
                    select  c.ITEM_ID, ID_NUMBER,POLICY_STATE,LOB
                    from    CLIENTDB.CLIENT_HAS_ITEM c
                    join    CLIENTDB.ITEM i
                    on      i.ITEM_ID=c.ITEM_ID
                    join    CLIENTDB.VERSION v
                    on      v.ITEM_ID=i.ITEM_ID
                    where   CLIENT_ID = ? and v.VERSION_TYPE = \'P\' and ';

                if($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} &&
                 ($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} ne 'Internal' &&
                  $ENGINE->{'AUTH'}->{'IMTOnline_UserType'} ne 'Insured'))
                {
                             my $sql .= ' (';
                        my $accessLineCodes = getLineCodesByAccess();

                        foreach my $key (keys %{$ENGINE->{'AUTH'}->{'AGENCY_ACCESS'}})
                        {
                            $sql.='\''.$key.'\',';
                            my @agency_access = ();

                            for my $access (keys %{$accessLineCodes})
                            {
                                if($ENGINE->{'AUTH'}->{'AGENCY_ACCESS'}->{$key}->{$access})
                                  { push(@agency_access,@{$accessLineCodes->{$access}}); }
                            }

                            if(scalar(@agency_access) > 0)
                            { push(@agencies, '(AGENCY = \''.$key.'\' )'); }
                        }
                        chop($sql);
                        $sql .= ')';

                        if(scalar(@agencies) > 0)
                        {
                            $sql .= '('.join(' OR ',@agencies).')';
                        }
                        else
                        {
                            $sql .= " 1=0 ";
                        }
                }
                if(rindex($sql,'and ')) {
                    $sql = substr($sql,0,-4);
                }

                my $sth = $ENGINE->{'DBH'}->prepare($sql) || error($ENGINE->{'DBH'}->errstr());
                $sth->execute($client_id) || error($ENGINE->{'DBH'}->errstr());
                my $policyResults = $sth->fetchall_arrayref({});
                push(@policies,@$policyResults);
            }
            my %policyHash = ();
            for my $p (@policies)
            {
                if(!defined($policyHash{$p->{'ID_NUMBER'}}))
                {
                    push(@$policy, $p);
                    $policyHash{$p->{'ID_NUMBER'}} = 1;
                }
            }
        }
        else
        {
            my $policyNum = $ENGINE->{'claimGeneral'}->{'POLICY_NUMBER'};
            if($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /301|302|331|332/ && $policyNum =~ /^12|^14|^26|^40|^48/)
              { $policyNum =~ s/^12|^14|^26|^40|^48//g; }
            # Add this policy to the list of policies in case it isn't on client.
            push(@$policy,{'ID_NUMBER'=>$policyNum,'ITEM_ID'=>'','POLICY_STATE'=>$ENGINE->{'claimGeneral'}->{'POLICY_STATE'},'LOB'=>$ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'}});
        }
    }
    elsif($ENGINE->{'PolicyGroup'})
    {
        my @policies = split(/,/,$ENGINE->{'PolicyGroup'}->{'POLICY_NUMBER'});
        my @lob = split(/,/,$ENGINE->{'PolicyGroup'}->{'LOB'});

        for(my $x =0;$x < scalar(@policies);$x++)
        {
            push(@$policy,{'ID_NUMBER'=>$policies[$x],'ITEM_ID'=>'','POLICY_STATE'=>'','LOB'=>$lob[$x]});
        }
    }
    else
    {
        my $policyNum = $ENGINE->{'claimGeneral'}->{'POLICY_NUMBER'};
        if($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /301|302|331|332/ && $policyNum =~ /^12|^14|^26|^40|^48/)
          { $policyNum =~ s/^12|^14|^26|^40|^48//g; }
        # Add this policy to the list of policies in case it isn't on client.
        push(@$policy,{'ID_NUMBER'=>$policyNum,'ITEM_ID'=>'','POLICY_STATE'=>$ENGINE->{'claimGeneral'}->{'POLICY_STATE'},'LOB'=>$ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'}});
    }

    if($policy->[0]) {
        my $claim;
        my $sdip;
        my $desc;
        my $var;
        my $cash;
        my $cash_off_res;
        my $reserve;
        my $loss_date;
        my $drivers;
        my $driver_name;
        my $sdip_list;
        my $sdip_help;
        my %done;
        my %itemID;
        my $clmpolicy;
        my $claimlinkbegin;
        my $claimlinkend;
        my $policylinkbegin;
        my $policylinkend;
        my %numstate = ('IA','14','IL','12','NE','26','SD','40','WI','48');
        my $claims_exist=0;
        my @params = ();
        my $clmsql = '
           select  POLICY_NUMBER,CLAIM_ID,IMT_CLAIM_NO,date(LOSS_DATE_TIME) as LOSS_DATE,PURGED_IND,CLAIM_STATUS,SUBRO_STATUS,SALV_STATUS,REINSURANCE_IND,CLOSE_RECOVERABLE
           from    CLAIMDB.CLM_GENERAL
           where   CLAIM_STATUS not in (\'P\',\'M\') and POLICY_NUMBER in (';
        my $sth_sdip = $ENGINE->{'DBH'}->prepare('
           select  s.SDIP_CODE,DEFINITION
           from    CLAIMDB.CLM_SDIPS s
           left join CLAIMDB.SDIP_CODES c
           on      c.SDIP_CODE=s.SDIP_CODE
           where   CLAIM_ID = ? and DATE_DELETED = \'9999-01-01-01.00.00.000000\' AND C.EFFECTIVE <= ? AND C.OBSOLETE > ?') || error($ENGINE->{'DBH'}->errstr());
        my $sth_var = $ENGINE->{'DBH'}->prepare('
           select  VARDATA
           from    CLAIMDB.CLM_VARDATA
           where   CLAIM_ID = ? and DATA_TYPE = ? and DATE_DELETED = \'9999-01-01-01.00.00.000000\'') || error($ENGINE->{'DBH'}->errstr());
        my $sth_cash = $ENGINE->{'DBH'}->prepare('
           select  sum(PAYMENT_AMT) as AMT
           from    CLAIMDB.CLM_CASH
           where   CLAIM_ID = ? and TYPE not in (\'R\') and LOSS_CODE not in (\'79\',\'80\',\'81\',\'82\') and DATE_DELETED = \'9999-01-01-01.00.00.000000\'') || error($ENGINE->{'DBH'}->errstr());
        #case TYPE when \'B\' then PAYMENT_AMT*-1 when \'S\' then PAYMENT_AMT*-1 else PAYMENT_AMT end
        my $sth_cash_off_res = $ENGINE->{'DBH'}->prepare('
           select  sum(PAYMENT_AMT) as AMT
           from    CLAIMDB.CLM_CASH
           where   CLAIM_ID = ? and RESERVED = ? and TYPE not in (\'B\',\'C\',\'S\',\'R\') and LOSS_CODE not in (\'79\',\'80\',\'81\',\'82\') and DATE_DELETED = \'9999-01-01-01.00.00.000000\'') || error($ENGINE->{'DBH'}->errstr());
        my $sth_reserve = $ENGINE->{'DBH'}->prepare('
           select  sum(RESERVE_AMT) as AMT
           from    CLAIMDB.CLM_RESERVES
           where   CLAIM_ID = ? and TYPE not in (\'B\',\'C\',\'S\',\'R\') and LOSS_CODE not in (\'79\',\'80\',\'81\',\'82\') and DATE_DELETED = \'9999-01-01-01.00.00.000000\'') || error($ENGINE->{'DBH'}->errstr());
        my $sth_driver = $ENGINE->{'DBH'}->prepare('
           select  FIRST_NAME,LAST_NAME,BUSINESS_NAME
           from    CLAIMDB.CLM_PARTIES AS P
           INNER JOIN CLAIMDB.CLM_PARTY_ROLES AS R ON P.PARTY_ID = R.PARTY_ID AND R.DATE_DELETED = \'9999-01-01-01.00.00.000000\'
           where   P.CLAIM_ID = ? AND R.ROLE = \'DR\' and P.DATE_DELETED = \'9999-01-01-01.00.00.000000\'') || error($ENGINE->{'DBH'}->errstr());
        for($x=0;$policy->[$x];$x++) {
           if(!$done{$policy->[$x]->{'ID_NUMBER'}}) {
               $clmpolicy = $policy->[$x]->{'ID_NUMBER'};
               if(defined($policy->[$x]->{'LOB'}) && $policy->[$x]->{'LOB'} =~ /301|302|331|332/ && $policy->[$x]->{'ID_NUMBER'} !~ /[0-9]/) {
                   $clmpolicy = $numstate{$policy->[$x]->{'POLICY_STATE'}}.$clmpolicy;
               }
               $clmsql.='?,';
               push(@params,$clmpolicy);
               $done{$clmpolicy}=$policy->[$x]->{'ID_NUMBER'};
               $itemID{$clmpolicy}=$policy->[$x]->{'ITEM_ID'};
           }
        }
        if($x>0) {
           chop($clmsql);
        }
        $clmsql.=')';
        my $sth_claim = $ENGINE->{'DBH'}->prepare($clmsql) || error($ENGINE->{'DBH'}->errstr());
        $sth_claim->execute(@params) || error($ENGINE->{'DBH'}->errstr());
        $claim = $sth_claim->fetchall_arrayref({});
        my %sortorder;
        my $srtct=99;
        @$claim = reverse sort {$a->{'LOSS_DATE'} cmp $b->{'LOSS_DATE'}} @$claim;
        for($y=0;$claim->[$y];$y++) {
           if(!$sortorder{$claim->[$y]->{'POLICY_NUMBER'}}) {
               $sortorder{$claim->[$y]->{'POLICY_NUMBER'}}=$srtct;
               $srtct--;
           }
        }
        @$claim = reverse sort {$sortorder{$a->{'POLICY_NUMBER'}}<=>$sortorder{$b->{'POLICY_NUMBER'}}||$a->{'LOSS_DATE'} cmp $b->{'LOSS_DATE'}} @$claim;
        for($y=0;$claim->[$y];$y++) {
            if(substr($claim->[$y]->{'AGENCY_NO'},0,6) eq '674008'){
              my $polk_agent = 0;
             if($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'Agent')
             {
               if(grep{$_ eq '674008'}keys %{$ENGINE->{'AUTH'}->{'AGENCY_ACCESS'}}){
                 $polk_agent = 1;
               }
             }
              if($ENGINE->{'AUTH'}->{'PolkAgency_Access'} ne '1' && $polk_agent != 1){
                next;
              }
            }

            $claims_exist=1;
            $sth_cash->execute($claim->[$y]->{'CLAIM_ID'}) || error($ENGINE->{'DBH'}->errstr());
            $cash = $sth_cash->fetchall_arrayref({});
            if($claim->[$y]->{'PURGED_IND'} eq 'N') {
               $sth_reserve->execute($claim->[$y]->{'CLAIM_ID'}) || error($ENGINE->{'DBH'}->errstr());
               $reserve = $sth_reserve->fetchall_arrayref({});
               $sth_cash_off_res->execute($claim->[$y]->{'CLAIM_ID'},'Y') || error($ENGINE->{'DBH'}->errstr());
               $cash_off_res = $sth_cash_off_res->fetchall_arrayref({});
            }
            $sth_driver->execute($claim->[$y]->{'CLAIM_ID'}) || error($ENGINE->{'DBH'}->errstr());
            $drivers = $sth_driver->fetchall_arrayref({});
            $driver_name = '';
            for my $d (@$drivers)
            {
                if($d->{'FIRST_NAME'} =~ /\w/)
                  { $driver_name .= $d->{'FIRST_NAME'}.' '; }
                if($d->{'LAST_NAME'} =~ /\w/)
                  { $driver_name .= $d->{'LAST_NAME'}.' '; }
                if($d->{'BUSINESS_NAME'} =~ /\w/)
                  { $driver_name .= $d->{'BUSINESS_NAME'}.' '; }
            }
            if($driver_name =~ /\w/)
              { $driver_name = ' - DRIVER: '.$driver_name; }
            $sth_sdip->execute($claim->[$y]->{'CLAIM_ID'},$claim->[$y]->{'LOSS_DATE'},$claim->[$y]->{'LOSS_DATE'}) || error($ENGINE->{'DBH'}->errstr());
            $sdip = $sth_sdip->fetchall_arrayref({});
            $sdip_list='';
            for($z=0;$sdip->[$z];$z++) {
                $sdip->[$z]->{'DEFINITION'} =~ s/&/&amp;/g;
                $sdip->[$z]->{'DEFINITION'} =~ s/</&lt;/g;
                $sdip_list.= <<EOF;
<span class="left">$sdip->[$z]->{'SDIP_CODE'}</span>
<a class="help" href="#" data-toggle="modal" data-target="#sdip$sdip->[$z]->{'SDIP_CODE'}$claim->[$y]->{'CLAIM_ID'}">&nbsp;&nbsp;</a>
<!--<div class="helptext hidden" id="sdip'.$sdip->[$z]->{'SDIP_CODE'}.$claim->[$y]->{'CLAIM_ID'}.'">'.$sdip->[$z]->{'DEFINITION'}.'</div>,'; -->

<div class="modal" id="sdip$sdip->[$z]->{'SDIP_CODE'}$claim->[$y]->{'CLAIM_ID'}" tabindex="-1" role="dialog" aria-labelledby="sdip$sdip->[$z]->{'SDIP_CODE'}$claim->[$y]->{'CLAIM_ID'}" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog" id="SDIPModal">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            </div>
            <div class="modal-body">
                <p>$sdip->[$z]->{'DEFINITION'}</p>
            </div>
        </div>
    </div>
</div>
EOF

            }
            chop($sdip_list);
            $loss_date = substr($claim->[$y]->{'LOSS_DATE'},5,2).'/'.substr($claim->[$y]->{'LOSS_DATE'},8,2).'/'.substr($claim->[$y]->{'LOSS_DATE'},0,4);
            $sth_var->execute($claim->[$y]->{'CLAIM_ID'},'DESCRIPT') || error($ENGINE->{'DBH'}->errstr());
            $var = $sth_var->fetchall_arrayref({});
            $desc='';
            for($z=0;$var->[$z];$z++) {
               $desc .= $var->[$z]->{'VARDATA'}.' ';
            }
            $desc =~ s/&/&amp;/g;
            $desc =~ s/</&lt;/g;
            if(!$desc) {
                $desc = '<em>No description available.</em>';
            }

            my $reserve_disp = '$0.00';
            if($claim->[$y]->{'PURGED_IND'} eq 'N') {
                $reserve_disp = '$'.CommaFormatted(sprintf("%0.2f",($reserve->[0]->{'AMT'}||0) - ($cash_off_res->[0]->{'AMT'}||0) ));
#                if($reserve_disp<0) {
#                    $reserve_disp=0;
#                }
            }
            else
              { $reserve_disp = 'Archived'; }
            if($reserve_disp eq '$0.00')
            {
                my $subroStatus = $claim->[$y]->{'SUBRO_STATUS'} || '';
                my $salvStatus = $claim->[$y]->{'SALV_STATUS'} || '';
                my $reinStatus = $claim->[$y]->{'REINSURANCE_IND'} || '';
                my $recovStatus = $claim->[$y]->{'CLOSE_RECOVERABLE'} || '';
                if($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'Internal' &&
                   ($subroStatus eq 'P' || $salvStatus eq 'P' || $reinStatus eq 'P'))
                {
                    $reserve_disp = 'Pending ';
                    if($subroStatus eq 'P')
                      { $reserve_disp .= 'Subr, '; }
                    if($salvStatus eq 'P')
                      { $reserve_disp .= 'Salv, '; }
                    if($reinStatus eq 'P')
                      { $reserve_disp .= 'Rein, '; }
                    if($recovStatus eq 'P')
                      { $reserve_disp .= 'Recov, '; }
                    chop($reserve_disp);
                }
                elsif($claim->[$y]->{'CLAIM_STATUS'} =~ /C/)
                  { $reserve_disp = 'Closed'; }
            }
            if($gray) {
               $class = 'class="altRow"';
               $gray=0;
            }
            else {
               $class = '';
               $gray=1;
            }
            if(!defined($client_id) && $itemID{$claim->[$y]->{'POLICY_NUMBER'}} =~ /\w/)
            {
                $policylinkbegin = '<a href="../Client/'.($ENGINE->{'AUTH'}->{'ClientEngine'} || 'ClientEngine.pl').'?load=ClientPView&amp;policy='.$done{$claim->[$y]->{'POLICY_NUMBER'}}.'">';
                $policylinkend = '</a>';
            }
            else
            {
                $policylinkbegin = '';
                $policylinkend = '';
            }
            if(defined($ENGINE->{'AUTH'}->{'Claims_Access'}) && $ENGINE->{'AUTH'}->{'Claims_Access'} =~ /A|I|S/) {
               $claimlinkbegin = '<a href="../Claims/Claims_Engine.pl?load=Claims_Info&amp;claimid='.$claim->[$y]->{'CLAIM_ID'}.'">';
               $claimlinkend = '</a>';
            }
            else {
               $claimlinkbegin = '';
               $claimlinkend = '';
            }
            $output .= '<tr>'.
                      '<td>'.$policylinkbegin.$done{$claim->[$y]->{'POLICY_NUMBER'}}.$policylinkend.'</td>'.
                      '<td>'.$claimlinkbegin.$claim->[$y]->{'IMT_CLAIM_NO'}.$claimlinkend.'</td>'.
                      '<td>'.$loss_date.'</td>'.
                      '<td class="numeric">$'.CommaFormatted(sprintf("%0.2f",($cash->[0]->{'AMT'}||0))).'</td>'.
                      '<td class="numeric">'.$reserve_disp.'</td>'.
                      '<td>'.$sdip_list.'</td>'.
                      '<td>'.$desc.$driver_name.'</td>'.
                      '</tr>';
        }
        if(!$claims_exist) {
            $output .= '<tr><td colspan="7"><em>No recent claim activity.</em></td></tr>';
        }
        $output .= '</tbody></table>';
        $output .= <<EOF;
<script type="text/javascript">
    \$(function () {
        \$('.table').footable();
    });
    \$('.sort-column').click(function (e) {
                e.preventDefault();

                //get the footable sort object
                var footableSort = \$('table').data('footable-sort');

                //get the index we are wanting to sort by
                var index = \$(this).data('index');

                footableSort.doSort(index, 'toggle');
            });
</script>
EOF
#        die Data::Dumper::Dumper($output);
        return $output;
    }
    else {
        return '';
    }
}

1;
