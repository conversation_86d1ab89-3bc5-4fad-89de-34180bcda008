$( document ).ready( function(){
    var focusValue = document.getElementById("focusSet").value;
    if(focusValue > 0)
    {
        document.getElementById("coveragesList"+focusValue).scrollIntoView({'block':'center','inline':'center'});
        document.getElementById("focusSet").value = '';
    }
});
function switchDetail()
{
         if (document.getElementById("showDetButton").innerHTML == "View Details")
         {
            document.getElementById("showDetButton").innerHTML = "Hide Details";
         }
         else if (document.getElementById("lossDetail").innerHTML > "" && document.getElementById("showDetButton").innerHTML == "View Details")
//         else if (document.getElementById("lossDetail").value > "")
         {
            document.getElementById("showDetButton").innerHTML = "Hide Details";
         }
         else if (document.getElementById("lossDetail").innerHTML > "" && document.getElementById("showDetButton").innerHTML == "Hide Details")
//         else if (document.getElementById("showDetButton").value == "Show Details")
         {
            document.getElementById("showDetButton").innerHTML = "View Details";
         }
         else if (document.getElementById("lossDetail").innerHTML == "")
         {
            document.getElementById("showDetButton").innerHTML = "View Details";
         }
}

function switchDetailRead()
{
         if (document.getElementById("showDetButton").innerHTML == "View Details")
         {
            document.getElementById("showDetButton").innerHTML = "Hide Details";
         }
         else if (document.getElementById("showDetButton").innerHTML == "Hide Details")
         {
            document.getElementById("showDetButton").innerHTML = "View Details";
         }
}

function addLossLoc(list)
{
        var newLossLocID = 'new'+newID;
        var lossLocArray = new Array();
        lossLocArray = list.split('|');
        var lossLocArrayLength = lossLocArray.length;
        var i = 0;
        var HTML1 = '';
        var HTML2 = '';
        var HTML = '<div id="addLossLocDiv'+newLossLocID+'" class="nowrap"><b>Add Loss Location</b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input type="hidden" name="ilossLocID'+newLossLocID+'" value="" /><select name="lossLocList'+newLossLocID+'" onchange="othLossLoc(this,\''+newLossLocID+'\')"><option value=""></option>';
        var j = 1;
        for (i=0;i< lossLocArrayLength;i=i+2)
        {
            HTML1 = HTML1+'<option value="'+lossLocArray[i]+'">'+lossLocArray[j]+'</option>';
            j = j +2;
        }
        HTML1 = HTML1+'</select><input type="button" class="delete" value="Remove" onclick="deleteOthLossLoc(\''+newLossLocID+'\')" /></div><br />';
        HTML = HTML+HTML1;
//        HTML2 = '<div style="display:none" id="othLossLoc'+newLossLocID+'"><b>Other Loss Location</b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input type="text" size="100" name="lossLocation" id="lossLocation" value="" /></div>';
        HTML2 = '<div style="display:none" id="othLossLoc'+newLossLocID+'"><b>Other Loss Location</b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input type="text" size="90" name="othLossLocText'+newLossLocID+'" id="othLossLocText'+newLossLocID+'" value="" /></div>';
        HTML = HTML+HTML2;
        var div = document.createElement("div");
        div.innerHTML = HTML;
        div.id = 'lossLoc'+newLossLocID;
        div.style.marginBottom = '1em';
        var lossLocListDiv = document.getElementById('lossLocListDiv');
        var selDiv = document.getElementById('addLossLocDiv');
        if(lossLocListDiv)
        {
            lossLocListDiv.insertBefore(div,selDiv);
        }
        newID++;
}

function deleteOthLossLoc(p)
{
    var loss = document.getElementById('lossLoc'+p);
    if(p.length < 3 || p.substr(0,3) != 'new')
    {
        var inp = document.createElement("input");
        inp.type = 'hidden';
        inp.name = 'delLossLoc'+p;
        inp.value = 1;
        loss.parentNode.appendChild(inp);
    }

    loss.parentNode.removeChild(loss);
}

function othLossLoc(loss,id)
{

    var val = loss.value;
    var div = document.getElementById('othLossLoc'+id);
    if(val == 'OT')
    {
        div.style.display = "";
    }
    else
    {
        div.style.display = "none";
    }
}

function createLabelHelp(label, help, data, id)
{
    var HTML = ''
+ ' <label>'
+ '     <span>'+label+'</span>'
+ '         <a href="#'+id+'" class="help" data-toggle="collapse" aria-expanded="false" aria-controls="'+id+'">&nbsp;&nbsp;</a>'
+ ' </label>'
+ ' <div class="collapse helptext" id="'+id+'">'+help+'</div>'
+ ' <div>'+data+'</div>';

    return HTML;
}

function addPolLoc(list)
{
        var newPolLocID = 'new'+newID;
        var polLocArray = new Array();
        polLocArray = list.split('|');
        var polLocArrayLength = polLocArray.length;
        var i = 0;

        var HTML1 = '<input type="hidden" name="ipolLocID'+newPolLocID+'" value="" /><select id="polLocList'+newPolLocID+'" name="polLocList'+newPolLocID+'" onchange="othPolicyLoc(this,\''+newPolLocID+'\')"><option value=""></option>';
        var HTML = '<input type="hidden" name="policyLocSection" id="policyLocSection" value=""/>';//'<div id="addPolLocDiv'+newPolLocID+'" class="nowrap">';
        var j = 1;
        for (i=0;i< polLocArrayLength;i=i+2)
        {
            var found = 0;
            for (k=0;k< newID;k=k+1)
            {
                var oldValue = document.getElementsByName('polLocListnew'+k);
                if (!oldValue.length)
                { continue; }
                if (oldValue[0].value == polLocArray[i])
                {
                    found = 1;
                    break;
                }
            }
            if (found == 1)
            {
                j = j +2;
                continue;
            }
            var n = polLocArray[j].search("Residence");
            if(n < 0)
            { HTML1 = HTML1+'<option value="'+polLocArray[i]+'">'+polLocArray[j]+'</option>'; }
            else
            { HTML1 = HTML1+'<option style="color:blue" value="'+polLocArray[i]+'">'+polLocArray[j]+'</option>'; }
            j = j +2;
        }
        HTML1 = HTML1+'</select><input class="delete" type="button" value="Remove" onclick="deleteOthPolLoc(\''+newPolLocID+'\')" />';

        var loc = createLabelHelp('<span style="color:red">*</span>Policy Location:','Select which location where the loss occurred, this is required if the loss occurred at a policy location. Location(s) marked blue indicate that the location is primarily occupied by the insured.',HTML1,'new_loc_help_'+newPolLocID);

        HTML = HTML+loc;//+'</div>';
//        HTML2 = '<div style="display:none" id="othPolicyLoc'+newPolLocID+'"><b>Other Policy Location Number</b>&nbsp;&nbsp;&nbsp;<input type="text" size="5" name="othPolLocLocN'+newPolLocID+'" id="othPolLocLocN'+newPolLocID+'" value="" />'
//        + '<b>Other Policy Unit Number</b>&nbsp;&nbsp;&nbsp;<input type="text" size="5" name="othPolLocUnitN'+newPolLocID+'" id="othPolLocUnitN'+newPolLocID+'" value="" /><br />'
        HTML2 = '<ul class="toplabel_onecol" style="display:none" id="othPolicyLoc'+newPolLocID+'">'
        + '<li><label>Address1</label><div><input type="text" size="30" name="othPolLocAdd1'+newPolLocID+'" id="othPolLocAdd1'+newPolLocID+'" value="" /></div></li>'
        + '<li><label>Address2</label><div><input type="text" size="30" name="othPolLocAdd2'+newPolLocID+'" id="othPolLocAdd2'+newPolLocID+'" value="" /></div></li>'
        + '<li><label>City</label><div><input type="text" disabled="disabled" size="30" name="othPolLocCity'+newPolLocID+'displayed" id="othPolLocCity'+newPolLocID+'displayed" value="" /><input type="hidden" name="othPolLocCity'+newPolLocID+'" id="othPolLocCity'+newPolLocID+'" value="" onchange="updateCity(\'othPolLocCity'+newPolLocID+'\')"/></div></li>'
        + '<li><label>State</label><div><input type="text" disabled="disabled" size="2" name="othPolLocState'+newPolLocID+'displayed" id="othPolLocState'+newPolLocID+'displayed" value="" /><input type="hidden" name="othPolLocState'+newPolLocID+'" id="othPolLocState'+newPolLocID+'" value="" /></div></li>'
        + '<li><label>Zip</label><div><input type="text" size="5" name="othPolLocZip'+newPolLocID+'" id="othPolLocZip'+newPolLocID+'" value="" onchange="ajaxZipRequest(\'othPolLocZip'+newPolLocID+'\',\'othPolLocCity'+newPolLocID+'\',\'othPolLocState'+newPolLocID+'\',\''+newPolLocID+'\')" /></div></li>'
        + '</ul>';
        HTML = HTML+HTML2;
        var div = document.createElement("li");
        div.innerHTML = HTML;
        div.id = 'polLoc'+newPolLocID;
        var polLocListDiv = document.getElementById('policyLocListDiv');
        var selDiv = document.getElementById('addPolLocDiv');

        if(selDiv)
        {
            $(div).insertBefore(selDiv);
        }
        newID++;
}

function othPolicyLoc(loss,id)
{

    var val = loss.value;
    var div = document.getElementById('othPolicyLoc'+id);
    if(val == 'OT')
    {
        div.style.display = "";
    }
    else
    {
        var claimType1 = false;
        var claimType2 = false;
        var claimType3 = false;
        if(document.getElementById('propLiabRadio1'))
        {claimType1 = document.getElementById('propLiabRadio1').checked;}
        if(document.getElementById('propLiabRadio2'))
        {claimType2 = document.getElementById('propLiabRadio2').checked;}
        if(document.getElementById('propLiabRadio3'))
        {claimType3 = document.getElementById('propLiabRadio3').checked;}
        if(!claimType1 && !claimType2 && !claimType3)
        {
            document.getElementById('polLocList'+id).value = '';
        }
        disableButtons();
//        div.style.display = "none";
        var focusSet = document.getElementById("focusSet");
        focusSet.value = 'set';
        load2('Claims_Details');
    }
}

function deleteOthPolLoc(p)
{
    var pol = document.getElementById('polLoc'+p);
    if(p.length < 3 || p.substr(0,3) != 'new')
    {
        var inp = document.createElement("input");
        inp.type = 'hidden';
        inp.name = 'delPolicyLoc'+p;
        inp.value = 1;
        pol.parentNode.appendChild(inp);
    }

    pol.parentNode.removeChild(pol);
}

function removeLossLoc(p)
{
    var lossLocValue = 'lossLocID'+p.value;
    var trValue = 'lossLocRow'+p.value;
    var lossLoc = document.getElementById(lossLocValue);
    var tr = document.getElementById(trValue);
    lossLoc.name = 'delLossLoc'+p.value;
    tr.style.display = "none";
}

function removePolicyLoc(p)
{
    var policyLocValue = 'policyLocIDCS'+p.value;
//    var trValue = 'policyLocRow'+p.value;
    var divValue = 'policyLocHeading'+p.value;
    var policyLoc = document.getElementById(policyLocValue);
    var tr = document.getElementById(divValue);
    policyLoc.name = 'delPolicyLoc'+p.value;
    tr.style.display = "none";

    document.getElementById('assignHide3'+p.value).style.display="none";
    document.getElementById('propertyStatHeading'+p.value).style.display="none";
}

function removeVarDataLoc(p)
{
    var varDataLocValue = 'varDataLocID'+p.value;
    var trValue = 'varDataLocRow'+p.value;
    var varDataLoc = document.getElementById(varDataLocValue);
    var tr = document.getElementById(trValue);
    varDataLoc.name = 'delVarDataLoc'+p.value;
    tr.style.display = "none";
}

function otherState()
{
   // SHOW/HIDE OTHER LOSS STATE/COUNTRY TEXT BOX BASED ON LOSS STATE SELECTION
   var lossState = document.getElementById('lossState').value;
   var stateID = lossState.substr(2,2);

   if (stateID == 'OT')
   {
      document.getElementById('lossStateOther').style.display="";
   }
   else
   {
      document.getElementById('lossStateOther').style.display="none";
   }
}

function prodInfo(claimType,lob)
{
    var type = claimType.value;

    if(lob == '112' || lob == '113' || lob == '120')
    {
        if(type == 'P' || type == 'B')
        {
            document.getElementById('assignHide3').style.display="";
        }
        else if(type == 'L')
        {
            document.getElementById('assignHide3').style.display="none";
        }
    }
    if(lob == '100' || lob == '575')
    {
        var list = document.getElementById('assignLocIDs').value;
        var polLocArray = new Array();
        polLocArray = list.split('|');
        var polLocArrayLength = polLocArray.length;

        for (i=0;i< polLocArrayLength;i=i+1)
        {
            var field = 'assignHide3'+polLocArray[i];

            if(type == 'P' || type == 'B')
            {
                document.getElementById(field).style.display="";
            }
            else if(type == 'L' && lob == '100')
            {
                document.getElementById(field).style.display="none";
                var assignLoc = document.getElementById('assignmentID'+polLocArray[i]);
                assignLoc.name = 'delAssignLoc'+polLocArray[i];
            }
            else if(type == 'L' && lob == '575')
            {
                document.getElementById(field).style.display="";
            }
        }
    }

    if(type == 'P' || type == 'B')
    {
        document.getElementById('assignHide1').style.display="";
        document.getElementById('assignHide2').style.display="";
    }
    else if(type == 'L')
    {
        document.getElementById('assignHide1').style.display="none";
        document.getElementById('assignHide2').style.display="none";
    }
}

//add another reserve for a new coverage/loss code combo
function addNewDetCov(me, whereFrom, cntr)
{

        var myId = me.id.substr(6);
        var appendToMe = whereFrom || '';

        //the new claimantNo is either the id from the current reserve
        //section where we are adding a new coverage,
        // or the id from the new reserve section where we are adding the
        //first coveage
        var myNewClaimantNo = cntr || myId;

        //find the original reserve information section and copy it
        //to build a new reserve information section
        //Needed to check WCLocUnit and bdLocUnit so WC and bonds can add coverage. 105750
        if (document.getElementById("coveragesList"+myId))
        {
            //increment the hidden variable coverage counter
            var myCount = document.getElementById("covCntr"+myId).value;
            myCount ++;
            document.getElementById("covCntr"+myId).value = myCount;

            //create new table element
            //var newElement = document.createElement('table');
            //creating new DIV element instead of table element.  IE
            //does not accept using inner.HTML to update a table's guts
           var newElement = document.createElement('ul');

                        //retrieve the coverage code list and place in a work field
           var workStr = document.getElementById("ResCovTab"+myId).innerHTML;

           if (appendToMe > '')
           {
               //get the actual element we are going to insertBefore
               //in the case of a new claimant, we insertBefore the Add
               //Claimant button
    //                         var x=document.getElementById(appendToMe);
               var x=document.getElementById("CoverageGroup"+myNewClaimantNo);
           }
           else
           {
              //get the actual element we are going to append to
              //in the case of a new coverage within a claimant, we
              //append to the existing coverges tables
              var x=document.getElementById("CoverageGroup"+myId);

              //add the _ to the new numbers for coverages being added
              //to existing claimants
              myNewClaimantNo = myNewClaimantNo +"_" + myCount;
            }

           var covTabName = new String("ResCovTab"+myNewClaimantNo);
           var myregexp = new RegExp("ResCovTab"+myId, "gim")
           workStr = workStr.replace(myregexp,covTabName);

           var covBoxName = new String("CovSelect"+myNewClaimantNo);
           var myregexp1 = new RegExp("CovSelect"+myId, "gim")
           workStr = workStr.replace(myregexp1,covBoxName);

           var covLossName = new String("LossCodeSelect"+myNewClaimantNo);
           var myregexp2 = new RegExp("LossCodeSelect"+myId, "gim")
           workStr = workStr.replace(myregexp2,covLossName);

           var colName = new String("COLSelect"+myNewClaimantNo);
           var myregexp3 = new RegExp("COLSelect"+myId, "gim")
           workStr = workStr.replace(myregexp3,colName);

           var covListName = new String("coveragesList"+myNewClaimantNo);
           var myregexp5 = new RegExp("coveragesList"+myId, "gim")
           workStr = workStr.replace(myregexp5,covListName);

           var lossListName = new String("lossCodesList"+myNewClaimantNo);
           var myregexp6 = new RegExp("lossCodesList"+myId, "gim");
           workStr = workStr.replace(myregexp6,lossListName);

           var colListName = new String("COLList"+myNewClaimantNo);
           var myregexp7 = new RegExp("COLList"+myId, "gim");
           workStr = workStr.replace(myregexp7,colListName);

           var delListName = new String("delCov"+myNewClaimantNo);
           var myregexp8 = new RegExp("delCov"+myId, "gim");
           workStr = workStr.replace(myregexp8,delListName);

           var resSelectedName = new String("resSelected"+myNewClaimantNo);
           var myregexp9 = new RegExp("resSelected"+myId, "gim");
           workStr = workStr.replace(myregexp9,resSelectedName);

           var assignmentIDName = new String("assignmentID"+myNewClaimantNo);
           var myregexp10 = new RegExp("assignmentID"+myId, "gim");
           workStr = workStr.replace(myregexp10,assignmentIDName);

         //update DIV to get to work in ie
            var tempString = '<table name="'
            + covTabName
            + '" id="'
            + covTabName
            + '" summary="Reserve Covs Loss Types">';

            newElement.id = covTabName;
            newElement.className = 'leftlabel_twocol';
            newElement.innerHTML = workStr;

        //add the new element into the reserve table division.
        //if this is added to new claimant, then we do an
        //insertBefore;
                me.parentNode.insertBefore(newElement,me);


        //set the display style of the delete button so it shows
        document.getElementById(delListName).style.display='';

        var Assignment = document.getElementById(assignmentIDName);
        Assignment.value = '';

        var Coverages = document.getElementById(covListName);
        //set select box Coverage values to the "Select Coverage" first option
        if (Coverages != null)
        {
                for (var count = 0; count < Coverages.options.length; count++)
                {
                    if(Coverages[count].value == 'XX_XX')
                    {
                        Coverages[count].selected = true;
                    } else
                    {
                        Coverages[count].selected = false;
                    }
                }
            }

        var LossCodes = document.getElementById(lossListName);
        //set select box Loss Code values to the "Select Type of Loss" first option
        if (LossCodes != null)
        {
                for (var count = 0; count < LossCodes.options.length; count++)
                {
                    if(LossCodes[count].value == 'XX_XX')
                    {
                        LossCodes[count].selected = true;
                    } else
                    {
                        LossCodes[count].selected = false;
                    }
                }
            }

        //we only have cause-of-loss for certain lines, so this might
        //not have a value in it
        var COLCodes = document.getElementById(colListName);
        //set select box Cause of Loss values to the "Select Type Cause of Loss" first option
        if (COLCodes != null)
        {
                for (var count = 0; count < COLCodes.options.length; count++)
                {
                    if(COLCodes[count].value == 'XX')
                    {
                        COLCodes[count].selected = true;
                    } else
                    {
                        COLCodes[count].selected = false;
                    }
                }
            }

         }
}

function roofReplacedChange(roof,commonStatId)
{
    var val = roof.value;
    var yearReplaced = document.getElementById('yearRoofReplacedLi'+commonStatId);
    var deprecAmt = document.getElementById('depreciationAmtLi'+commonStatId);

    if(val == 'Y')
    {
        yearReplaced.style.display = "";
        deprecAmt.style.display = "";
    }
    else
    {
        yearReplaced.style.display = "none";
        deprecAmt.style.display = "none";
    }
}
