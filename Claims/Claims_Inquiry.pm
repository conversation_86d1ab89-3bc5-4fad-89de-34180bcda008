#!/usr/local/bin/perl
package Claims_Inquiry;

require 5.000;
use strict;
use vars qw($VERSION @ISA @EXPORT_OK);

use Exporter;

@ISA = qw(Exporter);
@EXPORT_OK = qw(loadScreen);

$VERSION = '0.01';

use Claims_Constants qw(getIMTLineCodeScreen getStateAbbrevNumeric LOBDescriptions getIMTPolicyPrefix getWadenaPolicyPrefix);
use Claims_Misc qw(validateClaimID getHeader getFooter getPrintLossNoticeButton);
use Claims_Error qw(error);
use IMT::CommaFormatted qw(CommaFormatted);
use IMT::stringParsers qw(parseTimeString parseDateString parsePhoneString);
use IMT::Access_Constants qw(getLineCodesByAccess);
use PolicyInterface qw(PolicyInterface);
use Claims_CLDCLMNO qw(CLDCLMNO);
use Claims_FileManager qw(defaultFolders);
use IMT::Files qw(createFileGroup insertFile insertFolder getFileGroup setPermissions renameFile moveFile returnFileTree returnFileTreeModify deleteFile deleteFolder :all);
use Claims_GraceCancel qw(Claims_GraceCancel);

use Common::Platform::platformCommon qw(get_policy_info_page_url);
use Client::ClientMisc qw(check_for_platform_version);

use Common::Platform::Users::usersCommon qw(fetch_acct_preferences);
use Common::Platform::Users::User_Obj;

my %GLLineCodes = (
'810'=>'Comp Gen Liab',
'814'=>'Owners &amp; Contractors Prot',
'816'=>'Products &amp; Completed Ops');

sub loadScreen
{
    my $ENGINE = shift;

    my $sessID = $ENGINE->{'SESSION'}->{'sessionID'};
    my $action = $ENGINE->{'ACTION'};

    my $name = $ENGINE->{'AUTH'}->{'name'};

    my $user_type = $ENGINE->{'AUTH'}->{'IMTOnline_UserType'};

    if(defined($ENGINE->{'CGI'}->param('claim_number')))
    {
        $ENGINE->{'SESSION'}->{'searchURL'} = $ENGINE->{'CGI'}->url(-query=>1);
    }
    my $thank_you_msg = '';
#    die Data::Dumper::Dumper ($ENGINE);
    if($ENGINE->{'CGI'}->{'transactionCode'}[0] eq 'submit' || $ENGINE->{'CGI'}->{'param'}{'transactionCode'}[0] eq 'submit')
    {
        my $loss_notice = Claims_Misc::getPrintLossNoticeButton($ENGINE);
        my $claim_number = '<a href="'.$action.'?claimid='.$ENGINE->{'claimGeneral'}->{'CLAIM_ID'}.'&amp;load=Claims_Details&amp;sessionID='.$sessID.'">'.$ENGINE->{'claimGeneral'}->{'IMT_CLAIM_NO'}.'</a>';
        my $policy_number = $ENGINE->{'claimGeneral'}->{'POLICY_NUMBER'};
        if($ENGINE->{'claimGeneral'}->{'POLICY_NUMBER'} =~ /^\d\d/)
        {$policy_number = substr($ENGINE->{'claimGeneral'}->{'POLICY_NUMBER'},2);}
        $thank_you_msg = <<HTML;
<div class="modal" id="thankYouModal" tabindex="-1" role="dialog" aria-labelledby="thankYouModal" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-body">
                <p>Thank You!  A claim for $policy_number has been submitted.<br/>
                Please refer to Claim Number <b>$claim_number.</b></p>
            </div>
            <div class="modal-footer">
                $loss_notice
                <a href="Claims_Engine.pl" onclick="disableButtons()" class="btn btn-primary"  id="closeButton"><span aria-hidden="true">Close</span></a>
                <!--<button type="button" class="btn btn-primary" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">Close</span></button>-->
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    \$(window).load(function(){
        \$('#thankYouModal').modal('show');
        \$('#thankYouModal').modal({
                               backdrop: 'static',
                               keyboard: false
                             })
        \$('.modal-dialog').css("margin-top", Math.max(0, (\$(window).height() - \$('.modal-dialog').height()) / 2));
    });
</script>
HTML
#        die Data::Dumper::Dumper($ENGINE);
    }

    my $glassClaimCorrection = '';
    my $glassClaimCorrectionChecked = '';
    if(defined($ENGINE->{'CGI'}->param('nc_glass_claim_err')) && $ENGINE->{'CGI'}->param('nc_glass_claim_err') eq 'E')
      { $glassClaimCorrectionChecked = ' checked="checked"'; }
#    if($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'Internal')
        if(defined $ENGINE->{'AUTH'}->{'Claims_Glass'}
                && $ENGINE->{'AUTH'}->{'Claims_Glass'} eq 'A')
    {
        $glassClaimCorrection = '<label>Glass Claim Correction: </label><div><input type="checkbox" '.$glassClaimCorrectionChecked.' value="E" name="nc_glass_claim_err" /></div>';
    }

    my %farmStates = getStateAbbrevNumeric();
    my $farmStateOption = '<select name="farmStateList"><option value=""></option>';
    for my $key (keys %farmStates)
    {
#       my $selected = '';
#           if ($policyState eq $farmStates{$key})
#           {
#               $selected = ' selected="selected"';
#           }

       if($key =~/IA|IL|MN|MO|ND|NE|SD|WI/)
       { next; }

       $farmStateOption .= '<option value="'.$key.'">'.$farmStates{$key}.'</option>';
    }

    #Build line of business select box for GL policies.
    my $GLLineOption = '<select name="GLLineList"><option value=""></option>';
    for my $key (sort keys %GLLineCodes)
    {
       my $selected = '';
       $GLLineOption .= '<option value="'.$key.'"'.$selected.'>'.$key.' '.$GLLineCodes{$key}.'</option>';
    }

    my $output = '';
#    $ENGINE->{'head'} = <<EOF;
#<script type="text/javascript">
#function L(req)
#{ self.location.href = "$action?claimnum="+req+"&amp;load=Claims_Info&amp;sessionID=$sessID"; }
#function O(req)
#{ window.open("$action?claimnum="+req+"&amp;load=Claims_Info&amp;sessionID=$sessID"); }
#</script>
#EOF
    my $glass_claim_wording = '<label>Glass Claim: </label><div><input type="checkbox" value="N" name="nc_glass_claim" /></div>';
    if($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'Agent')
    {$glass_claim_wording = '';}

$ENGINE->{'head'} = <<EOF;
<script type="text/javascript" src="jquery-ui.js"></script>
<link href="jquery-ui.css" rel="stylesheet"/>
<script type="text/javascript" src="Claims.js"></script>
<link href="footable.core.css" rel="stylesheet" type="text/css" />
<script src="footable.js" type="text/javascript"></script>
<script src="footable.sort.js" type="text/javascript"></script>
<!--<script type="text/javascript" src="popupCalendar.js"></script><script type="text/javascript" src="Claims.js"></script>-->
<script type="text/javascript">
//<![CDATA[
// Reset the form in which the reset button resides.
function clearNode(node)
{
    if(node.nodeName == 'INPUT') {
        if(node.type=='text') {
            node.value='';
        }
        else if(node.type=='checkbox') {
            node.checked=0;
        }
    }
    else if(node.nodeName == 'SELECT') {
        node.selectedIndex=0;
    }
    else {
        for(var x=0;node.childNodes[x];x++) {
            clearNode(node.childNodes[x]);
        }
    }
}
function saveClaim()
{
    disableButtons();
    document.getElementById('claimExists').value = 'Y';
    document.getElementById('newClaimForm').submit();
}
function continueClaimNo()
{
    document.getElementById('claimExists').value = 'X';
    document.getElementById('submitGrace').value = 'X';
    document.getElementById('newClaimForm').submit();
}
function graceCancelYes()
{
    document.getElementById('submitGrace').value = 'Y';
    document.getElementById('newClaimForm').submit();
}
function glassClaims(el)
{
    var user_type = '$user_type';
    var pre = el.value.substr(0,2).toUpperCase();
    if((pre == 'AP' || pre == 'CV' || el.value.substr(0,3).toUpperCase() == 'WAP') && user_type != 'Agent')
    {
        if(\$("#glassClaim").hasClass('hidden'))
        {\$("#glassClaim").toggleClass('hidden');
        document.getElementById('glassClaim').innerHTML = '$glass_claim_wording';}
        if(\$("#glassClaimCorrection").hasClass('hidden'))
        {\$("#glassClaimCorrection").toggleClass('hidden');
        document.getElementById('glassClaimCorrection').innerHTML = '$glassClaimCorrection';}
    }
    else
    {
        if(!\$("#glassClaim").hasClass('hidden'))
        {
                \$("#glassClaim").toggleClass('hidden');
                document.getElementById('glassClaim').innerHTML = '';
        }
        if(!\$("#glassClaimCorrection").hasClass('hidden'))
        {
                \$("#glassClaimCorrection").toggleClass('hidden');
                document.getElementById('glassClaimCorrection').innerHTML = '';
        }
    }

    if(pre == 'GL')
    {
        if(\$("#GLLine").hasClass('hidden'))
        {\$("#GLLine").toggleClass('hidden');
        document.getElementById('GLLine').innerHTML = '<label>Line of Business:</label><div>$GLLineOption</select></div>';}
    }
    else
    {
            if(!\$("#GLLine").hasClass('hidden'))
        {
                \$("#GLLine").toggleClass('hidden');
                document.getElementById('GLLine').innerHTML = '';
        }
    }

    if(!isNaN(el.value.substr(0,2)) && el.value > '' && user_type == 'Internal')
    {
        if(\$("#farmState").hasClass('hidden'))
        {\$("#farmState").toggleClass('hidden');
        document.getElementById('farmState').innerHTML = '<label>State:</label><div>$farmStateOption</select></div>';}
    }
    else
    {
            if(!\$("#farmState").hasClass('hidden'))
        {
                \$("#farmState").toggleClass('hidden');
                document.getElementById('farmState').innerHTML = '';
            }
    }
}
//]]>
</script>
<!--[if lte IE 7]>
    <style type="text/css">
    .searchTableDiv table {
    width:99%;
    }
    </style>
<![endif]-->
EOF

    $output .= getHeader($ENGINE);
    $output .= "<!--/header--></div>";
    my $claim_number = uc($ENGINE->{'CGI'}->param('claim_number')) || '';
    my $pol_number = uc($ENGINE->{'CGI'}->param('pol_number')) || '';
    my $insd_fname = ($ENGINE->{'CGI'}->param('insd_fname')) || '';
    my $insd_lname = ($ENGINE->{'CGI'}->param('insd_lname')) || '';
    my $agency = $ENGINE->{'CGI'}->param('agency') || '';
    my $claim_fname = ($ENGINE->{'CGI'}->param('claim_fname')) || '';
    my $claim_lname = ($ENGINE->{'CGI'}->param('claim_lname')) || '';
    my $claim_phone = $ENGINE->{'CGI'}->param('claim_phone') || '';
    my $claim_partial = '';
    my $yesChecked = '';
    my $noChecked = '';

    if(defined($ENGINE->{'CGI'}->param('claim_partial')) && $ENGINE->{'CGI'}->param('claim_partial') eq 'on')
      { $claim_partial = ' checked="checked"';
        $yesChecked = 'checked'; }
    elsif (defined($ENGINE->{'CGI'}->param('claim_partial')) && $ENGINE->{'CGI'}->param('claim_partial') eq 'off')
      { $noChecked = 'checked'; }
    elsif ($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} ne 'Internal')
    {
          my $api_response_hash_ref = fetch_acct_preferences({
			   auth_token => $ENGINE->{'AUTH'}->{'platform_access_token'},
			   platform_id => $ENGINE->{'AUTH'}->{'platform_key'}
		   });
		   my $preferences = $api_response_hash_ref->{data};

		   if ($preferences && $preferences->[0]->{'SEARCH_PARTIAL_MATCH'} eq "Y")
	       {
	           $claim_partial = ' checked="checked"';
	           $yesChecked = 'checked';
	       }
	       else
	       {
	           $noChecked = 'checked';
	       }
    }
    else
    {
	    $noChecked = 'checked';
	}

    my @where = ();
    my @whereA = ();
    my @warnings = ();

    if(defined($ENGINE->{'SystemLock'}) && $ENGINE->{'SystemLock'} eq 'Y')
    {
        push(@warnings,'The Claims system is locked.  Please try again later.');
    }

    my $comp = '=';
    my $fuzzy = '';
    if($claim_partial =~ /\w/)
      { $comp = 'LIKE'; $fuzzy = '%'; }

    my $claim_number_error = '';
    $claim_number =~ s/^\s*//g;
    $claim_number =~ s/\s*$//g;
    $claim_number =~ s/"/&quot;/g;
    if($claim_number ne '')
    {
        if($claim_number =~ /[^0-9a-zA-Z]/)
         { $claim_number_error = '<div class="error"></div>'; push(@warnings,'Claim numbers must only contain characters 0-9, a-z and/or A-Z.'); }
        elsif(length($claim_number) > 9)
         { $claim_number_error = '<div class="error"></div>'; push(@warnings,'Claim numbers must be shorter than 10 characters in length.'); }
        elsif(length($claim_number) < 5)
         { $claim_number_error = '<div class="error"></div>'; push(@warnings,'Claim numbers must be longer than 4 characters in length.'); }
        else
         { push(@where,"IMT_CLAIM_NO $comp '$claim_number$fuzzy'"); push(@whereA,"IMT_CLAIM_NO $comp '$claim_number$fuzzy'");}

        if($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'Internal' &&
           $ENGINE->{'AUTH'}->{'Claims_Access'} ne 'A' &&
           (!defined($ENGINE->{'AUTH'}->{'Claims_Unsub'}) ||
             (defined($ENGINE->{'AUTH'}->{'Claims_Unsub'}) && $ENGINE->{'AUTH'}->{'Claims_Unsub'} ne 'I')))
        {
            my $searchQuery = $ENGINE->{'DBH'}->prepare('
                SELECT
                        G.CLAIM_STATUS
                FROM
                        CLAIMDB.CLM_GENERAL AS G
                WHERE
                        G.IMT_CLAIM_NO = ?
                        AND G.DATE_DELETED = \'9999-01-01 01:00:00.000000\'') || error($ENGINE,'Search query prepare failed: '.$ENGINE->{'DBH'}->errstr);
            $searchQuery->execute($claim_number) || error($ENGINE,'Search query execute failed: '.$ENGINE->{'DBH'}->errstr);
            my $results = $searchQuery->fetchall_arrayref({});
            if(defined(@$results[0]) && @$results[0]->{'CLAIM_STATUS'} eq 'P')
            {push(@warnings,'User does not have access to claim '.$claim_number.' while claim is pending.'); }
        }

    }

    my $pol_number_error = '';

    substr($pol_number,2) =~ s/O/0/gi;
    $pol_number =~ s/^\s*//g;
    $pol_number =~ s/\s*$//g;
    $pol_number =~ s/"/&quot;/g;
   # $pol_number =~ s/o/0/gi;
#sjs 106900 change policy number edit to be longer than 6, not longer than 4 10/18/2012
    if($pol_number ne '')
    {
        if($pol_number =~ /[^0-9a-zA-Z]/)
         { $pol_number_error = '<div class="error"></div>'; push(@warnings,'Policy numbers must only contain characters 0-9, a-z and/or A-Z.'); }
        elsif(length($pol_number) > 7)
         { $pol_number_error = '<div class="error"></div>'; push(@warnings,'Policy numbers must be shorter than 8 characters in length.'); }
        elsif(length($pol_number) < 7)
         { $pol_number_error = '<div class="error"></div>'; push(@warnings,'Policy numbers must be longer than 6 characters in length.'); }
        elsif($pol_number =~ /^\d\d/ && $claim_partial =~ /\w/)
         { push(@where,"POLICY_NUMBER LIKE '__$pol_number\%'"); push(@whereA,"POLICY_NUMBER LIKE '__$pol_number\%'");}
        elsif($pol_number =~ /^\d\d/)
         { push(@where,"POLICY_NUMBER IN ('12$pol_number','14$pol_number','26$pol_number','40$pol_number','48$pol_number','$pol_number')"); push(@whereA,"POLICY_NUMBER IN ('12$pol_number','14$pol_number','26$pol_number','40$pol_number','48$pol_number','$pol_number')");}
        else
         { push(@where,"POLICY_NUMBER $comp '$pol_number$fuzzy'"); push(@whereA,"POLICY_NUMBER $comp '$pol_number$fuzzy'");}
    }

    my $agency_error = '';
    $agency =~ s/^\s*//g;
    $agency =~ s/\s*$//g;
    $agency =~ s/"/&quot;/g;
    my $agencyPIWhere = '';
    my $agencyPCWhere = '';
    my $POLK_AGENCY = '674008';
    my $polk_count = 0;
    my $polkWarning = '';
    if($agency ne '' && $ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'Internal')
    {

        if($agency =~ /[^0-9a-zA-Z]/)
         { $agency_error = '<div class="error"></div>'; push(@warnings,'Agency must only contain characters 0-9, a-z and/or A-Z.'); }
        elsif(length($agency) > 6)
         { $agency_error = '<div class="error"></div>'; push(@warnings,'Agency must be shorter than 7 characters in length.'); }
        elsif($agency eq $POLK_AGENCY)
         {
#/# Check for Polk Agency and authority
            my $polk_agent = 0;
            if($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'Agent')
            {
              if(grep{$_ eq '674008'}keys %{$ENGINE->{'AUTH'}->{'AGENCY_ACCESS'}}){
                $polk_agent = 1;
              }
            }
             if($ENGINE->{'AUTH'}->{'PolkAgency_Access'} ne '1' && $polk_agent != 1){
                 $agency_error = '<div class="error"></div>'; push(@warnings,'You do not have authority to view records for this agency.');
             }else{
                push(@where,"G.AGENCY_NO = '$agency'"); $agencyPIWhere = " AND PI.AGENCY_NO = '$agency'"; $agencyPCWhere = " AND PC.AGENCY_NO = '$agency'"; push(@whereA,"G.AGENCY_NO = '$agency'");
              }
         }
         else{
           push(@where,"G.AGENCY_NO = '$agency'"); $agencyPIWhere = " AND PI.AGENCY_NO = '$agency'"; $agencyPCWhere = " AND PC.AGENCY_NO = '$agency'"; push(@whereA,"G.AGENCY_NO = '$agency'");
         }
    }

    my $agencySearch = '';
    my $noPolicyAccess = 0;
    if($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'Internal')
      { $agencySearch = '<li><label>Agency:</label><div>'.$agency_error.'<input name="agency" id="agency" maxlength="6" value="'.$agency.'" /></div></li>'; }
    elsif($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} ne 'LawFirm')
    {
        my $sql .= ' (';
        my @agencies = ();
        my $accessLineCodes = getLineCodesByAccess();
        foreach my $key (keys %{$ENGINE->{'AUTH'}->{'AGENCY_ACCESS'}})
        {
            $sql.='\''.$key.'\',';
            my @agency_access = ();

            for my $access (keys %{$accessLineCodes})
            {
                if($ENGINE->{'AUTH'}->{'AGENCY_ACCESS'}->{$key}->{$access})
                  { push(@agency_access,@{$accessLineCodes->{$access}}); }
            }

            if(scalar(@agency_access) > 0)
              { push(@agencies, '(G.AGENCY_NO = \''.$key.'\' AND G.IMT_LINE_CODE IN (\''.join('\',\'',@agency_access).'\'))'); }
        }
        chop($sql);
        $sql .= ')';

        if(scalar(@agencies) > 0)
          { push(@where,'('.join(' OR ',@agencies).')'); push(@whereA,'('.join(' OR ',@agencies).')');}
        ## VERY IMPORTANT!!!!!!!!
        # The else below prevents a user with no policy line access from being able to query without agency restrictions
        ## VERY IMPORTANT!!!!!!!!
        else
        {
            push(@where,'1=0');
            push(@whereA,'1=0');
            $noPolicyAccess = 1;
        }

        $agencyPIWhere = " AND PI.AGENCY_NO IN ".$sql;
        $agencyPCWhere = " AND PC.AGENCY_NO IN ".$sql;
        #push(@where,"G.AGENCY_NO IN ".$sql); $agencyPIWhere = " AND PI.AGENCY_NO IN ".$sql; $agencyPCWhere = " AND PC.AGENCY_NO IN ".$sql;
    }
    my @searchQueryArgs = ();

    $insd_fname =~ s/^\s*//g;
    $insd_fname =~ s/\s*$//g;
    $insd_fname =~ s/ +[a-zA-Z]$//;
    $insd_fname =~ s/"/&quot;/g;
    $insd_lname =~ s/^\s*//g;
    $insd_lname =~ s/\s*$//g;
    $insd_lname =~ s/^#//g;
    $insd_lname =~ s/ sr$| jr$| sr.$| jr.$//gi;
    $insd_lname =~ s/"/&quot;/g;
    $claim_fname =~ s/^\s*//g;
    $claim_fname =~ s/\s*$//g;
    $claim_fname =~ s/ +[a-zA-Z]$//;
    $claim_fname =~ s/"/&quot;/g;
    $claim_lname =~ s/^\s*//g;
    $claim_lname =~ s/\s*$//g;
    $claim_lname =~ s/^#//g;
    $claim_lname =~ s/ sr$| jr$| sr.$| jr.$//gi;
    $claim_lname =~ s/"/&quot;/g;

    my $insd_fname_alpha = uc($insd_fname);
    $insd_fname_alpha =~ s/[^A-Z]//g;
    my $insd_lname_alpha = uc($insd_lname);
    $insd_lname_alpha =~ s/[^A-Z]//g;
    my $claim_fname_alpha = uc($claim_fname);
    $claim_fname_alpha =~ s/[^A-Z]//g;
    my $claim_lname_alpha = uc($claim_lname);
    $claim_lname_alpha =~ s/[^A-Z]//g;

    $insd_lname_alpha =~ s/^DBA//;
    $claim_lname_alpha =~ s/^DBA//;

    my $queryJoins = '';
    my $queryJoinsA = '';

    #put the lawfirm joins into the query if this is a lawfirm
    if ($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'LawFirm')
    {
                $queryJoins .= ' INNER JOIN CLAIMDB.CLM_SPECIAL_ACCESS AS ACCESS ON ACCESS.CLAIM_ID = G.CLAIM_ID';
                $queryJoins .= ' INNER JOIN CLAIMDB.CLM_LAW_FIRMS AS LAW ON LAW.CLM_LAW_FIRMS_ID = ACCESS.CLM_LAW_FIRMS_ID ';
        push(@where,'ACCESS.ACCESS_REVOKED = \'9999-01-01 01:00:00.000000\' AND ACCESS_GRANTED <> \'9999-01-01 01:00:00.000000\'');
                push (@where, 'LAW.CLM_LAW_FIRMS_ID = '.$ENGINE->{'AUTH'}->{'law_firm_id'});
                push(@whereA,'ACCESS.ACCESS_REVOKED = \'9999-01-01 01:00:00.000000\' AND ACCESS_GRANTED <> \'9999-01-01 01:00:00.000000\'');
                push (@whereA, 'LAW.CLM_LAW_FIRMS_ID = '.$ENGINE->{'AUTH'}->{'law_firm_id'});

    }
    my $insd_lname_error = '';
    my $claim_lname_error = '';
    if($insd_lname =~ /\w/ && $claim_lname =~ /\w/)
      { $insd_lname_error = '<div class="error"></div>'; $claim_lname_error = '<div class="error"></div>'; push(@warnings,'Must not specify both insured and claimant name.'); }

    my $insd_fname_error = '';
    if($insd_fname =~ /\w/ &&
       !($insd_lname =~ /\w/))
      { $insd_fname_error = '<div class="error"></div>'; push(@warnings,'Insured last name is required when searching by first name.'); }
    elsif($insd_lname =~ /\w/ && length($insd_lname) < 2)
         { $insd_lname_error = '<div class="error"></div>'; push(@warnings,'Last name must be longer than 1 character in length.'); }
    elsif($insd_fname =~ /\w/ ||
       $insd_lname =~ /\w/)
    {
        $queryJoins .= ' INNER JOIN CLAIMDB.CLM_PARTIES AS PI ON PI.CLAIM_ID = G.CLAIM_ID AND PI.DATE_DELETED = \'9999-01-01 01:00:00.000000\' INNER JOIN CLAIMDB.CLM_PARTY_ROLES AS PRI ON PI.PARTY_ID = PRI.PARTY_ID AND PRI.DATE_DELETED = \'9999-01-01 01:00:00.000000\'';
        if($insd_fname =~ /\w/ &&
           $insd_lname =~ /\w/)
        {
#            $insuredQuery = 'P.FIRST_NAME '.$comp.' ? AND P.LAST_NAME '.$comp.' ? AND PR.ROLE = \'IN\'';
            push(@where,'PI.FIRST_SEARCH LIKE ? AND (PI.LAST_SEARCH '.$comp.' ? OR PI.BUSINESS_SEARCH '.$comp.' ?) AND PRI.ROLE = \'IN\''.$agencyPIWhere);
            push(@whereA,'PI.FIRST_SEARCH LIKE ? AND (PI.LAST_SEARCH '.$comp.' ? OR PI.BUSINESS_SEARCH '.$comp.' ?) AND PRI.ROLE = \'IN\''.$agencyPIWhere);
            push(@searchQueryArgs,(substr($insd_fname_alpha,0,30).'%',substr($insd_lname_alpha,0,30).$fuzzy,substr($insd_lname_alpha,0,90).$fuzzy));
        }
#        elsif($insd_fname =~ /\w/)
#        {
##            $insuredQuery = 'P.FIRST_NAME '.$comp.' ? AND PR.ROLE = \'IN\'';
#            push(@where,'PI.FIRST_NAME LIKE ? AND PRI.ROLE = \'IN\''.$agencyPIWhere);
#            push(@searchQueryArgs,$insd_fname.'%');
#        }
        elsif($insd_lname =~ /\w/)
        {
#            $insuredQuery = 'P.LAST_NAME '.$comp.' ? AND PR.ROLE = \'IN\'';
            # The FIRST_SEARCH LIKE clause is used to trick DB2 into the correct execution path.
            push(@where,'((PI.LAST_SEARCH '.$comp.' ? AND (PI.FIRST_SEARCH LIKE ? OR PI.FIRST_SEARCH IS NULL)) OR PI.BUSINESS_SEARCH '.$comp.' ?) AND PRI.ROLE = \'IN\''.$agencyPIWhere);
            push(@whereA,'((PI.LAST_SEARCH '.$comp.' ? AND (PI.FIRST_SEARCH LIKE ? OR PI.FIRST_SEARCH IS NULL)) OR PI.BUSINESS_SEARCH '.$comp.' ?) AND PRI.ROLE = \'IN\''.$agencyPIWhere);
            push(@searchQueryArgs,substr($insd_lname_alpha,0,30).$fuzzy,'%',substr($insd_lname_alpha,0,90).$fuzzy);
        }

#        my $subquery = 'SELECT CLAIM_ID FROM CLAIMDB.CLM_PARTIES AS P INNER JOIN CLAIMDB.CLM_PARTY_ROLES AS PR ON P.PARTY_ID = PR.PARTY_ID WHERE '.$insd;
#        push(@where,"CLAIM_ID IN ($subquery)");
    }
    my $claim_fname_error = '';
    if($claim_fname =~ /\w/ &&
       !($claim_lname =~ /\w/))
      { $claim_fname_error = '<div class="error"></div>'; push(@warnings,'Claimant last name is required when searching by first name.'); }
    elsif($claim_lname =~ /\w/ && length($claim_lname) < 2)
         { $claim_lname_error = '<div class="error"></div>'; push(@warnings,'Last name must be longer than 1 character in length.'); }
    elsif($claim_fname =~ /\w/ ||
       $claim_lname =~ /\w/)
    {
        $queryJoins .= ' INNER JOIN CLAIMDB.CLM_PARTIES AS PC ON PC.CLAIM_ID = G.CLAIM_ID AND PC.DATE_DELETED = \'9999-01-01 01:00:00.000000\' INNER JOIN CLAIMDB.CLM_PARTY_ROLES AS PRC ON PC.PARTY_ID = PRC.PARTY_ID AND PRC.DATE_DELETED = \'9999-01-01 01:00:00.000000\'';
        if($claim_fname =~ /\w/ &&
           $claim_lname =~ /\w/)
        {
#            $claimant = 'P.FIRST_NAME '.$comp.' ? AND P.LAST_NAME '.$comp.' ? AND PR.ROLE = \'CL\'';
#            push(@where,'PC.FIRST_SEARCH LIKE ? AND (PC.LAST_SEARCH '.$comp.' ? OR PC.BUSINESS_SEARCH '.$comp.' ?) AND PRC.ROLE = \'IP\''.$agencyPCWhere);
#            push(@whereA,'PC.FIRST_SEARCH LIKE ? AND (PC.LAST_SEARCH '.$comp.' ? OR PC.BUSINESS_SEARCH '.$comp.' ?) AND PRC.ROLE = \'IP\''.$agencyPCWhere);
            push(@where,'PC.FIRST_SEARCH LIKE ? AND (PC.LAST_SEARCH '.$comp.' ? OR PC.BUSINESS_SEARCH '.$comp.' ?) AND PRC.ROLE IN(\'IP\',\'CL\')'.$agencyPCWhere);
            push(@whereA,'PC.FIRST_SEARCH LIKE ? AND (PC.LAST_SEARCH '.$comp.' ? OR PC.BUSINESS_SEARCH '.$comp.' ?) AND PRC.ROLE IN(\'IP\',\'CL\')'.$agencyPCWhere);

            push(@searchQueryArgs,(substr($claim_fname_alpha,0,30).'%',substr($claim_lname_alpha,0,30).$fuzzy,substr($claim_lname_alpha,0,90).$fuzzy));
        }
#        elsif($claim_fname =~ /\w/)
#        {
##            $claimant = 'P.FIRST_NAME '.$comp.' ? AND PR.ROLE = \'CL\'';
#            push(@where,'PC.FIRST_NAME LIKE ? AND PRC.ROLE = \'CL\''.$agencyPCWhere);
#            push(@searchQueryArgs,$claim_fname.'%');
#        }
        elsif($claim_lname =~ /\w/)
        {
#            $claimant = 'P.LAST_NAME '.$comp.' ? AND PR.ROLE = \'CL\'';
            # The FIRST_SEARCH LIKE clause is used to trick DB2 into the correct execution path.
#            push(@where,'((PC.LAST_SEARCH '.$comp.' ? AND (PC.FIRST_SEARCH LIKE ? OR PC.FIRST_SEARCH IS NULL)) OR PC.BUSINESS_SEARCH '.$comp.' ?) AND PRC.ROLE = \'IP\''.$agencyPCWhere);
#            push(@whereA,'((PC.LAST_SEARCH '.$comp.' ? AND (PC.FIRST_SEARCH LIKE ? OR PC.FIRST_SEARCH IS NULL)) OR PC.BUSINESS_SEARCH '.$comp.' ?) AND PRC.ROLE = \'IP\''.$agencyPCWhere);
            push(@where,'((PC.LAST_SEARCH '.$comp.' ? AND (PC.FIRST_SEARCH LIKE ? OR PC.FIRST_SEARCH IS NULL)) OR PC.BUSINESS_SEARCH '.$comp.' ?) AND PRC.ROLE IN(\'IP\',\'CL\')'.$agencyPCWhere);
            push(@whereA,'((PC.LAST_SEARCH '.$comp.' ? AND (PC.FIRST_SEARCH LIKE ? OR PC.FIRST_SEARCH IS NULL)) OR PC.BUSINESS_SEARCH '.$comp.' ?) AND PRC.ROLE IN(\'IP\',\'CL\')'.$agencyPCWhere);
            push(@searchQueryArgs,substr($claim_lname_alpha,0,30).$fuzzy,'%',substr($claim_lname_alpha,0,90).$fuzzy);
        }
#        my $subquery = 'SELECT CLAIM_ID FROM CLAIMDB.CLM_PARTIES AS P INNER JOIN CLAIMDB.CLM_PARTY_ROLES AS PR ON P.PARTY_ID = PR.PARTY_ID WHERE '.$claimant;
#        push(@where,"CLAIM_ID IN ($subquery)");
    }


    my $advSearch = 0;
    my $advSearchDisplay = 'none;';
    my $normAdv = 'Advanced';
    my $search_class = 'half_page';
    my $ul_search_class = 'first_search';
    if($ENGINE->{'CGI'}->param('advSearch'))
      { $advSearch = 1; $advSearchDisplay = 'block'; $normAdv = 'Hide Advanced'; $search_class = 'three_quarter_page'; $ul_search_class = 'first_adv_search'; }

    my $paymentsMadeChecked = '';
    my $largeReserveChangesChecked = '';
    my $fromPayDate = '';
    my $toPayDate = '';
    my $payDateDisplay = 'display:none;';
    my $payDateError = '';
    # payments made
    if(defined($ENGINE->{'CGI'}->param('payments_made')) && $ENGINE->{'CGI'}->param('payments_made') =~ /\w/)
    {
        $fromPayDate = $ENGINE->{'CGI'}->param('fromPayDate') || '';
        $toPayDate = $ENGINE->{'CGI'}->param('toPayDate') || '';
        $fromPayDate =~ s/^\s*//g;
        $fromPayDate =~ s/\s*$//g;
        $fromPayDate =~ s/"/&quot;/g;
        $toPayDate =~ s/^\s*//g;
        $toPayDate =~ s/\s*$//g;
        $toPayDate =~ s/"/&quot;/g;

        $payDateDisplay = '';
        $paymentsMadeChecked = ' checked="checked"';

        if($fromPayDate eq '' && $toPayDate eq '')
        {
            push(@warnings,'Payment dates are required');
            $payDateError = '<div class="error"></div>';
        }
        else
        {
            my $dates = '';
            my ($day, $month, $year) = parseDateString($fromPayDate);

            if(defined($day))
            {
                $fromPayDate = $month.'/'.$day.'/'.$year;
                my $exclude = ' AND PCASH.TRANSACTION_DATE <> \'9999-01-01-01.00.00\'';
                if($fromPayDate eq '01/01/9999')
                  { $exclude = ''; }
                $dates .= ' PCASH.TRANSACTION_DATE >= \''.$year.'-'.$month.'-'.$day.'-00.00.00\''.$exclude;
            }
            else
            {
                push(@warnings,'Invalid "from" date used in Payment Date');
                $payDateError = '<div class="error"></div>';
            }

            ($day, $month, $year) = parseDateString($toPayDate);

            if(defined($day))
            {
                $toPayDate = $month.'/'.$day.'/'.$year;
                $dates .= ' AND PCASH.TRANSACTION_DATE <= \''.$year.'-'.$month.'-'.$day.'-23.59.59'.'\'';
            }
            else
            {
                push(@warnings,'Invalid "to" date used in Payment Date');
                $payDateError = '<div class="error"></div>';
            }

            push(@where,'G.CLAIM_ID IN (SELECT PCASH.CLAIM_ID FROM CLAIMDB.CLM_CASH AS PCASH WHERE G.CLAIM_ID = PCASH.CLAIM_ID AND PCASH.PAYMENT_AMT > 0 AND PCASH.DATE_DELETED = \'9999-01-01 01:00:00.000000\' AND'.$dates.')');
              #push(@where,'EXISTS (SELECT * FROM CLAIMDB.CLM_CASH WHERE G.CLAIM_ID = CLAIM_ID AND PAYMENT_AMT > 0 AND DATE_DELETED = \'9999-01-01 01:00:00.000000\' AND'.$dates.')');
            #push(@where,'CLAIM_ID IN (SELECT DISTINCT(CLAIM_ID) FROM CLAIMDB.CLM_CASH WHERE PAYMENT_AMT > 0 FETCH FIRST 101 ROWS ONLY)');
            #$queryJoins .= ' INNER JOIN CLAIMDB.CLM_CASH AS C ON G.CLAIM_ID = C.CLAIM_ID AND PAYMENT_AMT > 0';
            #push(@where,'CLAIM_STATUS IN (\'A\',\'FT\')');
        }
    }

    my $fromResDate = '';
    my $toResDate = '';
    my $resDateDisplay = 'display:none;';
    my $resDateError = '';
    # large reserve changes???
    if(defined($ENGINE->{'CGI'}->param('large_reserve_changes')) && $ENGINE->{'CGI'}->param('large_reserve_changes') =~ /\w/)
    {
        $fromResDate = $ENGINE->{'CGI'}->param('fromResDate') || '';
        $toResDate = $ENGINE->{'CGI'}->param('toResDate') || '';
        $fromResDate =~ s/^\s*//g;
        $fromResDate =~ s/\s*$//g;
        $fromResDate =~ s/"/&quot;/g;
        $toResDate =~ s/^\s*//g;
        $toResDate =~ s/\s*$//g;
        $toResDate =~ s/"/&quot;/g;

        $resDateDisplay = '';
        $largeReserveChangesChecked = ' checked="checked"';

        if($fromResDate eq '' && $toResDate eq '')
        {
            push(@warnings,'Reserve dates are required');
            $resDateError = '<div class="error"></div>';
        }
        else
        {
            my $dates = '';
            my ($day, $month, $year) = parseDateString($fromResDate);
            if(defined($day))
            {
                $fromResDate = $month.'/'.$day.'/'.$year;
                my $exclude = ' AND TRANSACTION_DATE <> \'9999-01-01-01.00.00\'';
                if($fromResDate eq '01/01/9999')
                  { $exclude = ''; }
                $dates .= ' TRANSACTION_DATE >= \''.$year.'-'.$month.'-'.$day.'-00.00.00\''.$exclude;
            }
            else
            {
                push(@warnings,'Invalid "from" date used in Reserve Date');
                $resDateError = '<div class="error"></div>';
            }

            ($day, $month, $year) = parseDateString($toResDate);

            if(defined($day))
            {
                $toResDate = $month.'/'.$day.'/'.$year;
                $dates .= ' AND TRANSACTION_DATE <= \''.$year.'-'.$month.'-'.$day.'-23.59.59'.'\'';
            }
            else
            {
                push(@warnings,'Invalid "to" date used in Reserve Date');
                $resDateError = '<div class="error"></div>';
            }

            push(@where,'EXISTS (SELECT * FROM CLAIMDB.CLM_RESERVES WHERE G.CLAIM_ID = CLAIM_ID AND (RESERVE_AMT > 25000 OR RESERVE_AMT < -25000) AND DATE_DELETED = \'9999-01-01 01:00:00.000000\' AND'.$dates.')');

                         #bug 107265 - cash part of the
                         #large reserve changes query causing timeouts in production.
                         #changed query to 'inner join' from 'where exists'
                         $queryJoinsA .= ' INNER JOIN CLAIMDB.CLM_CASH AS C ON C.CLAIM_ID = G.CLAIM_ID ';
            push(@whereA,'G.CLAIM_ID = C.CLAIM_ID AND (PAYMENT_AMT > 25000 OR PAYMENT_AMT < -25000) AND C.DATE_DELETED = \'9999-01-01 01:00:00.000000\' AND'.$dates.' ');

#            push(@whereA,'EXISTS (SELECT * FROM CLAIMDB.CLM_CASH WHERE G.CLAIM_ID = CLAIM_ID AND (PAYMENT_AMT > 25000 OR PAYMENT_AMT < -25000) AND DATE_DELETED = \'9999-01-01 01:00:00.000000\' AND'.$dates.')');
            #$queryJoins .= ' INNER JOIN CLAIMDB.CLM_RESERVES AS R ON G.CLAIM_ID = R.CLAIM_ID AND (RESERVE_AMT > 10000 OR RESERVE_AMT < -10000)';
            #push(@where,'CLAIM_STATUS IN (\'CC\',\'CF\',\'CWP\')');
        }
    }

    my $openClaimsChecked = '';
    my $pendingClaimsChecked = '';
    my $closedClaimsChecked = '';
    my $newClaimsChecked = '';
    my $claimStatus = '';
    my $fromNewDate = '';
    my $toNewDate = '';
    my $NotThisClaimStatus = '';
    my $newDateDisplay = 'display:none;';
    my $newDateError = '';
    if(defined($ENGINE->{'CGI'}->param('new_claims')) && $ENGINE->{'CGI'}->param('new_claims') =~ /\w/)
    {
        $fromNewDate = $ENGINE->{'CGI'}->param('fromNewDate') || '';
        $toNewDate = $ENGINE->{'CGI'}->param('toNewDate') || '';
        $fromNewDate =~ s/^\s*//g;
        $fromNewDate =~ s/\s*$//g;
        $fromNewDate =~ s/"/&quot;/g;
        $toNewDate =~ s/^\s*//g;
        $toNewDate =~ s/\s*$//g;
        $toNewDate =~ s/"/&quot;/g;

        $newDateDisplay = '';
        $newClaimsChecked = ' checked="checked"';

        if($fromNewDate eq '' && $toNewDate eq '')
        {
            push(@warnings,'New Claims dates are required');
            $newDateError = '<div class="error"></div>';
        }
        else
        {
            my $dates = '';
            my ($day, $month, $year) = parseDateString($fromNewDate);
            if(defined($day))
            {
                $fromNewDate = $month.'/'.$day.'/'.$year;
                my $exclude = ' AND CREATION_DATE <> \'9999-01-01-01.00.00\'';
                if($fromNewDate eq '01/01/9999')
                  { $exclude = ''; }
                push(@where,'CREATION_DATE >= \''.$year.'-'.$month.'-'.$day.'-00.00.00\''.$exclude);
                push(@whereA,'CREATION_DATE >= \''.$year.'-'.$month.'-'.$day.'-00.00.00\''.$exclude);
            }
            else
            {
                push(@warnings,'Invalid "from" date used in New Claims Date');
                $newDateError = '<div class="error"></div>';
            }

            ($day, $month, $year) = parseDateString($toNewDate);
            if(defined($day))
            {
                $toNewDate = $month.'/'.$day.'/'.$year;
                push(@where,'CREATION_DATE <= \''.$year.'-'.$month.'-'.$day.'-23.59.59\'');
                push(@whereA,'CREATION_DATE <= \''.$year.'-'.$month.'-'.$day.'-23.59.59\'');
            }
            else
            {
                push(@warnings,'Invalid "to" date used in New Claims Date');
                $newDateError = '<div class="error"></div>';
            }
            $NotThisClaimStatus .= "'P',";
            #die Data::Dumper::Dumper($claimStatus);
            #$claimStatus .= "'NA','P','M',";
        }
    }

    if(defined($ENGINE->{'CGI'}->param('open_claims')) && $ENGINE->{'CGI'}->param('open_claims') =~ /\w/)
    {
        $openClaimsChecked = ' checked="checked"';
        $claimStatus .= "'A','FT',";
    }

    if(defined($ENGINE->{'CGI'}->param('pending_claims')) && $ENGINE->{'CGI'}->param('pending_claims') =~ /\w/)
    {
        $pendingClaimsChecked = ' checked="checked"';
        $claimStatus .= "'P',";
    }

    my $dateError = '';
    my $fromDate = $ENGINE->{'CGI'}->param('fromDate') || '';
    my $toDate = $ENGINE->{'CGI'}->param('toDate') || '';
    $fromDate =~ s/^\s*//g;
    $fromDate =~ s/\s*$//g;
    $fromDate =~ s/"/&quot;/g;
    $toDate =~ s/^\s*//g;
    $toDate =~ s/\s*$//g;
    $toDate =~ s/"/&quot;/g;
    if($fromDate ne '')
    {
        my ($day, $month, $year) = parseDateString($fromDate);
        if(defined($day))
        {
            $fromDate = $month.'/'.$day.'/'.$year;
            my $exclude = ' AND LOSS_DATE_TIME <> \'9999-01-01-01.00.00\'';
            if($fromDate eq '01/01/9999')
              { $exclude = ''; }
            push(@where,'LOSS_DATE_TIME >= \''.$year.'-'.$month.'-'.$day.'-00.00.00\''.$exclude);
            push(@whereA,'LOSS_DATE_TIME >= \''.$year.'-'.$month.'-'.$day.'-00.00.00\''.$exclude);
        }
        else
        {
            push(@warnings,'Invalid "from" date used in Loss Date');
            $dateError = '<div class="error"></div>';
        }
    }
    if($toDate ne '')
    {
        my ($day, $month, $year) = parseDateString($toDate);
        if(defined($day))
        {
            $toDate = $month.'/'.$day.'/'.$year;
            push(@where,'LOSS_DATE_TIME <= \''.$year.'-'.$month.'-'.$day.'-23.59.59'.'\'');
            push(@whereA,'LOSS_DATE_TIME <= \''.$year.'-'.$month.'-'.$day.'-23.59.59'.'\'');
        }
        else
        {
            push(@warnings,'Invalid "to" date used in Loss Date');
            $dateError = '<div class="error"></div>';
        }
    }

    my $fromClosedDate = '';
    my $toClosedDate = '';
    my $closedDateDisplay = 'display:none;';
    my $closedDateError = '';
    if(defined($ENGINE->{'CGI'}->param('closed_claims')) && $ENGINE->{'CGI'}->param('closed_claims') =~ /\w/)
    {
        $closedClaimsChecked = ' checked="checked"';
        $claimStatus .= "'CC','CF','CWP',";

        $fromClosedDate = $ENGINE->{'CGI'}->param('fromClosedDate') || '';
        $toClosedDate = $ENGINE->{'CGI'}->param('toClosedDate') || '';
        $fromClosedDate =~ s/^\s*//g;
        $fromClosedDate =~ s/\s*$//g;
        $fromClosedDate =~ s/"/&quot;/g;
        $toClosedDate =~ s/^\s*//g;
        $toClosedDate =~ s/\s*$//g;
        $toClosedDate =~ s/"/&quot;/g;

        $closedDateDisplay = '';

        if($fromClosedDate eq '' && $toClosedDate eq '' && $fromDate eq '' && $toDate eq '')
        {
            push(@warnings,'Closed Claims Dates or Loss Dates are required');
            $closedDateError = '<div class="error"></div>';
        }
        elsif(($fromClosedDate =~/\w/ || $toClosedDate =~/\w/) && ($fromDate =~/\w/ || $toDate =~/\w/))
        {
            push(@warnings,'Closed Claims Dates and Loss Dates cannot both be specified.');
            $closedDateError = '<div class="error"></div>';
        }
        elsif($fromClosedDate =~/\w/ || $toClosedDate =~/\w/)
        {
            my $dates = '';
            my ($day, $month, $year) = parseDateString($fromClosedDate);
            if(defined($day))
            {
                $fromClosedDate = $month.'/'.$day.'/'.$year;
                my $exclude = ' AND CLAIM_CLOSED_DATE <> \'9999-01-01-01.00.00\'';
                if($fromClosedDate eq '01/01/9999')
                  { $exclude = ''; }
                push(@where,'CLAIM_CLOSED_DATE >= \''.$year.'-'.$month.'-'.$day.'-00.00.00\''.$exclude);
                push(@whereA,'CLAIM_CLOSED_DATE >= \''.$year.'-'.$month.'-'.$day.'-00.00.00\''.$exclude);
            }
            else
            {
                push(@warnings,'Invalid "from" date used in Closed Claims Date');
                $closedDateError = '<div class="error"></div>';
            }

            ($day, $month, $year) = parseDateString($toClosedDate);
            if(defined($day))
            {
                $toClosedDate = $month.'/'.$day.'/'.$year;
                push(@where,'CLAIM_CLOSED_DATE <= \''.$year.'-'.$month.'-'.$day.'-23.59.59\'');
                push(@whereA,'CLAIM_CLOSED_DATE <= \''.$year.'-'.$month.'-'.$day.'-23.59.59\'');
            }
            else
            {
                push(@warnings,'Invalid "to" date used in Closed Claims Date');
                $closedDateError = '<div class="error"></div>';
            }
        }
    }
    if(scalar(@where) > 0 && $ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'Internal' &&
           $ENGINE->{'AUTH'}->{'Claims_Access'} ne 'A' &&
           (!defined($ENGINE->{'AUTH'}->{'Claims_Unsub'}) ||
             (defined($ENGINE->{'AUTH'}->{'Claims_Unsub'}) && $ENGINE->{'AUTH'}->{'Claims_Unsub'} ne 'I')))
        {$NotThisClaimStatus .= "'P',";}

   #die Data::Dumper::Dumper($claimStatus,$NotThisClaimStatus);
    if($claimStatus ne '')
      { chop($claimStatus); push(@where,'CLAIM_STATUS IN ('.$claimStatus.')'); push(@whereA,'CLAIM_STATUS IN ('.$claimStatus.')');}

    if($NotThisClaimStatus ne '')
      { chop($NotThisClaimStatus); push(@where,'CLAIM_STATUS NOT IN ('.$NotThisClaimStatus.')'); push(@whereA,'CLAIM_STATUS NOT IN ('.$NotThisClaimStatus.')');}
#    die Data::Dumper::Dumper('ln 852',@where);
    my $ocnpFound = 0;
    my $openClaimErr = '';
    my $closedClaimErr = '';
    my $newClaimErr = '';
    my $pendingClaimErr = '';
    if(defined($ENGINE->{'CGI'}->param('open_claims')) && $ENGINE->{'CGI'}->param('open_claims')  =~ /\w/)
    { $ocnpFound++; }
    if(defined($ENGINE->{'CGI'}->param('closed_claims')) && $ENGINE->{'CGI'}->param('closed_claims')  =~ /\w/)
    { $ocnpFound++; }
    if(defined($ENGINE->{'CGI'}->param('new_claims')) && $ENGINE->{'CGI'}->param('new_claims')  =~ /\w/)
    { $ocnpFound++; }
    if(defined($ENGINE->{'CGI'}->param('pending_claims')) && $ENGINE->{'CGI'}->param('pending_claims')  =~ /\w/)
    { $ocnpFound++; }

    if($ocnpFound > 1)
    {
        push(@warnings,'Please select one of the following: Open Claims, Closed Claims, New Claims or Un-Submitted Claims.');
            if(defined($ENGINE->{'CGI'}->param('open_claims')) && $ENGINE->{'CGI'}->param('open_claims')  =~ /\w/)
            { $openClaimErr = '<div class="error"></div>'; }
            if(defined($ENGINE->{'CGI'}->param('closed_claims')) && $ENGINE->{'CGI'}->param('closed_claims')  =~ /\w/)
            { $closedClaimErr = '<div class="error"></div>'; }
            if(defined($ENGINE->{'CGI'}->param('new_claims')) && $ENGINE->{'CGI'}->param('new_claims')  =~ /\w/)
            { $newClaimErr = '<div class="error"></div>'; }
            if(defined($ENGINE->{'CGI'}->param('pending_claims')) && $ENGINE->{'CGI'}->param('pending_claims')  =~ /\w/)
            { $pendingClaimErr = '<div class="error"></div>'; }
    }

    my $policyCancelledSW = 'N';
    for my $key (%{$ENGINE->{'errors'}})
    {
        for my $e (@{$ENGINE->{'errors'}->{$key}})
          {
              push(@warnings,$e);
              if($e eq 'Policy Cancelled.')
              { $policyCancelledSW = 'Y'; }
          }
    }
    if(scalar(@warnings) > 0)
    {
        $output .= '<div class="errorInfo"><ul><li>'.join('</li><li>',@warnings).'</li></ul></div>';
    }

    my $nc_pol_number = uc($ENGINE->{'CGI'}->param('nc_pol_number')) || '';
    substr($nc_pol_number,2) =~ s/O/0/gi;
    my $nc_pol_number_error = defined($ENGINE->{'errors'}->{'nc_pol_number'}) ? '<div class="error"></div>' : '';
    my $nc_lossDate = $ENGINE->{'CGI'}->param('nc_loss_date') || '';
    my ($day,$month,$year) = parseDateString($nc_lossDate);
    if(defined($day))
      { $nc_lossDate = $month.'/'.$day.'/'.$year; }
    my $nc_loss_date_error = defined($ENGINE->{'errors'}->{'nc_loss_date'}) ? '<div class="error"></div>' : '';
    my $nc_manual_entry = '';
    my $nc_polState = '';
    my $showManual = 'hidden';
    if(($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'Internal'
            && defined($ENGINE->{'errors'}->{'nc_manual_entry'}))
            || (defined($ENGINE->{'Inquiry'}->{'MANUAL_ENTRY'})
            && $ENGINE->{'Inquiry'}->{'MANUAL_ENTRY'} eq 'Y')
            || ($policyCancelledSW eq 'Y' && $nc_pol_number !~ /[^0-9]/))
    {
        my $badPolState = '';
        my $badLOB = '';
        if(defined($ENGINE->{'errors'}->{'polStateList'}))
        { $badPolState = '<div class="error"></div>'; }
        if(defined($ENGINE->{'errors'}->{'lobList'}))
        { $badLOB = '<div class="error"></div>'; }
        my $radioYesChecked = '';
        my $radioNoChecked = '';
        $nc_manual_entry = '<li class="newClaim" id="createManual"><label>Create Manual Claim?:</label><div> Yes ';
        if(defined($ENGINE->{'Inquiry'}->{'MANUAL_ENTRY'}) && $ENGINE->{'Inquiry'}->{'MANUAL_ENTRY'} eq 'Y')
        {
            $radioYesChecked = 'checked="checked"';
            $radioNoChecked = '';
            $showManual = '';
        }
        else
        {
            $radioYesChecked = '';
            $radioNoChecked = 'checked="checked"';
        }
        $nc_manual_entry .= '<input type="radio" value="Y" name="nc_manual_entry" '.$radioYesChecked.' onclick="toggleHelp(\'manualClaimDiv\')"/> No ';
        $nc_manual_entry .= '<input type="radio" value="N" name="nc_manual_entry" '.$radioNoChecked.' onclick="toggleHelp(\'manualClaimDiv\')"/></div></li>';
        my $policyState = '';
        if (scalar(keys %{$ENGINE->{'errors'}}) > 0)
        { $policyState = $ENGINE->{'CGI'}->param('polStateList')||''; }
        my %states = getStateAbbrevNumeric();
        my $stateOption = '<select name="polStateList"><option value=""></option>';
        my $lob = substr($nc_pol_number,0,2);
        for my $key (keys %states)
        {
           my $selected = '';
               if ($policyState eq $states{$key})
               {
                   $selected = ' selected="selected"';
               }

           if($key =~/IA|IL|MN|MO|ND|NE|SD|WI/ ||
              (!($lob =~ /FD|SY/) && $key =~ /29|38/))
           { next; }

           $stateOption .= '<option value="'.$states{$key}.'"'.$selected.'>'.$states{$key}.'</option>';
        }

        $nc_polState .= '<li class="newClaim"><label>Policy State:</label><div>'.$stateOption.'</select></div></li>'.$badPolState;
        my $lineOfBusiness = '';
        if (scalar(keys %{$ENGINE->{'errors'}}) > 0)
        { $lineOfBusiness = $ENGINE->{'CGI'}->param('lobList')||''; }
        my %LOBDescrip = LOBDescriptions();
        my $LOBOption = '<select name="lobList"><option value=""></option>';
        for my $key (sort keys %LOBDescrip)
        {
           my $selected = '';

           if ($lineOfBusiness eq $key)
           {
              $selected = ' selected="selected"';
           }

           $LOBOption .= '<option value="'.$key.'"'.$selected.'>'.$key.' '.$LOBDescrip{$key}.'</option>';
        }
        $nc_polState .= '<li class="newClaim"><label>Line of Business:</label><div>'.$LOBOption.'</select></div></li>'.$badLOB.$badLOB;
    }

    my $nc_glass_claim = '';
    my $nc_glass_claim_correction = '';
    my $glassClaimChecked = '';
    if(defined($ENGINE->{'CGI'}->param('nc_glass_claim')) && $ENGINE->{'CGI'}->param('nc_glass_claim') eq 'N')
      { $glassClaimChecked = ' checked="checked"'; }
    if(defined($ENGINE->{'errors'}->{'nc_glass_claim'})
      || (defined($nc_pol_number) && substr($nc_pol_number,0,2) eq 'AP' || substr($nc_pol_number,0,2) eq 'CV' || substr($nc_pol_number,0,3) eq 'WAP'))
    {
        if($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'Agent')
        {
#            $nc_glass_claim = '<div class="red">For glass claims, click <a href="https://agencyadvantage.safelite.com" target="_blank">here</a> to report and schedule a repair or replacement.</div>';
        }
        else
        {
           $nc_glass_claim = '<label>Glass Claim:</label><div><input type="checkbox" '.$glassClaimChecked.' value="N" name="nc_glass_claim" /></div>';
           $nc_glass_claim_correction = $glassClaimCorrection;
        }
    }

    my $badFarmState = '';
    if(defined($ENGINE->{'errors'}->{'farmStateList'}))
    { $badFarmState = '<div class="error"></div>'; }
    my $nc_farmState = '';
    if (scalar(keys %{$ENGINE->{'errors'}}) > 0 && $nc_pol_number gt '' && $nc_pol_number !~ /[^0-9]/)
#    if(defined($ENGINE->{'errors'}->{'farmStateList'}))
    {
        my $farmselected = $ENGINE->{'CGI'}->param('farmStateList')||'';
        my %states = getStateAbbrevNumeric();
            my $farmStateOption = '<select name="farmStateList"><option value=""></option>';
            for my $key (keys %states)
            {
               my $selected = '';
                   if ($farmselected eq $key)
                   {
                       $selected = ' selected="selected"';
                   }

               if($key =~/IA|IL|MN|NE|SD|WI/)
               { next; }

               $farmStateOption .= '<option value="'.$key.'"'.$selected.'>'.$states{$key}.'</option>';
            }
        $nc_farmState = '<label>State:</label><div>'.$farmStateOption.'</select></div>'.$badFarmState;
    }

    #Build line of business select bos for GL when nothing is selected.
    my $nc_GLLineSelect = '';
    my $badGLLine = '';
    if(defined($ENGINE->{'errors'}->{'GLLineList'}))
    { $badGLLine = '<div class="error"></div>'; }

    if (scalar(keys %{$ENGINE->{'errors'}}) > 0 && $nc_pol_number =~ /^GL/)
    {
        my $lineOfBusiness = '';
        if (scalar(keys %{$ENGINE->{'errors'}}) > 0)
        { $lineOfBusiness = $ENGINE->{'CGI'}->param('GLLineList')||''; }
        my $LOBOption = '<select name="GLLineList"><option value=""></option>';
        for my $key (sort keys %GLLineCodes)
        {
           my $selected = '';

           if ($lineOfBusiness eq $key)
           {
              $selected = ' selected="selected"';
           }

           $LOBOption .= '<option value="'.$key.'"'.$selected.'>'.$key.' '.$GLLineCodes{$key}.'</option>';
        }
        $nc_GLLineSelect .= '<li class="newClaim"><label>Line of Business:</label><div>'.$LOBOption.'</select></div></li>'.$badGLLine;
    }

    #set up sql to show for Pending claims on search for users who
    #have special AUTHMAN authority, and users who have claims submit
    #authority
    my $pendingClaims = '';
    if ((defined $ENGINE->{'AUTH'}->{'Claims_Unsub'}
            && $ENGINE->{'AUTH'}->{'Claims_Unsub'} eq 'I')
            || (defined $ENGINE->{'AUTH'}->{'Claims_Access'}
            && $ENGINE->{'AUTH'}->{'Claims_Access'} eq 'S')
            || (defined $ENGINE->{'AUTH'}->{'Claims_Access'}
            && $ENGINE->{'AUTH'}->{'Claims_Access'} eq 'A'))
    {
            $pendingClaims = '<li><div>'.$pendingClaimErr.'<input name="pending_claims" id="pending_claims" type="checkbox"'.$pendingClaimsChecked.' />&nbsp;Unsubmitted&nbsp;Claims</div></li>';
    }

    my $internalSearch = '';
    my $repClaimsChecked = '';
    my $openClaims = '';
    if($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'Internal')
    {
        if(defined($ENGINE->{'CGI'}->param('rep_claims')) && $ENGINE->{'CGI'}->param('rep_claims') =~ /\w/)
        {
            $repClaimsChecked = ' checked="checked"';
            $queryJoins .= ' INNER JOIN CLAIMDB.CLM_REP_ASSIGNED AS REP ON REP.CLAIM_ID = G.CLAIM_ID AND DATE_COMPLETED = \'9999-01-01\' AND REP.DATE_REMOVED = \'9999-01-01 01:00:00.000000\'';
            push(@where,('REP.USER_KEY = '.$ENGINE->{'AUTH'}->{'user_key'},'CLAIM_STATUS IN (\'A\',\'FT\')'));
            push(@whereA,('REP.USER_KEY = '.$ENGINE->{'AUTH'}->{'user_key'},'CLAIM_STATUS IN (\'A\',\'FT\')'));
        }

        $openClaims = '<li><label>Your Open Claims: </label><div><input name="rep_claims" id="rep_claims" type="checkbox"'.$repClaimsChecked.'/></div></li>';

        $internalSearch .= <<EOF;
<li>
   <div>
       <input name="new_claims" id="new_claims" type="checkbox"$newClaimsChecked onclick="CKtoggle(this,'newDateDiv')"/>&nbsp;New&nbsp;Claims
       $newClaimErr
   </div>
   <div id="newDateDiv" style="width:27em;padding-left:2em;$newDateDisplay">
       <span class="date_wording">Date:</span>
       <input type="text" id="fromNewDate" name="fromNewDate" value="$fromNewDate" class="date" maxlength="10"/>
       <!--<span style="position:relative;" id="fromNewDateIcon"></span>
       <img style="border:0;cursor:pointer;vertical-align:middle" src="calendar.gif" onclick="showCalendar('fromNewDate','fromNewDateIcon');"  alt="from pop-up calendar" class="date_icon"/>-->
       <span class="date_wording">to</span>
       $newDateError
       <input type="text" id="toNewDate" name="toNewDate" value="$toNewDate" class="date" maxlength="10"/>
       <!--<span style="position:relative;" id="toNewDateIcon"></span>
       <img style="border:0;cursor:pointer;vertical-align:middle;" src="calendar.gif" onclick="showCalendar('toNewDate','toNewDateIcon');" alt="to pop-up calendar" class="date_icon"/> -->
   </div>
</li>
EOF
    }
    else
    {
        $agencySearch .= <<EOF;
<li>
    <label>New&nbsp;Claims</label>
    <div>
        <input name="new_claims" id="new_claims" type="checkbox"$newClaimsChecked onclick="CKtoggle(this,'newDateDiv')"/>
        $newClaimErr
    </div>
    <div id="newDateDiv" style="width:27em;padding-left:2em;$newDateDisplay">
        <span class="date_wording">Date:</span>
        <input type="text" id="fromNewDate" name="fromNewDate" value="$fromNewDate" class="date" maxlength="10"/>
        <!--<span style="position:relative;" id="fromNewDateIcon"></span>
        <img style="border:0;cursor:pointer;vertical-align:middle" src="calendar.gif" onclick="showCalendar('fromNewDate','fromNewDateIcon');"  alt="from pop-up calendar" class="date_icon"/> -->
        <span class="date_wording">to</span>
        $newDateError
        <input type="text" id="toNewDate" name="toNewDate" value="$toNewDate" class="date" maxlength="10"/>
        <!--<span style="position:relative;" id="toNewDateIcon"></span>
        <img style="border:0;cursor:pointer;vertical-align:middle;" src="calendar.gif" onclick="showCalendar('toNewDate','toNewDateIcon');" alt="to pop-up calendar" class="date_icon"/> -->
    </div>
</li>
EOF
    }
    $output .= $thank_you_msg;
    $output .= <<HTML;
<div class="full_page">
    <div class="$search_class" id="search_criteria">
        <script type="text/javascript">
            var DATE_PICKER_OPTIONS = {
                minDate: new Date(2000, 0, 1),
                maxDate: 0,
                yearRange: 'c-20:c'
            };

            \$(function() {
                \$("#nc_loss_date").datepicker(DATE_PICKER_OPTIONS);
                \$("#fromClosedDate").datepicker(DATE_PICKER_OPTIONS);
                \$("#toClosedDate").datepicker(DATE_PICKER_OPTIONS);
                \$("#fromPayDate").datepicker(DATE_PICKER_OPTIONS);
                \$("#toPayDate").datepicker(DATE_PICKER_OPTIONS);
                \$("#fromResDate").datepicker(DATE_PICKER_OPTIONS);
                \$("#toResDate").datepicker(DATE_PICKER_OPTIONS);
                \$("#fromDate").datepicker(DATE_PICKER_OPTIONS);
                \$("#toDate").datepicker(DATE_PICKER_OPTIONS);
                \$("#fromNewDate").datepicker(DATE_PICKER_OPTIONS);
                \$("#toNewDate").datepicker(DATE_PICKER_OPTIONS);
            });
        </script>
        <form id="searchForm" method="get" action="$action">
            <h2>Search</h2>
            <ul id="first_search_ul" class="$ul_search_class">
                <li>
                    <label>Claim Number:</label>
                    <div>$claim_number_error<input name="claim_number" id="claim_number" maxlength="9" value="$claim_number" /></div>
                    <script type="text/javascript">document.getElementById('claim_number').focus();</script>
                </li>
                <li>
                    <label>Policy Number:</label>
                    <div>$pol_number_error<input name="pol_number" id="pol_number" maxlength="10" value="$pol_number" /></div>
                </li>
                <li>
                    <label>Insured Last/Business Name:</label>
                    <div>$insd_lname_error<input name="insd_lname" id="insd_lname" maxlength="90" value="$insd_lname" /></div>
                </li>
                <li>
                    <label>Insured First Name:</label>
                    <div>$insd_fname_error<input name="insd_fname" id="insd_fname" maxlength="30" value="$insd_fname" /></div>
                </li>
                <li>
                    <label>Involved Party Last/Business Name:</label>
                    <div>$claim_lname_error<input name="claim_lname" id="claim_lname" maxlength="90" value="$claim_lname" /></div>
                </li>
                <li>
                    <label>Involved Party First Name:</label>
                    <div>$claim_fname_error<input name="claim_fname" id="claim_fname" maxlength="30" value="$claim_fname" /></div>
                </li>
                <!--<div class="label">Claimant Phone:</div><input name="claim_phone" id="claim_phone" maxlength="15" value="$claim_phone" /><br />-->
                $agencySearch

                <li>
                    <label>Partial Match:</label>
                    <span class="radio-inline">
                         <input type="radio" name="claim_partial" id="partial"  onclick="checking('$claim_partial')" $yesChecked value="on"/>Yes
                    </span>
                    <span class="radio-inline">
                         <input type="radio" name="claim_partial" id="notpartial" onclick="checking('$claim_partial')" $noChecked value="off"/>No
                    </span>
                </li>
                $openClaims
                <li>&nbsp;</li>
            </ul>

            <ul class="adv_search" id="advancedSearch" style="display:$advSearchDisplay">
                <li><div>$openClaimErr<input name="open_claims" id="open_claims" type="checkbox"$openClaimsChecked /><span>&nbsp;Open&nbsp;Claims</span></div></li>
                $pendingClaims
                <li>
                    <div>
                        $closedClaimErr
                        <input name="closed_claims" id="closed_claims" type="checkbox"$closedClaimsChecked onclick="CKtoggle(this,'closedDateDiv')"/><span>&nbsp;Closed&nbsp;Claims</span>
                    </div>
                    <div id="closedDateDiv" style="padding-left:2em;$closedDateDisplay">
                      <span class="date_wording">Date:</span>
                      $closedDateError
                      <input maxlength="10" type="text" id="fromClosedDate" name="fromClosedDate" value="$fromClosedDate" class="date"/>
                      <span class="date_wording">to</span>
                      <input maxlength="10" type="text" id="toClosedDate" name="toClosedDate" value="$toClosedDate" class="date" />
                    </div>
                </li>

                $internalSearch

                <li>
                    <div>
                        <input name="payments_made" id="payments_made" type="checkbox"$paymentsMadeChecked onclick="CKtoggle(this,'payDateDiv')"/><span>&nbsp;Payments&nbsp;Made</span>
                    </div>
                    <div id="payDateDiv" style="width:27em;padding-left:2em;$payDateDisplay">
                        <span class="date_wording">Date:</span>
                        $payDateError
                        <input type="text" id="fromPayDate" name="fromPayDate" value="$fromPayDate" class="date" maxlength="10" />
                        <span class="date_wording">to</span>
                        <input type="text" id="toPayDate" name="toPayDate" value="$toPayDate" class="date" maxlength="10"/>
                    </div>
                </li>

                <li>
                    <div>
                        <input name="large_reserve_changes" id="large_reserve_changes" type="checkbox"$largeReserveChangesChecked onclick="CKtoggle(this,'resDateDiv')"/>
                        <span>Large Reserve Changes</span>
                        <a class="help" onclick="toggleHelp(\'large_reserve_help\')">&nbsp;&nbsp;</a>
                    </div>
                    <div class="helptext hidden" id="large_reserve_help">
                        Reserve Changes > \$25,000
                    </div>
                    <div id="resDateDiv"  style="width:27em;padding-left:2em;$resDateDisplay">
                        <span class="date_wording">Date:</span>
                        $resDateError<input size="12" type="text" id="fromResDate" name="fromResDate" value="$fromResDate" class="date" maxlength="10"/>
                        <span class="date_wording">to</span>
                        <input size="12" type="text" id="toResDate" name="toResDate" value="$toResDate" class="date" maxlength="10"/>
                    </div>
                </li>

                <li>
                    <label>Loss Date: </label>
                    $dateError
                    <input type="text" id="fromDate" name="fromDate" value="$fromDate" class="date" maxlength="10"/>
                    <span class="date_wording">to</span>
                    <input type="text" id="toDate" name="toDate" value="$toDate" class="date" maxlength="10"/>
                </li>
            </ul>
            <ul class="toplabel_onecol">
                <li>
                    <a href="javascript:///" onclick="toggle('advancedSearch');var adv = document.getElementById('advSearch'); adv.value = adv.value == 0 ? 1 : 0; if(adv.value == 0){clearNode(getElementById('advancedSearch')); this.innerHTML='Advanced Search'}else{this.innerHTML='Hide Advanced Search'}">$normAdv Search</a><br />
                </li>
                <li>
                    <div style="width:100%"><input type="submit" value="Search" class="primary"/> <input type="button" value="Reset" onclick="clearNode(getElementById('searchForm'));" class="secondary"/></div>
                </li>
            </ul>
            <div class="hidden"><input type="hidden" id="advSearch" name="advSearch" value="$advSearch" /><input type="hidden" name="load" value="Claims_Inquiry" /><input type="hidden" name="sessionID" value="$sessID" /><input type="hidden" name="leave" value="" /><input type="hidden" value="N" name="wc_detail_jump" id="wc_detail_jump"/>
                                <input type="hidden" value="" name="desktop_or_mobile" id="desktop_or_mobile"/> </div>
        </form>
    </div><!--half_page-->
HTML


#adding the New Claim box
my $claimExists = 'N';
my $dump = '';
if($ENGINE->{'AUTH'}->{'Claims_Access'} eq 'A'
        || $ENGINE->{'AUTH'}->{'Claims_Access'} eq 'S')
{

    if(defined($ENGINE->{'CGI'}->param('claimExists')) && $ENGINE->{'CGI'}->param('claimExists') ne 'X')
    { $claimExists = $ENGINE->{'CGI'}->param('claimExists'); }
    else
    { $claimExists = 'N'; }
    #This if checks to see if uers can create a new claim.
    if((defined($ENGINE->{'AUTH'}->{'Claims_Submit'}) && $ENGINE->{'AUTH'}->{'Claims_Submit'} eq 'A') ||
       $ENGINE->{'AUTH'}->{'Claims_Access'} eq 'S')
    {
        my $insdQuery = $ENGINE->{'DBH'}->prepare('SELECT P.FIRST_NAME, P.LAST_NAME, P.BUSINESS_NAME FROM CLAIMDB.CLM_PARTIES AS P INNER JOIN CLAIMDB.CLM_PARTY_ROLES AS R ON P.PARTY_ID = R.PARTY_ID WHERE P.CLAIM_ID = ? AND R.ROLE = \'IN\' AND P.DATE_DELETED = \'9999-01-01 01:00:00.000000\' AND R.DATE_DELETED = \'9999-01-01 01:00:00.000000\'') || error($ENGINE,'insured query prepare failed: '.$ENGINE->{'DBH'}->errstr);
        my $searchQuery = $ENGINE->{'DBH'}->prepare('
                SELECT
                        G.CLAIM_ID, POLICY_NUMBER, IMT_CLAIM_NO, IMT_LINE_CODE, LOSS_DATE_TIME, CREATION_DATE, AGENCY_NO
                FROM
                        CLAIMDB.CLM_GENERAL AS G
                WHERE
                        CLAIM_STATUS = \'P\'
                        AND G.USER_KEY = ?
                        AND G.DATE_DELETED = \'9999-01-01 01:00:00.000000\' FETCH FIRST 251 ROWS ONLY') || error($ENGINE,'Search query prepare failed: '.$ENGINE->{'DBH'}->errstr);
        $searchQuery->execute($ENGINE->{'AUTH'}->{'user_key'}) || error($ENGINE,'Search query execute failed: '.$ENGINE->{'DBH'}->errstr);
        my $results = $searchQuery->fetchall_arrayref({});
        my @sortedResults = sort( {$b->{'CLAIM_ID'} cmp $a->{'CLAIM_ID'}} @$results);

        my $unsub_claims_data = '';

        my $i = 0;
        for my $r (@sortedResults)
        {
             my $lob = $r->{'IMT_LINE_CODE'};
             my $claim_num = $r->{'IMT_CLAIM_NO'};
             my $claim_id = $r->{'CLAIM_ID'};
             my $policy_num = $r->{'POLICY_NUMBER'};
             my $loss_date_time = $r->{'LOSS_DATE_TIME'};
             my @creation_date = split(/-/,substr($r->{'CREATION_DATE'},0,10));
             $insdQuery->execute($claim_id) || error($ENGINE,'Insured query execute failed: '.$ENGINE->{'DBH'}->errstr);
             my $insured = $insdQuery->fetchall_arrayref({});
             my $ins_name = '';
             if(defined(@$insured[0]) && @$insured[0]->{'FIRST_NAME'} ne '')
             {$ins_name = @$insured[0]->{'LAST_NAME'}.', '.@$insured[0]->{'FIRST_NAME'};}
             elsif(defined(@$insured[0]) && @$insured[0]->{'BUSINESS_NAME'} ne '')
             {$ins_name = @$insured[0]->{'BUSINESS_NAME'};}

             if($ins_name ne '')
             {$ins_name = substr($ins_name, 0, 20); }
             else
             {$ins_name = '&nbsp';}

             my $itemIDQuery = $ENGINE->{'DBH'}->prepare('SELECT I.ITEM_ID FROM CLIENTDB.ITEM AS I INNER JOIN CLIENTDB.VERSION AS V ON I.ITEM_ID = V.ITEM_ID WHERE ID_NUMBER = ? AND V.STATUS <> \'Z\'') || error($ENGINE,'clientID query prepare failed: '.$ENGINE->{'DBH'}->errstr);
             if($r->{'IMT_LINE_CODE'} =~ /301|302|331|332/)
             { $policy_num =~ s/^12|^14|^26|^40|^48//g; }
             $itemIDQuery->execute($policy_num) || error($ENGINE,'clientID query execute failed: '.$ENGINE->{'DBH'}->errstr);
             my $clientIDResult = $itemIDQuery->fetchall_arrayref({});
             if(scalar(@$clientIDResult) > 0 && defined($clientIDResult->[0]->{'ITEM_ID'}))
             {
                $policy_num = '<a href="../Client/'.($ENGINE->{'AUTH'}->{'ClientEngine'} || 'ClientEngine.pl').'?load=ClientPView&amp;id='.$clientIDResult->[0]->{'ITEM_ID'}.'">'.$policy_num.'</a>';
                if (check_for_platform_version({dbh => $ENGINE->{'DBH'}, item_id => $clientIDResult->[0]->{'ITEM_ID'}})) {
                    $policy_num = '<a href="'.get_policy_info_page_url({policy_number => $policy_num}).'" target="_blank">'.$policy_num.'</a>';
                }
             }

             my $date = '';
             if($loss_date_time eq '9999-01-01 01:00:00.000000')
             { $date = 'Unknown'; }
             else
             { $date = substr($loss_date_time,5,2).'/'.substr($loss_date_time,8,2).'/'.substr($loss_date_time,0,4); }

             #Calculate 30 days from claim started date
             # if we are 10 days out, put warning that claim will expire on $day_30_date
             # make it work like the help icon
             # "If not submitted by $day_30_date this claim will be removed from the system."
             use Date::Calc qw(Add_Delta_Days Delta_Days);
             my @date_30_days = Add_Delta_Days(@creation_date, 31);
             my @today = (localtime)[5,4,3];
                $today[0] += 1900;
                $today[1]++;
             my $date_diff = Delta_Days( @today, @date_30_days );
             my $info = '<a class="space" aria-hidden="true">&nbsp;</a>';
             my $info_help = '';
             my $day_30_date = $date_30_days[1].'/'.$date_30_days[2].'/'.$date_30_days[0];
             if($date_diff <= 11)
             {  $i++;
                $info = '<a class="info-alert glyphicon glyphicon-exclamation-sign" aria-hidden="true" onclick="toggleHelp(\'claim'.$claim_num.'_help\')"></a>';
                $info_help = '<li class="hidden" id="claim'.$claim_num.'_help"><div class="infotext">If not submitted by '.$day_30_date.' this claim will be removed from the system</div></li>';
             }
                 $unsub_claims_data .= <<EOF;
<li>
        <div class="claim">$info<a href=\"$action?claimid=$claim_id&amp;load=Claims_Details&amp;sessionID=$sessID\">$claim_num</a></div>
        <div class="policy">$policy_num</div>
        <div class="insured">$ins_name</div>
</li>
$info_help
EOF

                 #die Data::Dumper::Dumper($ins_name,$policy_num,$date);
            }
        #die Data::Dumper::Dumper($unsub_claims_data);
        my $unsub_claims = '';
        if(scalar(@sortedResults) > 0){
                $unsub_claims = '<ul class="unsubmitted">'.$unsub_claims_data.'</ul>';
        }
        # die Data::Dumper::Dumper($nc_farmState,$nc_GLLineSelect,$nc_glass_claim,$nc_glass_claim_correction);
        my $hideFarmState = 'hidden';
        my $hideGLLine = 'hidden';
        my $hideGlass = 'hidden';
        my $hideGlassCorrection = 'hidden';
        if($nc_farmState ne ''){$hideFarmState = '';}
        if($nc_GLLineSelect ne ''){$hideGLLine = '';}
        if($nc_glass_claim ne ''){$hideGlass = '';}
        if($nc_glass_claim_correction ne ''){$hideGlassCorrection = '';}
        my $agent_glass_wording = '';
        if($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'Agent')
        {$agent_glass_wording = '<li class="newClaim"><div class="agent_glass">For Safelite Auto glass claims, click&nbsp;<a href="https://safeliteforagents.com" target="_blank">here</a>. <br />For all other glass claims, start a new claim&nbsp;above. <br />For Roadside Assistance claims call (877)&nbsp;218-9022</div></li>';}
$output .= <<HTML;
    <div class="quarter_page">
        <div class="full_div"><!--full_div--><!--start new claim-->
            <form id="newClaimForm" method="post" action="$action">
                <h2>Enter a New Claim</h2>
                <ul class="leftlabel_twocol">
                    <li class="newClaim">
                        <label>Policy Number:</label>
                        <div>
                            $nc_pol_number_error
                            <input name="nc_pol_number" id="nc_pol_number" maxlength="10" value="$nc_pol_number" onkeyup="glassClaims(this)" />
                        </div>
                    </li>
                    <li class="newClaim">
                        <label>Loss Date:</label>
                        <div>
                            $nc_loss_date_error
                            <input type="text" id="nc_loss_date" name="nc_loss_date" value="$nc_lossDate" class="date" maxlength="10" datepicker_disable="0"/>
                        </div>
                    </li>
                    $agent_glass_wording
                    $nc_manual_entry
                    <li id="farmState" class="$hideFarmState newClaim">
                    $nc_farmState
                    </li>
                    <li id="GLLine" class="$hideGLLine newClaim">
                    $nc_GLLineSelect
                    </li>
                    <li id="glassClaim" class="$hideGlass newClaim">
                    $nc_glass_claim
                    </li>
                    <li id ="glassClaimCorrection" class="$hideGlassCorrection newClaim">
                        $nc_glass_claim_correction
                    </li>
                </ul>
                <ul class="leftlabel_twocol $showManual" id="manualClaimDiv">
                    $nc_polState
                </ul>
                <ul class="leftlabel_twocol">
                    <li>
                        <input type="submit" value="Start" class="primary" onclick="disableButtons()"/> <input type="button" value="Reset" onclick="clearNode(getElementById('newClaimForm'));resetSearch()" class="secondary"/>
                    </li>
                </ul>
                <div>
                    <input type="hidden" name="claimExists" id="claimExists" value="$claimExists" />
                    <input type="hidden" name="submitGrace" id="submitGrace" value="N" />
                    <input type="hidden" name="load" id="load"value="Claims_Inquiry" />
                    <input type="hidden" name="save" value="Claims_Inquiry" />
                    <input type="hidden" name="sessionID" value="$sessID" />
                    <input type="hidden" value="N" name="wc_detail_jump" id="wc_detail_jump"/>
                </div>
            </form>
        </div><!--/full_div--><!--start new claim-->

        <div class="full_div_no_bottom"><!--full_div_no_bottom--><!--unsubmitted claims-->
            <h2 class="unsub">
                <span class="left">Your Unsubmitted Claims</span>
                <a class="white-help glyphicon glyphicon-question-sign" href="#" data-toggle="modal" data-target="#unsub_claims_help">&nbsp;&nbsp;</a>
            </h2>
            <div class="modal" id="unsub_claims_help" tabindex="-1" role="dialog" aria-labelledby="unsub_claims_help" data-keyboard="false" data-backdrop="static">
                <div class="modal-dialog" id="SDIPModal">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        </div>
                        <div class="modal-body">
                            <p>Claims listed in this section are unsubmitted and will not become open claims until submitted.  If they are not submitted within 30 days of when they were started they will be removed from the system.</p>
                        </div>
                    </div>
                </div>
            </div>
            <script type="text/javascript">
                \$(window).load(function(){
                    \$('.modal-dialog').css("margin-top", Math.max(0, (\$(window).height() - \$('.modal-dialog').height()) / 3));
                });
            </script>
            <div class="unsub_claims"><!--unsub_claims-->
                $unsub_claims
            </div><!--/unsub_claims-->
        </div><!--/full_div_no_bottom--><!--/unsubmitted claims-->
    </div><!--quarter_page-->
</div><!--full_page-->
HTML
   }
}
#die Data::Dumper::Dumper($dump);
$output .= '<div style="clear:both"></div> ';

#    if(((scalar(@where) > 0 && $ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'Internal') || scalar(@where) > 1) && scalar(@warnings) == 0)
# Error reporting to the mobile apps
my $appOrigin = $ENGINE->{'CGI'}->param('appOrigin') || '';
if(scalar(@warnings) > 0 && $appOrigin ne '')
{
    my $errors = \@warnings;
    print encode_json($errors);
    $ENGINE->{'DBH'}->rollback();
            $ENGINE->{'DBH'}->disconnect();
    exit;
}
    if((
            (scalar(@where) > 0
                    && ($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'Internal'
                      || $ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'LawFirm')
                      && scalar(@warnings) == 0)
            || (scalar(@where) > 1)
                    && scalar(@warnings) == 0))
    {
        my $searchResults = '';
        $searchResults .= '<fieldset><h2>Search Results</h2>';
        my $where = ' WHERE '.join(' AND ',@where);
        #$output .= 'SELECT G.CLAIM_ID, POLICY_NUMBER, IMT_CLAIM_NO, IMT_LINE_CODE, LOSS_DATE_TIME, PURGED_IND, CLAIM_STATUS, SUBRO_STATUS, SALV_STATUS, REINSURANCE_IND FROM CLAIMDB.CLM_GENERAL AS G'.$queryJoins.$where.' AND G.DATE_DELETED = \'9999-01-01 01:00:00.000000\' FETCH FIRST 251 ROWS ONLY';
        my $searchQuery = $ENGINE->{'DBH'}->prepare('SELECT G.CLAIM_ID, POLICY_NUMBER, IMT_CLAIM_NO, IMT_LINE_CODE, LOSS_DATE_TIME, PURGED_IND, CLAIM_STATUS, SUBRO_STATUS, SALV_STATUS, REINSURANCE_IND, CLOSE_RECOVERABLE, G.AGENCY_NO FROM CLAIMDB.CLM_GENERAL AS G'.$queryJoins.$where.' AND G.DATE_DELETED = \'9999-01-01 01:00:00.000000\' FETCH FIRST 251 ROWS ONLY') || error($ENGINE,'Search query prepare failed: '.$ENGINE->{'DBH'}->errstr);

        #This if is needed below so we can check the cash table for payments > 25000 or < -25000.
        #We could not include this in the same query that we use for the reserves.  It ran too long.
        my $resultsA = [];
        if(defined($ENGINE->{'CGI'}->param('large_reserve_changes')) && $ENGINE->{'CGI'}->param('large_reserve_changes') =~ /\w/)
        {

            my $whereA = ' WHERE '.join(' AND ',@whereA);
#print '<br>claims search query A is: ' .
# '<br> SELECT G.CLAIM_ID, POLICY_NUMBER, IMT_CLAIM_NO, IMT_LINE_CODE, LOSS_DATE_TIME, PURGED_IND, CLAIM_STATUS, SUBRO_STATUS, SALV_STATUS, REINSURANCE_IND FROM CLAIMDB.CLM_GENERAL AS G'.$queryJoinsA.$whereA.' AND G.DATE_DELETED = \'9999-01-01 01:00:00.000000\' FETCH FIRST 251 ROWS ONLY';

            my $searchQueryA = $ENGINE->{'DBH'}->prepare('SELECT G.CLAIM_ID, POLICY_NUMBER, IMT_CLAIM_NO, IMT_LINE_CODE, LOSS_DATE_TIME, PURGED_IND, CLAIM_STATUS, SUBRO_STATUS, SALV_STATUS, REINSURANCE_IND, CLOSE_RECOVERABLE, G.AGENCY_NO FROM CLAIMDB.CLM_GENERAL AS G'.$queryJoinsA.$queryJoins.$whereA.' AND G.DATE_DELETED = \'9999-01-01 01:00:00.000000\' FETCH FIRST 251 ROWS ONLY') || error($ENGINE,'Search query prepare failed: '.$ENGINE->{'DBH'}->errstr);
            $searchQueryA->execute(@searchQueryArgs) || error($ENGINE,'Search query execute failed: '.$ENGINE->{'DBH'}->errstr);
            $resultsA = $searchQueryA->fetchall_arrayref({});
        }
#print '<br>claims search query is: ' .
# '<br> SELECT G.CLAIM_ID, POLICY_NUMBER, IMT_CLAIM_NO, IMT_LINE_CODE, LOSS_DATE_TIME, PURGED_IND, CLAIM_STATUS, SUBRO_STATUS, SALV_STATUS, REINSURANCE_IND FROM CLAIMDB.CLM_GENERAL AS G'.$queryJoins.$where.' AND G.DATE_DELETED = \'9999-01-01 01:00:00.000000\' FETCH FIRST 251 ROWS ONLY';



        $searchQuery->execute(@searchQueryArgs) || error($ENGINE,'Search query execute failed: '.$ENGINE->{'DBH'}->errstr);
        my $results = $searchQuery->fetchall_arrayref({});
        # die Dumper($queryJoins,$where,@searchQueryArgs);
        my @sortedResults = sort( {$b->{'LOSS_DATE_TIME'} cmp $a->{'LOSS_DATE_TIME'}} @$results,@$resultsA);
        my $paidQuery = $ENGINE->{'DBH'}->prepare('SELECT PAYMENT_AMT*100 AS PAYCENTS, LOSS_CODE, TYPE, RESERVED FROM CLAIMDB.CLM_CASH WHERE CLAIM_ID = ? AND LOSS_CODE NOT IN (\'79\',\'80\',\'81\',\'82\') AND TYPE <> \'R\' AND DATE_DELETED = \'9999-01-01 01:00:00.000000\'') || error($ENGINE,'Amount paid query prepare failed: '.$ENGINE->{'DBH'}->errstr);
        my $reserveQuery = $ENGINE->{'DBH'}->prepare('SELECT  SUM(RESERVE_AMT)*100 AS RESERVECENTS FROM CLAIMDB.CLM_RESERVES
        WHERE CLAIM_ID = ? AND TYPE NOT IN (\'B\',\'C\',\'S\',\'R\') AND LOSS_CODE NOT IN (\'79\',\'80\',\'81\',\'82\') AND DATE_DELETED = \'9999-01-01-01.00.00.000000\'') || error($ENGINE,$ENGINE->{'DBH'}->errstr());
        my $descQuery = $ENGINE->{'DBH'}->prepare('SELECT VARDATA FROM CLAIMDB.CLM_VARDATA WHERE CLAIM_ID = ? AND DATA_TYPE = \'DESCRIPT\' AND LINE_TYPE = \'D\' AND DATE_DELETED = \'9999-01-01 01:00:00.000000\'') || error($ENGINE,'Amount paid query prepare failed: '.$ENGINE->{'DBH'}->errstr);
        #        my $SDIPQuery = $ENGINE->{'DBH'}->prepare('SELECT S.SDIP_CODE, C.DEFINITION FROM CLAIMDB.CLM_SDIPS AS S
        #LEFT JOIN CLAIMDB.SDIP_CODES C ON C.SDIP_CODE=S.SDIP_CODE
        #WHERE CLAIM_ID = ? AND C.EFFECTIVE <= ? AND C.OBSOLETE > ? AND DATE_DELETED = \'9999-01-01 01:00:00.000000\'') || error($ENGINE,'SDIP query prepare failed: '.$ENGINE->{'DBH'}->errstr);
        my $itemIDQuery = $ENGINE->{'DBH'}->prepare('SELECT I.ITEM_ID FROM CLIENTDB.ITEM AS I INNER JOIN CLIENTDB.VERSION AS V ON I.ITEM_ID = V.ITEM_ID WHERE ID_NUMBER = ? AND V.STATUS <> \'Z\'') || error($ENGINE,'clientID query prepare failed: '.$ENGINE->{'DBH'}->errstr);
        my $insdQuery = $ENGINE->{'DBH'}->prepare('SELECT P.FIRST_NAME, P.LAST_NAME, P.BUSINESS_NAME FROM CLAIMDB.CLM_PARTIES AS P INNER JOIN CLAIMDB.CLM_PARTY_ROLES AS R ON P.PARTY_ID = R.PARTY_ID WHERE P.CLAIM_ID = ? AND R.ROLE = \'IN\' AND P.DATE_DELETED = \'9999-01-01 01:00:00.000000\' AND R.DATE_DELETED = \'9999-01-01 01:00:00.000000\'') || error($ENGINE,'insured query prepare failed: '.$ENGINE->{'DBH'}->errstr);
        $searchResults .= '<div class="searchTableDiv"><table class="footable table toggle-square toggle-medium"><thead>';
####### plug for mobile app

my @arrayOfClaims;
my $appOrigin = $ENGINE->{'CGI'}->param('appOrigin') || '';
#die $ENGINE->{'CGI'}->param('appOrigin');
if ($appOrigin eq lc("android") || $appOrigin eq lc("ios")){
    use JSON;
    use Data::Dumper;

    # die Dumper($results);

    for(my $j=0;$results->[$j];$j++){

        my $claimid = $results->[$j]->{'CLAIM_ID'};
        # die Dumper($claimid);
            $paidQuery->execute($claimid) || error($ENGINE,'Amount paid query execute failed: '.$ENGINE->{'DBH'}->errstr);
        my $paidResults = $paidQuery->fetchall_arrayref({});
        my $grossPay = 0;
        for my $p (@$paidResults)
        {
            if(defined($p->{'TYPE'}) && $p->{'TYPE'} eq 'G')
                {
                    $grossPay += $p->{'PAYCENTS'};

                }

            }
        if ($paymentsMadeChecked gt '' && $grossPay == 0)
        {
            next; }
        my $claimnum = $results->[$j]->{'IMT_CLAIM_NO'};
        my $DBlossDate = substr($results->[$j]->{'LOSS_DATE_TIME'},0,10);

        $insdQuery->execute($claimid) || error($ENGINE,'Insured query execute failed: '.$ENGINE->{'DBH'}->errstr);

        my $insured = $insdQuery->fetchall_arrayref({});

    # die Dumper($insured);

        for(my $x=0;$insured->[$x];$x++) {
            if($insured->[$x]->{'BUSINESS_NAME'}) {
                $name = $insured->[$x]->{'BUSINESS_NAME'};
                                if ($x == 0){

                $results->[$j]->{BUSINESS_NAME1} = $name;
            }
            if ($x == 1){

                $results->[$j]->{BUSINESS_NAME2} = $name;
            }
            if ($x == 2){

                $results->[$j]->{BUSINESS_NAME3} = $name;
            }
                # if($insured->[$x]->{'BUSINESS_NAME2'}) {
                #     $name .= "&nbsp;".$insured->[$x]->{'BUSINESS_NAME2'};
                # }
                $results->[$j]->{LAST_NAME2} = '';
                            $results->[$j]->{FIRST_NAME2} = '';
            }
            else {
                $name = $insured->[$x]->{'LAST_NAME'}.', '.$insured->[$x]->{'FIRST_NAME'};
                if ($x == 0){
                    $results->[$j]->{LAST_NAME} = $insured->[$x]->{'LAST_NAME'};
                    $results->[$j]->{FIRST_NAME} = $insured->[$x]->{'FIRST_NAME'};

                }
                if ($x > 0){
                    # die Dumper($insured->[1]->{'LAST_NAME'});
                    $results->[$j]->{LAST_NAME2} = $insured->[$x]->{'LAST_NAME'};
                    $results->[$j]->{FIRST_NAME2} = $insured->[$x]->{'FIRST_NAME'};
                }else{
                    $results->[$j]->{LAST_NAME2} = '';
                    $results->[$j]->{FIRST_NAME2} = '';
                }
            }
        }
        push @arrayOfClaims, $results->[$j];

    }
    # die Dumper($results);

    my $aref = \@arrayOfClaims;
    my $json = encode_json($aref);
    print $json;
    $ENGINE->{'DBH'}->rollback();
            $ENGINE->{'DBH'}->disconnect();
    # die Dumper (@sortedResults);

    exit;
}
####### end plug
        if(scalar(@sortedResults) == 1 && length($claim_number) >= 8)
        {
                my $lawAccess = 'N';
                my $polk_count = 0;
            # Load the claimid parameter for the claim we want to launch
            $ENGINE->{'CGI'}->param('claimid'=>$sortedResults[0]->{'CLAIM_ID'});
            # Set the load screen variable to Claims_Info so the menu will show the right tab highlighted
#            $ENGINE->{'load'} = 'Claims_Info';
            # Validate the claimid, this loads the engine with some information and verifies the user can access this claim
            if ($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} ne 'LawFirm')
            {
                    if(!validateClaimID($ENGINE))
                    {
                        $ENGINE->{'DBH'}->rollback();
                        $ENGINE->{'DBH'}->disconnect();
                        error(100);
                    }
            }
            else
            {   #if its a lawfirm and they cannot acces this claim
                #put out a special message instead of blowing up on them.
                if (defined $ENGINE->{'lawWarn'}
                        && $ENGINE->{'lawWarn'} gt '')
                {
                         $searchResults .=  $ENGINE->{'lawWarn'};
                }
                elsif(!validateClaimID($ENGINE))
                {
                             $searchResults .= '<tr><td colspan="10" class="warning">ATTENTION: Claim is not set up to allow lawfirm access.  Contact IMT Claims.</tr></td></thead>';
                     }
                     else
                     {
                         $lawAccess = 'Y';
                     }
            }
            if (($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'LawFirm'
                    && $lawAccess eq 'Y')
                    ||
                    ($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} ne 'LawFirm'))
            {
                    if(defined($ENGINE->{'SystemLock'}) && $ENGINE->{'SystemLock'} eq 'Y')
                    {
                         #skip the next ifs
                         push(@warnings,'The Claims system is locked tighter than a drum.  Please try again later.');
                    $output .= '<div style="background-color:white;margin-left:0.5em;margin-right:0.5em"><ul><li>'.join('</li><li>',@warnings).'</li></ul></div>';
                    }
	                elsif($sortedResults[0]->{'AGENCY_NO'} eq '674008'){
	                    my $polk_agent = 0;
	                   if($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'Agent')
	                   {
	                     if(grep{$_ eq '674008'}keys %{$ENGINE->{'AUTH'}->{'AGENCY_ACCESS'}}){
	                       $polk_agent = 1;
	                     }
	                   }
	                    if($ENGINE->{'AUTH'}->{'PolkAgency_Access'} ne '1' && $polk_agent != 1){
	                      $polk_count++;
	                      if($polk_count == 1){
	                        $searchResults .= '<tr><td colspan="10" class="warning">ATTENTION: Some records are hidden because you do not have authority to view them.</tr></td></thead>';
	                      }
	                      push(@warnings,'You do not have authority to view records for this agency.');
	                      $polkWarning = 'Some records are hidden because you do not have authority to view them.';
	                    }
	                    elsif($ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} eq 'P')
	                    {
	                        # Set the load screen variable to Claims_Info so the menu will show the right tab highlighted
	                        $ENGINE->{'load'} = 'Claims_Details';
	                        # Finally call the Claims_Info loadScreen function to generate the screen
	                        require "Claims_Details.pm";
	                        Claims_Details::loadScreen($ENGINE);
	                        # Return to stop executing this function.
	                        return;
	                    }
	                    else
	                    {
	                        # Set the load screen variable to Claims_Info so the menu will show the right tab highlighted
	                        $ENGINE->{'load'} = 'Claims_Info';
	                        # Finally call the Claims_Info loadScreen function to generate the screen
	                        require "Claims_Info.pm";
	                        Claims_Info::loadScreen($ENGINE);
	                        # Return to stop executing this function.
	                        return;
	                    }
	                  }
                    elsif($ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} eq 'P')
                    {
                        # Set the load screen variable to Claims_Info so the menu will show the right tab highlighted
                        $ENGINE->{'load'} = 'Claims_Details';
                        # Finally call the Claims_Info loadScreen function to generate the screen
                        require "Claims_Details.pm";
                        Claims_Details::loadScreen($ENGINE);
                        # Return to stop executing this function.
                        return;
                    }
                    else
                    {
                        # Set the load screen variable to Claims_Info so the menu will show the right tab highlighted
                        $ENGINE->{'load'} = 'Claims_Info';
                        # Finally call the Claims_Info loadScreen function to generate the screen
                        require "Claims_Info.pm";
                        Claims_Info::loadScreen($ENGINE);
                        # Return to stop executing this function.
                        return;
                    }
            }
        }
        elsif(scalar(@sortedResults) > 0)
        {

                if (defined $ENGINE->{'lawWarn'}
                        && $ENGINE->{'lawWarn'} gt '')
            {
                         $searchResults .=  $ENGINE->{'lawWarn'};
            }
            if(scalar(@sortedResults)>250)
              { $searchResults .= '<tr><td colspan="10" class="warning">ATTENTION: Maximum number of results returned. You may want to refine your search criteria.</td></tr>'; }
              if($polkWarning ne ''){
                  $searchResults .= '<tr><td colspan="10" class="warning">ATTENTION: Some records may be hidden because you do not have authority to view them.</tr></td></thead>';
              }
                                                                                                            #<th onclick="tableSort(this,\'searchResults\',1,\'a\')">SDIP</th>
#            $searchResults .= '<tr><th onclick="tableSort(this,\'searchResults\',0,\'a\')">Claim Number</th><th onclick="tableSort(this,\'searchResults\',1,\'a\')">Insured Name</th><th onclick="tableSort(this,\'searchResults\',2,\'a\')">Policy #</th><th onclick="tableSort(this,\'searchResults\',3,\'d\')">Loss&nbsp;Date</th><th class="n" onclick="tableSort(this,\'searchResults\',4,\'n\')" data-hide="phone,tablet">Net Paid</th><th class="n" onclick="tableSort(this,\'searchResults\',5,\'n\')" data-hide="phone,tablet">Gross Paid</th><th class="n" onclick="tableSort(this,\'searchResults\',6,\'n\')" data-hide="phone,tablet">Net Reserved</th><th onclick="tableSort(this,\'searchResults\',7,\'a\')" data-hide="phone">Description</th></tr></thead><tbody id="searchResults">';
                        $searchResults .= '<tr><th>Claim #</th><th>Insured Name</th><th>Policy #</th><th data-sort-initial="descending">Loss&nbsp;Date</th><th class="n" data-hide="phone,tablet" data-type="numeric">Gross Paid</th><th data-hide="phone,tablet" data-type="numeric">Net Paid</th><th class="n" data-hide="phone,tablet" data-type="numeric">Net Reserved</th><th data-hide="phone">Description</th></tr></thead><tbody id="searchResults">';
#   $output .= '<table id="anyid" class="tableSortable" style="line-height: 1em;width:100%;padding:0.1em"><tbody><tr><th>Claim Number</th><th>SDIP</th><th>Insured Name</th><th>Policy #</th><th>Loss&nbsp;Date</th><th>Amt. Paid</th><th>Description</th></tr>';


            my $class = '';
            my %printed = ();
            my $polk_count = 0;
            for my $r (@sortedResults)
            {
              #/#
              if($r->{'AGENCY_NO'} eq '674008'){
                my $polk_agent = 0;
               if($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'Agent')
               {
                 if(grep{$_ eq '674008'}keys %{$ENGINE->{'AUTH'}->{'AGENCY_ACCESS'}}){
                   $polk_agent = 1;
                 }
               }
                if($ENGINE->{'AUTH'}->{'PolkAgency_Access'} ne '1' && $polk_agent != 1){
                  $polk_count++;
                  if($polk_count == 1){
                    $searchResults .= '<h6>ATTENTION: Some records are hidden because you do not have authority to view them.</h6>';
                  }
                  push(@warnings,'You do not have authority to view records for this agency.');
                  $r = undef;
                  $polkWarning = 'Some records are hidden because you do not have authority to view them.';
                  next;
                }
              }
                if(defined($printed{$r->{'CLAIM_ID'}}))
                  { next; }
                else
                  { $printed{$r->{'CLAIM_ID'}} = 1; }
                if($class eq '')
                  { $class = ' class="altRow"'; }
                else
                  { $class = ''; }
                my $claimid = $r->{'CLAIM_ID'};
                my $claimnum = $r->{'IMT_CLAIM_NO'};
                my $DBlossDate = substr($r->{'LOSS_DATE_TIME'},0,10);
#                $SDIPQuery->execute($claimid,$DBlossDate,$DBlossDate) || error($ENGINE,'SDIP query execute failed: '.$ENGINE->{'DBH'}->errstr);
#                my $SDIPResults = $SDIPQuery->fetchall_arrayref({});
#                my $SDIP = '';
#                for my $r (@$SDIPResults)
#                {
#                    $r->{'DEFINITION'} =~ s/&/&amp;/g;
#                    $r->{'DEFINITION'} =~ s/</&lt;/g;
#                    $SDIP .='<span class="more-info" title="'.$r->{'DEFINITION'}.'">'.$r->{'SDIP_CODE'}.'</span> ';
#                }
                $insdQuery->execute($claimid) || error($ENGINE,'Insured query execute failed: '.$ENGINE->{'DBH'}->errstr);

                my $insdResults = $insdQuery->fetchall_arrayref({});
                my $insName = '';
                for my $r (@$insdResults)
                {
                 if($r->{'FIRST_NAME'} =~ /\w/ || $r->{'LAST_NAME'} =~ /\w/)
                   { $insName .= $r->{'LAST_NAME'}.',&nbsp;'.$r->{'FIRST_NAME'}.'<br />'; }
                 elsif($r->{'BUSINESS_NAME'} =~ /\w/)
                   { $insName .= $r->{'BUSINESS_NAME'}.'<br />'; }
                }
                $insName =~ s/<br \/>$//;
                my $polNum = $r->{'POLICY_NUMBER'};
                if($r->{'IMT_LINE_CODE'} =~ /301|302|331|332/)
                  { $polNum =~ s/^12|^14|^26|^40|^48//g; }
                $itemIDQuery->execute($polNum) || error($ENGINE,'clientID query execute failed: '.$ENGINE->{'DBH'}->errstr);
                my $clientIDResult = $itemIDQuery->fetchall_arrayref({});

                if($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} ne 'LawFirm')
                {
                    if(scalar(@$clientIDResult) > 0 && defined($clientIDResult->[0]->{'ITEM_ID'}))
                    {
                        $polNum = '<a href="../Client/'.($ENGINE->{'AUTH'}->{'ClientEngine'} || 'ClientEngine.pl').'?load=ClientPView&amp;id='.$clientIDResult->[0]->{'ITEM_ID'}.'">'.$polNum.'</a>';
                        if (check_for_platform_version({dbh => $ENGINE->{'DBH'}, item_id => $clientIDResult->[0]->{'ITEM_ID'}})) {
                            $polNum = '<a href="'.get_policy_info_page_url({policy_number => $polNum}).'" target="_blank">'.$polNum.'</a>';
                        }
                    }
                }
                my $date = $r->{'LOSS_DATE_TIME'};
                my $initial_date = '\''.$date.'\'';
                if($date eq '9999-01-01 01:00:00.000000')
                  { $date = 'Unknown'; }
                else
                  { $date = substr($date,5,2).'/'.substr($date,8,2).'/'.substr($date,0,4); }
                $paidQuery->execute($claimid) || error($ENGINE,'Amount paid query execute failed: '.$ENGINE->{'DBH'}->errstr);
                my $paidResults = $paidQuery->fetchall_arrayref({});
                my $netPaid = '$0.00';
                my $grossPaid = '$0.00';
                my $netPay = 0;
                my $grossPay = 0;
                my $reservedPay = 0;
                for my $p (@$paidResults)
                {
                    $netPay += $p->{'PAYCENTS'};
                    if(defined($p->{'TYPE'}) && $p->{'TYPE'} eq 'G')
                    {
                        $grossPay += $p->{'PAYCENTS'};
                        if(defined($p->{'RESERVED'}) && $p->{'RESERVED'} eq 'Y')
                          { $reservedPay += $p->{'PAYCENTS'}; }
                    }
                }
                if($netPay != 0)
                  { $netPaid = '$'.CommaFormatted(sprintf("%0.2f",($netPay/100))); }
                if($grossPay != 0)
                  { $grossPaid = '$'.CommaFormatted(sprintf("%0.2f",($grossPay/100))); }
                #This if statement was put in for bug 107070.
                if($paymentsMadeChecked gt '' && $grossPay == 0)
                {
                 next; }
                my $reserve = 0;
                my $value = 0;
                if($r->{'PURGED_IND'} eq 'N')
                {
                    $reserveQuery->execute($claimid) || error($ENGINE,'Amount paid query execute failed: '.$ENGINE->{'DBH'}->errstr);
                    my $reserveResults = $reserveQuery->fetchall_arrayref({});

                    if(scalar(@$reserveResults)>0)
                      { $reserve = $reserveResults->[0]->{'RESERVECENTS'}; }
                    $reserve -= $reservedPay;
                }
                $value = $reserve;
                $reserve = '$'.CommaFormatted(sprintf("%0.2f",($reserve/100)));
                if($reserve eq '$0.00')
                {
                    my $subroStatus = $r->{'SUBRO_STATUS'} || '';
                    my $salvStatus = $r->{'SALV_STATUS'} || '';
                    my $reinStatus = $r->{'REINSURANCE_IND'} || '';
                    my $recovStatus = $r->{'CLOSE_RECOVERABLE'} || '';
                    if($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'Internal' &&
                       ($subroStatus eq 'P' || $salvStatus eq 'P' || $reinStatus eq 'P'))
                    {
                        $reserve = 'Pending ';
                        if($subroStatus eq 'P')
                          { $reserve .= 'Subr, '; }
                        if($salvStatus eq 'P')
                          { $reserve .= 'Salv, '; }
                        if($reinStatus eq 'P')
                          { $reserve .= 'Rein, '; }
                        if($recovStatus eq 'P')
                          { $reserve .= 'Recov, '; }
                        chop($reserve);
                        chop($reserve);
                        $value = -3;
                    }
                    elsif($subroStatus eq 'P')
                      { $reserve = 'Pending Subr';
                        $value = -3;}
                    elsif($r->{'PURGED_IND'} eq 'Y'
                            || $r->{'PURGED_IND'} eq 'R')
                      { $reserve = 'Archived';
                        $value = -1;}
                    elsif($r->{'CLAIM_STATUS'} =~ /C/)
                      { $reserve = 'Closed';
                        $value = -2;}
                }
                $descQuery->execute($claimid) || error($ENGINE,'Amount paid query execute failed: '.$ENGINE->{'DBH'}->errstr);
                my $descResults = $descQuery->fetchall_arrayref({});
                my $desc = '';
                for my $d (@$descResults)
                  { $desc .= $d->{'VARDATA'}.' '; }
                chop($desc);
                $desc =~ s/&/&amp;/g;
                $desc =~ s/</&lt;/g;
                my $desktop_or_mobile = $ENGINE->{'CGI'}->param('desktop_or_mobile') eq 'mobile' ? "&amp;desktop_or_mobile=mobile" : '';
                if($r->{'CLAIM_STATUS'} eq 'P')
                { $searchResults .= "<tr><td class='footable-first-column'><a href=\"$action?claimid=$claimid&amp;load=Claims_Details&amp;sessionID=$sessID\">$claimnum</a></td><td>$insName</td><td>$polNum</td><td data-value=\"$initial_date\">$date</td><td class='n' style='display:table-cell'>$grossPaid</td><td class='n' style='display:table-cell'>$netPaid</td><td class='n' style='display:table-cell' data-value=".$value.">$reserve</td><td class='footable-last-column'>$desc</td></tr>"; }
                else                                                                                                               #<td>$SDIP</td>
                { $searchResults .= "<tr><td class='footable-first-column'><a href=\"$action?claimid=$claimid&amp;load=Claims_Info&amp;sessionID=$sessID$desktop_or_mobile\">$claimnum</a></td><td>$insName</td><td>$polNum</td><td data-value=\"$initial_date\">$date</td><td class='n' style='display:table-cell'>$grossPaid</td><td class='n' style='display:table-cell'>$netPaid</td><td class='n' style='display:table-cell' data-value=".$value.">$reserve</td><td class='footable-last-column'>$desc</td></tr>"; }
                #$searchResults .= "<tr$class><td><a href=\"$action?claimid=$claimid&amp;load=Claims_Info&amp;sessionID=$sessID\">$claimnum</a></td><td>$SDIP</td><td>$insName</td><td>$polNum</td><td>$date</td><td>$paid</td><td>$desc</td></tr>";
            }
            $searchResults .= '</tbody>';
           # $output .= '<table style="line-height: 1em;width:100%"><tr><th>Claim Number</th><th>SDIP</th><th>Insured Name</th><th>Policy #</th><th>Loss Date</th><th>Amt. Paid</th><th>Description</th></tr>',
           #   $searchResults,'</table>';
        }
        elsif($noPolicyAccess)
          {
                  $searchResults .= '<tr><td colspan="10" class="warning">ATTENTION: You do not have access permissions for any policy line.</tr></td></thead>'; }
        else
          {
              $searchResults .= '<tr><td colspan="10" class="warning">ATTENTION: No results matching your search criteria were found.</tr></td></thead>';
          }
#/#        die Data::Dumper::Dumper($searchResults);
        # if($polkWarning ne '' && scalar(@sortedResults) == 1){
        #   $output .= '<div class="errorInfo"><ul><li>'.join('</li><li>',@warnings).'</li></ul></div>';
        #   $searchResults = '';
        # }
        $output .= $searchResults.'</table></div></fieldset>';
    }
    my @footLinks = ();
   # $output .= '<div class="footer"><a href="Claims_Engine.pl">In Basket</a> | <a href="Claims_Engine.pl">Administration</a> | <a href="claims_engine.pl">Reinstate Purged Claim</a></div></body></html>';
    if($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} ne 'LawFirm')
    {
            @footLinks = ('<a href="../Client/'.($ENGINE->{'AUTH'}->{'ClientEngine'} || 'ClientEngine.pl').'">Client</a>');
    }

    if($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'Internal')
    {
        push(@footLinks,'<a href="Claims_Engine.pl?load=Claims_IRS&amp;sessionID='.$sessID.'">IRS Search</a>');
        if(defined($ENGINE->{'AUTH'}->{'Claims_Access'}) && $ENGINE->{'AUTH'}->{'Claims_Access'} eq 'A')
          {
              push(@footLinks,'<a href="Claims_InboxManager.pl">Claims Inbox</a>');
              push(@footLinks,'<a href="Claims_Tracker.pl">Tracker</a>');
          }
    }
    if(defined($ENGINE->{'AUTH'}->{'Claims_Admin'}) && $ENGINE->{'AUTH'}->{'Claims_Admin'} eq 'I')
      { push(@footLinks,'<a href="Claims_Engine.pl?load=Claims_Reports&amp;sessionID='.$sessID.'">Administration</a>'); }

    if(defined($ENGINE->{'AUTH'}->{'Claims_BulkPay'}) && $ENGINE->{'AUTH'}->{'Claims_BulkPay'} =~ /A|I/)
      { push(@footLinks,'<a href="Claims_Bulk_Pay_Admin.php?sessionID='.$sessID.'">Bulk Pay</a>'); }

    #show link to glass claims processing only to those users with authority
        if(defined $ENGINE->{'AUTH'}->{'Claims_Glass'}
                && $ENGINE->{'AUTH'}->{'Claims_Glass'} eq 'A')
      { push(@footLinks,'<a href="Claims_Engine.pl?load=Claims_GlassReports&amp;sessionID='.$sessID.'">Glass Claims</a>'); }

#    if($ENGINE->{'AUTH'}->{'id'} eq 'CV21724' || $ENGINE->{'AUTH'}->{'id'} eq 'CV21164' || $ENGINE->{'AUTH'}->{'id'} eq 'CI21520' || $ENGINE->{'AUTH'}->{'id'} eq 'CV21768' || $ENGINE->{'AUTH'}->{'id'} eq  'CV21750' || $ENGINE->{'AUTH'}->{'id'} eq  'CV21241')
#      { push(@footLinks,'<a href="'.$action.'?load=Claims_Stub&amp;sessionID='.$sessID.'">Auto Stub</a>'); } #<div style="float:right;margin-right:1em">
#    if($ENGINE->{'AUTH'}->{'id'} eq 'CV21724' || $ENGINE->{'AUTH'}->{'id'} eq 'CV21164' || $ENGINE->{'AUTH'}->{'id'} eq 'CV21690' || $ENGINE->{'AUTH'}->{'id'} eq 'CV21768' || $ENGINE->{'AUTH'}->{'id'} eq  'CV21750' || $ENGINE->{'AUTH'}->{'id'} eq 'CI21520' )
#      { push(@footLinks,'<a href="'.$action.'?load=IMTHoStub&amp;sessionID='.$sessID.'">HO Stub</a>'); }
#    if($ENGINE->{'AUTH'}->{'id'} eq 'CV21724' || $ENGINE->{'AUTH'}->{'id'} eq 'CV21164' || $ENGINE->{'AUTH'}->{'id'} eq 'CI21520' || $ENGINE->{'AUTH'}->{'id'} eq 'CV21768' || $ENGINE->{'AUTH'}->{'id'} eq  'CV21750' )
#      { push(@footLinks,'<a href="'.$action.'?load=IMTFLStub&amp;sessionID='.$sessID.'">Farm Liability Stub</a>'); }
#    if($ENGINE->{'AUTH'}->{'id'} eq 'CV21724' || $ENGINE->{'AUTH'}->{'id'} eq 'CV21164' || $ENGINE->{'AUTH'}->{'id'} eq 'CI21520' || $ENGINE->{'AUTH'}->{'id'} eq 'CV21768' || $ENGINE->{'AUTH'}->{'id'} eq  'CV21750')
#      { push(@footLinks,'<a href="'.$action.'?load=IMTBOStub&amp;sessionID='.$sessID.'">BOP Stub</a>'); }
#    if($ENGINE->{'AUTH'}->{'id'} eq 'CV21724' || $ENGINE->{'AUTH'}->{'id'} eq 'CV21164' || $ENGINE->{'AUTH'}->{'id'} eq 'CI21520' || $ENGINE->{'AUTH'}->{'id'} eq 'CV21768' || $ENGINE->{'AUTH'}->{'id'} eq  'CV21750')
#      { push(@footLinks,'<a href="'.$action.'?load=IMTCPStub&amp;sessionID='.$sessID.'">CP Stub</a>'); }
#    if($ENGINE->{'AUTH'}->{'id'} eq 'CV21724' || $ENGINE->{'AUTH'}->{'id'} eq 'CV21164' || $ENGINE->{'AUTH'}->{'id'} eq 'CI21520' || $ENGINE->{'AUTH'}->{'id'} eq 'CV21768' || $ENGINE->{'AUTH'}->{'id'} eq  'CV21750')
#      { push(@footLinks,'<a href="'.$action.'?load=IMTGLStub&amp;sessionID='.$sessID.'">GL Stub</a>'); }
#    if($ENGINE->{'AUTH'}->{'id'} eq 'CV21724' || $ENGINE->{'AUTH'}->{'id'} eq 'CV21164' || $ENGINE->{'AUTH'}->{'id'} eq 'CI21520' || $ENGINE->{'AUTH'}->{'id'} eq 'CV21768' || $ENGINE->{'AUTH'}->{'id'} eq  'CV21750')
#      { push(@footLinks,'<a href="'.$action.'?load=IMTWCStub&amp;sessionID='.$sessID.'">WC Stub</a>'); }
#    if($ENGINE->{'AUTH'}->{'id'} eq 'CV21724' || $ENGINE->{'AUTH'}->{'id'} eq 'CV21164' || $ENGINE->{'AUTH'}->{'id'} eq 'CI21520' || $ENGINE->{'AUTH'}->{'id'} eq 'CV21768' || $ENGINE->{'AUTH'}->{'id'} eq  'CV21750' || $ENGINE->{'AUTH'}->{'id'} eq  'CV21241')
#      { push(@footLinks,'<a href="'.$action.'?load=WADAUPSStub&amp;sessionID='.$sessID.'">Wadena Auto Stub</a>'); }
#    if($ENGINE->{'AUTH'}->{'id'} eq 'CV21724' || $ENGINE->{'AUTH'}->{'id'} eq 'CV21164' || $ENGINE->{'AUTH'}->{'id'} eq 'CI21520' || $ENGINE->{'AUTH'}->{'id'} eq 'CV21768' || $ENGINE->{'AUTH'}->{'id'} eq  'CV21750' || $ENGINE->{'AUTH'}->{'id'} eq  'CV21241')
#      { push(@footLinks,'<a href="'.$action.'?load=IMTCVStub&amp;sessionID='.$sessID.'">Commercial Auto Stub</a>'); }
#    if($ENGINE->{'AUTH'}->{'id'} eq 'CV21724' || $ENGINE->{'AUTH'}->{'id'} eq 'CV21164' || $ENGINE->{'AUTH'}->{'id'} eq 'CI21520' || $ENGINE->{'AUTH'}->{'id'} eq 'CV21768' || $ENGINE->{'AUTH'}->{'id'} eq  'CV21750' || $ENGINE->{'AUTH'}->{'id'} eq  'CV21241')
#      { push(@footLinks,'<a href="'.$action.'?load=IMTARStub&amp;sessionID='.$sessID.'">Artisans Stub</a>'); }
#    if($ENGINE->{'AUTH'}->{'id'} eq 'CV21724' || $ENGINE->{'AUTH'}->{'id'} eq 'CV21164' || $ENGINE->{'AUTH'}->{'id'} eq 'CI21520' || $ENGINE->{'AUTH'}->{'id'} eq 'CV21768' || $ENGINE->{'AUTH'}->{'id'} eq  'CV21750' || $ENGINE->{'AUTH'}->{'id'} eq  'CV21241')
#      { push(@footLinks,'<a href="'.$action.'?load=IMTIMStub&amp;sessionID='.$sessID.'">Inland Marine Stub</a>'); }
#    if($ENGINE->{'AUTH'}->{'id'} eq 'CV21724' || $ENGINE->{'AUTH'}->{'id'} eq 'CV21164' || $ENGINE->{'AUTH'}->{'id'} eq 'CI21520' || $ENGINE->{'AUTH'}->{'id'} eq 'CV21768' || $ENGINE->{'AUTH'}->{'id'} eq  'CV21750' || $ENGINE->{'AUTH'}->{'id'} eq  'CV21241')
#      { push(@footLinks,'<a href="'.$action.'?load=WADBOATStub&amp;sessionID='.$sessID.'">Wadena Boat Stub</a>'); }
#    if($ENGINE->{'AUTH'}->{'id'} eq 'CV21724' || $ENGINE->{'AUTH'}->{'id'} eq 'CV21164' || $ENGINE->{'AUTH'}->{'id'} eq 'CI21520' || $ENGINE->{'AUTH'}->{'id'} eq 'CV21768' || $ENGINE->{'AUTH'}->{'id'} eq  'CV21750' || $ENGINE->{'AUTH'}->{'id'} eq  'CV21241' || $ENGINE->{'AUTH'}->{'id'} eq  'CV21690')
#      { push(@footLinks,'<a href="'.$action.'?load=IMTDFStub&amp;sessionID='.$sessID.'">Dwelling Fire Stub</a>'); }
#    if($ENGINE->{'AUTH'}->{'id'} eq 'CV21724' || $ENGINE->{'AUTH'}->{'id'} eq 'CV21164' || $ENGINE->{'AUTH'}->{'id'} eq 'CI21520' || $ENGINE->{'AUTH'}->{'id'} eq 'CV21768' || $ENGINE->{'AUTH'}->{'id'} eq  'CV21750' || $ENGINE->{'AUTH'}->{'id'} eq  'CV21241' || $ENGINE->{'AUTH'}->{'id'} eq  'CI21240')
#      { push(@footLinks,'<a href="'.$action.'?load=MoneyStub&amp;sessionID='.$sessID.'">Money Stub</a>'); }
#    if($ENGINE->{'AUTH'}->{'id'} eq 'CV21164')
#      {
#        push(@footLinks,'<a href="BankDraftInterface.pl?sessionID='.$sessID.'">Bank Drafts</a>');
#        push(@footLinks,'<a href="'.$action.'?load=NEWBOPStub&amp;sessionID='.$sessID.'">New Bop Stub</a>');
#      }
#    if($ENGINE->{'AUTH'}->{'id'} eq 'CI21240')
#      { push(@footLinks,'<a href="TransPac_Notes.pl?sessionID='.$sessID.'">Transpac Notes</a>'); }
#    if($ENGINE->{'AUTH'}->{'id'} eq 'CI21240')
#      { push(@footLinks,'<a href="TransPac_Dates.pl?sessionID='.$sessID.'">Transpac Dates</a>'); }
#    if($ENGINE->{'AUTH'}->{'id'} eq 'CI21240')
#      { push(@footLinks,'<a href="TransPac_LossNotice.pl?sessionID='.$sessID.'">Transpac LossNot</a>'); }
#    if($ENGINE->{'AUTH'}->{'id'} eq 'CI21240')
#      { push(@footLinks,'<a href="TransPac_Claimant.pl?sessionID='.$sessID.'">Transpac Clmnt</a>'); }
#    if($ENGINE->{'AUTH'}->{'id'} eq 'CI21240')
#      { push(@footLinks,'<a href="TransPac_Transaction.pl?sessionID='.$sessID.'">Transpac Trans</a>'); }


#    $output .= '<div class="footer">'.join(' | ',@footLinks).'</div></body></html>';

         $output .= '<div class="footer">'.join(' | ',@footLinks).'</div>';
     my $searchFooter = getFooter($ENGINE);
     $output .= <<EOF;
<script type="text/javascript">
    \$(function () {
        \$('.table').footable();
    });
    \$('.sort-column').click(function (e) {
                e.preventDefault();

                //get the footable sort object
                var footableSort = \$('table').data('footable-sort');

                //get the index we are wanting to sort by
                var index = \$(this).data('index');

                footableSort.doSort(index, 'toggle');
            });
</script>
EOF
     $output .= $searchFooter . '</div></div></body></html>';
#   die Data::Dumper::Dumper($output);
    print $output;
}

sub saveScreen
{
    my $ENGINE = shift;

    my $sessID = $ENGINE->{'SESSION'}->{'sessionID'};
    my $action = $ENGINE->{'ACTION'};

    my $error = $ENGINE->{'error'};

    my $name = $ENGINE->{'AUTH'}->{'name'};
    my $claimId = '';

    if(editScreen($ENGINE))
    {
        my $errInd = '';

        my $transType = 'NEW';
        my $companyNo = '';

        if(defined($ENGINE->{'Inquiry'}->{'GLASS_CLAIM'}) && $ENGINE->{'Inquiry'}->{'GLASS_CLAIM'} eq 'N')
          { $transType = 'GLASS'; }
        elsif(defined($ENGINE->{'Inquiry'}->{'GLASS_CLAIM_ERR'}) && $ENGINE->{'Inquiry'}->{'GLASS_CLAIM_ERR'} eq 'E')
          { $transType = 'GLASSERR'; }

#        if(defined($ENGINE->{'Inquiry'}->{'MANUAL_ENTRY'}) && $ENGINE->{'Inquiry'}->{'MANUAL_ENTRY'} eq 'Y')
#        { $errInd = ''; }
#        else
#        {
        my $PIReturn = PolicyInterface($ENGINE,{'polNo'=>$ENGINE->{'Inquiry'}->{'POLICY_NUMBER'},'lossDate'=>$ENGINE->{'Inquiry'}->{'LOSS_DATE'},'claimid'=>$claimId,'trans'=>$transType});

        $errInd = $PIReturn->{'errInd'};
        $claimId = $PIReturn->{'claimId'};
        $ENGINE->{'driverRef'} = $PIReturn->{'driverRef'};
        $ENGINE->{'vehRef'} = $PIReturn->{'vehRef'};
        my $progErr = $PIReturn->{'progErr'};

        if(!($errInd =~ /\w/))
        {
            ## validateClaimID reads the claimid from CGI, so insert it here
            $ENGINE->{'CGI'}->param('claimid'=>$claimId);
            #Reload $ENGINE->{'claimGeneral'}
            validateClaimID($ENGINE);

            #build the claims folders for document processing
            my $clmSTH = $ENGINE->{'DBH'}->prepare('select
                            g.imt_claim_no,
                            fgid,
                            IMT_LINE_CODE
                    from
                            claimdb.clm_general as g
                    left join
                            claimdb.clm_files as f
                    on
                            g.claim_id = f.claim_id
                    where
                            g.claim_id = ?') || die ($ENGINE->{'DBH'}->errstr());
            $clmSTH->execute($claimId) || die ($ENGINE->{'DBH'}->errstr());
            my $clmResult = $clmSTH->fetchall_arrayref({});
            my $FGID = 1;
            my $lineCode = $clmResult->[0]->{'IMT_LINE_CODE'};
            my $PFID = 1;
            my $claim = $clmResult->[0]->{'IMT_CLAIM_NO'};

            my $results = createFileGroup($ENGINE,{'FILENAME'=>$claim});
            $FGID = $results->{'FGID'};
            my $FID = $results->{'FID'};
            my $clmFGIDSTH = $ENGINE->{'DBH'}->prepare('insert into claimdb.clm_files (claim_id,fgid) values (?,?)') || die ($ENGINE->{'DBH'}->errstr());
            $clmFGIDSTH->execute($claimId,$FGID) || die ($ENGINE->{'DBH'}->errstr());

            # Add default folders:
            defaultFolders($ENGINE,$lineCode,$FID,$FGID);

            #load the claims details page
            $ENGINE->{'load'} = 'Claims_Details';

            #Update general IMT_LINE_CODE for GL policies.
            my $policyNumber = $ENGINE->{'Inquiry'}->{'POLICY_NUMBER'}||'';
            my $policyPrefix2 = substr($policyNumber,0,2);
            if($policyPrefix2 =~ /GL/ && (defined($ENGINE->{'claimGeneral'}->{'SYSTEM_IND'} ne 'N') && $ENGINE->{'claimGeneral'}->{'SYSTEM_IND'} ne 'N'))
            {
                    my $generalUpdate = $ENGINE->{'DBH'}->prepare(
                       'UPDATE CLAIMDB.CLM_GENERAL
                           SET IMT_LINE_CODE = ?
                         WHERE CLAIM_ID = ?') || $error->($ENGINE);
                $generalUpdate->execute($ENGINE->{'CGI'}->param('GLLineList'),
                                 $claimId) || $error->($ENGINE);
            }

            if($transType eq 'GLASSERR')
            {
                my $sdipInsert = $ENGINE->{'DBH'}->prepare
                    ('INSERT INTO CLAIMDB.CLM_SDIPS
                            (CLM_SDIP_ID,
                            CLAIM_ID,
                            SDIP_CODE)
                        VALUES
                            (NEXT VALUE FOR CLAIMDB.CLM_SDIP_ID_SEQ,
                            ?,
                            ?)') || $error->($ENGINE);
                $sdipInsert->execute(
                    $claimId,
                    '05'
                    ) || $error->($ENGINE);

                ## Rep Assigned
                my $repInsert = $ENGINE->{'DBH'}->prepare
                        ('INSERT INTO CLAIMDB.CLM_REP_ASSIGNED
                                                        (CLM_REP_ASGN_ID,
                                                        CLAIM_ID,
                                                        DATE_ASSIGNED,
                                                        USER_KEY)
                                                VALUES
                                                        (NEXT VALUE FOR CLAIMDB.CLM_REP_ASGN_ID_SEQ,
                                                        ?,
                                                        CURRENT TIMESTAMP,
                                                        ?)') || $error->($ENGINE);

                       #$repInsert->execute($claimId,NETWORK_GLASS_ADJUSTER) || $error->($ENGINE);
                $repInsert->execute(
                        $claimId,
                        6327
                        ) || $error->($ENGINE);

            }
            if(defined($ENGINE->{'CGI'}->param('submitGrace')) && $ENGINE->{'CGI'}->param('submitGrace') eq 'Y')
            {
                my ($curDay, $curMonth, $curYear) = (localtime)[3,4,5];
                $curYear = $curYear+1900;
                $curDay = length($curDay)<2 ? '0'.$curDay : $curDay;
                $curMonth = $curMonth+1;
                $curMonth = length($curMonth)<2 ? '0'.$curMonth : $curMonth;
                my $currentDate = $curMonth.'/'.$curDay.'/'.$curYear;
                my $graceCancelMessage = 'As of '.$currentDate.' Billing account is currently past due.';
                my $polFlagsInsert = $ENGINE->{'DBH'}->prepare
                    ('INSERT INTO CLAIMDB.CLM_POL_FLAGS
                            (CLM_POL_FLAGS_ID,
                            CLAIM_ID,
                            CLM_FLAG_ID,
                            CLM_FLAG_DATE,
                            CLM_FLAG_DETAILS,
                            DATE_DELETED)
                        VALUES
                            (NEXT VALUE FOR CLAIMDB.CLM_POL_FLAGS_ID_SEQ,
                            ?,?,CURRENT TIMESTAMP,?,?)') || $error->($ENGINE);
                $polFlagsInsert->execute(
                    $claimId,'445',$graceCancelMessage,'9999-01-01 01:00:00.000000') || $error->($ENGINE);
            }
        }
        elsif($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'Internal' && ($errInd eq 'F' || $errInd eq 'N' || $errInd eq 'C'))
        {
            if(defined($ENGINE->{'Inquiry'}->{'MANUAL_ENTRY'}) && $ENGINE->{'Inquiry'}->{'MANUAL_ENTRY'} eq 'Y')
            {
                my $errInd;

                my $return = CLDCLMNO($ENGINE,{'polNo'=>$ENGINE->{'Inquiry'}->{'POLICY_NUMBER'}});

                $errInd = $return->{'errInd'};
                $claimId = $return->{'claimId'};
                my $lineOfBusiness = $ENGINE->{'CGI'}->param('lobList')||'';
                my $policyState = $ENGINE->{'CGI'}->param('polStateList')||'';
                my $lossDate = $ENGINE->{'Inquiry'}->{'DB_LOSS_DATE'};
                my $policyNumber = $ENGINE->{'Inquiry'}->{'POLICY_NUMBER'}||'';
                my $policyPrefix2 = substr($policyNumber,0,2);
                my $policyPrefix3 = substr($policyNumber,0,3);
                if($policyPrefix3 =~ /WAP|WRS|WBT/ || $policyPrefix2 =~ /WO|WW/)
                { $companyNo = '02'; }
                else
                { $companyNo = '01'; }
                my $claimNo = $return->{'claimNumber'};
                my $progErr = $return->{'progErr'};
                if(!($errInd =~ /\w/))
                {
                    my $branch = '20';
                    my $userkey = $ENGINE->{'AUTH'}->{'user_key'};

                    ## Perform insert
                    my $generalInsert = $ENGINE->{'DBH'}->prepare('INSERT INTO CLAIMDB.CLM_GENERAL
(CLAIM_ID,COMPANY_NO,POLICY_NUMBER,IMT_CLAIM_NO,BRANCH,USER_KEY,MANUAL_OR_WHAT,IMT_LINE_CODE,POLICY_STATE,CLAIM_STATUS,LOSS_DATE_TIME,LOSS_TIME_GENERIC) VALUES (?,?,?,?,?,?,?,?,?,?,?,?)') || $error->($ENGINE);

                    $generalInsert->execute($claimId,$companyNo,$ENGINE->{'Inquiry'}->{'POLICY_NUMBER'},$claimNo,$branch,$userkey,'M',$lineOfBusiness,$policyState,'P',$lossDate,'  ') || $error->($ENGINE);

                        ## validateClaimID reads the claimid from CGI, so insert it here
                        $ENGINE->{'CGI'}->param('claimid'=>$claimId);
                        #Reload $ENGINE->{'claimGeneral'}
                        validateClaimID($ENGINE);

                        #build the claims folders for document processing
                        my $clmSTH = $ENGINE->{'DBH'}->prepare('select
                                        g.imt_claim_no,
                                        fgid,
                                        IMT_LINE_CODE
                                from
                                        claimdb.clm_general as g
                                left join
                                        claimdb.clm_files as f
                                on
                                        g.claim_id = f.claim_id
                                where
                                        g.claim_id = ?') || die ($ENGINE->{'DBH'}->errstr());
                        $clmSTH->execute($claimId) || die ($ENGINE->{'DBH'}->errstr());
                        my $clmResult = $clmSTH->fetchall_arrayref({});
                        my $FGID = 1;
                        my $lineCode = $clmResult->[0]->{'IMT_LINE_CODE'};
                        my $PFID = 1;
                        my $claim = $clmResult->[0]->{'IMT_CLAIM_NO'};

                        my $results = createFileGroup($ENGINE,{'FILENAME'=>$claim});
                        $FGID = $results->{'FGID'};
                        my $FID = $results->{'FID'};
                        my $clmFGIDSTH = $ENGINE->{'DBH'}->prepare('insert into claimdb.clm_files (claim_id,fgid) values (?,?)') || die ($ENGINE->{'DBH'}->errstr());
                        $clmFGIDSTH->execute($claimId,$FGID) || die ($ENGINE->{'DBH'}->errstr());

                        # Add default folders:
                        defaultFolders($ENGINE,$lineCode,$FID,$FGID);

                    if($ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} eq 'P')
                    {
                        $ENGINE->{'load'} = 'Claims_Details';
                    }
                    else
                    {
                        $ENGINE->{'load'} = 'Claims_PolicyInfo';
                    }
                }
            }
            elsif(defined($ENGINE->{'Inquiry'}->{'MANUAL_ENTRY'}) && $ENGINE->{'Inquiry'}->{'MANUAL_ENTRY'} eq 'N')
            {
                push(@{$ENGINE->{'errors'}->{'generic'}},'No Claim Created.');
                $ENGINE->{'load'} = 'Claims_Inquiry';
            }
            elsif($errInd eq 'C')
            {
                push(@{$ENGINE->{'errors'}->{'nc_pol_number'}},'Policy Cancelled.');
                $ENGINE->{'load'} = 'Claims_Inquiry';
            }
            else
            {
                push(@{$ENGINE->{'errors'}->{'nc_manual_entry'}},'Policy is not on file.');
                $ENGINE->{'load'} = 'Claims_Inquiry';
            }
        }
        elsif($errInd eq 'F')
        {
            push(@{$ENGINE->{'errors'}->{'nc_pol_number'}},'A policy could not be found for the specified loss date. Please review your entry.'.
'If it is correct, contact IMT directly regarding this claim.');
            $ENGINE->{'load'} = 'Claims_Inquiry';
        }
        elsif($errInd eq 'N'
                && $ENGINE->{'AUTH'}->{'IMTOnline_UserType'} ne 'Internal')
        {    #this error is for agents only
            push(@{$ENGINE->{'errors'}->{'nc_pol_number'}},'Sorry, you are unable to start a claim for this policy type.  Please contact IMT Insurance directly regarding this claim.');
            $ENGINE->{'load'} = 'Claims_Inquiry';
        }
        elsif($errInd eq 'C')
        {
            push(@{$ENGINE->{'errors'}->{'nc_pol_number'}},'Policy Cancelled.');
            $ENGINE->{'load'} = 'Claims_Inquiry';
        }
        else
        {
            push(@{$ENGINE->{'errors'}->{'nc_pol_number'}},'An error occurred trying to find the policy for the specified loss date.');
            $ENGINE->{'load'} = 'Claims_Inquiry';
        }
#        }

#        my $returnHTML = 'returned values: ' . $errInd . ', ' . $claimId . ', ' .
#           join(' ',@$driverRef) . ', ' . join(' ',@$vehRef);
#        warn $returnHTML;
#        for my $r (@$driverRef)
#        {
#            for my $k (keys %{$r})
#              { warn $k,' => ', $r->{$k}; }
#        }
        #  $ENGINE->{'CGI'}->delete_all();
    }
    else
     {
       $ENGINE->{'load'} = 'Claims_Inquiry';
     }
}

sub editScreen
{
    my $ENGINE = shift;
    my %errors = ();
    my $return = 1;

    my ($curDay, $curMonth, $curYear) = (localtime)[3,4,5];
    $curYear = $curYear+1900;
    $curDay = length($curDay)<2 ? '0'.$curDay : $curDay;
    $curMonth = $curMonth+1;
    $curMonth = length($curMonth)<2 ? '0'.$curMonth : $curMonth;
    my $checkDate = $curYear.$curMonth.$curDay;

    my $lossDate = $ENGINE->{'CGI'}->param('nc_loss_date')||'';
    my $DBlossDate = '';
    my ($day, $month, $year) = parseDateString($lossDate);
#    my $checkLossDate = $year.$month.$day;
    if(defined($day))
    {
        my $checkLossDate = $year.$month.$day;
        if($checkLossDate le $checkDate)
        {
            $DBlossDate = $year.'-'.$month.'-'.$day.' 01:00:00.000000';

            $ENGINE->{'Inquiry'}->{'LOSS_DATE'} = $month.'/'.$day.'/'.$year;
            $ENGINE->{'Inquiry'}->{'DB_LOSS_DATE'} = $DBlossDate;
            #$lossDate = $month.'/'.$day.'/'.$year;
        }
        else
        { push(@{$errors{'nc_loss_date'}},'Loss Date cannot be greater than the current date.'); $return=0; }
    }
    else
      { push(@{$errors{'nc_loss_date'}},'Invalid loss date.'); $return=0; }

    my $pol_number = uc($ENGINE->{'CGI'}->param('nc_pol_number'))||'';
    substr($pol_number,2) =~ s/O/0/gi;
    $pol_number =~ s/^\s*//g;
    $pol_number =~ s/\s*$//g;
#sjs 106900 edit so policy number is 7 charcters in length.
    if($pol_number ne '')
    {
        if($pol_number =~ /[^0-9a-zA-Z]/)
         { push(@{$errors{'nc_pol_number'}},'Policy numbers must only contain characters 0-9, a-z and/or A-Z.'); $return=0; }
        elsif(length($pol_number) > 7)
         { push(@{$errors{'nc_pol_number'}},'Policy numbers must be shorter than 8 characters in length.'); $return=0; }
        elsif(length($pol_number) < 7)
         { push(@{$errors{'nc_pol_number'}},'Policy numbers must be longer than 6 characters in length.'); $return=0; }
        else
         { $ENGINE->{'Inquiry'}->{'POLICY_NUMBER'} = $pol_number; }
#        elsif($pol_number =~ /^\d\d/ && $claim_partial =~ /\w/)
#         { push(@where,"POLICY_NUMBER LIKE '__$pol_number\%'"); }
#        elsif($pol_number =~ /^\d\d/)
#         { push(@where,"POLICY_NUMBER IN ('12$pol_number','14$pol_number','26$pol_number','40$pol_number','48$pol_number','$pol_number')"); }
    }
    else
      { push(@{$errors{'nc_pol_number'}},'Must enter policy number.'); $return=0; }

    if($pol_number gt '' && substr($pol_number,0,2) !~ /[^0-9]/)
    {
         my $farmState = $ENGINE->{'CGI'}->param('farmStateList')||'';
         my %states = getStateAbbrevNumeric();
         my $validState = 0;
         for my $key (keys %states)
         {
            if ($key eq $farmState)
            {$validState = 1; last;}
         }
         if ($validState == 0)
         {  #If we don't have a valid state, let's check the database to see what the state is.
            my $stateQuery = $ENGINE->{'DBH'}->prepare('
                SELECT  DISTINCT
                    SUBSTR(G.POLICY_NUMBER,1,2)
                FROM FARMDB.GENERAL G
                WHERE
                    G.CHG_IND = \' \'
                    AND G.POLICY_NUMBER LIKE ?');
            $stateQuery->execute('%'.$pol_number.'%') || error($ENGINE,'Search query execute failed: '.$ENGINE->{'DBH'}->errstr);
            my $results = $stateQuery->fetchall_arrayref({});

            if(scalar(@$results) > 1)
            {   #if we have more than one valid state, let internal users decide the state,
                # but determine the state for agents based on their agency.
                if($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'Internal')
                {push(@{$errors{'farmStateList'}},'Please select a Farm State'); $return=0;}
                else
                {   #This may not be working quite right {EMR}
                    my $pol_prefix = substr($pol_number,0,2);
                    for my $agency (keys %{$ENGINE->{'AUTH'}->{'AGENCY_ACCESS'}})
                    {
                        my $pre = '12'.$pol_prefix;
                        if($agency =~ /^$pre/)
                        {$farmState = '12';}
                        $pre = '99'.$pol_prefix;
                        if($agency =~ /^$pre/)
                        {$farmState = '14';}
                    }
                }
            }
            else
            {
                my $key = (keys %{@$results[0]});
                $farmState = @$results[0]->{$key};
            }
         }
         if($farmState ne '')
         {$ENGINE->{'Inquiry'}->{'POLICY_NUMBER'} = $farmState.$pol_number;}
    }

    if($ENGINE->{'CGI'}->param('claimExists') eq 'X')
    {
        undef %errors;
        $return=1;
    }
    elsif((defined($pol_number) && $pol_number gt '') &&
       (defined($lossDate) && $lossDate gt '') && $ENGINE->{'CGI'}->param('claimExists') eq 'N')
    {
        my $holdNewLossDay = '';
        my $holdNewLossMonth = '';
        my $holdNewLossYear = '';
        if ($lossDate =~ /^(\d\d)(\d\d)(\d\d|\d\d\d\d)$/ || $lossDate =~ /^(\d\d?)[^0-9](\d\d?)[^0-9](\d\d|\d\d\d\d)$/)
        {
            $holdNewLossDay = length($2)<2 ? '0'.$2 : $2;
            $holdNewLossMonth = length($1)<2 ? '0'.$1 : $1;
            $holdNewLossYear = length($3)<3 ? 2000 + $3 : $3;
        }

        my $holdNewLossDate = $holdNewLossYear.'-'.$holdNewLossMonth.'-'.$holdNewLossDay;
        if(length($pol_number) < 8)
        {
                my $generalSelect = $ENGINE->{'DBH'}->prepare("SELECT
                        CLAIM_ID,
                        IMT_CLAIM_NO
                    FROM
                        CLAIMDB.CLM_GENERAL
                    WHERE
                        POLICY_NUMBER = ?
                        AND SUBSTR(CHAR(LOSS_DATE_TIME),1,10) = ?
                        AND DATE_DELETED = '9999-01-01-01.00.00.000000'")
                    || error($ENGINE,'General select query prepare failed: '.$ENGINE->{'DBH'}->errstr);
                $generalSelect->execute($ENGINE->{'Inquiry'}->{'POLICY_NUMBER'},$holdNewLossDate)
                    || error($ENGINE,'General select query execute failed: '.$ENGINE->{'DBH'}->errstr);;
                my $generalSelectResults = $generalSelect->fetchall_arrayref({});

                if(scalar(@$generalSelectResults) > 0)
                {
                    if ($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'Internal')
                    { push(@{$errors{'nc_loss_date'}},'A claim '.$generalSelectResults->[0]->{'IMT_CLAIM_NO'}.' exists on this policy with this loss date.  Are you sure you want to continue?<input type="button" value="Yes" onclick="saveClaim()" /><input type="button" value="No" onclick="clearNode(getElementById(\'newClaimForm\'));continueClaimNo(\'Claims_Inquiry\')" />'); $return=0; }
                    else
                    { push(@{$errors{'nc_loss_date'}},'A claim '.$generalSelectResults->[0]->{'IMT_CLAIM_NO'}.' with the same loss date for this policy already exists, if you need to submit another claim please contact the Home Office at **************.'); $return=0; }
                }
        }
    }

    #edit line of business selection for GL policies.
    my $GLLine = $ENGINE->{'CGI'}->param('GLLineList')||'';
    if($pol_number =~ /^GL/)
    {
        if($GLLine eq '')
        { push(@{$errors{'GLLineList'}},'Must select a line of business for a GL policy.'); $return=0; }
    }

    my $glassClaim = uc($ENGINE->{'CGI'}->param('nc_glass_claim'))||'';
    if($glassClaim =~ /\w/ && $pol_number =~ /^WAP|AP|CV|GR/)
      { $ENGINE->{'Inquiry'}->{'GLASS_CLAIM'} = $glassClaim; }

    my $glassClaimErr = uc($ENGINE->{'CGI'}->param('nc_glass_claim_err'))||'';
    if($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'Internal' && $glassClaimErr =~ /\w/ && $pol_number =~ /^WAP|AP|CV|GR/)
      { $ENGINE->{'Inquiry'}->{'GLASS_CLAIM_ERR'} = $glassClaimErr; }

    if(defined($ENGINE->{'Inquiry'}->{'GLASS_CLAIM'}) && defined($ENGINE->{'Inquiry'}->{'GLASS_CLAIM_ERR'}))
      { push(@{$errors{'nc_glass_claim'}},'Must select Glass Claim or Glass Claim Correction, not both.'); $return=0; }


    my $manualEntry = uc($ENGINE->{'CGI'}->param('nc_manual_entry'))||'';
    if($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'Internal' && $manualEntry =~ /\w/)
      {
         $ENGINE->{'Inquiry'}->{'MANUAL_ENTRY'} = $manualEntry;
         if($manualEntry eq 'Y')
         {
                 my $lineOfBusiness = $ENGINE->{'CGI'}->param('lobList')||'';
                 my $polPrefix = substr($pol_number,0,2);
                 my $wadPolPrefix = substr($pol_number,0,3);
                 if($polPrefix =~ /(^\d+)/)
                 {
                     if($lineOfBusiness !~ /301|302|331|332/)
                     { push(@{$errors{'lobList'}},'Line of Business is invalid for the Policy Prefix entered.'); $return=0; }
                 }
                 elsif($wadPolPrefix eq 'WAP')
                 {
                     if($lineOfBusiness !~ /010/)
                     { push(@{$errors{'lobList'}},'Line of Business is invalid for the Policy Prefix entered.'); $return=0; }
                 }
                 elsif($wadPolPrefix eq 'WRS')
                 {
                     if($lineOfBusiness !~ /030/)
                     { push(@{$errors{'lobList'}},'Line of Business is invalid for the Policy Prefix entered.'); $return=0; }
                 }
                 elsif($wadPolPrefix eq 'WBT')
                 {
                     if($lineOfBusiness !~ /052/)
                     { push(@{$errors{'lobList'}},'Line of Business is invalid for the Policy Prefix entered.'); $return=0; }
                 }
                 elsif($polPrefix eq 'AP')
                 {
                     if($lineOfBusiness !~ /010|012/)
                     { push(@{$errors{'lobList'}},'Line of Business is invalid for the Policy Prefix entered.'); $return=0; }
                 }
                 elsif($polPrefix eq 'HO')
                 {
                     if($lineOfBusiness !~ /110|111/)
                     { push(@{$errors{'lobList'}},'Line of Business is invalid for the Policy Prefix entered.'); $return=0; }
                 }
                 elsif($polPrefix eq 'HM')
                 {
                     if($lineOfBusiness !~ /112|113/)
                     { push(@{$errors{'lobList'}},'Line of Business is invalid for the Policy Prefix entered.'); $return=0; }
                 }
                 else
                 {
                     my %IMTPolicyPrefixes = getIMTPolicyPrefix();
                     my %wadenaPolicyPerfixes = getWadenaPolicyPrefix();
                 my $wadPolPrefixFirst = substr($pol_number,0,1);
                 my %policyPrefixes = ();
                 if(($wadPolPrefixFirst ne 'W')
                         || (substr($pol_number,0,2) =~ /WC|WP/))
                 { %policyPrefixes = %IMTPolicyPrefixes; }
                 else
                 { %policyPrefixes = %wadenaPolicyPerfixes; }

                 my $LOBError = 'Y';
                 my $PrefixError = 'Y';
                     for my $key (sort keys %policyPrefixes)
                     {
                         if($policyPrefixes{$key} eq $polPrefix)
                         {
                         $PrefixError = 'N';
                             if($key eq $lineOfBusiness)
                             { $LOBError = 'N'; }
                         }
                     }
                     if($PrefixError eq 'Y')
                     { push(@{$errors{'lobList'}},'The Policy Prefix entered is not currently used. Please enter a valid prefix.'); $return=0; }
                     if($LOBError eq 'Y')
                     { push(@{$errors{'lobList'}},'Line of Business is invalid for the Policy Prefix entered.'); $return=0; }
                 }

                 my $policyState = $ENGINE->{'CGI'}->param('polStateList')||'';
                 my %states = getStateAbbrevNumeric();
                 my $validState = 0;
                 for my $key (keys %states)
                 {
                    if ($key eq $policyState)
                    {
                       $validState = 1; last;
                    }
                 }
                 if ($validState == 0)
                 {
                    { push(@{$errors{'polStateList'}},'Please select a Policy State'); $return=0; }
                 }
         }
      }

    #verify the user has access to the policy he is trying to
    #set the claim for
    if($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'Agent' && $return==1)
    {
        my $foundAgent = 'N';
        my $foundLine = 'N';
        my $accessLineCodes = getLineCodesByAccess();
        my $loss_dt = substr($ENGINE->{'Inquiry'}->{'DB_LOSS_DATE'},0,10);
        my @agency_access = ();
        my $getPolicyAgentNoQuery = $ENGINE->{'DBH'}->prepare('SELECT V.AGENCY, V.SOL_AGENT, V.LOB
            from clientdb.item I
            left outer join
            clientdb.version V
            on
                I.ITEM_ID = V.ITEM_ID
            where
                ID_NUMBER = ?
                AND V.EFF_DATE <= ?
                AND V.EXP_DATE >= ?
                ') || error($ENGINE,'Security query failed: '.$ENGINE->{'DBH'}->errstr);
        $getPolicyAgentNoQuery->execute($pol_number,$loss_dt,$loss_dt) || error($ENGINE,'Security query execute failed: '.$ENGINE->{'DBH'}->errstr);
        my $polAgentResults = $getPolicyAgentNoQuery->fetchall_arrayref({});
#        die Data::Dumper::Dumper($ENGINE->{'AUTH'}->{'AGENCY_ACCESS'},$polAgentResults);
        foreach my $key (keys %{$ENGINE->{'AUTH'}->{'AGENCY_ACCESS'}})
        {
            #this is the agent number
#            if ($key eq $polAgentResults->[0]->{'AGENCY'})
            if (defined($polAgentResults->[0]->{'AGENCY'}) && $polAgentResults->[0]->{'AGENCY'} == $key)
            {
                    for my $access (keys %{$accessLineCodes})
                    {
                         #this has stuff like Bonds_Access, Umbrella_Access,
                         #Commercial_Access, Auto_Access, Farm_Access, Home_Access
                         #verify the agent# from the policy is one of the agencies for
                         #the user signed in
                         if ($key eq $polAgentResults->[0]->{'AGENCY'})
                         {
                            $foundAgent = 'Y';
                         }

                        #get the lines of business codes this user can access
                        if($ENGINE->{'AUTH'}->{'AGENCY_ACCESS'}->{$key}->{$access})
                          { push(@agency_access,@{$accessLineCodes->{$access}}); }
                    }

                    #find the line code on the valid line code list
                    if(scalar(@agency_access) > 0)
                    {
                        foreach my $x (@agency_access)
                        {
                            if ($polAgentResults->[0]->{'LOB'} eq $x)
                            {
                                $foundLine = 'Y';
                                last;
                            }
                        }
                    }
                }
            }

            if ($foundAgent eq 'N' || $foundLine eq 'N')
            {
                 #this user does not have access to this policy so he
                 #cannot set a claim on it
                 push(@{$errors{'nc_pol_number'}},'Sorry, you do not have authority to create a claim for this policy.  Please contact your System Administrator.');
                 $return=0;
            }
    }

    #if we have no other errors, call the grace cancel check program
    if($ENGINE->{'CGI'}->param('submitGrace') eq 'X')
    {
        undef %errors;
        $return=0;
    }
    elsif ($return==1 && $ENGINE->{'CGI'}->param('submitGrace') eq 'N')
    {
        my $clientSelect = $ENGINE->{'DBH'}->prepare("SELECT
            V.EFF_DATE
            FROM CLIENTDB.ITEM I
            JOIN CLIENTDB.VERSION V
            ON V.ITEM_ID = I.ITEM_ID
            WHERE I.ID_NUMBER = ?
            AND V.VERSION_TYPE = 'P' AND V.STATUS= 'A'")
            || error($ENGINE,'Client select query prepare failed: '.$ENGINE->{'DBH'}->errstr);
        $clientSelect->execute($ENGINE->{'Inquiry'}->{'POLICY_NUMBER'})
            || error($ENGINE,'Client select query execute failed: '.$ENGINE->{'DBH'}->errstr);;
        my $clientSelectResults = $clientSelect->fetchall_arrayref({});

        if(scalar(@$clientSelectResults) > 0)
        {
            if(defined($clientSelectResults->[0]->{'EFF_DATE'}) && $clientSelectResults->[0]->{'EFF_DATE'} gt '')
            {
                my $pol_eff_date = $clientSelectResults->[0]->{'EFF_DATE'};
                my ($responseObj) = Common::GAS::maj_web_service::AccountInterface::maj_billingInquiry({
                    'policy_number' => $ENGINE->{'Inquiry'}->{'POLICY_NUMBER'},
                    'pol_eff_date'  => $pol_eff_date,
                    'NameOfInquiry' => 'POLICY_INQUIRY',
                    'cgi'           => $ENGINE->{'CGI'},
                });
                my $billingStatus = $responseObj->{'soap:Body'}->{'ns2:serviceResponse'}->{'return'}->{'RequestResponse'}->{'SuccessFlag'};
                my $pastDueAmt = $responseObj->{'soap:Body'}->{'ns2:serviceResponse'}->{'return'}->{'BillingInquirySummary'}->{'BillingInquiryInvoiceDetail'}->{'PastDueAmount'};
                if(defined($pastDueAmt) &&$pastDueAmt eq '0.00')
                {
                    $pastDueAmt = '0';
                }

                if(defined($billingStatus) && $billingStatus eq 'SUCCESS')
                {
                    if(defined($pastDueAmt) && $pastDueAmt gt 0)
                    {
#                        push(@{$errors{'nc_pol_number'}},'Billing account is currently past due, do you wish to continue setting up this claim? <input type="button" value="Yes" onclick="graceCancelYes()" /><input type="button" value="No" onclick="clearNode(getElementById(\'newClaimForm\'));continueClaimNo(\'Claims_Inquiry\')" />');
                        push(@{$errors{'nc_pol_number'}},'Billing account is currently past due. <input type="button" value="Continue" onclick="graceCancelYes()" />');
                        $return=0;
                    }
                }
            }
        }

#            my $checkCancel = Claims_GraceCancel($ENGINE,{'polNo'=>$ENGINE->{'Inquiry'}->{'POLICY_NUMBER'},'lossDate'=>$ENGINE->{'Inquiry'}->{'LOSS_DATE'}});
#        my $errInd = $checkCancel->{'errInd'};
#        if (!($errInd =~ /N|F/))
#        {
#            push(@{$errors{'nc_pol_number'}},'Grace Cancelled policy, do you wish to continue setting up this claim? <input type="button" value="Yes" onclick="graceCancelYes()" /><input type="button" value="No" onclick="clearNode(getElementById(\'newClaimForm\'));continueClaimNo(\'Claims_Inquiry\')" />');
#            $return=0;
#        }

    }

    $ENGINE->{'errors'} = \%errors;
    return $return;
}

1;

#    if($insd_fname =~ /\w/ ||
#       $insd_lname =~ /\w/ ||
#       $claim_fname =~ /\w/ ||
#       $claim_lname =~ /\w/)
#    {
#        my $insd = '';
#        my $comp = '=';
#        my $fuzzy = '';
#        if($claim_partial =~ /\w/)
#          { $comp = 'LIKE'; $fuzzy = '%'; }
#        if($insd_fname =~ /\w/ &&
#           $insd_lname =~ /\w/)
#        {
#            $insd = 'P.FIRST_NAME '.$comp.' ? AND P.LAST_NAME '.$comp.' ? AND PR.ROLE = \'IN\'';
#            push(@searchQueryArgs,($insd_fname.$fuzzy,$insd_lname.$fuzzy));
#        }
#        elsif($insd_fname =~ /\w/)
#        {
#            $insd = 'P.FIRST_NAME '.$comp.' ? AND PR.ROLE = \'IN\'';
#            push(@searchQueryArgs,$insd_fname.$fuzzy);
#        }
#        elsif($insd_lname =~ /\w/)
#        {
#            $insd = 'P.LAST_NAME '.$comp.' ? AND PR.ROLE = \'IN\'';
#            push(@searchQueryArgs,$insd_lname.$fuzzy);
#        }
#        my $claimant = '';
#        if($claim_fname =~ /\w/ &&
#           $claim_lname =~ /\w/)
#        {
#            $claimant = 'P.FIRST_NAME '.$comp.' ? AND P.LAST_NAME '.$comp.' ? AND PR.ROLE = \'CL\'';
#            push(@searchQueryArgs,($claim_fname.$fuzzy,$claim_lname.$fuzzy));
#        }
#        elsif($claim_fname =~ /\w/)
#        {
#            $claimant = 'P.FIRST_NAME '.$comp.' ? AND PR.ROLE = \'CL\'';
#            push(@searchQueryArgs,$claim_fname.$fuzzy);
#        }
#        elsif($claim_lname =~ /\w/)
#        {
#            $claimant = 'P.LAST_NAME '.$comp.' ? AND PR.ROLE = \'CL\'';
#            push(@searchQueryArgs,$claim_lname.$fuzzy);
#        }
#        my $subquery = 'SELECT CLAIM_ID FROM CLAIMDB.CLM_PARTIES AS P INNER JOIN CLAIMDB.CLM_PARTY_ROLES AS PR ON P.PARTY_ID = PR.PARTY_ID WHERE ';
#        if($insd =~ /\w/ && $claimant =~ /\w/)
#          { $subquery .= $insd.' AND '.$claimant; }
#        elsif($insd =~ /\w/)
#          { $subquery .= $insd; }
#        elsif($claimant =~ /\w/)
#          { $subquery .= $claimant; }
#        push(@where,"CLAIM_ID IN ($subquery)");
#    }
