//claims monetary entries javascript

$(document).ready(function(){
   $('#PMETHOD_IC_ProxyNumber_retype').bind("cut copy paste",function(e) {
      e.preventDefault();
   });
});

var killPasteVar = 'N';
//kill the paste
function killPaste(x)
{
        //the killPaste is called before the paste takes effect
        //when using onpaste,
        //so must call an onchange too, in order to overwrite the pasted
        //data with nothingness.  the onchange kicks in after the paste
        //happens and over-writes the data!!

         //set the killpaste variable!!
    killPasteVar = 'Y';

}
function killPaste2(x)
{

           var fieldName1 = x.id;
           if (killPasteVar == 'Y')
           {
            document.getElementById(fieldName1).value = '';
            killPasteVar = 'N';
    }
}

// Reset the form in which the reset button resides.
function saveView()
{
    var view = document.getElementById('historyViewSelect').value;
    var date = new Date();
    date.setFullYear(date.getFullYear() + 10);
    document.cookie = 'historyview='+view+'; expires='+date.toUTCString()+'; path=/imtonline/Claims/';
    alert('Default history view saved');
}

function twentyFiveMsgSubmit()
{
    disableButtons();
         document.getElementById("twentyFiveTR").style.display = "none";
         document.getElementById("twentyFiveAnswer").value = 'Y';
         document.mainform.submit();
}

function expandPayments()
{
    var pay;
    for(var i=0; pay = document.getElementById('byCovPay'+i); i++)
    {
        pay.style.display="";
        pay = document.getElementById('byTransPay'+i);
        pay.style.display="";
    }
    document.getElementById('showAllPay').style.display="none";
    document.getElementById('hideAllPay').style.display="";
    document.getElementById('showAllPayD').style.display="none";
    document.getElementById('hideAllPayD').style.display="";

}
function hidePayments()
{
    var pay;
    for(var i=0; pay = document.getElementById('byCovPay'+i); i++)
    {
        pay.style.display="none";
        pay = document.getElementById('byTransPay'+i);
        pay.style.display="none";
    }
    document.getElementById('showAllPay').style.display="";
    document.getElementById('hideAllPay').style.display="none";
    document.getElementById('showAllPayD').style.display="";
    document.getElementById('hideAllPayD').style.display="none";

}

/////new functions start here!!

function switchHist()
{

         if (document.getElementById("showDetButton").value == "Hide History")
         {
            document.getElementById("showDetButton").value = "Show History";
         }
         else
         {
            document.getElementById("showDetButton").value = "Hide History";
         }
}

function reOpenClaim(x)
{
    if(x == 1)
    {
        document.getElementById("transactionCode").value = "ReOpen";
        document.mainform.submit();
    }
    else if(x > 1)
    {
        //open selection modal
        $('#adjusterReopenModal').modal('show');
         $('#adjusterReopenModal').modal({
                                backdrop: 'static',
                                keyboard: false
                              });
         $('#adjusterReopenModal .modal-dialog').css("margin-top", Math.max(0, ($(window).height() - $('#adjusterReopenModal .modal-dialog').height()) / 3));
     }
}
function reopenClaimNow()
{
    var adjs = document.getElementsByName("adjuster_reopen");
    var idList = '';
    var adjSelected = 0;
    for(i=0; i < adjs.length; i++)
    {
        if(adjs[i].checked == true)
        {
            adjSelected = 1;
            idList += adjs[i].id+',';
        }
    }
    if(adjSelected == 0)
    {$('#adjuster_select_error').removeClass('hidden');}
    else
    {
        $('#reopen_adjuster_list').val(idList);
        document.getElementById("transactionCode").value = "ReOpen";
        document.mainform.submit();
    }
}

function saveMyClaim()
{

         document.getElementById("transactionCode").value = "SaveClaim";
         document.mainform.submit();
}

function glassFix(x)
{
        var unfix = (x || '');

        if (unfix == 'N')
        {
            document.getElementById("transactionCode").value = "GlassUndoFix";
        }
        else if (unfix == 'E')
        {
            document.getElementById("transactionCode").value = "GlassErrorStatus";
        }
        else
        {
                 document.getElementById("transactionCode").value = "GlassFix";
         }
         document.mainform.submit();
}

function showCloseOpts(x)
{
        var payAmt = x;

    //if the button says Close Claim on it, do this processing:
    if (document.getElementById('closeClaimButton').value == "Close Claim")
    {
            //Disable what do you want to do dropdown
            document.getElementById('whatToDo').disabled = true;

            //show the close without pay options - maybe
            if (payAmt == "\$0.00")
            {
                document.getElementById('clopo').style.display="";
                document.getElementById('closeWoPayHidden').value="N";
            }
            document.getElementById('clopr').style.display="";
            document.getElementById('clopsub').style.display="";
            document.getElementById('pendClose').style.display="";
            document.getElementById('clWarn').style.display="";
            document.getElementById('closeClaimButton').value="Undo Close Claim";
            if (document.getElementById('closeAlertsMess'))
            {
                    document.getElementById('closeAlertsMess').style.display="";
            }

            //if radio button was already set at Yes and claim closed and
            //then reopened, need to display the memo again
        //check for yesMemo.checked being 'true'
        if (document.getElementById("yesMemo").checked == 1)
        {
                 showMemo();
        }
        }
        else
        {
             //Enable What do you want to do dropdown
             document.getElementById('whatToDo').disabled = false;

             //if the button says "Undo Close Claim" on it, do this processing
             //to reverse showing close claim options
             hideMemo();
             document.getElementById('clopsub').style.display="none";
             document.getElementById('clopr').style.display="none";
             document.getElementById('clopo').style.display="none";
             document.getElementById('closeWoPayHidden').value="Y";
             document.getElementById('pendClose').style.display="none";
             document.getElementById('clWarn').style.display="none";
             document.getElementById('closeClaimButton').value="Close Claim";
             if (document.getElementById('closeAlertsMess'))
            {
                    document.getElementById('closeAlertsMess').style.display="none";
            }

        }
}

function showMemo()
{
        //display the memo wordage field
        document.getElementById('clopmemo').style.display="block";
        document.getElementById('closeMemoData').style.display="block";

}

function hideMemo()
{
        //hide the memo wordage field
        document.getElementById('clopmemo').style.display="none";
        document.getElementById('closeMemoData').style.display="none";
}

function closePendingRecov()
{
//    window.alert ("got to closePendingRecov function!");
         disableButtons();
         document.getElementById("transactionCode").value = "closeClaimPend";
         document.mainform.submit();
}

function closeClaim()
{
//    window.alert ("got to closClaim function!");
         disableButtons();
         document.getElementById("transactionCode").value = "closeClaim";
         document.mainform.submit();
}

function submitToAdjust()
{
//        window.alert ("got into submit to adjuster function!");
         disableButtons();
         document.getElementById("transactionCode").value = "subToAdjuster";
         document.mainform.submit();
}

//this function will either hide the sdip row in the sdip table, or
//actually delete the sdip row from the sdip table. We are hiding the
//first row instead of deleting it, because the sdip table is in it and
//is copied when additional sdip codes are requested.
function deleteSDIP(num)
{
    var row;

    row = document.getElementById("SDIP"+num);

    //we are just hiding the first sdip because we need it to add new
    //ones
    if (num == '0')
    {
            document.getElementById("SDIP"+num).style.display = "none";
            document.getElementById("SDIPselect").value = "99";
    }
    else
    {
      if(row)
      {
         //remove the row completely from the DOM
         row.parentNode.removeChild(row);

         //assuming that if the sdip# is less than 100, we haven't added
         //the sdip to the table.  SDIPS added to the table have the row_id
         //from the table as their num value, which should be much higher than 100
         if (num > 100)
         {
            document.getElementById("sdipDeletes").value = document.getElementById("sdipDeletes").value + num + "|";
//            document.mainform.submit();
         }
      }
   }
}

//this function adds new rows and cells within those rows into
//the sdip table on the monetary entries screen
function addSDIP()
{
    //first time clicking Add Sdip button, need to just change to display
    //the hidden sdip select box that is there and get out
    if (document.getElementById("SDIPGroup").style.display == "none")
    {
                document.getElementById("SDIPGroup").style.display = "";
                //create hidden field with sdip counter in it to keep new
                //sdip ids unique
                var newElement = document.createElement('div');
                //var hiddenField = '<input type="hidden" name="newSdipId" value="0"/>';
        newElement.id = "newSdipId";
        newElement.name = "newSdipId";
        newElement.value = 0;
        newElement.type = "hidden";

        document.mainform.appendChild(newElement);
            return;
    }

    //initialize sdip variable
    var sdipNo = 0;
    var sdipRow = 0;

    //NOTE!!!  sdipRow is the number of rows in the html table.
    //NOTE!!!  sdipNo is the ID of the new row added to the html table
    //NOTE!!!  These two items are different and should be in different fields.
    //retrieving the number of rows in the sdip table
    sdipRow = document.getElementById("SDIPtable").childNodes.length;

    //get hidden sdip id# value from hidden field
    sdipNo = document.getElementById("newSdipId").value;
    //increment the sdip id# value
    sdipNo++;
    //put new sdip id# into hidden sdip id# field.
    document.getElementById("newSdipId").value = sdipNo;

        var new_li = document.createElement("li");
        new_li.id = 'SDIP' + sdipNo;
        var workStr = document.getElementById("SDIPtd0").innerHTML;
        var sdipBoxName = new String("SDIPselect" + sdipNo);
        workStr = workStr.replace(/SDIPselect/igm,sdipBoxName);
        new_li.innerHTML = workStr+'<input type="button" id="DelSDIPBut" name="DelSDIPBut" value="Remove SDIP" class="delete" onclick="deleteSDIP(\'' + sdipNo + '\')"></input>';

        var sel = document.getElementById('SDIPtable');

        if(sel)
        {
                $(sel).append(new_li)
        }

        /*
    //inserting a row in the sdip table
         var x = document.getElementById("SDIPtable").insertRow(sdipRow);

         //inserting two cells into the row just inserted into the sdip table
         var y = x.insertCell(0);
         var z = x.insertCell(1);

         //setting the name and id values of the cells (z & Y)
         //and the row (x) just added to the table
         x.name = 'SDIP' + sdipNo;
         x.id = 'SDIP' + sdipNo;
         y.name = 'SDIPtd' + sdipNo;
         y.id = 'SDIPtd' + sdipNo;
         z.name = 'SDIPtda' + sdipNo;
         z.id = 'SDIPtda' + sdipNo;

         //retrieve the sdip code list and place in a work field
         var workStr = document.getElementById("SDIPtd0").innerHTML;

         //use a regex to change select box name from SDIPselect to
         //a unique name
        var sdipBoxName = new String("SDIPselect" + sdipNo);
    workStr = workStr.replace(/SDIPselect/igm,sdipBoxName);

    //set the td inner.html to the new select box name and values
         y.innerHTML = workStr;

        //hard-coding the delete button because we need the SDIP number in order
        //to delete the proper sdip row.  This goes into the second cell
        //added to the sdip table
        z.innerHTML = '<input type="button" id="DelSDIPBut" name="DelSDIPBut" value="Remove SDIP" style="font-weight:bold; margin-bottom:2em;" onclick="deleteSDIP(\'' + sdipNo + '\')"></input>';
        */
}

//****************************************************************
//VOID A DRAFT FUNCTIONS
//****************************************************************
//to void a draft
function voidPayment()
{
        //if we are voiding the draft, show the reason select box and Other options
        //and the button to process the void
        if (document.getElementById('VoidPayment1').value == "Void this Draft")
        {
            document.getElementById('voidRow1').style.display = "";
            document.getElementById('voidSure').style.display = "";
            document.getElementById('VoidPayment1').value="Undo Void";
        }
        else
        {
                //call function to hide the void variables
                cancelVoid();
        }
}

function cancelVoid()
{
           //hide the void select box and void reason box
           document.getElementById('voidRow1').style.display = "none";
           document.getElementById('voidRow2').style.display = "none";
           document.getElementById('voidSure').style.display = "none";
    document.getElementById('VoidPayment1').value = "Void this Draft";
           document.getElementById('voidReasonselect')[0].selected = true;
}

function processVoid()
{
        //protect undo void, hold, and save buttons while process void is
        //processing:
        disableButtons();
        //document.getElementById('yesHoldSure1').disabled=true;
        //document.getElementById('HoldPayment1').disabled=true;
        //document.getElementById('VoidSure1').disabled=true;
    //document.getElementById('VoidPayment1').disabled=true;
         document.getElementById('specialCorrections').value = 'V';
         document.mainform.submit().then((value) => {enableButtons()});
}

function otherReason()
{
        if (document.getElementById('voidReasonselect').value == 'OT')
        {
             document.getElementById('voidRow2').style.display = "";
        }
        else
        {
             document.getElementById('voidRow2').style.display = "none";
        }

}
//****************************************************************
//END OF VOID A DRAFT FUNCTIONS
//****************************************************************

//******************************************************************
//DELETE A DRAFT FUNCTIONS
//******************************************************************
function deletePayment()
{
        //if we are deleting the draft, show
        //the button to process the delete
        if (document.getElementById('DeletePayment1').value == "Delete this Draft")
        {
            document.getElementById('deleteSure').style.display = "";
            document.getElementById('DeletePayment1').value="Undo Delete";
        }
        else
        {
                //call function to hide the void variables
                cancelDelete();
        }
}

function cancelDelete()
{
           //hide the delete "are you sure" button and reset the specialCorrections
           //indicator
           document.getElementById('deleteSure').style.display = "none";
    document.getElementById('DeletePayment1').value = "Delete this Draft";
}

function processDelete()
{
        document.getElementById('DeletePayment1').disabled=true;
    document.getElementById('deleteSure').disabled=true;
         document.getElementById('specialCorrections').value = 'D';
         document.mainform.submit();
}

//******************************************************************
//END OF DELETE A DRAFT FUNCTIONS
//******************************************************************


//******************************************************************
//Reinstate A DRAFT FUNCTIONS
//******************************************************************
function reinstatePayment()
{
        //if we are deleting the draft, show
        //the button to process the delete
        if (document.getElementById('ReinstatePayment1').value == "Reinstate this Draft")
        {
            document.getElementById('reinstateSure').style.display = "";
            document.getElementById('ReinstatePayment1').value="Undo Reinstate";
        }
        else
        {
                //call function to hide the void variables
                cancelReinstate();
        }
}

function cancelReinstate()
{
           //hide the delete "are you sure" button and reset the specialCorrections
           //indicator
           document.getElementById('reinstateSure').style.display = "none";
    document.getElementById('ReinstatePayment1').value = "Reinstate this Draft";
}

function processReinstate()
{
        document.getElementById('yesReinstateSure1').disabled=true;
        document.getElementById('ReinstatePayment1').disabled=true;
         document.getElementById('specialCorrections').value = 'R';
         document.mainform.submit();
}

//******************************************************************
//END OF REINSTATE A DRAFT FUNCTIONS
//******************************************************************


//******************************************************************
//Hold A DRAFT FUNCTIONS
//******************************************************************
function holdPayment()
{
        //if we are deleting the draft, show
        //the button to process the delete
        if (document.getElementById('HoldPayment1').value == "Hold this Draft")
        {
            document.getElementById('holdSure').style.display = "";
            document.getElementById('HoldPayment1').value="Undo Hold";
        }
        else
        {
                //call function to hide the void variables
                cancelHold();
        }
}

function cancelHold()
{
           //hide the delete "are you sure" button and reset the specialCorrections
           //indicator
           document.getElementById('holdSure').style.display = "none";
    document.getElementById('HoldPayment1').value = "Hold this Draft";
}

function processHold()
{
        document.getElementById('yesHoldSure1').disabled=true;
        document.getElementById('HoldPayment1').disabled=true;
        document.getElementById('VoidSure1').disabled=true;
    document.getElementById('VoidPayment1').disabled=true;
         document.getElementById('specialCorrections').value = 'H';
         document.mainform.submit();
}

//******************************************************************
//END OF Hold A DRAFT FUNCTIONS
//******************************************************************

//******************************************************************
//Release A DRAFT FUNCTIONS
//******************************************************************
function releasePayment()
{
        //if we are releasing the draft hold, show
        //the button to process the release
        if (document.getElementById('ReleasePayment1').value == "Release Hold on this Draft")
        {
            document.getElementById('releaseSure').style.display = "";
            document.getElementById('ReleasePayment1').value="Undo Release";
        }
        else
        {
                //call function to hide the void variables
                cancelRelease();
        }
}

function cancelRelease()
{
           //hide the delete "are you sure" button and reset the specialCorrections
           //indicator
           document.getElementById('releaseSure').style.display = "none";
    document.getElementById('ReleasePayment1').value = "Release Hold on this Draft";
           document.getElementById('specialCorrections').value = '';
}

function processRelease()
{
        document.getElementById('ReleasePayment1').disabled=true;
    document.getElementById('yesReleaseSure1').disabled=true;
         document.getElementById('specialCorrections').value = 'RH';
         document.mainform.submit();
}

//******************************************************************
//END OF Release A DRAFT FUNCTIONS
//******************************************************************

//******************************************************************
//reverse A DRAFT FUNCTIONS
//******************************************************************
function revPay()
{
        //if we are deleting the draft, show
        //the button to process the delete
        if (document.getElementById('reversePayment').value == "Reverse this Transaction")
        {
            document.getElementById('reverseSure').style.display = "";
            document.getElementById('reversePayment').value="Undo Reverse";
        }
        else
        {
                //call function to hide the void variables
                cancelReverse();
        }
}

function cancelReverse()
{
           //hide the delete "are you sure" button and reset the specialCorrections
           //indicator
           document.getElementById('reverseSure').style.display = "none";
    document.getElementById('reversePayment').value = "Reverse this Draft";
}

function processReverse()
{
        document.getElementById('reversePayment').disabled=true;
    document.getElementById('reversePayment').disabled=true;
         document.getElementById('specialCorrections').value = 'REVERSE';
         document.mainform.submit();
}

//******************************************************************
//END OF reverse A DRAFT FUNCTIONS
//******************************************************************



//******************************************************************
//DELETE A CASH FUNCTIONS
//******************************************************************
function deleteCheck()
{
   //if we are deleting the draft, show
   //the button to process the delete
   if (document.getElementById('DeleteCheck').value == "Delete this Cash Entry")
   {
      document.getElementById('deleteSure').style.display = "";
      document.getElementById('DeleteCheck').value="Undo Delete";
   }
   else
   {
          //call function to hide the void variables
          cancelDeleteCheck();
   }
}

function cancelDeleteCheck()
{
    //hide the delete "are you sure" button and reset the specialCorrections
    //indicator
    document.getElementById('deleteSure').style.display = "none";
    document.getElementById('DeleteCheck').value = "Delete this Cash Entry";
}

function processDeleteCheck()
{
    document.getElementById('DeleteCheck').disabled=true;
    document.getElementById('yesDeleteSure').disabled=true;
    document.getElementById('specialCorrections').value = 'DC';
    document.mainform.submit();
}

//******************************************************************
//END OF DELETE A CASH FUNCTIONS
//******************************************************************

//******************************************************************
//NSF A CASH FUNCTIONS
//******************************************************************
function NSFPayment()
{
        //if we are deleting the draft, show
        //the button to process the delete
        if (document.getElementById('NSFCheck').value == "NSF Cash Entry")
        {
            document.getElementById('nsfSure').style.display = "";
            document.getElementById('NSFCheck').value="Undo NSF";
        }
        else
        {
                //call function to hide the void variables
                cancelNSFCheck();
        }
}

function cancelNSFCheck()
{
    //hide the delete "are you sure" button and reset the specialCorrections
    //indicator
    document.getElementById('nsfSure').style.display = "none";
    document.getElementById('NSFCheck').value = "NSF Cash Entry";
}

function processNSF()
{
    document.getElementById('NSFCheck').disabled=true;
    document.getElementById('yesNSFSure').disabled=true;
    document.getElementById('specialCorrections').value = 'NSF';
    disableButtons();
    document.mainform.submit();
}

//******************************************************************
//END OF NSF A CASH FUNCTIONS
//******************************************************************


//******************************************************************
//CLOSE THE CLAIM FUNCTIONS
//******************************************************************

// uncheck all the close - pending checkboxes.  None of them
// can be checked if the close - no pending checkbox is checked.
function pendUncheck()
{
          if (document.getElementById('noPend').checked == true)
         {
       document.getElementById('reinPend').checked = false;
       document.getElementById('subroPend').checked = false;
       document.getElementById('salvPend').checked = false;
       document.getElementById('recovPend').checked = false;
         }
}

//uncheck the Close - No Pending Recovery checkbox.  It cannot
//be checked if any of the close - pending checkboxes are checked.
function allpendUncheck()
{
        if  (document.getElementById('subroPend').checked == true
                || document.getElementById('salvPend').checked == true
                || document.getElementById('reinPend').checked == true
                || document.getElementById('recovPend').checked == true)

        {
                if (document.getElementById('noPend'))
                {
                         document.getElementById('noPend').checked = false;
                 }
         }
}

//******************************************************************
//END CLOSE THE CLAIM FUNCTIONS
//******************************************************************


//
function hideMyself(hideMe,whatNode,checkIt)
{
         //window.alert ('in function hideMyself!' + hideMe + ' ' + whatNode);
    //window.alert('it is: ' + document.getElementById(whatNode).value);

    //if the user is not sure, then recheck the checkbox before
    //hiding the node
    if (document.getElementById(whatNode).value == 'Cancel')
    {
             document.getElementById(checkIt).checked = true;
    }

    //hide the node
    document.getElementById(hideMe).style.display = "none";

}

function showMyself(ShowMe,whatNode,var1,var2)
{
         //window.alert ('in function showMyself!' + ShowMe + ' ' + whatNode);

    //if incoming item is checked, then hide node
    if (document.getElementById(whatNode).checked == true)
    {
             document.getElementById(ShowMe).style.display = "none";
    }
    else
    {
            //show the node
            document.getElementById(ShowMe).style.display = "";
    }

    //reset checked items to not be checked
    document.getElementById(var1).checked = false;
    document.getElementById(var2).checked = false;

}

//storms can only be one per claim
function deleteSTORM(num)
{
        //retrieve row element for storm
    var row;
    row = document.getElementById("STORM"+num);

    //hide the storm table
    document.getElementById("STORMtable").style.display = "none";

    //display the add button
    document.getElementById("AddSTORMBut").style.display = "";

    //set the selected storm to "99 - Please select storm"
    document.getElementById("STORMselect").value = '99';

    //set delete indicator
    document.getElementById("stormDeletes").value = "Y";

    //hide the new storm fields too
    document.getElementById("STORMAdd").style.display = "none";

    //if this is storm 0, its the storm table and the storm wasn't on the
    //db yet, so we don't need to store it's number for later deletion.
    if((row) && (num != 0))
    {
        //remove the row completely from the DOM
        row.parentNode.removeChild(row);

        //add sdip to the hidden field that stores deleted storms
//        document.getElementById("stormDeletes").value =
//            document.getElementById("stormDeletes").value + num + "|"
//        document.getElementById("stormDeletes").value = "Y";
    }
}

//only one storm per claim.  If the user wants to add it, display the
//select box and remove the add button so he can't try to add two or
//more storms.
function addSTORM()
{

    //show the storm select box
    document.getElementById("STORMtable").style.display = "";

    //hide the add button
    document.getElementById("AddSTORMBut").style.display = "none";

    //reset the delete storm indicator:
    document.getElementById("stormDeletes").value = "N";

}

//show and hide the new storm section as needed
function newStorm()
{
        if (document.getElementById("STORMselect").value == '999')
        {
            document.getElementById("STORMAdd").style.display = "";
        }
        else
        {
            document.getElementById("STORMAdd").style.display = "none";
        }
}

//add another claimant to the section
function addNewClaimant(thisIsIt)
{

        if (document.getElementById("reserveTab100"))
        {

                //increment the hidden variable coverage counter
                var myCount = document.getElementById("claimantCntr").value;
                myCount = (myCount*1+100);
                document.getElementById("claimantCntr").value = myCount;

                //resvRadio100
                var resvRadio = $('#resvRadio100').clone();
                resvRadio.attr('id','resvRadio'+myCount);
                resvRadio.attr('name','resvRadio'+myCount);
                resvRadio.find('#resType100').attr('name','resType'+myCount);
                resvRadio.find('#resType100').attr('id','resType'+myCount);
                resvRadio.find('#delRes100').attr('name','delRes'+myCount);
                resvRadio.find('#delRes100').attr('id','delRes'+myCount);
                resvRadio.find('#resSelected100').attr('name','resSelected'+myCount);
                resvRadio.find('#resSelected100').attr('id','resSelected'+myCount);

                //CLAIMANTselect100
                var claimant = $('#CLAIMANTselect100').parent().parent().clone();
                var onchange = claimant.find('#CLAIMANTselect100').attr('onchange');
                if(onchange)
                {
                    var myregexp = new RegExp("WCCLocUnit100", "gim");
                    onchange = onchange.replace(myregexp,'WCCLocUnit'+myCount);
                    var myregexp2 = new RegExp("bodLocUnit100", "gim");
                    onchange = onchange.replace(myregexp2,'bodLocUnit'+myCount);
                }
                claimant.find('#CLAIMANTselect100').attr('onchange',onchange);
                claimant.find('#CLAIMANTselect100').attr('name','CLAIMANTselect'+myCount);
                claimant.find('#CLAIMANTselect100').attr('id','CLAIMANTselect'+myCount);
                claimant.find('#WCCLocUnit100').attr('name','WCCLocUnit'+myCount);
                claimant.find('#WCCLocUnit100').attr('id','WCCLocUnit'+myCount);
                claimant.find('#bodLocUnit100').attr('name','bodLocUnit'+myCount);
                claimant.find('#bodLocUnit100').attr('id','bodLocUnit'+myCount);

                //ITEMselect100
                var item = $('#ITEMselect100').parent().parent().clone();
                item.find('#ITEMselect100').attr('name','ITEMselect'+myCount);
                item.find('#ITEMselect100').attr('id','ITEMselect'+myCount);

                //CoverageGroup100
                var covGroup = $('#CoverageGroup100').clone();
                covGroup.children().each(function () {
                    if(this.id != 'ResCovTab100')
                    {this.remove()}
                });
                covGroup.attr('name','CoverageGroup'+myCount);
                covGroup.attr('id','CoverageGroup'+myCount);
                covGroup.find('#ResCovTab100').attr('name','ResCovTab'+myCount);
                covGroup.find('#ResCovTab100').attr('id','ResCovTab'+myCount);
                covGroup.find('#CovSelect100').attr('name','CovSelect'+myCount);
                covGroup.find('#CovSelect100').attr('id','CovSelect'+myCount);
                covGroup.find('#delCov100').attr('style','display:none');
                covGroup.find('#delCov100').attr('name','delCov'+myCount);
                covGroup.find('#delCov100').attr('id','delCov'+myCount);
                covGroup.find('#premEndClass100').attr('name','premEndClass'+myCount);
                covGroup.find('#premEndClass100').attr('id','premEndClass'+myCount);
                covGroup.find('#premEndClassCode100').attr('name','premEndClassCode'+myCount);
                covGroup.find('#premEndClassCode100').attr('id','premEndClassCode'+myCount);
                covGroup.find('#LossCodeSelect100').attr('name','delCov'+myCount);
                covGroup.find('#LossCodeSelect100').attr('id','LossCodeSelect'+myCount);
                covGroup.find('#COLSelect100').attr('name','COLSelect'+myCount);
                covGroup.find('#COLSelect100').attr('id','COLSelect'+myCount);
                covGroup.find('#reserveAmt100').val('');
                covGroup.find('#reserveAmt100').attr('name','reserveAmt'+myCount);
                covGroup.find('#reserveAmt100').attr('id','reserveAmt'+myCount);
                if(covGroup.find('#coveragesList100'))
                {covGroup.find('#coveragesList100').remove()}
                if(covGroup.find('#lossCodesList100'))
                {covGroup.find('#lossCodesList100').remove()}

                //addCov100
                var addCov = $('#addCov100').parent().clone();
                addCov.find('#addCov100').attr('name','addCov'+myCount);
                addCov.find('#addCov100').attr('id','addCov'+myCount);


                var newElement = $(document.createElement('li'));
                var newUL = $(document.createElement('ul'));
                newUL.addClass('leftlabel_twocol');
                newUL.attr('id','reserveTab'+myCount);

                newUL.append(resvRadio);
                newUL.append(claimant);
                newUL.append(item);
                newUL.append(covGroup);
                newUL.append(addCov);
                newElement.append(newUL);

                //get the actual element we are going to insert the new
                //claimant section in front of.  In this case, its the
                //add claimant button
                $(newElement).insertBefore($('#addClaimantBtnLi'));

                //set the display style of the delete button so it doesn't show
                //on the first coverage under the claimant
                if(document.getElementById("delCov"+myCount))
                {
                    document.getElementById("delCov"+myCount).style.display='none';
                }

                //set the display of the delete claimant button so it
                //does show
                if(document.getElementById("delRes"+myCount))
                {
                    document.getElementById("delRes"+myCount).style.display='';
                }

    }


}

//add another reserve for a new coverage/loss code combo
function addNewCov(me, whereFrom, cntr)
{

        var myId = me.id.substr(6,3);
        var appendToMe = whereFrom || '';

        //the new claimantNo is either the id from the current reserve
        //section where we are adding a new coverage,
        // or the id from the new reserve section where we are adding the
        //first coveage
        var myNewClaimantNo = cntr || myId;

        //find the original reserve information section and copy it
        //to build a new reserve information section
        //Needed to check WCLocUnit and bdLocUnit so WC and bonds can add coverage. 105750
        if (document.getElementById("ITEMselect"+myId) ||
            document.getElementById("WCCLocUnit"+myId) ||
            document.getElementById("bodLocUnit"+myId))
        {
            //increment the hidden variable coverage counter
            var myCount = document.getElementById("covCntr").value;
            myCount ++;
            document.getElementById("covCntr").value = myCount;

            //create new table element
            //var newElement = document.createElement('table');
            //creating new DIV element instead of table element.  IE
            //does not accept using inner.HTML to update a table's guts
           var newElement = document.createElement('ul');

                        //retrieve the coverage code list and place in a work field
           var workStr = document.getElementById("ResCovTab"+myId).innerHTML;

           if (appendToMe > '')
           {
               //get the actual element we are going to insertBefore
               //in the case of a new claimant, we insertBefore the Add
               //Claimant button
    //                         var x=document.getElementById(appendToMe);
               var x=document.getElementById("CoverageGroup"+myNewClaimantNo);
           }
           else
           {
              //get the actual element we are going to append to
              //in the case of a new coverage within a claimant, we
              //append to the existing coverges tables
              var x=document.getElementById("CoverageGroup"+myId);

              //add the _ to the new numbers for coverages being added
              //to existing claimants
              myNewClaimantNo = myNewClaimantNo +"_" + myCount;
            }

          //use a regex to change element names from to
          //a unique names
           var premEndClassSpan = new String("premEndClass"+myNewClaimantNo);
           var myregexp = new RegExp("premEndClass"+myId, "gim")
           workStr = workStr.replace(myregexp,premEndClassSpan);

           var premEndClassCode = new String("premEndClassCode"+myNewClaimantNo);
           var myregexp = new RegExp("premEndClassCode"+myId, "gim")
           workStr = workStr.replace(myregexp,premEndClassCode);

           var covTabName = new String("ResCovTab"+myNewClaimantNo);
           var myregexp = new RegExp("ResCovTab"+myId, "gim")
           workStr = workStr.replace(myregexp,covTabName);

           var covBoxName = new String("CovSelect"+myNewClaimantNo);
           var myregexp1 = new RegExp("CovSelect"+myId, "gim")
           workStr = workStr.replace(myregexp1,covBoxName);

           var covLossName = new String("LossCodeSelect"+myNewClaimantNo);
           var myregexp2 = new RegExp("LossCodeSelect"+myId, "gim")
           workStr = workStr.replace(myregexp2,covLossName);

           var colName = new String("COLSelect"+myNewClaimantNo);
           var myregexp3 = new RegExp("COLSelect"+myId, "gim")
           workStr = workStr.replace(myregexp3,colName);

           var resAmtName = new String("reserveAmt"+myNewClaimantNo);
           var myregexp4 = new RegExp("reserveAmt"+myId, "gim")
           workStr = workStr.replace(myregexp4,resAmtName);

           var covListName = new String("coveragesList"+myNewClaimantNo);
           var myregexp5 = new RegExp("coveragesList"+myId, "gim")
           workStr = workStr.replace(myregexp5,covListName);

           var lossListName = new String("lossCodesList"+myNewClaimantNo);
           var myregexp6 = new RegExp("lossCodesList"+myId, "gim");
           workStr = workStr.replace(myregexp6,lossListName);

           var colListName = new String("COLList"+myNewClaimantNo);
           var myregexp7 = new RegExp("COLList"+myId, "gim");
           workStr = workStr.replace(myregexp7,colListName);

           var delListName = new String("delCov"+myNewClaimantNo);
           var myregexp8 = new RegExp("delCov"+myId, "gim");
           workStr = workStr.replace(myregexp8,delListName);

         //update DIV to get to work in ie
            var tempString = '<table name="'
            + covTabName
            + '" id="'
            + covTabName
            + '" summary="Reserve Covs Loss Types">';

            newElement.id = covTabName;
            newElement.className = 'leftlabel_twocol';
            newElement.innerHTML = workStr;

        //add the new element into the reserve table division.
        //if this is added to new claimant, then we do an
        //insertBefore;  if added to an existing claimant,
        //we do an append
//        if (appendToMe > '')
//        {
                x.parentNode.insertBefore(newElement,x);
//        }
//        else
//        {
                x.appendChild(newElement);
//        }

        //set the display style of the delete button so it shows
        document.getElementById(delListName).style.display='';

        //set the reserve amount to blank for the new entry
        document.getElementById(resAmtName).value='';

        document.getElementById("premEndClass"+myNewClaimantNo).style.display = 'none';

        var Coverages = document.getElementById(covListName);
        //set select box Coverage values to the "Select Coverage" first option
        if (Coverages != null)
        {
                for (var count = 0; count < Coverages.options.length; count++)
                {
                    if(Coverages[count].value == 'XX_XX')
                    {
                        Coverages[count].selected = true;
                    } else
                    {
                        Coverages[count].selected = false;
                    }
                }
            }

        var LossCodes = document.getElementById(lossListName);
        //set select box Loss Code values to the "Select Type of Loss" first option
        if (LossCodes != null)
        {
                for (var count = 0; count < LossCodes.options.length; count++)
                {
                    if(LossCodes[count].value == 'XX_XX')
                    {
                        LossCodes[count].selected = true;
                    } else
                    {
                        LossCodes[count].selected = false;
                    }
                }
            }

        //we only have cause-of-loss for certain lines, so this might
        //not have a value in it
        if (COLCodes != null)
        {
                var COLCodes = document.getElementById(colListName);
                //set select box Cause of Loss values to the "Select Type Cause of Loss" first option
                for (var count = 0; count < COLCodes.options.length; count++)
                {
                    if(COLCodes[count].value == 'XX')
                    {
                        COLCodes[count].selected = true;
                    } else
                    {
                        COLCodes[count].selected = false;
                    }
                }
            }

         }
}

function addNewPayee(x)
{

         //increment the hidden variable payee counter
         var myCount = document.getElementById("payeeCntr").value;
         myCount ++;
         document.getElementById("payeeCntr").value = myCount;

         var paymentTabName = 'paymentTab100_' + myCount;

    //NOTE!!!  IE did not allow an innerHTML update to the table
    //element, so had to change it to a DIV element and put the table
    //within the DIV.
    //create new table element and new div element
    //var newElement = document.createElement('table');
    var newElement = document.createElement('li');
        newElement.id = paymentTabName;
    var newDiv = document.createElement('li');

    //retrieve the payee list and place in a work field
         var workStr = document.getElementById("paymentTab100").innerHTML;

    //change the name of the payee select box
         var paySelName = new String("PayeeSelect100_"+myCount);
          var myregexp = new RegExp("PayeeSelect100", "gim")
          workStr = workStr.replace(myregexp,paySelName);

          //add new delete button for payee
          var payeeDelBut = new String("delPayee100_"+myCount);
          var myregexp1 = new RegExp("delPayee100", "gim")
          workStr = workStr.replace(myregexp1,payeeDelBut);

          //add new payee first name field
          var payeefname = new String("payeeFname100_"+myCount);
          var myregexp2 = new RegExp("payeeFname100", "gim")
          workStr = workStr.replace(myregexp2,payeefname);

          //add new payee last name field
          var payeelname = new String("payeeLname100_"+myCount);
          var myregexp3 = new RegExp("payeeLname100", "gim")
          workStr = workStr.replace(myregexp3,payeelname);

          //add new business name field
          var payeebname = new String("payeeBname100_"+myCount);
          var myregexp21 = new RegExp("payeeBname100", "gim")
          workStr = workStr.replace(myregexp21,payeebname);

          //name radio button field
          var persOrBus = new String("persOrBus100_"+myCount);
          var myregexp24 = new RegExp("persOrBus100", "gim")
          workStr = workStr.replace(myregexp24,persOrBus);


          //row id for new payee name fields
          var payeeRname = new String("payeeNameRow100_"+myCount);
          var myregexp4 = new RegExp("payeeNameRow100", "gim")
          workStr = workStr.replace(myregexp4,payeeRname);
          var payeeRnamea = new String("payeeNameRowa100_"+myCount);
          var myregexp5 = new RegExp("payeeNameRowa100", "gim")
          workStr = workStr.replace(myregexp5,payeeRnamea);
          var payeeRnameb = new String("payeeNameRowb100_"+myCount);
          var myregexp20 = new RegExp("payeeNameRowb100", "gim")
          workStr = workStr.replace(myregexp20,payeeRnameb);
          var payeeRnamec = new String("payeeNameRowc100_"+myCount);
          var myregexp22 = new RegExp("payeeNameRowc100", "gim")
          workStr = workStr.replace(myregexp22,payeeRnamec);


          //rowids for the irs search section
          var irsRowa = new String("irsRowa100_"+myCount);
          var myregexp6 = new RegExp("irsRowa100", "gim")
          workStr = workStr.replace(myregexp6,irsRowa);
          var irsRowb = new String("irsRowb100_"+myCount);
          var myregexp7 = new RegExp("irsRowb100", "gim")
          workStr = workStr.replace(myregexp7,irsRowb);
          var irsRowc = new String("irsRowc100_"+myCount);
          var myregexp8 = new RegExp("irsRowc100", "gim")
          workStr = workStr.replace(myregexp8,irsRowc);
          var irsRowd = new String("irsRowd100_"+myCount);
          var myregexp9 = new RegExp("irsRowd100", "gim")
          workStr = workStr.replace(myregexp9,irsRowd);
          var irsRowe = new String("irsRowe100_"+myCount);
          var myregexp10 = new RegExp("irsRowe100", "gim")
          workStr = workStr.replace(myregexp10,irsRowe);

          //field names for the irs search section
          var irsNumber = new String("irsNumber100_"+myCount);
          var myregexp11 = new RegExp("irsNumber100", "gim")
          workStr = workStr.replace(myregexp11,irsNumber);
          var irsName = new String("irsName100_"+myCount);
          var myregexp12 = new RegExp("irsName100", "gim")
          workStr = workStr.replace(myregexp12,irsName);
          var irsCity = new String("irsCity100_"+myCount);
          var myregexp13 = new RegExp("irsCity100", "gim")
          workStr = workStr.replace(myregexp13,irsCity);
          var irsZip = new String("irsZip100_"+myCount);
          var myregexp14 = new RegExp("irsZip100", "gim")
          workStr = workStr.replace(myregexp14,irsZip);
          var irsPartial = new String("irsPartial100_"+myCount);
          var myregexp15 = new RegExp("irsPartial100", "gim")
          workStr = workStr.replace(myregexp15,irsPartial);
          var irsSearch = new String("irsSearch100_"+myCount);
          var myregexp16 = new RegExp("irsSearch100", "gim")
          workStr = workStr.replace(myregexp16,irsSearch);

    //get the div section of the IRS search results and add
    //buttons.
          var workStr2 = document.getElementById("irsSelectRow100").innerHTML;

    //change div and element names for IRS search results
          var irsSelectRow = new String("irsSelectRow100_"+myCount);
          var myregexp25 = new RegExp("irsSelectRow100", "gim")
          workStr = workStr.replace(myregexp25,irsSelectRow);
          var irsSelectRowA = new String("irsSelectRowA100_"+myCount);
          var myregexp26 = new RegExp("irsSelectRowA100", "gim")
          workStr = workStr.replace(myregexp26,irsSelectRowA);
          var irsSelectButt = new String("irsSelectButt100_"+myCount);
          var myregexp27 = new RegExp("irsSelectButt100", "gim")
          workStr = workStr.replace(myregexp27,irsSelectButt);
          var irsSelectdiv = new String("irsSelectdiv100_"+myCount);
          var myregexp28 = new RegExp("irsSelectdiv100", "gim")
          workStr = workStr.replace(myregexp28,irsSelectdiv);
          var IRS_Selected = new String("IRS_Selected100_"+myCount);
          var myregexp29 = new RegExp("IRS_Selected100", "gim")
          workStr = workStr.replace(myregexp29,IRS_Selected);

          //put the innerhtml into the new table on the screen
          //doing the innerHTML this way does NOT work in IE!!
//    newElement.innerHTML = workStr;
//    newElement.id = paymentTabName;
//    newElement.name = paymentTabName;
//    newElement.summary='Payment Entry';

    //update DIV to get to work in ie
    var tempString = '<table name="'
            + paymentTabName
            + '" id="'
            + paymentTabName
            + '" summary="Payment Entry">';


    newElement.innerHTML = workStr;

    //put the innerhtml into the new div on the screen
    newDiv.innerHTML = workStr2;
    newDiv.id = irsSelectRow;
    newDiv.name = irsSelectRow;

    //retrieve data element we need to append to
    var x=document.getElementById("PaymentGroup100");

    //add in the new table
    x.appendChild(newElement);

    //get the data element again with the new table
    x=document.getElementById("PaymentGroup100");
    //add the div into the data element
    //x.appendChild(newDiv);

    //make the delete button appear
    document.getElementById(payeeDelBut).style.display='';

    //make the Other Name rows disappear
    document.getElementById(payeeRname).style.display ='none';
    document.getElementById(payeeRnamea).style.display ='none';
    document.getElementById(payeeRnameb).style.display ='none';
    document.getElementById(payeeRnamec).style.display ='none';

    //make the IRS rows disappear
    document.getElementById(irsRowa).style.display ='none';
    document.getElementById(irsRowb).style.display ='none';
    document.getElementById(irsRowc).style.display ='none';
    document.getElementById(irsRowd).style.display ='none';
    document.getElementById(irsRowe).style.display ='none';

    document.getElementById(irsSelectRowA).style.display = 'none';
    document.getElementById(irsSelectRow).style.display = 'none';

        //when the user has selected anIRS person, we put that person
        //into the select box.  If the user did this on the first Payable To
        //selection, then the IRS person will be in all the subsequent select boxes
        //that copy the first Payable To.  This makes the hidden fields for
        //Other and  IRS Search all way over to the right and makes the screen
        //layout icky.  So here we are removing any payable to options that
        //have a value that starts with IRS_.
        var mySelElement = document.getElementById(paySelName);
        for(i=mySelElement.options.length-1;i>=0;i--)
        {
               //check to see if the option value starts with IRS_
                if (mySelElement.options[i].value.substr(0,4) == 'IRS_')
                {
                mySelElement.remove(i);
                }
        }

}

function delNewPayee(myPayee)
{

        //get the id number of the delete button so we can use it
        //to create the table name that we want to delete.
           var num=myPayee.id.substr(12);

    var tableIt = document.getElementById("paymentTab100_"+num);

    //we are just hiding the first cov table because we need it to add new
    //ones
    if (num == '')
    {
            document.getElementById("PaymentTab100").style.display = "none";
    }
    else
    {
                if(tableIt)
            {
                    //remove the coverage table completely from the DOM
                    tableIt.parentNode.removeChild(tableIt);
            }
        }
        updatePayto(myPayee);

}

function changePayee(thePayee)
{

         //pull the id# off the incoming Payable to name
         var idno = thePayee.id.substr(11);

         //special code for idno for Salvage/subro/reinsurance/other
         if (idno == '9000_2')
         {  idno = '9000'; }
         if (idno == '9000_3')
         {  idno = '9000'; }
         else if (idno == '1000_2')
         {  idno = '1000'; }

    var payToNames = 'payeeNameRow' + idno;
    var payToNamesa = 'payeeNameRowa' + idno;
    var payeeNameRowb = 'payeeNameRowb' + idno;
    var payeeNameRowc = 'payeeNameRowc' + idno;
    var irsSelectRow = 'irsSelectRow' + idno;
    var irsSelectRowA = 'irsSelectRowA' + idno;
    var irsSelectdiv = 'irsSelectdiv'+idno;
    var rowa = 'irsRowa' + idno;
    var rowb = 'irsRowb' + idno;
    var rowc = 'irsRowc' + idno;
    var rowd = 'irsRowd' + idno;
    var rowe = 'irsRowe' + idno;
    var addr2 = 'addTR2ID' + idno;
    var addr3 = 'addTR3ID' + idno;
    var addr4 = 'addTR4ID' + idno;
    var persOrBus = 'persOrBus' + idno;

         //check the value the user selected.  If it was Other, then
         //we need to show the payee name fields, otherwise we are hiding them.
         //If it was IRS, we need to display the IRS select box fields, otherwise
         //we are hiding them.
         if (thePayee.value == '999')
         {
                 //user selected Other so display only radio buttons
                 //for person or business to begin with
                 if (document.getElementById(payeeNameRowb))
                 {
                         document.getElementById(payeeNameRowb).style.display = "";
                         //uncheck the person/business checkboxes too
                         if (document.getElementById(persOrBus))
                         {
                      document.getElementById(persOrBus).checked = false;
//                      document.getElementById(persOrBus).getNextElement();
//                      document.getNextElementSibling;
//                      document.getNextElement.Sibling.persOrBus;
//                      document.getNextElementSibling(persOrBus).checked = false;
              //        document.mainform.persOrBus.checked = false;
              //        document.getElementById(persOrBus)[1].checked = false;
                         }
                 }
                 else if (document.getElementById(payeeNameRowc))
                 {
                         document.getElementById(payeeNameRowc).style.display = "";
                 }
                 //hide IRS fields
                 if (document.getElementById(rowa))
                 {
                document.getElementById(rowa).style.display = "none";
                document.getElementById(rowb).style.display = "none";
                document.getElementById(rowc).style.display = "none";
                document.getElementById(rowd).style.display = "none";
                document.getElementById(rowe).style.display = "none";
                document.getElementById(irsSelectRow).style.display='none';
                 }
                 if (document.getElementById(addr2))
                 {
                 document.getElementById(addr2).style.display = "";
                 document.getElementById(addr3).style.display = "";
                 document.getElementById(addr4).style.display = "";
                 }

//                 document.getElementById(irsSelectdiv).style.display = "none";
//                 document.getElementById(irsSelectRowA).style.display='none';

         }
         else if (thePayee.value == '99')
         {
                 //user selected IRS so show those fields
                 if (document.getElementById(rowa))
                 {
                document.getElementById(rowa).style.display = "";
                document.getElementById(rowb).style.display = "";
                document.getElementById(rowc).style.display = "";
                document.getElementById(rowd).style.display = "";
                document.getElementById(rowe).style.display = "";
                 }

                 //and hide the pay to name fields
                 if (document.getElementById(payToNames))
                 {
                     document.getElementById(payToNames).style.display = "none";
                     document.getElementById(payToNamesa).style.display = "none";
                     document.getElementById(payeeNameRowb).style.display = "none";
//                     document.getElementById(payeeNameRowc).style.display = "none";
             }
             if (document.getElementById(payeeNameRowc))
                 {
                         document.getElementById(payeeNameRowc).style.display = "none";
                 }
        if (document.getElementById(addr2))
                 {
                 document.getElementById(addr2).style.display = "none";
                 document.getElementById(addr3).style.display = "none";
                 document.getElementById(addr4).style.display = "none";
                 }

         }
         else
         {
                 //hide pay to first-last name fields
                 if (document.getElementById(payToNames))
                 {
                     document.getElementById(payToNames).style.display = "none";
                     document.getElementById(payToNamesa).style.display = "none";
                     document.getElementById(payeeNameRowb).style.display = "none";
             }
             if (document.getElementById(payeeNameRowc))
                 {
                         document.getElementById(payeeNameRowc).style.display = "none";
                 }                 //hide IRS fields
                 if (document.getElementById(rowa))
                 {
                document.getElementById(rowa).style.display = "none";
                document.getElementById(rowb).style.display = "none";
                document.getElementById(rowc).style.display = "none";
                document.getElementById(rowd).style.display = "none";
                document.getElementById(rowe).style.display = "none";
        //      document.getElementById(irsSelectdiv).style.display = "none";
                document.getElementById(irsSelectRow).style.display='none';
        //        document.getElementById(irsSelectRowA).style.display='none';
                }
        if (document.getElementById(addr2))
                 {
                 document.getElementById(addr2).style.display = "none";
                 document.getElementById(addr3).style.display = "none";
                 document.getElementById(addr4).style.display = "none";
                 }
                 if (idno != '1000'
                         && idno != '9000')
                 {
                         updatePayto(thePayee);
                 }
         }
}

function showHideName(radBut)
{
        //this function shows and hides the business name and
        //person name fields on the monetary entries screen
         var myId = radBut.id;
         var myVal = radBut.value;
         var idno = radBut.id.substr(9);
    var payToNames = 'payeeNameRow' + idno;
    var payToNamesa = 'payeeNameRowa' + idno;
    var payToNamesc =  'payeeNameRowc' + idno;
    var payeeFname = 'payeeFname' + idno;
    var payeeLname = 'payeeLname' + idno;
    var payeeBname = 'payeeBname' + idno;

         if (myVal == 'Person')
         {
             //show the person fields
                 document.getElementById(payToNames).style.display = "";
                 document.getElementById(payToNamesa).style.display = "";

             //hide the business name field
             document.getElementById(payToNamesc).style.display = "none";

             //clear any value the user might have put in business name
        document.getElementById(payeeBname).value = '';
         }
         else
         {
            //show the business field
            document.getElementById(payToNamesc).style.display = "";

            //hide the person field
                 document.getElementById(payToNames).style.display = "none";
                document.getElementById(payToNamesa).style.display = "none";

                //clear any value the user might have put in the person names
                 document.getElementById(payeeFname).value = "";
                document.getElementById(payeeLname).value = "";

         }
}


function delNewCov(it)
{

        //get the id number of the delete button so we can use it
        //to create the table name that we want to delete.
         var num=it.id.substr(6);

    var tableIt = document.getElementById("ResCovTab"+num);

    //we are just hiding the first cov table because we need it to add new
    //ones
    if (num == '')
    {
            document.getElementById("ResCovTab"+num).style.display = "none";
    }
    else
    {
                if(tableIt)
            {
                    //remove the coverage table completely from the DOM
                    tableIt.parentNode.removeChild(tableIt);
            }
        }

}

function delNewRes(thisClaimant)
{
        //get the id number of the delete button so we can use it
        //to create the table name that we want to delete.
         var num=thisClaimant.id.substr(6);
    var tableIt = document.getElementById("reserveTab"+num);
    var divIt = document.getElementById("CoverageGroup"+num);
    var butIt = document.getElementById("addCovTab"+num);

    //we are just hiding the first cov table because we need it to add new
    //ones
    if (num == '100')
    {
            document.getElementById("reserveTab"+num).style.display = "none";
            document.getElementById("CoverageGroup"+num).style.display = "none";
            document.getElementById("addCovTab"+num).style.display = "none";

    }
    else
    {
                if(tableIt)
            {
                    //remove the reserve and coverage table completely from the DOM
                    tableIt.parentNode.removeChild(tableIt);
                    divIt.parentNode.removeChild(divIt);
                    butIt.parentNode.removeChild(butIt);
            }
        }
}

//ajax global vars
var globalReq = '';
var ajaxreq=false;
var sequence=0;
var idNo=0;
var timeOut = '';
var dotsDone = 'N';
var Next25IRS = '';
var contSearch = '';
var verifyIRS = '';

function covAjax(x, id)
{

        //if the user has selected the "Select Vehicle" or
    //the "Select Location" option, we want to get out
    //without doing anything
    if (x.value == 'None')
    {
             return;
    }

    disableButtons();
    var lc = document.getElementById("lineCode").value;

    //split key info from select box on pipes |
    var whichOne = x.value.split('|');

    //get the variable id of the coverages select box figured out
    //so it can be used to load the data from the ajax request
    sequence = new String(x.id);
    sequence = sequence.substr(10);

    //get the unit and location
    var loc = whichOne[1];
    var unit = whichOne[2];

    //get whatToDo
    var whatToDo = document.getElementById("whatToDo").value;

    //get the claim id
    var claimId = id;

        try {
    // Firefox / IE7 / Others
    ajaxreq= new XMLHttpRequest();
   } catch (error) {
    try {
      // IE 5 / IE 6
      ajaxreq = new ActiveXObject("Microsoft.XMLHTTP");
    } catch (error) {
      return false;
    }
   }
   globalReq = x;
   ajaxreq.open("GET",'CoveragesList.pl?unit='+unit+';loc='+loc+';claimId='+claimId+';line='+lc+';seq='+sequence+';whatToDo='+whatToDo);
   ajaxreq.onreadystatechange = ajaxCovResponse;
   ajaxreq.send(null);
}

// ajaxResponse: Waits for response and calls a function
//this one loads data into covSelect
function ajaxCovResponse(x)
{
   if (ajaxreq.readyState == 4)
   {
                   var response = new Array();
        response = ajaxreq.responseText.split('|');

                var freshResponse = response[0];

                //if the user had put multiple coverage sections on the screen
                //then changing the vehicle has to change ALL of the coverages select
                //boxes, not just the original one.
                var tableArray = document.getElementsByTagName("table");
                var z=0;
                //don't put the 'g' global option into this regular expression
                //or you may have some weird problems.  Using 'g' causes the regular
                //expression to start where the last one left off, so if you were to
                //put some window.alerts into this code, things start going haywire.
                var regexp1 = new RegExp("ResCovTab"+sequence, "im");
                var myCovId = sequence;
                                var cov_num = '';
                                var num = 0;
                                var cov_valid = 1;

                                while(cov_valid == 1)
                                {
                                        myCovId = myCovId+cov_num;
                                        var covListName = new String("coveragesList"+myCovId);
                                        var select_span = document.getElementById("CovSelect"+myCovId);
                                        if(select_span){
                                                var myregexp5 = new RegExp("coveragesList"+sequence, "gim")
                                                freshResponse = freshResponse.replace(myregexp5,covListName);
                                                //alert(freshResponse);
                                                document.getElementById("CovSelect"+myCovId).innerHTML=freshResponse;

                                                num++;
                                                cov_num = '_'+num;
                                        }
                                        else{cov_valid = 0;}
                                }

                /*for (z=0; z<tableArray.length; z++)
                {
                        if (tableArray[z].id > ' ')
                        {
                    if (regexp1.test(tableArray[z].id))
                    {
                            //get the coverage id value 100, 100_1, 100_2 etc...
                            //and store it in myCovId
                            myCovId = tableArray[z].id.substr(9);

                                        //move an unchanged copy of the ajax response from hold
                                        //area so we aren't changing an already-changed version
                                        //of the response.  This prevents the field name id numbers from '
                                        //changing like this:   100, 100_1, 100_2_1, 100_3_2_1.
                                        freshResponse = response[0];

                    //create the new coverage id that we want to use for
                    //the select box, and a regexp with the old coverage id
                    //value, so we can change the value in the response[0]
                                var covListName = new String("coveragesList"+myCovId);
                                var myregexp5 = new RegExp("coveragesList"+sequence, "gim")
                                freshResponse = freshResponse.replace(myregexp5,covListName);
                                        document.getElementById("CovSelect"+myCovId).innerHTML=freshResponse;

                    }
                        }
                }*/
        enableButtons();
   }
   return true;
}

function setReserveSelected(x,type)
{
    sequence = new String(x.id);
    sequence = sequence.substr(7,3);
    document.getElementById("resSelected"+sequence).value = type;
}

function lossCodeAjax(x)
{

        disableButtons();
        var lc = document.getElementById("lineCode").value;

    //get the variable id of the losscode select box figured out
    //so it can be used to load the data from the ajax request
    sequence = new String(x.id);
    sequence = sequence.substr(13);

    //retrieve the text of the selected option.  textContent
    //works for Firefox and straight 'text' works for IE
    var sel = document.getElementById("coveragesList"+sequence);
    var selText = (sel.options[sel.selectedIndex].textContent
            || sel.options[sel.selectedIndex].text);
    var whatToDo = document.getElementById("whatToDo").value;
    if(whatToDo == '3' || whatToDo == '4' || whatToDo == '7')
    {

    }
    else
    {
            if(selText == '50 Premium Endorsements' || selText == '50 Premium Endorsements ')
            {
                document.getElementById("premEndClass"+sequence).style.display = '';
            }
            else
            {
                if (document.getElementById("premEndClass"+sequence))
                {
                    document.getElementById("premEndClass"+sequence).style.display = 'none';
                }
            }
    }

    var resSeq = new String(x.id);
    resSeq = resSeq.substr(13);
    var resType = '';
    if(document.getElementById("resSelected"+resSeq))
    {resType = document.getElementById("resSelected"+resSeq).value;}

        //split key info from select box on underline
    var whichOne = x.value.split('_');

    //get the loss code group
    var lossg = whichOne[1];

    //get the coverage id too
    var cov_id = whichOne[0];

    //get covcode from details screen if it exist
    var covCode = '';
    if(whichOne[2])
    { covCode = whichOne[2]; }
    else
    { covCode = selText.substring(0,2); }

    //get stormType if it is on Claims_PropLib screen.
    var stormClaim = '';
    if (lc == '300')
    { stormClaim = 'C'; }
    else if (lc == '301')
    { stormClaim = 'C'; }
    else if (lc == '302')
    { stormClaim = 'C'; }
    else if(document.getElementById("stormType"))
    { stormClaim = document.getElementById("stormType").value; }

    var claimStatus = '';
    claimStatus = document.getElementById("screenStatus").value;

        try {
    // Firefox / IE7 / Others
    ajaxreq= new XMLHttpRequest();
   } catch (error) {
    try {
      // IE 5 / IE 6
      ajaxreq = new ActiveXObject("Microsoft.XMLHTTP");
    } catch (error) {
      return false;
    }
   }
   globalReq = x;
   var systemInd = document.getElementById("systemInd");
   systemInd = systemInd.value;
   var load = document.getElementById("load");
   load = load.value;
   ajaxreq.open("GET",'LossCodesList.pl?lossCdGroup='+lossg+';line='+lc+';seq='+sequence+';coverage='+cov_id+';systemind='+systemInd+';covcode='+covCode+';restype='+resType+';stormType='+stormClaim+';claimStatus='+claimStatus+';load='+load);
   ajaxreq.onreadystatechange = ajaxLossCResponse;
   ajaxreq.send(null);


}

// ajaxResponse: Waits for response and calls a function
//this one loads data into loss code select box
function ajaxLossCResponse(x)
{
   if (ajaxreq.readyState == 4)
   {
                   var response = new Array();
        response = ajaxreq.responseText.split('|');
                document.getElementById("LossCodeSelect"+sequence).innerHTML=response[0];
       if(document.getElementById("stormType"))
       {
           var testelm = document.createElement('div');
           testelm.innerHTML=response[0];
           //alert(testelm.firstChild);
           causeOfLossAjax(testelm.firstChild);
       }
        enableButtons();
   }
   return true;
}


function causeOfLossAjax(x)
{
        disableButtons();
        var lc = document.getElementById("lineCode").value;

         //if this is not one of the linecodes that uses cause of loss, we
         //can return from this function now
         if (lc == '112'
                 || lc == '113'
                 || lc == '120'
                 || lc == '110'
                 || lc == '111'
                 || lc == '010'
                 || lc == '011'
                 || lc == '012'
                 || lc == '030'
                 || lc == '031'
                 || lc == '051')
         {
            //do nothing
         }
         else
         {
             enableButtons();
             return true;
         }
    //get the variable id of the cause-of-loss select box figured out
    //so it can be used to load the data from the ajax request
    sequence = new String(x.id);
    sequence = sequence.substr(13);

        //split key info from select box on pipes |
    var whichOne = x.value.split('_');

    //get the unit and location
    var causeLoss = whichOne[1];

    //get stormType if it is on Claims_PropLib screen.
    var stormClaim = '';
    if(document.getElementById("stormType"))
    { stormClaim = document.getElementById("stormType").value; }

    var claimStatus = '';
    claimStatus = document.getElementById("screenStatus").value;

   var load = document.getElementById("load");
   load = load.value;

        try {
    // Firefox / IE7 / Others
    ajaxreq= new XMLHttpRequest();
   } catch (error) {
    try {
      // IE 5 / IE 6
      ajaxreq = new ActiveXObject("Microsoft.XMLHTTP");
    } catch (error) {
      return false;
    }
   }
   globalReq = x;
   ajaxreq.open("GET",'CauseOfLossList.pl?causeLoss='+causeLoss+';line='+lc+';seq='+sequence+';stormType='+stormClaim+';claimStatus='+claimStatus+';load='+load);
   ajaxreq.onreadystatechange = ajaxCOLResponse;
   ajaxreq.send(null);

}

// ajaxResponse: Waits for response and calls a function
//this one loads data into cause of loss select box
function ajaxCOLResponse(x)
{
   if (ajaxreq.readyState == 4)
   {
                   var response = new Array();
        response = ajaxreq.responseText.split('|');
                document.getElementById("COLSelect"+sequence).innerHTML=response[0];
        enableButtons();
   }
   return true;
}

function closeReserve(x)
{


}

function searchIRSAjax(irsIt)
{
//         window.alert ('in searchIRS function!');

         idNo = irsIt.id.substr(9);
//         window.alert ('id is: ' + idNo);

         //retrieve the variables for reading the IRS master file
         var irsNumber = 'irsNumber' + idNo;
         var irsName = 'irsName' + idNo;
         var irsCity = 'irsCity' + idNo;
         var irsZip = 'irsZip' + idNo;
         var irsPartial = 'irsPartial' + idNo;
         var sessionID = document.getElementById('sessionID').value;

    //overwrite my variable names with their actual values
         irsNumber = document.getElementById(irsNumber).value;
         irsName = document.getElementById(irsName).value;
         irsCity = document.getElementById(irsCity).value;
         irsZip = document.getElementById(irsZip).value;
         irsPartial = document.getElementById(irsPartial).value;

   //show the irs section but keep the button hidden for now
   document.getElementById("irsSelectdiv"+idNo).style.display="";
   document.getElementById("irsSelectRow"+idNo).style.display="";
   document.getElementById("irsSelectRowA"+idNo).style.display="none";


   //create info line detailing that the businesses are being
   //searched for
   //first, change the style of the div so it really stands out
   var irsDiv = 'irsSelectdiv' + idNo;
   document.getElementById(irsDiv).innerHTML='';
   document.getElementById(irsDiv).className="waitDots";
   timeOut = setTimeout('dots("Retrieving matching businesses")',250);

        try {
    // Firefox / IE7 / Others
    ajaxreq= new XMLHttpRequest();
   } catch (error) {
    try {
      // IE 5 / IE 6
      ajaxreq = new ActiveXObject("Microsoft.XMLHTTP");
    } catch (error) {
      return false;
    }
   }
   globalReq = irsIt;

   ajaxreq.open("GET",'Claims_Monetary_IRS.pl?IRSnumber='+irsNumber
           +';Name1='+irsName+';ZIP1='+irsZip+';partialName1='+irsPartial
           +';idNo='+idNo+';sessionID='+sessionID+';City1='+irsCity
           +';func=readIRSMaster;Next25IRS='+Next25IRS+';contSearch='+contSearch);
   ajaxreq.onreadystatechange = ajaxIRSResponse;
   ajaxreq.send(null);

}

// ajaxResponse: Waits for response and calls a function
//this one loads data into cause of loss select box
function ajaxIRSResponse(x)
{
//        setTimeout('showProcessing("Retrieving matching businesses.")',6000);
   if (ajaxreq.readyState == 4)
   {
                   //stop the timeout from running
                   clearTimeout(timeOut);
                   dotsDone = 'Y';

                   //return the font information in the div we are loading back to normal
            var irsDiv = 'irsSelectdiv' + idNo;
            document.getElementById(irsDiv).innerHTML = '';
            document.getElementById(irsDiv).className="undoDots";

        //retrieve data from ajax call
                   var response = new Array();
        response = ajaxreq.responseText.split('|');
                document.getElementById("irsSelectdiv"+idNo).innerHTML=response[0];
                document.getElementById("irsSelectRow"+idNo).style.display="";

                //set button for adding IRS to show
                document.getElementById("irsSelectRowA"+idNo).style.display="";

                //reinitialize the continue search work variables
                Next25IRS = '';
                contSearch = '';

   }
   return true;
}

//this function displays message with dots on it to hold the user's
//attention while the system is doing a slow process.
function dots(dotString)
{
        //special code added to dots to make it exit when I want
        //it to
        if (dotsDone == 'Y')
        {
                dotsDone = 'N';
             return;
        }

    if(dotString.length>"Retrieving matching businesses".length + 10)
    {
        dotString = "Retrieving matching businesses";
    }
    else
    {
        dotString = dotString+".";
    }
    var irsName = 'irsSelectdiv' + idNo;
    document.getElementById(irsName).innerHTML = dotString;
    setTimeout('dots("'+dotString+'")',250);
}

//this function runs when the Select next 25 IRS entries button is pressed.
//it moves a couple of values into two javascript variables and presses
//the irs search button again to continue the search
function next(val,val2)
{
  Next25IRS = val;
  contSearch = val2;
  //press the IRS search button
  document.getElementById("irsSearch"+idNo).click();

 }

//this function takes the IRS information from the select box
//and writes it into the Payable To select box
function selectIRS(myButtObject)
{
        //retrieve the id from the button irsSelectButt100
        var myIdno = myButtObject.id.substr(13);

        //string the id with the select box name
        var selectId = 'IRS_Selected' + myIdno;

    //retrieve the text of the selected option.  textContent
    //works for Firefox and straight 'text' works for IE
    var sel = document.getElementById(selectId);
        var selText = (sel.options[sel.selectedIndex].textContent
                || sel.options[sel.selectedIndex].text);

        //retrieve the value of the selected option
        var selValue = 'IRS_' + document.getElementById(selectId).value;

    //get the name of the Payable To select box
    var PayeeSelBox = 'PayeeSelect' + myIdno;

        //insert selected IRS item into Payable To select box
    var myBox = document.getElementById(PayeeSelBox);
        myBox.options.add(new Option(selText,selValue,true));

    //****************************************************
    //this section of code will add the IRS address to the address select box
    //****************************************************
    //split info from select box on comma
    var addressDetails = new Array();
    addressDetails = selText.split(', ');

    //string the address details together for the address select box key
    //using a pipe.
    var addressKeyString = new String();
    addressKeyString = 'NewLoc_NewParty_'
            + addressDetails[1] + '_';

    if (addressDetails[2] > ' ')
    {
               addressKeyString += addressDetails[2] + '_' + addressDetails[3];
    }
    else
    {
        addressKeyString += addressDetails[3] + '_' + addressDetails[2];
    }
    addressKeyString += '_'
            + addressDetails[4] + '_'
            + addressDetails[5] + '_'
            + addressDetails[6];

    var addressValueString = new String();
    //first value is the name
    addressValueString = addressDetails[1];
    //second value is name 2, which is sometimes blank so we want to
    //poke the address in there or it will err for a missing address
    if (addressDetails[2] > ' ')
    {
            addressValueString += ', ' + addressDetails[2] + ', ' + addressDetails[3];
    }
    else
    {
             addressValueString += ', ' + addressDetails[3] + ', ' + addressDetails[2];
    }

    addressValueString += ', '
            + addressDetails[4] + ', '
            + addressDetails[5] + ', '
            + addressDetails[6];

        var myAddressBox = document.getElementById('selAddress');
        myAddressBox.options.add(new Option(addressValueString,addressKeyString,false));

    //****************************************************
    //end of section adding IRS address to address select box
    //****************************************************

        //hide irs search fields
        var irsSelectRow = 'irsSelectRow' + myIdno;
    var irsSelectRowA = 'irsSelectRowA' + myIdno;
    var irsSelectdiv = 'irsSelectdiv'+myIdno;
    var rowa = 'irsRowa' + myIdno;
    var rowb = 'irsRowb' + myIdno;
    var rowc = 'irsRowc' + myIdno;
    var rowd = 'irsRowd' + myIdno;
    var rowe = 'irsRowe' + myIdno;
        document.getElementById(rowa).style.display = "none";
    document.getElementById(rowb).style.display = "none";
    document.getElementById(rowc).style.display = "none";
    document.getElementById(rowd).style.display = "none";
    document.getElementById(rowe).style.display = "none";
    document.getElementById(irsSelectRow).style.display='none';

    //we also want to add the wording for the IRS selection into
    //a hidden field in the webpage so Perl can access it to update
    //claims tables
        //create new input hidden element
    var newHidInput = document.createElement('input');
    newHidInput.id = 'newIRS_' + myIdno;
    newHidInput.name = 'newIRS_' + myIdno;
    newHidInput.type = 'hidden';
    newHidInput.value = selText;

    document.mainform.appendChild(newHidInput);

    //select the new item added into the Payee select box;
    //need to do this for IE.  The 'true' parameter above in
    //the myBox.options.add command works fine for FireFox.
    for (var count = 0; count < myBox.options.length; count++)
    {
        if(myBox[count].value == selValue)
        {
            myBox[count].selected = true;
        } else
        {
            myBox[count].selected = false;
            }
        }
    updatePayto(myButtObject);
}

function updatePayto(x)
{

         //get the id# for the 'Name as it appears on Draft' text box
         //var myIdno = x.id.substr(11, 3);
         var myIdno = "100";
         var myTextBox = 'draftPayTo' + myIdno;
         var myTextBox2 = 'DRAFTPAYTO2' + myIdno;

    //retrieve all the select boxes from the html page so we can
    //loop through them and find the right ones to pull data from
    var tableArray = document.getElementsByTagName("select");
    var z=0;
    var y='';    //store string of names
    var y2 = '';  //store second string of names
    var firstTime = 'Y';

    //get the addClaimantTab table element
    //so we can insert in front of it
    for (z=0; z<tableArray.length; z++)
    {
        if (tableArray[z].id.substr(0,11) == 'PayeeSelect')
        {
                //retrieve the text of the selected payee
                var mySel = tableArray[z];
                //textcontent works for FireFox;  IE requires plain text
                var selText = (mySel.options[mySel.selectedIndex].textContent
                        || mySel.options[mySel.selectedIndex].text);

                 var selValue = mySel.options[mySel.selectedIndex].value;

            //retrieve the value of the selected payee.
            //if the value is OT - Other, then we need to get first/last
            //name field data or business name data instead of select
            //box data
            if (selValue == '999')
            {
                //retrieve the id of the select box
                var selectBoxIdNo = tableArray[z].id.substr(11);
                                 var selectBoxId = 'PayeeSelect' + selectBoxIdNo;

                        //retrieve the text of the person and business name
                        //var persBusName = 'persOrBus' + selectBoxIdNo;
                        var persFirst = 'payeeFname' + selectBoxIdNo;
                        var persLast = 'payeeLname' + selectBoxIdNo;
                        var busName = 'payeeBname' + selectBoxIdNo;

                    //if its a person, get the person name
                    if (document.getElementById(busName).value == '')
                    {
                        selText = document.getElementById(persFirst).value
                                + ' '
                                + document.getElementById(persLast).value
                    }
                    else
                    {
                            //otherwise get the business name
                    selText = document.getElementById(busName).value;
                    }
            }
            //if its an IRS person, use this to create the name info
            if (selValue.substr(0,4) == 'IRS_')
            {
                    var myArray = new Array();
                    myArray = selText.split(/,/g);

                selText = myArray[1] + ' ' + myArray[2];

                    var workField = '';
                    //remove leading spaces from selected text
                    for  (w=0; w<3; w++)
                    {
                        workField = selText.substr(0,1);
                        if (workField == ' ')
                        {
                            //drop off the leading space
                            selText = selText.substring(1);
                        }
                    }

            }

                //first time through we don't want a comma
                if (firstTime == 'Y')
                {
                                y=selText;
                                firstTime = 'N';
                }
                else
                {
                    y=y + ' & ' + selText;
            }

        }
    }

    //truncate name string to tso lines of 73 characters each
    if (y > '')
    {
            y2 = y.substr(73,73);
            y = y.substr(0,73);
    }

    //plop name string into text box
    document.getElementById(myTextBox).value = y;
    document.getElementById(myTextBox2).value = y2;
}

//this function will show the Field draft, print and mailing options, or hide
//it based on if the wire transfer checkbox is checked or not.
function showDraftOpts(x)
{
        var idNo = x.id.substr(11);

         var draftTabName = 'draftDetailsTable' + idNo;

         if (x.checked == true)
         {
             document.getElementById(draftTabName).style.display='none';
         }
         else
         {
             document.getElementById(draftTabName).style.display='';
         }

}

//this function will show or hide the field draft date and draft
//numbers, and the print options select box
function showFieldDraft(x)
{

    var mySeq = x.id.substr(10);
           var myField = 'printOptID' + mySeq;
           var myDateTD = 'draftDateTD' + mySeq;
           var myprintOptTR = 'printOptTR' + mySeq;
           var fieldDraftTR = 'fieldDraftTR' + mySeq;
           var fieldDraftTR2 = 'fieldDraftTR2' + mySeq;
           var fieldDraftTR3 = 'fieldDraftTR3' + mySeq;
//           var mailOptTR = 'mailOptTR' + mySeq;
           //fieldDraftID100


    //decide to show or hide the draft date/number fields
    if (document.getElementById(myField).value == 'F'
            || document.getElementById(myField).value ==  'P'
            || document.getElementById(myField).value ==  'W'
            || document.getElementById(myField).value ==  'E')
    {
            if (document.getElementById(myField).value ==  'F'
                    || document.getElementById(myField).value ==  'W')
            {
                document.getElementById(fieldDraftTR3).style.display="";
                if(!$("#insurpay_fields").hasClass('hidden'))
                {$("#insurpay_fields").addClass('hidden');}
            }
            else
            {
                document.getElementById(fieldDraftTR3).style.display="none";
            }
            if (document.getElementById(myField).value ==  'F'
                    || document.getElementById(myField).value ==  'P' )
            {
                document.getElementById(fieldDraftTR).style.display="";
                document.getElementById(fieldDraftTR2).style.display="";
                if(!$("#insurpay_fields").hasClass('hidden'))
                {$("#insurpay_fields").addClass('hidden');}
            }
            else
            {
                document.getElementById(fieldDraftTR).style.display="none";
                document.getElementById(fieldDraftTR2).style.display="none";
            }
            if(document.getElementById(myField).value ==  'E')
            {$("#insurpay_fields").removeClass('hidden');}
//                document.getElementById(fieldDraftTR).style.display="";
//                document.getElementById(fieldDraftTR2).style.display="";
//                document.getElementById(fieldDraftTR3).style.display="";
    }
    else
    {
            //removing mail options select box
//            if (document.getElementById(myField).value == 'B')
//            {
//                    if (document.getElementById(mailOptTR))
//                    {
//                          document.getElementById(mailOptTR).style.display="";
//                  }
//            }
//            else
//            {
//                    if (document.getElementById(mailOptTR))
//                    {
//                         document.getElementById(mailOptTR).style.display="none";
//                 }
//            }
//            if (document.getElementById(myDateTD))
//            {
                    document.getElementById(fieldDraftTR).style.display="none";
                           document.getElementById(fieldDraftTR2).style.display="none";
                    document.getElementById(fieldDraftTR3).style.display="none";
//            }
    }

    //call function to show/hide the mailing address information
    //showHideMail(document.getElementById('mailOptID'+mySeq));

    //will show the mailing options for all print options except wire transfer
    showHideMail(document.getElementById('printOptID'+mySeq));

}

//this function will show or hide the mailing address information
//for the draft
function showHideMail(x)
{

//    var mySeq = x.id.substr(9);
        var mySeq = x.id.substr(10);
    var mailValue = x.value;
           var row1 = 'mailTR1ID' + mySeq;
           var row2 = 'mailTR2ID' + mySeq;
           var row3 = 'mailTR3ID' + mySeq;
           var row4 = 'mailTR4ID' + mySeq;
    var myField = 'printOptID' + mySeq;
    $('#mailTR2ID100').hide();
    $('#mailTR3ID100').hide();
    $('#mailTR4ID100').hide();

//    //An "I" means to mail it out, so show mailing fields in this case
//    if (mailValue == 'O'
//            && document.getElementById(myField).value == 'B')

        //show mailto information for all drafts except Wire Transfer
        if ((document.getElementById(myField).value != 'W'
                && document.getElementById(myField).value != ''
                && document.getElementById(myField).value != 'A'
                && document.getElementById(myField).value != 'E')
                || ($("#"+myField).val() == 'E'
                   && $("#PM_PaymentType").val() == 1))
    {
            if (document.getElementById('AddrSelTR'))
            {
                    document.getElementById('AddrSelTR').style.display="";
                    if (document.getElementById('selAddress').value == "OT")
                    {
                        if (document.getElementById(row1))
                        {
                            document.getElementById(row1).style.display="none";
                        }
                    }
                    else
                    {
                        if (document.getElementById(row1))
                        {
                            document.getElementById(row1).style.display="none";
                        }
                    }
            }

    }
    else
    {
            //otherwise don't show mailing fields.
            if (document.getElementById('AddrSelTR'))
            {
                    document.getElementById('AddrSelTR').style.display="none";
            }
            if (document.getElementById(row1))
            {
                    document.getElementById(row1).style.display="none";
            }
            document.getElementById(row2).style.display="none";
            document.getElementById(row3).style.display="none";
            document.getElementById(row4).style.display="none";
    }


}

//this section is used to show/hide the correction options in the screen,
//including correct payments, void drafts, remove payments, reinstate drafts,
//etc...

var workDiv = '';

function changePayment(x, claimId, theDiv)
{

        var myId = x.id;
        var myNumber = myId.substr(10);
        var myDraftId = '';
        var myCashId = ''
        var myClaim = claimId;
        var myDiv = theDiv;

        //first, clear up any innerhtml left over from other correction
        //seletions:
        if (document.getElementById('correctionDiv'))
        {
                document.getElementById('correctionDiv').innerHTML='';
        }
        if (document.getElementById('subroCorrectDiv'))
        {
                document.getElementById('subroCorrectDiv').innerHTML='';
        }
        if (document.getElementById('salvCorrectDiv'))
        {
                document.getElementById('salvCorrectDiv').innerHTML='';
        }
        if (document.getElementById('ROCCorrectDiv'))
        {
                document.getElementById('ROCCorrectDiv').innerHTML='';
        }

    //this is a draft, get the draft#
        if (x.value.substr(0,6) == 'Draft_')
        {
       myDraftId = x.value.substr(6);
        }
        else
        {
                //we are assuming all other id's are cash record id's
             myCashId = x.value;
        }


         if (x.value == 'X')
         {
                 //no draft or cash selected, don't show data
                 document.getElementById(myDiv).style.display="none";
         }
         else
         {
                 //draft selected, show data
        document.getElementById(myDiv).style.display="";

        workDiv = myDiv;

        //call ajax function to build cash correction section
        cashCorrectAjax(myDraftId,myCashId,myClaim);
         }

}



function cashCorrectAjax(draft,cashRec, myClaim)
{

        //get claimid from screen:
        var claimid = myClaim;
        var sessionID = 0;

        var whatToDo = (document.getElementById('whatToDo').value || '');

   //create info line detailing that the businesses are being
   //searched for
   //first, change the style of the div so it really stands out
    document.getElementById(workDiv).innerHTML='';
    document.getElementById(workDiv).className="waitDots";
    timeOut = setTimeout('dots2("Retrieving information.  Please wait.")',250);

        try {
    // Firefox / IE7 / Others
    ajaxreq= new XMLHttpRequest();
   } catch (error) {
    try {
      // IE 5 / IE 6
      ajaxreq = new ActiveXObject("Microsoft.XMLHTTP");
    } catch (error) {
      return false;
    }
   }

   var rand_number = Math.random()
   //globalReq = irsIt;

   ajaxreq.open("GET",'ClaimsCorrections.pl?DraftNumber='+draft
           +';claimid='+claimid+';whatToDo='+whatToDo+';sessionID='+sessionID+';CashNumber='+cashRec+';preventCaching='+rand_number);

   ajaxreq.onreadystatechange = ajaxcashCorrectResponse;
   ajaxreq.send(null);

}


// ajaxResponse: Waits for response and calls a function
//this one loads data into cause of loss select box
function ajaxcashCorrectResponse(x)
{
//        setTimeout('showProcessing("Retrieving matching businesses.")',6000);
   if (ajaxreq.readyState == 4)
   {
                   //stop the timeout from running
                   clearTimeout(timeOut);
                   dotsDone = 'Y';

                   //return the font information in the div we are loading back to normal
            document.getElementById(workDiv).innerHTML = '';
            document.getElementById(workDiv).className="undoDots";

        //retrieve data from ajax call
                   var response = new Array();
        response = ajaxreq.responseText.split('|');
                document.getElementById(workDiv).innerHTML=response[0];
                document.getElementById(workDiv).style.display="";


   }
   return true;
}


//this function displays message with dots on it to hold the user's
//attention while the system is doing a slow process.
function dots2(dotString)
{
        //special code added to dots to make it exit when I want
        //it to
        if (dotsDone == 'Y')
        {
                dotsDone = 'N';
             return;
        }

    if(dotString.length>"Retrieving Information.  Please wait.".length + 10)
    {
        dotString = "Retrieving Information.  Please wait.";
    }
    else
    {
        dotString = dotString+".";
    }
    document.getElementById(workDiv).innerHTML = dotString;
    setTimeout('dots2("'+dotString+'")',250);
}

//end of correction section code

//this little function loops thru the input fields on the web page
//and strips off non-numeric data values and adds them up; then
//displays the total on the screen.
function reCalc(x)
{

    var totalAmt = 0;
        var numbErr = 'N';
        var returnTotalAmt = new Number(0);

    //loop thru table elements to find payments
    var tableArray = document.getElementsByTagName("input");
    var z=0;
    var y='';    //store string of names
    var firstTime = 'Y';
    var Dollar = 0;

    //loop thru input elements to add them up for total field
    for (z=0; z<tableArray.length; z++)
    {
        if (tableArray[z].id.substr(0,4) == 'pay_'
                || tableArray[z].id.substr(0,7) == 'contAmt')
        {
            Dollar = tableArray[z].value;

            //when payment found, remove commas and $ signs
            if (Dollar == '')
            {
                //spaces default to zero
                Dollar = 0;
            }
            else
            {
                //first, remove white space
                Dollar=Dollar.replace(/\s/g,'');

                //next, remove commas
                Dollar=Dollar.replace(/\,/g,'');

                //remove $ signs
                Dollar=Dollar.replace(/\$/g,'');

            }

            //then add them up
            if (!isNaN(Dollar))
            {
                totalAmt += (Dollar*1);
            }
            else
            {
                     numbErr = 'Y';
            }
        }
    }

         if (numbErr == 'N')
         {
             //at the end, move payment to screen total
                         //Call toFixed to make the totalAmt a dollar amount with 2 decemal number.
             returnTotalAmt = totalAmt.toFixed(2);
             if (document.getElementById('draftScreenTotal'))
             {
                     document.getElementById('draftScreenTotal').value='$'+returnTotalAmt;
             }
             else if (document.getElementById('checkScreenTotal') )
             {
                 document.getElementById('checkScreenTotal').value='$'+returnTotalAmt;
                 $("#checkScreenTotalDiv").text('$'+returnTotalAmt);
             }
         }
         else
         {
                 //or move a message indicating we can't add it up
                 if (document.getElementById('draftScreenTotal'))
             {
                     document.getElementById('draftScreenTotal').value="Unable to Calculate";
             }
             else if (document.getElementById('checkScreenTotal') )
             {
                 document.getElementById('checkScreenTotal').value="Unable to Calculate";
             }
         }
}

function verifyIRSAjax(irsIt)
{

         idNo = irsIt.id.substr(9);
//         window.alert ('id is: ' + idNo);

         //retrieve the variables for reading the IRS master file
         var irsNumber = 'irsNumber' + idNo;
          var irsName = '';
          var irsCity = '';
         var irsZip = '';
         var irsPartial = '0';
         var sessionID = document.getElementById('sessionID').value;
    verifyIRS = irsNumber;
    //overwrite my variable names with their actual values
         irsNumber = document.getElementById(irsNumber).value;


        try {
    // Firefox / IE7 / Others
    ajaxreq= new XMLHttpRequest();
   } catch (error) {
    try {
      // IE 5 / IE 6
      ajaxreq = new ActiveXObject("Microsoft.XMLHTTP");
    } catch (error) {
      return false;
    }
   }
   globalReq = irsIt;

   ajaxreq.open("GET",'Claims_Monetary_IRS.pl?IRSnumber='+irsNumber
           +';Name1='+irsName+';ZIP1='+irsZip+';partialName1='+irsPartial
           +';idNo='+idNo+';sessionID='+sessionID+';City1='+irsCity
           +';func=readIRSMaster;Next25IRS='+Next25IRS+';contSearch='+contSearch);
   ajaxreq.onreadystatechange = ajaxIRSverify;
   ajaxreq.send(null);

}

// ajaxResponse: Waits for response and calls a function
//this one loads data into cause of loss select box
function ajaxIRSverify(x)
{
//        setTimeout('showProcessing("Retrieving matching businesses.")',6000);
   if (ajaxreq.readyState == 4)
   {
        //retrieve data from ajax call
                   var response = new Array();
        response = ajaxreq.responseText.split('|');

                if (response[0]  == "<p style='color:red'> No matching IRS business found! </p>")
                {

                        //add the CoverageGroup DIV section to the screen now
                        //first, nab the existing CoverageGroup
                    var newElDiv = document.createElement('div');

                        //next, name my new div
                    var divName = "irsInvalid";
                newElDiv.id = divName;
                newElDiv.name = divName;

                var y=document.getElementById(verifyIRS);

                //now, put the new Div into the webpage immediately in front of
                        //the Add Claimant button table
                        y.parentNode.insertBefore(newElDiv,y);

                        //set the div class for the error:
                        document.getElementById(divName).className="error";

                        //put invalid in the irs# box
                  document.getElementById(verifyIRS).value='invalid!!';

                }

   }
   return true;
}

function clearNode(node)
{
    if(node != null)
    {
        if(node.nodeName == 'INPUT') {
            if(node.type=='text') {
                if(node.value == '0')
                {node.value = '0';}
                else if(node.value == '0.00')
                {node.value = '0.00';}
                else
                {node.value='';}
            }
            else if(node.type=='checkbox') {
                node.checked=0;
            }
        }
        else if(node.nodeName == 'TEXTAREA')
        {node.value = '';}
        else if(node.nodeName == 'SELECT') {
            node.selectedIndex=0;
        }
        else if(node.nodeName == 'DIV' && node.className == 'errorInfo'){
            //when we clear the node, let's get rid of any screen errors
            node.parentNode.parentNode.removeChild(node.parentNode);
        }
        else {
            for(var x=0;node.childNodes[x];x++) {
                clearNode(node.childNodes[x]);
            }
        }
    }
}

function swapSpecial(x, id)
{
        //first incoming parameter is part of the section name we want to swap
        //second incoming parameter is the numeric identifier of that section.


        //if its sections 3, 4, or 5 or 7 (subro, contribution, reinsurance, other)
        //then we want to show section 3.  This function was built for
        //that purpose, to switch to section 3 if sections
        //4 or 5 or 7 were selected and update the heading and
        //update the hidden field contOthRein so the Perl knows what to do.
    //clearNode(document.getElementById('entry0'));
    //clearNode(document.getElementById('entry1'));
    //clearNode(document.getElementById('entry2'));

    //this code enables and disables the Close Claim button.
    if(document.getElementById('closeClaimButton') && document.getElementById('screenStatus').value == 'A')
    {
        if(document.getElementById('whatToDo').value == 'X')
        { document.getElementById('closeClaimButton').disabled = false; }
        else
        { document.getElementById('closeClaimButton').disabled = true; }
    }
    else if(document.getElementById('closePending') && document.getElementById('screenStatus').value == 'CF')
    {
        if(document.getElementById('whatToDo').value == '2' || document.getElementById('whatToDo').value == '3' ||
          document.getElementById('whatToDo').value == '4' || document.getElementById('whatToDo').value == '5' ||
          document.getElementById('whatToDo').value == '7')
        { document.getElementById('closePending').disabled = true; }
        else
        { document.getElementById('closePending').disabled = false; }
    }

    clearNode(document.getElementById('entry3'));
    if(!($('#entry12').hasClass('hidden')))
    {$('#entry12').addClass('hidden')}
    if(id == 0)
    {clearNode(document.getElementById('entry0'));}
    if(id == 1)
    {
        if($('#entry1').children().length > 1)
        {clearNode(document.getElementById('entry1'));}
    }
    //clearNode(document.getElementById('entry6'));
    if (id == 4)
    {
            //Received Reinsurance
        document.getElementById('contOthRein').value = 'R';
        document.getElementById('receivedDiv3').innerHTML = 'RECEIVED REINSURANCE';
        document.getElementById('salvDiv').style.display = 'none';
        document.getElementById('contAmtTH9000').innerHTML = 'Amount:';
        if(document.getElementById('payOptIDTR_9000'))
        {document.getElementById('payOptIDTR_9000').style.display = ''; }
        if(document.getElementById('checkAmtTR9000'))
        {document.getElementById('checkAmtTR9000').style.display = '';}
        document.getElementById('remitterIDS9000').style.display = 'none';
        document.getElementById('remitterIDS9000_2').style.display = 'none';
        document.getElementById('remitterIDS9000_3').style.display = '';
        if ( document.getElementById('remitterIDS1000'))
        {
                 document.getElementById('remitterIDS1000').style.display = 'none';
                 document.getElementById('remitterIDS1000_2').style.display = 'none';
        }
            if (document.getElementById('irsRow'))
            {
                document.getElementById('irsRow').style.display="none";
            }
            id = 3;
    }
    else if (id == 5)
    {
               //Received Salvage
         document.getElementById('contOthRein').value = 'S';
         document.getElementById('receivedDiv3').innerHTML = 'RECEIVED SALVAGE';
         document.getElementById('salvDiv').style.display = '';
         document.getElementById('contAmtTH9000').innerHTML = '<span>Check Amount:</span><a class="help" onclick="toggleHelp(\'amt_help\')">&nbsp;&nbsp;</a>';
         if(document.getElementById('payOptIDTR_9000'))
        {document.getElementById('payOptIDTR_9000').style.display = 'none'; }
        if(document.getElementById('checkAmtTR9000'))
        {document.getElementById('checkAmtTR9000').style.display = 'none';}
         document.getElementById('remitterIDS9000').style.display = '';
         document.getElementById('remitterIDS9000_2').style.display = 'none';
                 document.getElementById('remitterIDS9000_3').style.display = 'none';
         if (document.getElementById('remitterIDS1000'))
         {
                 document.getElementById('remitterIDS1000').style.display = 'none';
                 document.getElementById('remitterIDS1000_2').style.display = 'none';
         }
            if (document.getElementById('irsRow'))
            {
                document.getElementById('irsRow').style.display="none";
            }
             id = 3;
    }
    else if (id == 3)
    {
               //Received Contribution
        document.getElementById('contOthRein').value = 'N';
        document.getElementById('receivedDiv3').innerHTML = 'RECEIVED CONTRIBUTION';
                 document.getElementById('salvDiv').style.display = 'none';
        document.getElementById('contAmtTH9000').innerHTML = 'Amount:';
        if(document.getElementById('payOptIDTR_9000'))
        {document.getElementById('payOptIDTR_9000').style.display = ''; }
        if(document.getElementById('checkAmtTR9000'))
        {document.getElementById('checkAmtTR9000').style.display = '';}
        document.getElementById('remitterIDS9000').style.display = 'none';
        document.getElementById('remitterIDS9000_2').style.display = '';
        document.getElementById('remitterIDS9000_3').style.display = 'none';
        if (document.getElementById('remitterIDS1000'))
        {
                document.getElementById('remitterIDS1000').style.display = 'none';
                document.getElementById('remitterIDS1000_2').style.display = 'none';
            }
            if (document.getElementById('irsRow'))
            {
                document.getElementById('irsRow').style.display="";
            }
    }
    else if (id == 7)
    {
               //Received Other
        document.getElementById('contOthRein').value = 'O';
        document.getElementById('receivedDiv3').innerHTML = 'RECEIVED OTHER';
        document.getElementById('salvDiv').style.display = 'none';
        document.getElementById('contAmtTH9000').innerHTML = 'Amount:';
        if(document.getElementById('payOptIDTR_9000'))
        {document.getElementById('payOptIDTR_9000').style.display = ''; }
        if(document.getElementById('checkAmtTR9000'))
        {document.getElementById('checkAmtTR9000').style.display = '';}
         document.getElementById('remitterIDS9000').style.display = 'none';
         document.getElementById('remitterIDS9000_2').style.display = '';
         document.getElementById('remitterIDS9000_3').style.display = 'none';
        if (document.getElementById('remitterIDS1000'))
        {
                 document.getElementById('remitterIDS1000').style.display = 'none';
                 document.getElementById('remitterIDS1000_2').style.display = 'none';
        }
            if (document.getElementById('irsRow'))
            {
                document.getElementById('irsRow').style.display="";
            }
                 id = 3;
    }
    else if (id == 2)
    {
            //received subrogation
        document.getElementById('contOthRein').value = 'B';
        document.getElementById('salvDiv').style.display = 'none';
        document.getElementById('contAmtTH9000').innerHTML = 'Amount';
        if(document.getElementById('payOptIDTR_9000'))
        {document.getElementById('payOptIDTR_9000').style.display = ''; }
        if(document.getElementById('checkAmtTR9000'))
        {document.getElementById('checkAmtTR9000').style.display = '';}
        if(document.getElementById('remitterIDS9000'))
        {document.getElementById('remitterIDS9000').style.display = 'none';}
        if(document.getElementById('remitterIDS9000_2'))
        {document.getElementById('remitterIDS9000_2').style.display = 'none';}
        if(document.getElementById('remitterIDS9000_3'))
        {document.getElementById('remitterIDS9000_3').style.display = 'none';}
        if (document.getElementById('remitterIDS1000'))
        {
                 document.getElementById('remitterIDS1000').style.display = 'none';
                 document.getElementById('remitterIDS1000_2').style.display = '';
        }
    }
    else if(id == 12)
    {
        $('#entry12').removeClass('hidden');
    }
    else
    {
            //reset the hidden fields
            document.getElementById('contOthRein').value = '';
            document.getElementById('salvDiv').style.display = 'none';
        document.getElementById('contAmtTH9000').innerHTML = 'Amount';
        if(document.getElementById('payOptIDTR_9000'))
        {document.getElementById('payOptIDTR_9000').style.display = ''; }
        if(document.getElementById('checkAmtTR9000'))
        {document.getElementById('checkAmtTR9000').style.display = '';}
        if(document.getElementById('remitterIDS9000'))
        {document.getElementById('remitterIDS9000').style.display = 'none';}
        if(document.getElementById('remitterIDS9000_2'))
        {document.getElementById('remitterIDS9000_2').style.display = 'none';}
        if(document.getElementById('remitterIDS9000_3'))
        {document.getElementById('remitterIDS9000_3').style.display = 'none';}
        if (document.getElementById('remitterIDS1000'))
        {
                 document.getElementById('remitterIDS1000').style.display = 'none';
                 document.getElementById('remitterIDS1000_2').style.display = 'none';
        }

        if (document.getElementById('fieldDraftTR100'))
        {
            document.getElementById('fieldDraftTR100').style.display = 'none';
            document.getElementById('draftNo100').value = '';
        }
        if (document.getElementById('fieldDraftTR2100'))
        {
            document.getElementById('fieldDraftTR2100').style.display = 'none';
            document.getElementById('draftNoX100').value = '';
        }
        if (document.getElementById('fieldDraftTR3100'))
        {
            document.getElementById('fieldDraftTR3100').style.display = 'none';
            document.getElementById('draftDate100').value = '';
        }
        //if (document.getElementById('CLAIMANTselect100'))
        //{document.getElementById('CLAIMANTselect100').value = '';}

    }

    //then we want to reset the index of all of the correction select boxes
    //if they exist
    if (document.getElementById('changeCash'))
    {
             document.getElementById('changeCash')[0].selected = true;
    }
    if (document.getElementById('changeSubro'))
    {
             document.getElementById('changeSubro')[0].selected = true;
    }
     if (document.getElementById('changeSalv'))
    {
             document.getElementById('changeSalv')[0].selected = true;
    }
    if (document.getElementById('changeROC'))
    {
             document.getElementById('changeROC')[0].selected = true;
    }
    if (document.getElementById('CLAIMANTselect9000'))
    {
            document.getElementById('CLAIMANTselect9000')[0].selected = true;
    }
    if (document.getElementById('ITEMselect9000'))
    {
            document.getElementById('ITEMselect9000')[0].selected = true;
    }
    if (document.getElementById('coveragesList9000'))
    {
            document.getElementById('coveragesList9000')[0].selected = true;
    }
    if (document.getElementById('lossCodesList9000'))
    {
            document.getElementById('lossCodesList9000')[0].selected = true;
    }
    if (document.getElementById('COLList9000'))
    {
            document.getElementById('COLList9000')[0].selected = true;
    }
    if (document.getElementById('payOptID_9000'))
    {
            document.getElementById('payOptID_9000')[0].selected = true;
    }
    if (document.getElementById('remitterIDS9000'))
    {
            document.getElementById('remitterIDS9000')[0].selected = true;
    }
    if (document.getElementById('remitterIDS9000_2'))
    {
            document.getElementById('remitterIDS9000_2')[0].selected = true;
    }
    if (document.getElementById('remitterIDS9000_3'))
    {
            document.getElementById('remitterIDS9000_3')[0].selected = true;
    }

    //then, clear up any innerhtml left over from other correction
        //seletions:
        if (document.getElementById('correctionDiv'))
        {
                document.getElementById('correctionDiv').innerHTML='';
        }
        if (document.getElementById('subroCorrectDiv'))
        {
                document.getElementById('subroCorrectDiv').innerHTML='';
        }
        if (document.getElementById('salvCorrectDiv'))
        {
                document.getElementById('salvCorrectDiv').innerHTML='';
        }
        if (document.getElementById('ROCCorrectDiv'))
        {
                document.getElementById('ROCCorrectDiv').innerHTML='';
        }

        //clear off common field data for the various sections in the
        //case the user selects a section, fills it out, then changes his
        //selection.
        if (document.getElementById('contAmt9000'))
        {
                document.getElementById('contAmt9000').value='';
        }
        if (document.getElementById('checkAmt9000'))
        {
                document.getElementById('checkAmt9000').value='';
        }
        if (document.getElementById('explain9000'))
        {
                document.getElementById('explain9000').value='';
        }
        if (document.getElementById('salvItemDesc9000'))
        {
                document.getElementById('salvItemDesc9000').value='';
        }

        //call swap function:
        swap(x, id);

}

//this function decides what to deafult the mailto information to
//for drafts entered by the user.
//Note - may not be using this function after all!!
function defaultMailTo ()
{
        //set the default to the insured address to begin
        var whatAddress = 'IN';
        var hasIRS = 'N';
        var keyToUse = '';
        var myIRS = new Array();
        var myParties = new Array();

        //decide to default to IRS address, insured address or something else.

        //search the dom for PayeeSelectxxx select boxes and look for
        //IRS_ in the first characters to determine if an IRS is in the list.
    var y='';
    var selectArray = document.getElementsByTagName("select");
    var z=0;

    //set up a regex to find the select box we want
    var paySelect = new RegExp("PayeeSelect", "im");
    var IRSSelect = new RegExp("IRS_", "im");


    //loop thru select array and set indicators
    for (z=0; z<selectArray.length; z++)
    {
        if (paySelect.test(selectArray[z].id))
        {
                //found a select box we want to look at!
            y=selectArray[z];

            //if the option value is '999' or 'EX', it is "Other" or
            //"Expenses" respectively.  If it is "XX", nothing has been
            //selected.  We cannot default the addrress
            //for these types.  So we ignore them.
            if (y.value == '999'
                    || y.value == 'EX'
                    || y.value == 'XX')
            {
                     //ignore
            }
            else if (IRSSelect.test(y.value))
            {
                    //gcheck for IRS_ in
                    //the first 4 characters
                hasIRS = 'Y';
                myIRS.push(y.value);
            }
            else
            {
                     //its a party on the claim
                     myParties.push(y.value);
            }
        }
    }

    //now decide what address to use
    if (myIRS.length == 0
            && myParties.length > 0)
    {
             //do nothing, we are defaulting to insured address
    }
    if (myIRS.length == 1
            && myParties.length == 0)
    {
             //change the whatAddress to IRS
             whatAddress = 'IRS';
    }
    //if we have multiple payees, do not default
    if (myIRS.length > 1)
    {
             whatAddress = 'NONE';
    }

        //the address array has 7 elements per address.  They are in the
        //following order:
        //party_id
        //loctype
        //address1
        //address2
        //city
        //state
        //zip
        var addrCntr = 0;
        var useCntr = 0;
        var partyCntr = 0;
        if (whatAddress == 'IN')
        {
             //loop thru the addresses and find the one assicated with the
             //party from the select box
             if (myAddresses)  //make sure we have some javascript addresses
             {
            //if we have more parties than just the insured on
            //here, we are defaulting to the insured address
            if (myParties.length > 1)
            {
                    //loop thru and find the "AD" insured address to use
                for (k=0; k<myParties.length; k++)
                {

                }
            }
                 if (addrCntr <= myAddresses.length
                         && myAddresses[addrCntr] == myParties[partyCntr])
                 {
                           //this is the address we want, lets get it!
                useCntr = addrCntr;
                addrCntr = myAddresses.length + 1;
                 }
                 //bump up the address list by 7 to get next party id
                 addrCntr += 7;
             }
        }
}

function updateAddr(x)
{
         //this function takes address information from select box,
         //breaks it apart and plops it into the address fields for the
         //draft payto person
    var workAddress = new String (x.value);
    var addressArray = new Array();
    addressArray = workAddress.split('_');

    //first item in the split is party id, so we'll start with the 2nd
    //document.getElementById('mailToName100').value = (addressArray[2] || '');
    document.getElementById('mailToAddr1100').value = (addressArray[3] || '');
    document.getElementById('mailToAddr2100').value = (addressArray[4] || '');
    if (addressArray.length > 5 && addressArray[5].length > 20)
    {
        document.getElementById('mailToCity100').value = (addressArray[5].substring(0,20) || '');
        document.getElementById('mailToCity100displayed').value = (addressArray[5].substring(0,20) || '');
    }
    else
    {
            document.getElementById('mailToCity100').value = (addressArray[5] || '');
            document.getElementById('mailToCity100displayed').value = (addressArray[5] || '');
    }
    document.getElementById('mailToState100').value = (addressArray[6] || '');
    document.getElementById('mailToState100displayed').value = (addressArray[6] || '');
    document.getElementById('mailToZip100').value = (addressArray[7] || '');

    if($('#mailToCity100').val() == '')
    {$('#mailToCity100displayed').attr("disabled","disabled");}
    else
    {$('#mailToCity100displayed').removeAttr("disabled");}

        //call function to show OR HIDE address
    showHideMail(document.getElementById('printOptID100'));

    if(x.value == " ")
    {
        $('#mailTR2ID100').hide();
        $('#mailTR3ID100').hide();
        $('#mailTR4ID100').hide();
    }
    else
    {
        $('#mailTR2ID100').show();
        $('#mailTR3ID100').show();
        $('#mailTR4ID100').show();
    }

}

//this little function loops thru the input fields on the web page
//and strips off non-numeric data values and adds them up; then
//displays the total on the screen.
function salvCalc(x)
{
        var totalAmt = 0;
        var numbErr = 'N';
        var workDollars = 0;
        var returnTotalAmt = new Number(0);

        //clear off extra stuff from saleprice and pop it into the totalAmt field
        if (document.getElementById('salePrice9000'))
        {
                workDollars = document.getElementById('salePrice9000').value;
                //first, remove white space
                workDollars=workDollars.replace(/\s/g,'');
                //next, remove commas
                workDollars=workDollars.replace(/\,/g,'');
                //remove $ signs
                workDollars=workDollars.replace(/\$/g,'');
                //Check and see if workDollars is a number.
                //If it is call parseFloat to make sure workDollars is a number not a string.
                if (!isNaN(workDollars))
                {
                        workDollars = parseFloat(workDollars);
                        totalAmt = workDollars;
                }
                else
                { numbErr = 'Y'; }
        }
        //next subtract storage expense
        if (document.getElementById('storeExp9000'))
        {
                workDollars = document.getElementById('storeExp9000').value;
                //first, remove white space
                workDollars=workDollars.replace(/\s/g,'');
                //next, remove commas
                workDollars=workDollars.replace(/\,/g,'');
                //remove $ signs
                workDollars=workDollars.replace(/\$/g,'');
                //Check and see if workDollars is a number.
                //If it is call parseFloat to make sure workDollars is a number not a string.
                if (!isNaN(workDollars))
                {
                        workDollars = parseFloat(workDollars);
                        totalAmt -= workDollars;
                }
                else
                { numbErr = 'Y'; }
        }
        //next subtract towing expense
        if (document.getElementById('towExp9000'))
        {
                workDollars = document.getElementById('towExp9000').value;
                //first, remove white space
                workDollars=workDollars.replace(/\s/g,'');
                //next, remove commas
                workDollars=workDollars.replace(/\,/g,'');
                //remove $ signs
                workDollars=workDollars.replace(/\$/g,'');
                //Check and see if workDollars is a number.
                //If it is call parseFloat to make sure workDollars is a number not a string.
                if (!isNaN(workDollars))
                {
                        workDollars = parseFloat(workDollars);
                        totalAmt -= workDollars;
                }
                else
                { numbErr = 'Y'; }
        }
        //last subtract miscellaneous fees
        if (document.getElementById('miscFees9000'))
        {
                workDollars = document.getElementById('miscFees9000').value;
                //first, remove white space
                workDollars=workDollars.replace(/\s/g,'');
                //next, remove commas
                workDollars=workDollars.replace(/\,/g,'');
                //remove $ signs
                workDollars=workDollars.replace(/\$/g,'');
                //Check and see if workDollars is a number.
                //If it is call parseFloat to make sure workDollars is a number not a string.
                if (!isNaN(workDollars))
                {
                        workDollars = parseFloat(workDollars);
                        totalAmt -= workDollars;
                }
                else
                { numbErr = 'Y'; }
        }

        if (numbErr == 'N')
        {
                //at the end, move payment to screen total
                //Call toFixed to make the totalAmt a dollar amount with 2 decemal number.
                returnTotalAmt = totalAmt.toFixed(2);
                if (document.getElementById('netRec9000'))
                {document.getElementById('netRec9000').value=returnTotalAmt;}
        }
        else
        {
                //or move a message indicating we can't add it up
                if (document.getElementById('netRec9000'))
                {document.getElementById('netRec9000').value="Unable to Calculate";}
        }
}

function ajaxZipRequest(zip,city,state,locid) {
   try {
    // Firefox / IE7 / Others
    ajaxreq= new XMLHttpRequest();
   } catch (error) {
    try {
      // IE 5 / IE 6
      ajaxreq = new ActiveXObject("Microsoft.XMLHTTP");
    } catch (error) {
      return false;
    }
   }


//   document.write(reqType);
//   lobs=reqType+lobs;
   var zipCode = document.getElementById(zip);
   var cityCode = document.getElementById(city);
   var stateCode = document.getElementById(state);
   var zipvalue = zipCode.value;
   var cityvalue = zipCode.value;
   var statevalue = zipCode.value;
   ajaxreq.open("GET",'Claims_zip.pl?city_field='+city+';state_field='+state+';zip='+zipvalue+';hidden_city='+city+locid+';locID='+locid);
//   ajaxreq.open("GET",'Claims_Zip.pl?city_field='+cityvalue+';state_field='+statevalue+';zip='+zipvalue+';hidden_city='+city+locid+';locID='+locid);
//   alert(ajaxreq.statusText);               // shank added
   ajaxreq.onreadystatechange = ajaxResponse;
   ajaxreq.send(null);
}

// ajaxResponse: Waits for response and calls a function
function ajaxResponse() {
//   alert(globalReq);
   if (ajaxreq.readyState ==4)
   {
        var response = new Array();
        response = ajaxreq.responseText.split('|');
      //alert(response[1]);

       document.getElementById(response[3]).value=response[0];
       document.getElementById(response[4]).value=response[1];
       if(document.getElementById(response[3]+'displayed'))
       {
        document.getElementById(response[3]+'displayed').value=response[0];
        if(response[0] == '')
        {document.getElementById(response[3]+'displayed').setAttribute("disabled",true);}
        else
        {document.getElementById(response[3]+'displayed').removeAttribute("disabled");}
       }
       if(document.getElementById(response[4]+'displayed'))
       {document.getElementById(response[4]+'displayed').value=response[1];}

   }
   return true;
}
function pmtTypeChange(x){
    var pmtType = x.value;

    if(!$("#type_is_contact").hasClass('hidden'))
    {$("#type_is_contact").addClass('hidden');}
    if(!$("#type_is_vendor").hasClass('hidden'))
    {$("#type_is_vendor").addClass('hidden');}
    if(!$("#BUS_SubType_li").hasClass('hidden'))
    {$("#BUS_SubType_li").addClass('hidden');}
    if(!$("#BUS_invoice_li").hasClass('hidden'))
    {$("#BUS_invoice_li").addClass('hidden');}
    if(!$("#PM_LAN_li").hasClass('hidden'))
    {$("#PM_LAN_li").addClass('hidden');}
    if(!$("#lienholder_select_li").hasClass('hidden'))
    {$("#lienholder_select_li").addClass('hidden');}
    if(!$("#PMETHOD_li").hasClass('hidden'))
    {$("#PMETHOD_li").addClass('hidden');}
    if(!$("#p_other_contact").hasClass('hidden'))
    {$("#p_other_contact").addClass('hidden');}
    if(!$("#s_other_contact").hasClass('hidden'))
    {$("#s_other_contact").addClass('hidden');}
    if(!$("#payment_memo").hasClass('hidden'))
    {$("#payment_memo").addClass('hidden');}
    if(!$("#reload_card_li").hasClass('hidden'))
    {$("#reload_card_li").addClass('hidden')}
    if(!$("#PMETHOD_IC_ProxyNumber_li").hasClass('hidden'))
    {$("#PMETHOD_IC_ProxyNumber_li").addClass('hidden');}
    if(!$("#PMETHOD_IC_ProxyNumber_retype_li").hasClass('hidden'))
    {$("#PMETHOD_IC_ProxyNumber_retype_li").addClass('hidden')}
    if(!$("#sup_doc_ul").hasClass('hidden'))
    {$("#sup_doc_ul").addClass('hidden');}
    $("#AddrSelInsurpay").css('display','none');
    $("#address_selection").css('display','none');
    $('#mailTR2IDinsp_100').hide();
    $('#mailTR3IDinsp_100').hide();
    $('#mailTR4IDinsp_100').hide();

    if(pmtType != 0)
    {
        if($("#payment_memo").hasClass('hidden'))
        {$("#payment_memo").removeClass('hidden');}
        if($("#sup_doc_ul").hasClass('hidden'))
        {$("#sup_doc_ul").removeClass('hidden');}
    }
    if(pmtType == 1)
    {//Payment type = Insured or Claimant
        $("#type_is_contact").removeClass('hidden');
        $("#PMETHOD_li").removeClass('hidden');
        $("#AddrSelInsurpay").css('display','');
        $("#address_selection").css('display','');
    }
    if(pmtType == 2 || pmtType == 4)
    {//Payment type = Vendor
        $("#type_is_vendor").removeClass('hidden');
        $("#BUS_SubType_li").removeClass('hidden');
        $("#BUS_invoice_li").removeClass('hidden');
        $("#AddrSelInsurpay").css('display','');
        $('#mailTR2IDinsp_100').show();
        $('#mailTR3IDinsp_100').show();
        $('#mailTR4IDinsp_100').show();
    }
    if(pmtType == 3)
    {//Payment type = Lienholder
        //$("#type_is_vendor").removeClass('hidden');
        $("#lienholder_select_li").removeClass('hidden');
        $("#PM_LAN_li").removeClass('hidden');
        $("#AddrSelInsurpay").css('display','');
        $('#mailTR2IDinsp_100').show();
        $('#mailTR3IDinsp_100').show();
        $('#mailTR4IDinsp_100').show();
    }
    if(pmtType == 4)
    {//Payment type = Vendor and Insured/Claimant
        $("#type_is_contact").removeClass('hidden');
        //$("#type_is_vendor").removeClass('hidden');
        //$("#BUS_SubType_li").removeClass('hidden');
        //$("#BUS_invoice_li").removeClass('hidden');
        //$("#AddrSelInsurpay").css('display','');
        //$("#address_selection").css('display','');
        if($("#p_other_contact").hasClass('hidden'))
        {$("#p_other_contact").removeClass('hidden');}
    }
    if(pmtType == 5)
    {//Reload a card
        if($("#reload_card_li").hasClass('hidden'))
        {$("#reload_card_li").removeClass('hidden')}
    }

}
function changePaymentFields(x){
    var pmtMthd = x.value;
    var pmtType = $("#PM_PaymentType").val();

    if(pmtMthd == 2)
    {
        $("#PMETHOD_IC_ProxyNumber_li").removeClass('hidden');
        $("#PMETHOD_IC_ProxyNumber_retype_li").removeClass('hidden');
    }
    else
    {
       if(!$("#PMETHOD_IC_ProxyNumber_li").hasClass('hidden'))
       {$("#PMETHOD_IC_ProxyNumber_li").addClass('hidden')}
       if(!$("#PMETHOD_IC_ProxyNumber_retype_li").hasClass('hidden'))
       {$("#PMETHOD_IC_ProxyNumber_retype_li").addClass('hidden')}
    }

    if(pmtMthd != 5 && pmtType != 4)
    {
        if($("#secondary_contact").val() == 0)
        {
            if(!$("#p_other_contact").hasClass('hidden'))
            {$("#p_other_contact").addClass('hidden');}
        }
    }
    else
    {
        if($("#p_other_contact").hasClass('hidden'))
        {$("#p_other_contact").removeClass('hidden');}
    }

}
function changeContact(x){
    var value = x.value;
    var id = x.id;

    var sVal = $("#secondary_contact").val();

    var method = $("#PMETHOD").val();
    var pmtType = $("#PM_PaymentType").val();

    if(method == 5 || pmtType == 4)
    {
        if($("#p_other_contact").hasClass('hidden'))
        {$("#p_other_contact").removeClass('hidden');}

        var pId = ($("#primary_contact").val() || '');
        var sId = ($("#secondary_contact").val() || '');
        try {
        // Firefox / IE7 / Others
        ajaxreq= new XMLHttpRequest();
        } catch (error) {
          try {
             // IE 5 / IE 6
             ajaxreq = new ActiveXObject("Microsoft.XMLHTTP");
          } catch (error) {
              return false;
          }
        }
        disableButtons();
        ajaxreq.open("GET",'Claims_Insurpay_ContactFill.pl?option=0;primary='+pId+';secondary='+sId);
        ajaxreq.onreadystatechange = ajaxContactResponse;
        ajaxreq.send(null);
        enableButtons()
    }

    if(id == 'secondary_contact' && sVal !=0)
    {
       if($("#p_other_contact").hasClass('hidden'))
       {$("#p_other_contact").removeClass('hidden');}
       if($("#s_other_contact").hasClass('hidden'))
       {$("#s_other_contact").removeClass('hidden');}

       var pId = ($("#primary_contact").val() || '');
        var sId = ($("#secondary_contact").val() || '');
        try {
        // Firefox / IE7 / Others
        ajaxreq= new XMLHttpRequest();
        } catch (error) {
          try {
             // IE 5 / IE 6
             ajaxreq = new ActiveXObject("Microsoft.XMLHTTP");
          } catch (error) {
              return false;
          }
        }
        disableButtons();
        ajaxreq.open("GET",'Claims_Insurpay_ContactFill.pl?option=0;primary='+pId+';secondary='+sId);
        ajaxreq.onreadystatechange = ajaxContactResponse;
        ajaxreq.send(null);
        enableButtons();
    }
    if(id == 'secondary_contact' && sVal ==0)
    {
        if(method != 5 && pmtType != 4)
        {
            if(!$("#p_other_contact").hasClass('hidden'))
            {$("#p_other_contact").addClass('hidden');}
            if($('#PCON_MobilePhone').val() != '')
            {$('#PCON_MobilePhone').val("");}
            if($('#PCON_EmailAddress').val() != '')
            {$('#PCON_EmailAddress').val("");}
        }
       if(!$("#s_other_contact").hasClass('hidden'))
       {$("#s_other_contact").addClass('hidden');}
        if($('#SCON_MobilePhone').val() != '')
        {$('#SCON_MobilePhone').val("");}
        if($('#SCON_EmailAddress').val() != '')
        {$('#SCON_EmailAddress').val("");}
    }

    if(method != 5 && sVal == 0 && pmtType != 4)
    {
        if(!$("#p_other_contact").hasClass('hidden'))
        {$("#p_other_contact").addClass('hidden');}
        if(!$("#s_other_contact").hasClass('hidden'))
        {$("#s_other_contact").addClass('hidden');}
        if($('#PCON_MobilePhone').val() != '')
        {$('#PCON_MobilePhone').val("");}
        if($('#PCON_EmailAddress').val() != '')
        {$('#PCON_EmailAddress').val("");}
        if($('#SCON_MobilePhone').val() != '')
        {$('#SCON_MobilePhone').val("");}
        if($('#SCON_EmailAddress').val() != '')
        {$('#SCON_EmailAddress').val("");}
    }
}


// ajaxResponse: Waits for response and calls a function
function ajaxContactResponse() {
   if (ajaxreq.readyState ==4)
   {
        var response = new Array();
        response = ajaxreq.responseText.split('|');

        var pconIdOld = $("#PCON_id").val();
        var sconIdOld = $("#SCON_id").val();

        var pconIdNew = $('#primary_contact').val();
        var sconIdNew = $('#secondary_contact').val();

        if(pconIdNew != 0)
        {
            if($('#PCON_MobilePhone').val() == '' || (pconIdOld != pconIdNew))
            {$('#PCON_MobilePhone').val(response[0]);}
            if($('#PCON_EmailAddress').val() == '' || (pconIdOld != pconIdNew))
            {$('#PCON_EmailAddress').val(response[1]);}
            $("#PCON_id").val(pconIdNew);
        }
        else
        {
            $('#PCON_MobilePhone').val("");
            $('#PCON_EmailAddress').val("");
            $("#PCON_id").val("");
        }

        if(sconIdNew != 0)
        {
            if($('#SCON_MobilePhone').val() == '' || (sconIdOld != sconIdNew))
            {$('#SCON_MobilePhone').val(response[2]);}
            if($('#SCON_EmailAddress').val() == '' || (sconIdOld != sconIdNew))
            {$('#SCON_EmailAddress').val(response[3]);}
            $("#SCON_id").val(sconIdNew);
        }
        else
        {
            $('#SCON_MobilePhone').val("");
            $('#SCON_EmailAddress').val("");
            $("#SCON_id").val("");
        }
   }
   return true;
}

function updateInsurpayAddr(x)
{
         //this function takes address information from select box,
         //breaks it apart and plops it into the address fields for the
         //draft payto person
    var workAddress = new String (x.value);
    var addressArray = new Array();
    addressArray = workAddress.split('_');

    //first item in the split is party id, so we'll start with the 2nd
    //document.getElementById('mailToName100').value = (addressArray[2] || '');
    $('#mailToAddr1insp_100').val((addressArray[3] || ''));
    $('#mailToAddr2insp_100').val((addressArray[4] || ''));
    if (addressArray.length > 5 && addressArray[5].length > 20)
    {
        $('#mailToCityinsp_100').val((addressArray[5].substring(0,20) || ''));
        $('#mailToCityinsp_100displayed').val((addressArray[5].substring(0,20) || ''));
    }
    else
    {
        $('#mailToCityinsp_100').val((addressArray[5] || ''));
        $('#mailToCityinsp_100displayed').val((addressArray[5] || ''));
    }
    $('#mailToStateinsp_100').val((addressArray[6] || ''));
    $('#mailToStateinsp_100displayed').val((addressArray[6] || ''));
    $('#mailToZipinsp_100').val((addressArray[7] || ''));

    if($('#mailToCityinsp_100').val() == '')
    {$('#mailToCityinsp_100displayed').attr("disabled","disabled");}
    else
    {$('#mailToCityinsp_100displayed').removeAttr("disabled");}

        //call function to show OR HIDE address
   // showHideMail(document.getElementById('printOptIDinsp_100'));

    if(x.value == " ")
    {
        $('#mailTR2IDinsp_100').hide();
        $('#mailTR3IDinsp_100').hide();
        $('#mailTR4IDinsp_100').hide();
    }
    else
    {
        $('#mailTR2IDinsp_100').show();
        $('#mailTR3IDinsp_100').show();
        $('#mailTR4IDinsp_100').show();
    }

}

function changeLienholder(x)
{
    var value = x.value;

    if(value == 'IRS')
    {
        $("#type_is_vendor").removeClass('hidden');
        $('#mailToAddr1insp_100').val('');
        $('#mailToAddr2insp_100').val('');
        $('#mailToCityinsp_100').val('');
        $('#mailToCityinsp_100displayed').val('');
        $('#mailToStateinsp_100').val('');
        $('#mailToStateinsp_100displayed').val('');
        $('#mailToZipinsp_100').val('');
        $('#mailToCityinsp_100displayed').attr("disabled","disabled");
    }
    else if(value != 0)
    {
        var selected = x.options[x.selectedIndex];
        $("#BUS_Name").val(selected.text);
        $("#BUS_Name").prop( "disabled", false );
        $('#mailToAddr1insp_100').val(selected.getAttribute("data-addr1"));
        $('#mailToAddr2insp_100').val(selected.getAttribute("data-addr2"));
        $('#mailToCityinsp_100').val(selected.getAttribute("data-city"));
        $('#mailToCityinsp_100displayed').val(selected.getAttribute("data-city"));
        $('#mailToStateinsp_100').val(selected.getAttribute("data-state"));
        $('#mailToStateinsp_100displayed').val(selected.getAttribute("data-state"));
        $('#mailToZipinsp_100').val(selected.getAttribute("data-zip"));

        if($('#mailToCityinsp_100').val() == '')
        {$('#mailToCityinsp_100displayed').attr("disabled","disabled");}
        else
        {$('#mailToCityinsp_100displayed').removeAttr("disabled");}

        if(!$("#type_is_vendor").hasClass('hidden'))
        {$("#type_is_vendor").addClass('hidden');}
    }
}

function selectIRSinsurpay(myButtObject)
{
    //retrieve the id from the button irsSelectButt100
    var myIdno = myButtObject.id.substr(13);

    //string the id with the select box name
    var selectId = 'IRS_Selected' + myIdno;

    //retrieve the text of the selected option.  textContent
    //works for Firefox and straight 'text' works for IE
    var sel = document.getElementById(selectId);
    var selText = (sel.options[sel.selectedIndex].textContent
                || sel.options[sel.selectedIndex].text);

    //retrieve the value of the selected option
    var selValue = 'IRS_' + document.getElementById(selectId).value;

    //****************************************************
    //this section of code will add the IRS address to the address select box
    //****************************************************
    //split info from select box on comma
    var addressDetails = new Array();
    addressDetails = selText.split(', ');

    $("#irs_num_insurpay").val(addressDetails[0]);

    var busName = addressDetails[1].replace(/^\s+|\s+$/g, "");
    //if($("#PM_PaymentType").val() != 4)
    //{
        if(addressDetails[2] > ' ')
        {
            if(!(/\&$/.test(busName)))
            {busName = busName+", ";}
            busName = busName+" "+addressDetails[2].replace(/^\s+|\s+$/g, "");
            $("#mailToAddr1insp_100").val(addressDetails[3].replace(/^\s+|\s+$/g, ""));
        }
        else
        {
            $("#mailToAddr1insp_100").val(addressDetails[3].replace(/^\s+|\s+$/g, ""));
            $("#mailToAddr2insp_100").val("");
        }
        $("#mailToCityinsp_100displayed").val(addressDetails[4].replace(/^\s+|\s+$/g, ""));
        $("#mailToCityinsp_100").val(addressDetails[4].replace(/^\s+|\s+$/g, ""));
        $("#mailToStateinsp_100displayed").val(addressDetails[5].replace(/^\s+|\s+$/g, ""));
        $("#mailToStateinsp_100").val(addressDetails[5].replace(/^\s+|\s+$/g, ""));
        $("#mailToZipinsp_100").val(addressDetails[6].replace(/^\s+|\s+$/g, ""));
    //}
    //$("#BUS_Name_displayed").val(busName);
    $("#BUS_Name").val(busName);
    $("#BUS_Name").prop( "disabled", false );
    //string the address details together for the address select box key
    //using a pipe.
    var addressKeyString = new String();
    addressKeyString = 'NewLoc_NewParty_'
            + addressDetails[1] + '_';

    if (addressDetails[2] > ' ')
    {
               addressKeyString += addressDetails[2] + '_' + addressDetails[3];
    }
    else
    {
        addressKeyString += addressDetails[3] + '_' + addressDetails[2];
    }
    addressKeyString += '_'
            + addressDetails[4] + '_'
            + addressDetails[5] + '_'
            + addressDetails[6];

    var addressValueString = new String();
    //first value is the name
    addressValueString = addressDetails[1];
    //second value is name 2, which is sometimes blank so we want to
    //poke the address in there or it will err for a missing address
    if (addressDetails[2] > ' ')
    {
            addressValueString += ', ' + addressDetails[2] + ', ' + addressDetails[3];
    }
    else
    {
             addressValueString += ', ' + addressDetails[3] + ', ' + addressDetails[2];
    }

    addressValueString += ', '
            + addressDetails[4] + ', '
            + addressDetails[5] + ', '
            + addressDetails[6];

    var myAddressBox = document.getElementById('selInsAddress');
    myAddressBox.options.add(new Option(addressValueString,addressKeyString,false));

    //we also want to add the wording for the IRS selection into
    //a hidden field in the webpage so Perl can access it to update
    //claims tables
        //create new input hidden element
    var newHidInput = document.createElement('input');
    newHidInput.id = 'newIRS_' + myIdno;
    newHidInput.name = 'newIRS_' + myIdno;
    newHidInput.type = 'hidden';
    newHidInput.value = selText;

    document.mainform.appendChild(newHidInput);

    var irsSelectRow = 'irsSelectRow' + myIdno;
    document.getElementById(irsSelectRow).style.display='none';
}

function getreloadinfo(x)
{
    var value = x.value;

    if(value != 0)
    {
       try {
          // Firefox / IE7 / Others
          ajaxreq= new XMLHttpRequest();
       } catch (error) {
       try {
          // IE 5 / IE 6
          ajaxreq = new ActiveXObject("Microsoft.XMLHTTP");
       } catch (error) {
          return false;
         }
       }
       disableButtons();
       ajaxreq.open("GET",'Claims_Insurpay_ContactFill.pl?option=1;stored='+value);
       ajaxreq.onreadystatechange = reloadResponse;
       ajaxreq.send(null);
       enableButtons();
    }
    else
    {
        $('#PCON_reload').addClass('hidden');
        $('#SCON_reload').addClass('hidden');
        $('#p_other_contact').addClass('hidden');
        $('#s_other_contact').addClass('hidden');
        $('#type_is_contact').addClass('hidden');
    }
}
function reloadResponse() {
   if (ajaxreq.readyState ==4)
   {
        var response = new Array();
        response = ajaxreq.responseText.split('|');
        if(response[5] != '')
        {
            $('#PCON_reload').removeClass('hidden');
            $('#SCON_reload').removeClass('hidden');
            $('#primary_contact').addClass('hidden');
            $('#secondary_contact').addClass('hidden');
            $('#p_other_contact').removeClass('hidden');
            $('#s_other_contact').removeClass('hidden');
            $('#type_is_contact').removeClass('hidden');
            $('#PCON_MobilePhone').val(response[0]);
            $('#PCON_EmailAddress').val(response[1]);
            $('#SCON_MobilePhone').val(response[2]);
            $('#SCON_EmailAddress').val(response[3]);
            $('#PCON_reload').val(response[4]);
            $('#SCON_reload').val(response[5]);
            $('#PCON_id').val(response[4]);
            $('#SCON_id').val(response[5]);
        }
        else
        {
            $('#PCON_reload').addClass('hidden');
            $('#SCON_reload').addClass('hidden');
            $('#p_other_contact').addClass('hidden');
            $('#s_other_contact').addClass('hidden');
            $('#type_is_contact').addClass('hidden');
        }
   }
   return true;
}

function updateStatus(x)
{
    var draft = x;

    if(draft != 0)
    {
       try {
          // Firefox / IE7 / Others
          ajaxreq= new XMLHttpRequest();
       } catch (error) {
       try {
          // IE 5 / IE 6
          ajaxreq = new ActiveXObject("Microsoft.XMLHTTP");
       } catch (error) {
          return false;
         }
       }
       disableButtons();
       ajaxreq.open("GET",'Claims_Insurpay_UpdateStatus.pl?draft='+draft);
       ajaxreq.onreadystatechange = statusResponse;
       ajaxreq.send(null);
       enableButtons();
    }
}
function statusResponse() {
   if (ajaxreq.readyState ==4)
   {
        var response = new Array();
        response = ajaxreq.responseText.split('|');
        if(response[1] != '')
        {
            var status = response[1];
            $('#'+response[0]+'_status_update').html(status);
            if($('#'+response[0]+'_status_update').hasClass('hidden'))
            {$('#'+response[0]+'_status_update').removeClass('hidden');}

            var green = new RegExp('Issued|Progress|Success|Pending|Submitted');
            var red = new RegExp('Error|Cancelled|Stop');
            if(red.test(status))
            {$('#'+response[0]+'_status_update').css('color','red')}
            else if(green.test(status))
            {$('#'+response[0]+'_status_update').css('color','green')}
        }
        else
        {
            $('#'+response[0]+'_status_update').html('Unknown');
            if($('#'+response[0]+'_status_update').hasClass('hidden'))
            {$('#'+response[0]+'_status_update').removeClass('hidden');}
        }
   }
   return true;
}

function uploadDocument(x)
{
    var draft = x;

    if(draft != 0)
    {
       try {
          // Firefox / IE7 / Others
          ajaxreq= new XMLHttpRequest();
       } catch (error) {
       try {
          // IE 5 / IE 6
          ajaxreq = new ActiveXObject("Microsoft.XMLHTTP");
       } catch (error) {
          return false;
         }
       }
       disableButtons();

       var fileInput = document.getElementById('insurpay_doc_upload');
       var file = fileInput.files[0];

       if(typeof file == 'undefined')
       {
           $('#doc_upload_errs').html('<div class="alert alert-danger">Please select a file to upload.</div>');
       }
       else
       {
           var fullPath = fileInput.value;
           var startIndex = (fullPath.indexOf('\\') >= 0 ? fullPath.lastIndexOf('\\') : fullPath.lastIndexOf('/'));
           var fileName = fullPath.substring(startIndex);
           if (fileName.indexOf('\\') === 0 || fileName.indexOf('/') === 0)
           {
               fileName = fileName.substring(1);
           }
           var fileData = '';
           var reader = new FileReader();
           reader.onload = function(e) {
               fileData = reader.result;
               var dataArray = fileData.split(",");
               //console.log(dataArray[1]);
               ajaxreq.open("POST",'Claims_Insurpay_UploadDocument.pl',true);
               ajaxreq.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
               ajaxreq.send('draft='+draft+'&fileData='+dataArray[1]+'&fileName='+fileName);
               ajaxreq.onreadystatechange = documentResponse;
           }
           //reader.readAsText(file);
           reader.readAsDataURL(file);
       }
       enableButtons();
    }
    else
    {
        $('#doc_upload_errs').html('<div class="alert alert-danger">There is no payment to link this document to. Please contact extension 605 for further assistance.</div>');
    }
}
function documentResponse() {
   if (ajaxreq.readyState ==4)
   {
        var response = new Array();
        response = ajaxreq.responseText.split('|');
        if(response[4] != '')
        {
           $('#doc_upload_errs').html(response[4]);
        }
   }
   return true;
}


