#!/usr/local/bin/perl
=head1 NAME

Claims_CLM_NOTES

=head1 SYNOPSIS




=head1 DESCRIPTION



=head2 Functions

=over 4

=cut
package Claims_NOTES;

require 5.000;
use strict;
use vars qw($VERSION @ISA @EXPORT_OK);

use Exporter;

@ISA = qw(Exporter);
@EXPORT_OK = qw(NOTEScreateTextArea NOTEScreateTextInput NOTESreadInput NOTESsave);

$VERSION = '0.01';

# This is not in production yet.
#use Claims_TransHist qw(transHistory);
use Claims_Constants qw(getIMTLineCodeScreen getLossCodeLOB getCauseOfLossLOB NOTES_LENGTH);
use Claims_Error qw(error);
use Claims_TransHist qw(transHistory);
use IMT::Files qw(insertFile insertFolder getFileGroup renameFile moveFile returnFileTree :all);

=item * NOTEScreateTextArea()


Creates a text area input using the attributes given and notes specified by keys.

Example call:
    $output .= NOTEScreateTextArea($ENGINE,{'attributes'=>{'name'=>'sample150'},
                                              'notes'=>\@sortedResult,
                                              'keys'=>{'CLAIM_ID'=>100,'PARTY_ID'=>150,'DATA_TYPE'=>'DAMAGE'},
                                             });

=cut
sub NOTEScreateTextArea
{
    my $ENGINE = shift;
    my $args = shift;

    return NOTEScreateTextField($ENGINE,{%{$args},'idname'=>'CLM_NOTES_ID','inptype'=>'textarea'});

}

=item * NOTEScreateTextInput()

Creates a text input field using the attributes given and notes specified by keys.

Example call:
    $output .= NOTEScreateTextInput($ENGINE,{'attributes'=>{'name'=>'sample150'},
                                               'notes'=>\@sortedResult,
                                               'keys'=>{'CLAIM_ID'=>100,'PARTY_ID'=>150,'DATA_TYPE'=>'DAMAGE'},
                                              });

=cut
sub NOTEScreateTextInput
{
    my $ENGINE = shift;
    my $args = shift;

    return NOTEScreateTextField($ENGINE,{%{$args},'idname'=>'CLM_NOTES_ID','inptype'=>'text'});
}


sub NOTEScreateTextField {
    my $ENGINE = shift;
    my $args = shift;
    my $userID = $ENGINE->{'AUTH'}->{'user_key'};

    my $attributes = $args->{'attributes'} || {};   # HTML attributes to add into the input or textarea
    my $notesRows = $args->{'notes'} || [];         # sorted notes records
    my $keys = $args->{'keys'} || {};               # keys used to match fields
    my $idName = $args->{'idname'} || '';           # name of the primary key field
    my $inpType = $args->{'inptype'} || 'textarea'; # type of input that should be created (textarea or text)
    my $noteType = $keys->{'NOTE_TYPE'} || '';

    my $notes = '';
    my $notesID = '';
    my $match = 1;
    my $rowUserID = '';
    for my $v (@$notesRows) {
        $match = 1;
        # If keys is an empty hash, every row will match
        for my $key (keys %{$keys}) {
            # Check if the values don't match, this needs to handle null values cleanly
            if((defined($keys->{$key}) && !defined($v->{$key})) ||
               (!defined($keys->{$key}) && defined($v->{$key})) ||
               (defined($keys->{$key}) && defined($v->{$key}) && $keys->{$key} ne $v->{$key}))
              { $match = 0; last; }
        }

        if ($match) {
            $notes .= $v->{'CLM_NOTE'};
            if ($v->{'ALLOW_UPDATE'} eq 'Y') {
                $rowUserID = $v->{'USER_KEY'};
            }

            if (defined($v->{$idName})) {
                $notesID .= $v->{$idName}.'|';
            }
        }
    }

    my $errorDiv = '';
    if (defined($ENGINE->{'errors'}->{$attributes->{'name'}})) {
        $errorDiv = '<div class="error"></div>';
    }

    my $attr = '';
    for my $a (keys %$attributes) {
        $attr .= $a.'="'.$attributes->{$a}.'" ';
    }
    chop($attr);

    if ($noteType eq 'D') {
        $notes = '<div>'.$notes.'</div>';
        return $notes . '<input type="hidden" name="' . $attributes->{'name'} . 'ID" value="' . $notesID . '" />';
    }

    if($ENGINE->{'claimGeneral'}->{'INITIATION_POINT'} eq 'CV' && $ENGINE->{'READONLY'} && $notes eq '')
      { $notes = 'Unknown'; }

    # If it is a textarea or the screen is in readonly, we must clean the input more
    # than just for a text input.
    if($ENGINE->{'READONLY'} && $attributes->{'name'} =~ /updateEntryN|updateEntryT/ && defined($keys->{'ALLOW_UPDATE'}) && $keys->{'ALLOW_UPDATE'} eq 'Y' &&
        $rowUserID gt '' && $rowUserID eq $userID)
    {
        $notes =~ s/&/&amp;/g;
        $notes =~ s/</&lt;/g;

        $notes = $errorDiv.'<textarea '.$attr.'>'.$notes.'</textarea>';
    }
    elsif($ENGINE->{'READONLY'})
    {
        $notes =~ s/&/&amp;/g;
        $notes =~ s/</&lt;/g;
        $notes =~ s/\n/<br \/>/g;
        $notes =~ s/  / &nbsp;/g;
        $notes = '<div>'.$notes.'</div>';
    }
    elsif(defined($keys->{'ALLOW_UPDATE'}) && $keys->{'ALLOW_UPDATE'} eq 'N')
    {
        $notes =~ s/&/&amp;/g;
        $notes =~ s/</&lt;/g;
        $notes =~ s/\n/<br \/>/g;
        $notes =~ s/  / &nbsp;/g;
        $notes = '<div>'.$notes.'</div>';
    }

    elsif($rowUserID gt '' && $rowUserID ne $userID)
    {
        $notes =~ s/&/&amp;/g;
        $notes =~ s/</&lt;/g;
        $notes =~ s/\n/<br \/>/g;
        $notes =~ s/  / &nbsp;/g;
        $notes = '<div>'.$notes.'</div>';
    }

    elsif($inpType eq 'textarea')
    {
        $notes =~ s/&/&amp;/g;
        $notes =~ s/</&lt;/g;

        $notes = $errorDiv.'<textarea '.$attr.'>'.$notes.'</textarea>';
    }
    else
    {
        $notes =~ s/&/&amp;/g;
        $notes =~ s/"/&quot;/g;
        $notes =~ s/\n//g;

        $notes = $errorDiv.'<input type="text" '.$attr.' value="'.$notes.'" />';
    }

    if($ENGINE->{'READONLY'} && $attributes->{'name'} eq 'activityEntry')
    {
        $notes =~ s/&/&amp;/g;
        $notes =~ s/</&lt;/g;

        $notes = '';
        $notes = $errorDiv.'<textarea '.$attr.'>'.$notes.'</textarea>';
    }

    if($ENGINE->{'READONLY'} && $attributes->{'name'} eq 'insContactDetailsEntry')
    {
        $notes =~ s/&/&amp;/g;
        $notes =~ s/</&lt;/g;

        $notes = '';
        $notes = $errorDiv.'<textarea '.$attr.'>'.$notes.'</textarea>';
    }

    if($ENGINE->{'READONLY'} && $attributes->{'name'} eq 'underwritingDetailsEntry')
    {
        $notes =~ s/&/&amp;/g;
        $notes =~ s/</&lt;/g;

        $notes = '';
        $notes = $errorDiv.'<textarea '.$attr.'>'.$notes.'</textarea>';
    }

    if (($ENGINE->{'READONLY'}
    	&& $attributes->{'name'} eq 'claimCovEntry')
    		&& ($ENGINE->{'claimGeneral'}->{'SUBRO_STATUS'} eq 'P'
       		|| $ENGINE->{'claimGeneral'}->{'SALV_STATUS'} eq 'P'
       		|| $ENGINE->{'claimGeneral'}->{'REINSURANCE_IND'} eq 'P'
            || $ENGINE->{'claimGeneral'}->{'CLOSE_RECOVERABLE'} eq 'P'))
    {
        $notes =~ s/&/&amp;/g;
        $notes =~ s/</&lt;/g;
        $notes = '';
        $notes = $errorDiv.'<textarea '.$attr.'>'.$notes.'</textarea>';

    }
#    if(!$ENGINE->{'READONLY'})
#      { $notes .= '<input type="hidden" name="'.$attributes->{'name'}.'ID" value="'.$notesID.'" />'; }
    $notes .= '<input type="hidden" name="'.$attributes->{'name'}.'ID" value="'.$notesID.'" />';

    return $notes;
}


sub NOTESreadInput
{
    my $ENGINE = shift;
    my $args = shift;

    my $name = $args->{'name'} || '';                   # name of input or textarea
    my $subro = $args->{'subro'} || '';                 # for Trover subro notes
    my $keys = $args->{'keys'} || {};                   # keys used to match fields
    my $idName = $args->{'idname'} || 'CLM_NOTES_ID'; # name of the primary key field

    my @rows = ();

    my $maxLength = NOTES_LENGTH;

    my $string = '';
    my $stringID = '';
    if($subro)
    { $string = $subro; }
    else
    {
        $string = $ENGINE->{'CGI'}->param($name)||'';
        $stringID = $ENGINE->{'CGI'}->param($name.'ID')||'';
    }
    my @Ids = split(/\|/,$stringID);
    my $i = 1;
    if(defined($string) && $string ne '')
    {
        my $hashRef;
        while(length($string) > $maxLength)
        {
            $hashRef = {%{$keys},'CLM_ACTIVITY_LOG_SEQ'=>$i,'CLM_NOTE'=>substr($string,0,$maxLength,'')};
            if(defined($Ids[$i-1]))
              { $hashRef->{$idName} = $Ids[$i-1]; }

            ## Only add this row if the DELETE flag is not set, unless the ID is defined.
            if(!defined($hashRef->{'DELETE'}) || defined($hashRef->{$idName}))
              { push(@rows,$hashRef); }
            $i++;
        }
        $hashRef = {%{$keys},'CLM_ACTIVITY_LOG_SEQ'=>$i,'CLM_NOTE'=>$string};
        if(defined($Ids[$i-1]))
          { $hashRef->{$idName} = $Ids[$i-1]; }

        ## Only add this row if the DELETE flag is not set, unless the ID is defined.
        if(!defined($hashRef->{'DELETE'}) || defined($hashRef->{$idName}))
          { push(@rows,$hashRef); }
        $i++;
    }

    ## Delete any excess notes rows
    while(defined($Ids[$i-1]))
    {
        push(@rows,{%{$keys},$idName=>$Ids[$i-1],'DELETE'=>1});
        $i++;
    }

    return \@rows;
}

=item * NOTESsave()



=cut
sub NOTESsave
{
    my $ENGINE = shift;
    my $newnotes = shift;
    my $oldnotes = shift;

    my $error = $ENGINE->{'error'};
    my $claimid = $ENGINE->{'claimGeneral'}->{'CLAIM_ID'};
    my $userID = $ENGINE->{'AUTH'}->{'user_key'};

###################
##  CLM_NOTES  ##
###################

    ## Loop through the new data and insert or update as needed.
    my $notesInsert = $ENGINE->{'DBH'}->prepare('SELECT CLM_NOTES_ID, CLM_NOTE_DATETIME FROM FINAL TABLE (INSERT INTO CLAIMDB.CLM_NOTES
(CLM_NOTES_ID,
CLAIM_ID,
CLM_ACTIVITY_LOG_SEQ,
USER_KEY,
PRIVATE_OR_PUBLIC,
NOTE_TYPE,
SHOW_IN_LOG,
CLM_NOTE_SCREEN,
CLM_NOTE,
ALLOW_UPDATE)
VALUES (NEXT VALUE FOR CLAIMDB.CLM_NOTES_ID_SEQ,?,?,?,?,?,?,?,?,?))')  || $error->($ENGINE);

    my $notesDateInsert = $ENGINE->{'DBH'}->prepare('SELECT CLM_NOTES_ID FROM FINAL TABLE (INSERT INTO CLAIMDB.CLM_NOTES
(CLM_NOTES_ID,
CLAIM_ID,
CLM_ACTIVITY_LOG_SEQ,
USER_KEY,
PRIVATE_OR_PUBLIC,
NOTE_TYPE,
SHOW_IN_LOG,
CLM_NOTE_DATETIME,
CLM_NOTE_SCREEN,
CLM_NOTE,
ALLOW_UPDATE)
VALUES (NEXT VALUE FOR CLAIMDB.CLM_NOTES_ID_SEQ,?,?,?,?,?,?,?,?,?,?))')  || $error->($ENGINE);

    my $notesUpdate = $ENGINE->{'DBH'}->prepare('UPDATE CLAIMDB.CLM_NOTES SET CLM_NOTE = ?, CLM_NOTE_DATETIME = ?, ALLOW_UPDATE = ?
WHERE CLM_NOTES_ID = ? AND CLAIM_ID = ?')  || $error->($ENGINE);
    my $notesDelete;
    if($ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} eq 'P')
    {
        $notesDelete = $ENGINE->{'DBH'}->prepare('DELETE FROM CLAIMDB.CLM_NOTES WHERE CLM_NOTES_ID = ? AND CLAIM_ID = ?')  || $error->($ENGINE);
    }
    else
    {
        $notesDelete = $ENGINE->{'DBH'}->prepare('UPDATE CLAIMDB.CLM_NOTES SET DATE_DELETED = CURRENT TIMESTAMP
WHERE CLM_NOTES_ID = ? AND CLAIM_ID = ?')  || $error->($ENGINE);
    }

#    my $holdClaimID = '';
#    my $holdUserKey = '';
#    my $holdPrivatePublic = '';
#    my $holdNoteType = '';
#    my $holdShowInLog = '';
#    my $holdDateTime = '';
#    my $holdNoteScreen = '';
    my $dateTimeStamp = '';
    my $noteNum = '';
    my @updateArray = ();

    for my $n (@{$newnotes})
    {
        if(defined($n->{'YESUPDATE'}) && $n->{'YESUPDATE'} eq 'Y')
        {
            push(@updateArray,$n->{'noteNum'});
        }
    }


    for my $n (@{$newnotes})
    {
        if (defined($n->{'noteNum'}) && $n->{'noteNum'} ne $noteNum)
        {
	        my $getTime = $ENGINE->{'DBH'}->prepare
	                            ('SELECT CURRENT TIMESTAMP as TIMEOFDAY
	                                        FROM SYSIBM.SYSDUMMY1')
	                            || error('Prepare select for Current Timestamp failed: '.$ENGINE->{'DBH'}->errstr);

	        $getTime->execute()
	                            || error('Execute select for Current Timestamp failed: '.$ENGINE->{'DBH'}->errstr);

	        my $timeSt = $getTime->fetchall_arrayref({})
	                            || error('Fetch for Current Timestamp failed: '.$ENGINE->{'DBH'}->errstr);
	        $dateTimeStamp = $n->{'CLM_NOTE_DATETIME'} || @$timeSt[0]->{'TIMEOFDAY'};
            $noteNum = $n->{'noteNum'};
	    }
# 		print ' actvlog seq '.$n->{'CLM_ACTIVITY_LOG_SEQ'};
        if($n->{'NOUPDATE'} && !$n->{'DELETE'})
          { next; }

  #      warn 'keys notes_id: ',$n->{'CLM_VARDATA_ID'},' claimid: ',$claimid,' party_id: ',$n->{'PARTY_ID'};

        ## If CLM_VARDATA_ID isn't defined, this is a new row and we must insert it, otherwise update the existing row
        if($n->{'DELETE'})
        {
            $notesDelete->execute($n->{'CLM_NOTES_ID'},$claimid) || $error->($ENGINE);
        }
        elsif(!defined($n->{'CLM_NOTES_ID'}))
        {
#            print 'actvlog seq '.$n->{'CLM_ACTIVITY_LOG_SEQ'};
#            my $printNotes = "$notesInsert->execute($claimid,$n->{'CLM_ACTIVITY_LOG_SEQ'},$n->{'USER_KEY'},$n->{'PRIVATE_OR_PUBLIC'},$n->{'NOTE_TYPE'},$n->{'SHOW_IN_LOG'},
#$n->{'CLM_NOTE_SCREEN'},$n->{'CLM_NOTE'},$n->{'ALLOW_UPDATE'}) || $error->($ENGINE)";
#            print 'print notes query '.$printNotes;
#            if($n->{'CLM_ACTIVITY_LOG_SEQ'} eq 1)
#            {
##                print ' insert date1 '.$newNoteDate;
#                $notesInsert->execute($claimid,
#                $n->{'CLM_ACTIVITY_LOG_SEQ'},
#                $n->{'USER_KEY'},
#                $n->{'PRIVATE_OR_PUBLIC'},
#                $n->{'NOTE_TYPE'},
#                $n->{'SHOW_IN_LOG'},
#                $n->{'CLM_NOTE_SCREEN'},
#                $n->{'CLM_NOTE'},
#                $n->{'ALLOW_UPDATE'}) || $error->($ENGINE);

#                my $result = $notesInsert->fetchall_arrayref({}) || error('CLM_NOTES insert execute failed: '.$ENGINE->{'DBH'}->errstr);
#                if(scalar(@$result) < 1)
#                  { $error->($ENGINE); }
#                $n->{'CLM_NOTES_ID'} = $result->[0]->{'CLM_NOTES_ID'};
#                $newNoteDate = $result->[0]->{'CLM_NOTE_DATETIME'};
#                # Add NEWROW flag for transhist
#                $n->{'NEWROW'} = 1;
#            }
#            else
#            {
#                print ' insert date2 '.$newNoteDate;
                $notesDateInsert->execute($claimid,
                $n->{'CLM_ACTIVITY_LOG_SEQ'},
                $userID || $n->{'USER_KEY'},
                $n->{'PRIVATE_OR_PUBLIC'},
                $n->{'NOTE_TYPE'},
                $n->{'SHOW_IN_LOG'},
                $dateTimeStamp,
                $n->{'CLM_NOTE_SCREEN'},
                $n->{'CLM_NOTE'},
                $n->{'ALLOW_UPDATE'}) || $error->($ENGINE);

                my $result = $notesDateInsert->fetchall_arrayref({}) || $error->($ENGINE);
                if(scalar(@$result) < 1)
                  { $error->($ENGINE); }
                $n->{'CLM_NOTES_ID'} = $result->[0]->{'CLM_NOTES_ID'};
                # Add NEWROW flag for transhist
                $n->{'NEWROW'} = 1;
#            }
        }
        else
        {
#            print ' YESUPDATE '.$n->{'YESUPDATE'};
#            if(defined($n->{'YESUPDATE'}) && $n->{'YESUPDATE'} eq 'Y')
#            {
#                print ' Note '.$n->{'CLM_NOTE'};
                my $doit = 'N';
                for my $a (@updateArray)
                {
                    if($a eq $n->{'noteNum'})
                    { $doit = 'Y'; }
                }
                if($doit eq 'Y')
                { $notesUpdate->execute($n->{'CLM_NOTE'},$dateTimeStamp,$n->{'ALLOW_UPDATE'},$n->{'CLM_NOTES_ID'},$claimid) || error('CLM_NOTES update execute failed: '.$ENGINE->{'DBH'}->errstr); }
#            }
        }
    }

#    for my $n (@{$newnotes})
#    {
#        if(defined($n->{'YESUPDATE'}) && $n->{'YESUPDATE'} eq 'Y')
#        {
#            my $updateNoteDate = $ENGINE->{'DBH'}->prepare('UPDATE CLAIMDB.CLM_NOTES SET USER_KEY = ?,CLM_NOTE_DATETIME = CURRENT TIMESTAMP
#WHERE CLAIM_ID = ? AND PRIVATE_OR_PUBLIC = ? AND NOTE_TYPE = ? AND SHOW_IN_LOG = ? AND CLM_NOTE_SCREEN = ? AND CLM_NOTE_DATETIME = ?')  || error('CLM_NOTES update prepare failed: '.$ENGINE->{'DBH'}->errstr);
#            $updateNoteDate->execute($userID,$claimid,$n->{'PRIVATE_OR_PUBLIC'},$n->{'NOTE_TYPE'},$n->{'SHOW_IN_LOG'},$n->{'CLM_NOTE_SCREEN'},$n->{'CLM_NOTE_DATETIME'}) || error('CLM_NOTES update execute failed: '.$ENGINE->{'DBH'}->errstr);
#        }
#    }


    if($ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} ne 'P')
     { transHistory($ENGINE,'CLM_NOTES',$oldnotes,$newnotes); }
}

=back

=head1 AUTHOR

Dave Schwenker (<EMAIL>)

=head1 COPYRIGHT

Copyright ???

This library is intended for use at IMT Insurance only.

=head1 SEE ALSO

perl(1).

=cut

1;
