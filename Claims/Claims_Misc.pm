#!/usr/local/bin/perl
=head1 NAME

Claims_Misc

=head1 SYNOPSIS

  This module provides common subroutines necessary for the claims system.


=head1 DESCRIPTION

  Description

=head2 Functions

=over 4

=cut
package Claims_Misc;

require 5.000;
use strict;
use vars qw($VERSION @ISA @EXPORT_OK);

use Exporter;

@ISA = qw(Exporter);
@EXPORT_OK = qw(draftIntfceErr getHeader createSelect createTextArea createTextInput createTextField
                createCheckBox getMenu getClaimsCentral validateClaimID getClaimHeading getCoveredItemDescQuery
                getCoveredItemDesc getCauseOfLoss screenDataSync getFooter editSubmitClaimData storeSubmitClaimData
                createTaskList calculateAge deleteLienholderMortgagee getClaimDetails getTop getBottom createLabelHelp
                getPrintLossNoticeButton updateLastSave setFlagshipResend getCovPartyMiscEndor validateEmail platformAuthError
                writeToTmp);

$VERSION = '0.01';

use Claims_Constants qw(getIMTLineCodeScreen getLossCodeLOB getCauseOfLossLOB getTablePrimaryKeyName getLOBTask
                        getStateAbbrevNumeric getWadenaPolicyPrefix getWadenaFormsFolder getTypeOfDeducts getSubCovDesc
                        getCASSymbolDesc getIMTPolicyPrefix getIMTFormsFolder getWadenaISOFormsFolder);
use Claims_Error qw(error);
use IMT::Files qw(insertFile insertFolder getFileGroup renameFile moveFile returnFileTree :all);
use IMT::Access_Constants qw(getAccessByLineCode);
use wadena_vehicle qw(fill_vin convertVIN);
use Claims_TrackerAPI qw(insert_notification :NTF_TYPES :MGMT_BASKETS :MSG_IDS);
use Claims_Parties;
use Document::Manager;
use IMT::Files qw(createFileGroup insertFile insertFolder setPermissions getFileGroup getIcon renameFile moveFile returnFileTree returnFileTreeModify deleteFile deleteFolder :all);
use IMT::CommaFormatted qw(CommaFormatted);
use Common::Platform::platformCommon qw(get_policy_info_page_url);
use Common::Platform::Users::usersCommon qw(fetch_user_key_data convert_dept_code);
use Common::Platform::Change_User::change_user_modals qw(display_imitate_modal display_change_role_modal import_imitate_js_css);
use Client::ClientMisc qw(check_for_platform_version);
use File::Path qw(make_path);


sub getClaimId {
    my $ENGINE = shift;
    my @session = keys %{$ENGINE->{'SESSION'}};
    my $claimId = $ENGINE->{'CGI'}->param('claimid') || $ENGINE->{'claim_id'};

    if (!defined $claimId) {
        foreach my $key (@session) {
            if (substr($key, 0, 7) eq 'claimid') {
                $claimId = substr($key, 7);
            }
        }
    }

    return $claimId;
}

=item * validateClaimID()

This function requires the $ENGINE reference and expects the CGI 'claimid' parameter.
If a valid claimid is found, the $ENGINE->{'claimGeneral'} record is built and
$ENGINE->{'READONLY'} is set.

    $ENGINE->{'READONLY'} = 0 if the user can update the current claim.
    $ENGINE->{'READONLY'} = 1 if the user cannot update the current claim.

=cut
sub validateClaimID
{
    my $ENGINE = shift;
    my $claimid = getClaimId($ENGINE);

    if($claimid =~ /[^0-9]/)
      { return 0; }

    my $STH = $ENGINE->{'DBH'}->prepare('SELECT * FROM CLAIMDB.CLM_GENERAL LEFT JOIN CLIENTDB.ITEM ON POLICY_NUMBER = ID_NUMBER WHERE CLAIM_ID = ? AND DATE_DELETED = \'9999-01-01 01:00:00.000000\'') ||
              error($ENGINE,'General query prepare failed: '.$ENGINE->{'DBH'}->errstr);
    $STH->execute($claimid) || error($ENGINE,'General query execute failed: '.$ENGINE->{'DBH'}->errstr);
    my $result = $STH->fetchall_arrayref({});

    if(scalar(@$result) == 0)
      { return 0; }

    $ENGINE->{'claimGeneral'} = $result->[0];

    my $policyNum = $ENGINE->{'claimGeneral'}->{'POLICY_NUMBER'};
    if($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /301|302|331|332/ && $policyNum =~ /^12|^14|^26|^40|^48/)
    {
        $policyNum =~ s/^12|^14|^26|^40|^48//;
        $ENGINE->{'claimGeneral'}->{'DISPLAY_POLICY_NUMBER'} = $policyNum;
        my $STH = $ENGINE->{'DBH'}->prepare('SELECT ITEM_ID FROM CLIENTDB.ITEM WHERE ID_NUMBER = ?') ||
              error($ENGINE,'Item query prepare failed: '.$ENGINE->{'DBH'}->errstr);
        $STH->execute($policyNum) || error($ENGINE,'Item query execute failed: '.$ENGINE->{'DBH'}->errstr);
        my $result = $STH->fetchall_arrayref({});
        if(scalar(@$result) > 0)
          { $ENGINE->{'claimGeneral'}->{'ITEM_ID'} = $result->[0]->{'ITEM_ID'}; }
    }
    else
      { $ENGINE->{'claimGeneral'}->{'DISPLAY_POLICY_NUMBER'} = $ENGINE->{'claimGeneral'}->{'POLICY_NUMBER'}; }
       #$ENGINE->{'AUTH'}->{'id'} eq 'CV21724' ||
    if($ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} =~ /C/ || !defined($ENGINE->{'AUTH'}->{'Claims_Access'}) || $ENGINE->{'AUTH'}->{'Claims_Access'} eq 'I'
       || ($ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} ne 'P' && $ENGINE->{'AUTH'}->{'Claims_Access'} eq 'S'))
      { $ENGINE->{'READONLY'} = 1; }
    elsif(defined($ENGINE->{'claimGeneral'}->{'MANUAL_OR_WHAT'}) && $ENGINE->{'claimGeneral'}->{'MANUAL_OR_WHAT'} eq 'G' && defined($ENGINE->{'claimGeneral'}->{'SUBMIT_TO_IMT_DATE'}) && $ENGINE->{'claimGeneral'}->{'SUBMIT_TO_IMT_DATE'} ne '9999-01-01 01:00:00.000000'
         && $ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} eq 'P')
      { $ENGINE->{'READONLY'} = 1; }
    else
      { $ENGINE->{'READONLY'} = 0; }

#######################################################################
#  Check system lock status before doing anything in system
#
#
#######################################################################
        #before we do anything, check the lock table
    my $clm_lockQuery = $ENGINE->{'DBH'}->prepare
        ("SELECT DRAFT_LOCK,
                SYSTEM_LOCK,
                VIEW_ONLY_LOCK
                FROM CLAIMDB.CLM_LOCK_TABLE")
        || error($ENGINE,'lock table prepare2 failed: '.$ENGINE->{'DBH'}->errstr);
    $clm_lockQuery->execute()
        || error($ENGINE,'lock table query2 execute failed: '.$ENGINE->{'DBH'}->errstr);
    my $lockValues = $clm_lockQuery->fetchall_arrayref({})
        || error($ENGINE,'lock table query2 fetch failed: '.$ENGINE->{'DBH'}->errstr);

        #worst first - system lockdown
        undef $ENGINE->{'SystemLock'};
    if ($lockValues->[0]->{'SYSTEM_LOCK'} eq 'Y')
    {
        #error - system locked!!
#        push(@{$errors{'systemLock'}},'System Lock');
#        $ENGINE->{'Monetary'}->{'SystemLock'}  =  'Y';
        $ENGINE->{'SystemLock'} = 'Y';
        #toss you out to the claims inquiry screen
        $ENGINE->{'load'} = 'Claims_Inquiry';
        return 1;
#        require "Claims_Inquiry.pm";
#        Claims_Inquiry::loadScreen($ENGINE);
#        return;
    }
    elsif ($lockValues->[0]->{'VIEW_ONLY_LOCK'} eq 'Y')
    {
        $ENGINE->{'READONLY'} = 1;
    }

    if($ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} =~ /C/ &&
       ($ENGINE->{'claimGeneral'}->{'SUBRO_STATUS'} eq 'P' || $ENGINE->{'claimGeneral'}->{'SALV_STATUS'} eq 'P' || $ENGINE->{'claimGeneral'}->{'REINSURANCE_IND'} eq 'P') &&
       defined($ENGINE->{'AUTH'}->{'Claims_Access'}) && $ENGINE->{'AUTH'}->{'Claims_Access'} eq 'A')
      { $ENGINE->{'MOREMONEY'} = 1; }
      else
      { undef $ENGINE->{'MOREMONEY'}; }


    # Get file group
    if(defined($ENGINE->{'session'}->{'FGID'}))
      { $ENGINE->{'claimGeneral'}->{'FGID'} = $ENGINE->{'session'}->{'FGID'}; }
    else
    {
        my $STH = $ENGINE->{'DBH'}->prepare('SELECT FGID, WORDING_LAST_SHOWN FROM CLAIMDB.CLM_FILES WHERE CLAIM_ID = ?') ||
              error($ENGINE,'Item query prepare failed: '.$ENGINE->{'DBH'}->errstr);
        $STH->execute($claimid) || error($ENGINE,'File Group ID execute failed: '.$ENGINE->{'DBH'}->errstr);
        my $result = $STH->fetchall_arrayref({});
        if(scalar(@$result) > 0)
          { 
            $ENGINE->{'claimGeneral'}->{'FGID'} = $result->[0]->{'FGID'}; 
            $ENGINE->{'claimGeneral'}->{'WORDING_LAST_SHOWN'} = $result->[0]->{'WORDING_LAST_SHOWN'}; 
          }
    }

    my $polAgency = $ENGINE->{'claimGeneral'}->{'AGENCY_NO'};
    my $access = getAccessByLineCode($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'});

        #if this is a lawfirm accessing the claim, then we must
        #verify that claims managers gave them access in the claims
        #system itself, to the claim.
        my $lawFirmIsOk = 'N';
    #    die Data::Dumper::Dumper($ENGINE->{'AUTH'});
        if ( $ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'LawFirm')
        {
                #query to check that this particular claim has been approved
                #for the lawfirm to see
            my $LFClaimsQuery = $ENGINE->{'DBH'}->prepare('SELECT
                    G.CLAIM_ID,
                    G.IMT_CLAIM_NO,
                    G.POLICY_NUMBER
             FROM CLAIMDB.CLM_LAW_FIRMS F
             left outer join
             CLAIMDB.CLM_SPECIAL_ACCESS S
             on
                 F.CLM_LAW_FIRMS_ID = S.CLM_LAW_FIRMS_ID
            left outer join
                CLAIMDB.CLM_GENERAL G
            on
                S.CLAIM_ID = G.CLAIM_ID
                where
                    F.CLM_LAW_FIRMS_ID = ?
                AND G.DATE_DELETED = \'9999-01-01 01:00:00.000000\'
                AND S.ACCESS_REVOKED = \'9999-01-01 01:00:00.000000\'') ||
              error($ENGINE,'Lawfirm query prepare failed: '.$ENGINE->{'DBH'}->errstr);
            $LFClaimsQuery->execute($ENGINE->{'AUTH'}->{'law_firm_id'}) || error($ENGINE,'Lawfirm query execute failed: '.$ENGINE->{'DBH'}->errstr);
            my $Lawresult = $LFClaimsQuery->fetchall_arrayref({});

            for my $laws (@$Lawresult)
            {
                 if ($claimid == $laws->{'CLAIM_ID'})
                 {
                     #claim is found, its ok
                $lawFirmIsOk = 'Y';
                 }
            }
        }

    #added special code for lawfirm access
    if ( $ENGINE->{'AUTH'}->{'IMTOnline_UserType'} ne 'LawFirm')
    {
            if($ENGINE->{'AUTH'}->{'internalUser'} eq 'YES' ||
            ($ENGINE->{'AUTH'}->{'Claims_Access'} &&
             $ENGINE->{'AUTH'}->{'AGENCY_ACCESS'}->{$polAgency} &&
             $ENGINE->{'AUTH'}->{'AGENCY_ACCESS'}->{$polAgency}->{$access}))
              { return 1; }
            elsif($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /303|550/ &&
             $ENGINE->{'AUTH'}->{'Claims_Access'} &&
             $ENGINE->{'AUTH'}->{'AGENCY_ACCESS'}->{$polAgency} &&
             $ENGINE->{'AUTH'}->{'AGENCY_ACCESS'}->{$polAgency}->{'Commercial_Access'})
              { return 1; }
            else
              { return 0; }
    }
    else
    {
            if ($lawFirmIsOk eq 'Y')
              {
                      return 1;
              }
              else
              {
                      return 0;
              }
     }
}

=item * createSelect()

This function requires the $ENGINE reference and creates a select list if the claim
isn't in READONLY, otherwise it returns the string value of the item.

=cut
sub createSelect
{
    my $ENGINE = shift;
    my $args = shift;

    my $attributes = $args->{'attributes'} || {};   # HTML attributes to add into the input or textarea
    my $value = $args->{'value'};                   # DB value
    my $options = $args->{'options'} || {};         # option
    my $readOnly = $args->{'readOnly'} || 0;

    if(!defined($value))
      { $value = ''; }

    my $errorDiv = '';
    if(defined($ENGINE->{'errors'}->{$attributes->{'name'}}))
      { $errorDiv = '<div class="error"></div>'; }

    my $attr = '';
    my $WCstateLossAppliesSW = 'N';
    for my $a (keys %$attributes)
      {
          $attr .= $a.'="'.$attributes->{$a}.'" ';
          if($attributes->{$a} eq 'stateLossApplies')
          { $WCstateLossAppliesSW = 'Y'; }
      }
    chop($attr);

    if($ENGINE->{'claimGeneral'}->{'INITIATION_POINT'} eq 'CV' && $ENGINE->{'READONLY'} && $value eq '')
      { $value = 'Unknown'; }

    if($ENGINE->{'READONLY'} || $readOnly)
    {
        if(defined($options->{$value}))
          { $value = $options->{$value}; }

          $value =~ s/&amp;/&/g;
          $value =~ s/&lt;/</g;

        $value =~ s/&/&amp;/g;
        $value =~ s/</&lt;/g;
    }
    else
    {
        my $select = $errorDiv.'<select '.$attr.'>';

        if($WCstateLossAppliesSW eq 'Y')
        {
            for my $key (sort {$$options{$a} cmp $$options{$b}} keys %$options)
            {
                my $selected = '';
                if ($value eq $key)
                {
                    $selected = ' selected="selected"';
                }
                $options->{$key} =~ s/&amp;/&/g;
                $options->{$key} =~ s/&lt;/</g;

                $options->{$key} =~ s/&/&amp;/g;
                $options->{$key} =~ s/</&lt;/g;
                $select .= '<option value="'.$key.'"'.$selected.'>'.$options->{$key}.'</option>';
            }
        }
        else
        {
                for my $key (sort keys %$options)
                {
                    my $selected = '';
                    if ($value eq $key)
                    {
                        $selected = ' selected="selected"';
                    }
                    $options->{$key} =~ s/&amp;/&/g;
                    $options->{$key} =~ s/&lt;/</g;

                    $options->{$key} =~ s/&/&amp;/g;
                    $options->{$key} =~ s/</&lt;/g;
                    $select .= '<option value="'.$key.'"'.$selected.'>'.$options->{$key}.'</option>';
                }
        }
        $select .= '</select>';
        $value = $select;
    }

    return $value;
}


=item * createTextArea()

Creates a text area input using the attributes given and vehicle vardata specified by keys.

Example call:
    $output .= createTextArea($ENGINE,{'attributes'=>{'name'=>'sample1000'},
                                       'value'=>$value
                                                 });

=cut
sub createTextArea
{
    my $ENGINE = shift;
    my $args = shift;

    return createTextField($ENGINE,{%{$args},'inptype'=>'textarea'});

}

=item * createTextInput()

Creates a text input field using the attributes given and vehicle vardata specified by keys.

Example call:
    $output .= createTextInput($ENGINE,{'attributes'=>{'name'=>'sample1000'},
                                                   'value'=>$value
                                                  });

=cut
sub createTextInput
{
    my $ENGINE = shift;
    my $args = shift;

    return createTextField($ENGINE,{%{$args},'inptype'=>'text'});
}

sub createTextField
{
    my $ENGINE = shift;
    my $args = shift;

    my $attributes = $args->{'attributes'} || {};   # HTML attributes to add into the input or textarea
    my $value = '';
    if(defined $args->{'value'})
    {$value = uc($args->{'value'})}         # sorted vardata records
    my $inpType = $args->{'inptype'} || 'textarea'; # type of input that should be created (textarea or text)

    if(!defined($value))
      { $value = ''; }

    my $errorDiv = '';
    if(defined($ENGINE->{'errors'}->{$attributes->{'name'}}))
      { $errorDiv = '<div class="error"></div>'; }

    my $attr = '';
    for my $a (keys %$attributes)
      { $attr .= $a.'="'.$attributes->{$a}.'" '; }
    chop($attr);

    if($ENGINE->{'claimGeneral'}->{'INITIATION_POINT'} eq 'CV' && $ENGINE->{'READONLY'} && $value eq '')
      { $value = 'Unknown'; }

    # If it is a textarea or the screen is in readonly, we must clean the input more
    # than just for a text input.
    if($ENGINE->{'READONLY'})
    {
        $value =~ s/&/&amp;/g;
        $value =~ s/</&lt;/g;
        $value =~ s/\n/<br \/>/g;
        $value =~ s/  / &nbsp;/g;
    }
    elsif($inpType eq 'textarea')
    {
        $value =~ s/&/&amp;/g;
        $value =~ s/</&lt;/g;

        $value = $errorDiv.'<textarea '.$attr.'>'.$value.'</textarea>';
    }
    else
    {
        $value =~ s/&/&amp;/g;
        $value =~ s/"/&quot;/g;
        $value =~ s/\n//g;

        $value = $errorDiv.'<input type="text" '.$attr.' value="'.$value.'" />';
    }

    return $value;
}

=item * createCheckBox()

Creates a checkbox using the attributes given, the value and onvalue to determine whether it is checked or not.
If neither value or onvalue are defined, the box will be checked.

Example call:
    $output .= createTextInput($ENGINE,{'attributes'=>{'name'=>'sample1000'},
                                        'value'=>$value,
                                        'onvalue'=>$onvalue
                                       });

=cut
sub createCheckBox
{
    my $ENGINE = shift;
    my $args = shift;

    my $attributes = $args->{'attributes'} || {};   # HTML attributes to add into the checkbox
    my $value = $args->{'value'};         # sorted vardata records
    my $onvalue = $args->{'onvalue'}; # type of input that should be created (textarea or text)

    if (!defined($value))
    {
      $value = '';
    }

    my $errorDiv = '';
    if(defined($ENGINE->{'errors'}->{$attributes->{'name'}}))
      { $errorDiv = '<div class="error"></div>'; }

    my $attr = '';
    for my $a (keys %$attributes)
      { $attr .= $a.'="'.$attributes->{$a}.'" '; }
    chop($attr);

    if($ENGINE->{'claimGeneral'}->{'INITIATION_POINT'} eq 'CV' && $ENGINE->{'READONLY'} && $value eq '')
      { $value = 'Unknown'; }

    if((defined($value) && defined($onvalue) && $value eq $onvalue) ||
       (!defined($value) && !defined($onvalue)))
    {
        if($ENGINE->{'READONLY'})
          { $value = '<img src="c.png" style="vertical-align:middle" alt="Checked" />' }
        else
          { $value = '<input type="checkbox" checked="yes" '.$attr.'/>'; }
    }
    else
    {
        if($ENGINE->{'READONLY'})
          { $value = '<img src="uc.png" style="vertical-align:middle" alt="Unchecked" />' }
        else
          { $value = '<input type="checkbox" '.$attr.'/>'; }
    }

    return $value;
}



=item * getHeader()

This function returns the HTML header section of the page including the DOCTYPE and opening <html> tag.
Pages can add their own header data by including it in the $ENGINE->{'head'} variable. Such as:


 $ENGINE->{'head'} = <<EOF;
   <style type="text/css">th { vertical-align:top }</style><script type="text/javascript" src="../Common/popupCalendar2909.js"></script>
   <script type="text/javascript" src="Claims_Parties.js?v=1.0.1"></script>
   <script type="text/javascript" src="Claims_Vehicle.js"></script>
   <script type="text/javascript">
   //<![CDATA[

   function addVehicle(sel)
   {
     if(sel.value != "")
     {
       var selRow = sel.parentNode.parentNode;
       var newVehID = 'new'+i;

       var inputs = '<input type="hidden" id="addVehicle'+newVehID+'" name="addVehicle'+newVehID+'" value="'+sel.value+'"> <input type="button" value="Remove" onclick="deleteVeh(\\''+newVehID+'\\')">';
       createRow('Vehicle Involved',sel.options[sel.selectedIndex].text + inputs,selRow,'nv1_'+newVehID);

       if(sel.value == "OT")
       {
           createSubRow('VIN','<input id="VIN'+newVehID+'" type="text" name="VIN'+newVehID+'" value="" />',selRow,'nv2_'+newVehID);
           createSubRow('Year','<input id="year'+newVehID+'" type="text" name="year'+newVehID+'" value="" />',selRow,'nv3_'+newVehID);
           createSubRow('Make','<input id="make'+newVehID+'" type="text" name="make'+newVehID+'" value="" />',selRow,'nv4_'+newVehID);
           createSubRow('Model','<input id="model'+newVehID+'" type="text" name="model'+newVehID+'" value="" />',selRow,'nv5_'+newVehID);
       }
       createSubRow('Driver Name','<div style="float:left;padding-right:1em"><select onchange="driver(this)" id="driverSel'+newVehID+'" name="driverSel'+newVehID+'">$driverOptions</select></div>'+
       '<div style="float:left;display:none;" id="dn_'+newVehID+'"><div style="float:left;padding-right:1em"><b>First</b>&nbsp;<input type="text"  size="15" name="driverFName'+newVehID+'" value="" /></div>'+
       '<div style="float:left;padding-right:1em"><b>Last</b>&nbsp;<input type="text"  size="30" name="driverLName'+newVehID+'" value="" /></div></div>',selRow,'nv6_'+newVehID);
       createSubRow('Describe Damage','<input id="damage'+newVehID+'" type="text" name="damage'+newVehID+'" value="" />',selRow,'nv7_'+newVehID);
       createSubRow('Where&nbsp;Vehicle&nbsp;Can&nbsp;Be&nbsp;Seen','<input id="seenAt'+newVehID+'" type="text" name="seenAt'+newVehID+'" value="", maxlength="25" />',selRow,'nv8_'+newVehID);
       createSubRow('Vehicle Direction','<select id="direction'+newVehID+'" name="direction'+newVehID+'">$directionOptions</select>',selRow,'nv11_'+newVehID);
       createSubRow('Road Type','<select id="roadType'+newVehID+'" name="roadType'+newVehID+'">$roadTypeOptions</select>',selRow,'nv12_'+newVehID);
       i++;

       sel.selectedIndex = 0;
     }
   }
   //]]>
   </script>
 EOF


=cut
sub getHeader
{
    my $ENGINE = shift;

    my $sessID = $ENGINE->{'SESSION'}->{'sessionID'};
    my $action = $ENGINE->{'ACTION'};

    my $name = $ENGINE->{'AUTH'}->{'name'};
    my $head = $ENGINE->{'head'} || '';
    my $pageTitle = 'IMT Claims';

    my $claimNumTitle = '';
    my $claimNum = '';

    my $polInfo = '';

    my $loadfunction = '';
    if (defined $ENGINE->{'vehicle'}->{'specialLoad'})
    {
             $loadfunction = $ENGINE->{'vehicle'}->{'specialLoad'};
    }

    if(defined($ENGINE->{'claimGeneral'})) {
        my $policyNum = $ENGINE->{'claimGeneral'}->{'DISPLAY_POLICY_NUMBER'} || '';
        $claimNum = $ENGINE->{'claimGeneral'}->{'IMT_CLAIM_NO'} || '';
        my $lossDate = $ENGINE->{'claimGeneral'}->{'LOSS_DATE_TIME'} || '';
        my $claimStatus = getClaimStatus($ENGINE);

        $claimNumTitle = ' - '.$claimNum;
        $pageTitle .= $claimNumTitle;
        if (length($lossDate) >= 10) {
            $lossDate = substr($lossDate,5,2).'/'.substr($lossDate,8,2).'/'.substr($lossDate,0,4); }
            $polInfo = <<HTML;
<div class="showprint">
    <table class="claiminfo" style="width:100%">
        <tr>
            <th>Policy:</th>
            <td>$policyNum</td>
        </tr>
        <tr>
            <th>Claim:</th>
            <td>$claimNum</td>
        </tr>
        <tr>
            <th>Loss Date:</th>
            <td>$lossDate</td>
        </tr>
        <tr>
            <th>Status:</th>
            <td>$claimStatus</td>
        </tr>
    </table>
</div>
HTML
        }

    my $scrollfix = '';
    my $docManagerHead = '';
    if(!($ENGINE->{'load'} =~ /Claims_Inquiry|Claims_IRS|Claims_Reports|Claims_Unassigned|Claims_Status|Claims_UnassignedStorm/))
    {
        $scrollfix = '<script type="text/javascript" src="scrollfix.js?v=2.0.0"></script>';
        $docManagerHead = DocumentManager::styles({engine => $ENGINE});
        $docManagerHead .= DocumentManager::headScripts({engine => $ENGINE});
    } else {
        # The engine seems to hold onto the claim that we were just looking at
        # if coming from a claim so let's get rid of it for the inqury screen
        if($ENGINE->{'load'} eq 'Claims_Unassigned')
        { $pageTitle = 'Claims Unassigned'; }
        elsif($ENGINE->{'load'} eq 'Claims_Status')
        { $pageTitle = 'Claims Status'; }
        elsif($ENGINE->{'load'} eq 'Claims_UnassignedStorm')
        { $pageTitle = 'Storm Claims'; }
        else
        { $pageTitle = 'IMT Claims'; }
    }

    my $trackerLink = '';
    if($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'Internal' &&
       ((!defined $ENGINE->{'AUTH'}->{'Claims_Access'})
                || ($ENGINE->{'AUTH'}->{'Claims_Access'} eq 'A')))
    { $trackerLink ='| <a href="Claims_Tracker.pl">Tracker</a>'; }

    my $claimStatusLink = '';
    if(defined($ENGINE->{'AUTH'}->{'automated_adjuster_assignment_access'}) && $ENGINE->{'AUTH'}->{'automated_adjuster_assignment_access'})
    {
        #If in test bring up QA Adjuster Admin.
        if ($ENV{'COMPUTERNAME'} =~ /LUIGI/)
        {
            $claimStatusLink = '| <a href="https://automated-adjuster-admin-qa.imtins.com/admin/" target="_blank">Adjuster Admin</a> ';
        }
        else
        {
            $claimStatusLink = '| <a href="https://automated-adjuster-admin.imtins.com/admin/" target="_blank">Adjuster Admin</a> ';
        }
    }

    if(defined($ENGINE->{'AUTH'}->{'Claims_AsgnAdjust'}) && $ENGINE->{'AUTH'}->{'Claims_AsgnAdjust'} eq 'A')
    { $claimStatusLink .= '| <a href="Claims_Engine.pl?load=Claims_Status&amp;sessionID='.$sessID.'">Claims Status</a> '; }

    if(defined($ENGINE->{'AUTH'}->{'Claims_AsgnAdjust'}) && $ENGINE->{'AUTH'}->{'Claims_AsgnAdjust'} eq 'A')
    { 
        $claimStatusLink .= '| <a href="Claims_Engine.pl?load=Claims_Unassigned&amp;sessionID='.$sessID.'">Claims Unassigned</a> '; 
        $claimStatusLink .= '| <a href="Claims_Engine.pl?load=Claims_UnassignedStorm&amp;sessionID='.$sessID.'">Storm Claims</a> '; 
    }

    # Google Analytics: Set tracking id based on environment
    my $ga = 'G-8KZD1X19R2';
    if  (lc($ENV{'COMPUTERNAME'}) =~ /luigi/i)
    {
        $ga = 'G-QLXBF6LSDG';
    }

    #add logic for imitate a user
    my $change_user_link = '';
    my $current_role = '';
    # die Data::Dumper::Dumper('FILE: '.__FILE__, 'LINE: '.__LINE__, $ENGINE->{'AUTH'});
    if ($ENV{'COMPUTERNAME'} =~ /LUIGI/) {
        if ($ENGINE->{'AUTH'}->{'can_impersonate_user'} && !$ENGINE->{'AUTH'}->{'imitate_key'}) {
            $change_user_link .= "| [<a class='headerlink' href='javascript:openImitateModal()'>Imitate A User</a>]";
        }
        elsif ($ENGINE->{'AUTH'}->{'imitate_key'}) {
            $change_user_link .= "| [<a class='headerlink' href='javascript:stopImitateUser()'>Stop Imitating User</a>]";
        }
    }
#    if ($ENGINE->{'AUTH'}->{'multiple_roles'}) {
#        $current_role = '- '.$ENGINE->{'AUTH'}->{'IMTOnline_UserType'};
#        $change_user_link .= "| [<a class='headerlink' href='javascript:openUserModal()'>Change User</a>]";
#    }
    my $header = <<HTML;
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<!-- Global tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=$ga"></script>
<script>
window.dataLayer = window.dataLayer || [];
function gtag(){dataLayer.push(arguments);}
gtag('js', new Date());

gtag('config', '$ga');
</script>
    <title>$pageTitle</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1"/>
    <script type="text/javascript" src="/imtonline/Common/js/jquery-1.10.2.min.js"></script>
    <script type="text/javascript" src="Claims.js"></script>
    <script type="text/javascript" src="../Common/tableSort.js"></script>
    <script type="text/javascript" src="bootstrap.min.js"></script>
    <script type="text/javascript">window.onresize = resize;</script>
    <script type="text/javascript" src="jquery-ui.js"></script>
    $scrollfix
    <script type="text/javascript" src="footable.js"></script>
    <link href="jquery-ui.css" rel="stylesheet"/>
    <link rel="stylesheet" type="text/css" href="File.css?v=1"/>
    <!-- <link rel="stylesheet" type="text/css" href="Claims.css"/>
    <link rel="stylesheet" type="text/css" href="bootstrap.css"/>
    <link rel="stylesheet" type="text/css" href="bootstrap-theme.css"/>
    <link rel="stylesheet" type="text/css" href="bootstrap-theme.min.css"/> -->
    <link rel="stylesheet" type="text/css" href="bootstrap.min.css"/>
    <link rel="stylesheet" type="text/css" href="footable.core.css"/>
    <link rel="stylesheet" type="text/css" href="claims_base_styles.css?v=3.1.2"/>
    <link rel="stylesheet" type="text/css" href="File.css"/>
    <script src="jquery.spin.js" type="text/javascript"></script>
    <script type="text/javascript" src="/imtonline/billing11/spin.min.js"></script>
    <link href="jquery.spin.css" rel="stylesheet" type="text/css" />
    <script src="footable.sort.js" type="text/javascript"></script>
    $head
    $docManagerHead
HTML
$header .= import_imitate_js_css();
$header .= <<HTML;
</head>

<body onload="$loadfunction" id="claimsBodyTag" name="claimsBodyTag">
<div class="redbar fixed-header">
    <div class="container">
        <div class="row">
            <div class="systemlinks"><a href="Claims_Engine.pl">Claims Search</a> $trackerLink $claimStatusLink</div>
            <div class="login"><span class="repname">Welcome, $name</span> <span>$change_user_link</span> | [ <span class="login_status"><a href="$action?Logout=1">Logout</a></span> ]</div>
        </div><!--/row-->
    </div><!--/container-->
</div><!--/redbar-->
<div id="loadingDiv" class="shadowDiv hidden"></div>
<div id="loadingDiv_content" class="spin hidden shadowContent" data-spin></div>

HTML

if ($ENGINE->{'AUTH'}->{'can_impersonate_user'}) {
      print display_imitate_modal();
    }
    elsif($ENGINE->{'AUTH'}->{'multiple_roles'}) {
      print display_change_role_modal($ENGINE->{'AUTH'});
    }
    print '<div id="loading"></div>';

    # Add in keyboard shortcuts for claims (add them in the claimsKeyboardShortcuts.js and minify it to update!)
    $header .= <<EOL;
<script src="/imtonline/document_management/claims/js/mousetrap.min.js"></script>
<script src="claimsKeyboardShortcuts.min.js"></script>
EOL

    my $claimStatus = $ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'};
    if(defined $ENGINE->{'claimGeneral'}->{'FGID'} && $ENGINE->{'claimGeneral'}->{'FGID'} != 0)
    {
        if (($ENGINE->{'load'} ne 'Claims_Inquiry' && $ENGINE->{'load'} ne 'Claims_IRS' && $ENGINE->{'load'} ne 'Claims_Reports') 
            && (!$ENGINE->{'READONLY'} || 
            ((defined $ENGINE->{'AUTH'}->{'IMTOnline_UserType'} && lc($ENGINE->{'AUTH'}->{'IMTOnline_UserType'}) =~ /internal|lawfirm/) 
            && (defined $ENGINE->{'AUTH'}->{'Claims_Access'} && lc($ENGINE->{'AUTH'}->{'Claims_Access'}) =~ 'a|i')
            && (defined $ENGINE->{'AUTH'}->{'Claims_Docs'} && lc($ENGINE->{'AUTH'}->{'Claims_Docs'}) =~ 'a|i')))) {
            validateClaimID($ENGINE);
            my %files = %{getFileGroup($ENGINE, {'FGID' => $ENGINE->{'claimGeneral'}->{'FGID'}, 'order' => 'descending'})};
            $header .= DocumentManager::claimsDocumentManager({auth => $ENGINE->{'AUTH'}, engine => $ENGINE, folderName => $claimNum, files => \%files, status => $claimStatus});
        }
    }

    my $fixedHeader = '';
    if ($ENGINE->{'load'} ne 'Claims_Inquiry' && $ENGINE->{'load'} ne 'Claims_IRS' && $ENGINE->{'load'} ne 'Claims_Reports') {
        $fixedHeader = ' fixed-header';
    }

    my $collapsed = '';
    if (($ENGINE->{'load'} eq 'Claims_Inquiry' && $ENGINE->{'load'} ne 'Claims_IRS' && $ENGINE->{'load'} ne 'Claims_Reports') || ($ENGINE->{'load'} eq 'Claims_Details' && $claimStatus eq 'P')) {
        $collapsed = ' collapsed';
    }
    $header .= <<HTML;
<div class="container maincontent $fixedHeader">
    <div class="row special$fixedHeader">
        <div class="header$fixedHeader$collapsed">
            <div class="systemtitle">Claims Center</div>
HTML

#    if($ENGINE->{'load'} eq 'Claims_Inquiry')
#    {
# commented logic so branded header shows on internal pages also mculbertson 2/2019
        $header .= <<HTML;
        <div class="container"><div class="row">
<div class="inquiry_logo"></div>
<div class="inquiry_header">
    <h1>Claims Center</h1>
</div></div></div>
HTML
#    }

    return $header;
}


=item * getMenu()

This function returns the navigation menu used at the top of each page.

=cut
sub getMenu
{

    my $ENGINE = shift;

    my $sessID = $ENGINE->{'SESSION'}->{'sessionID'};
    my $action = $ENGINE->{'ACTION'};

    my $load = $ENGINE->{'load'};
    my $claimid = $ENGINE->{'claimGeneral'}->{'CLAIM_ID'};
    my %selected = ($load=>' class="active"');
    if($load =~ /Claims_Vehicle|Claims_PropLiab|Claims_WorkComp/)
    {
        $selected{'Claims_Details'} = ' class="active"';
    }
    my $inquiry = $action.'?load=Claims_Inquiry&amp;sessionID='.$sessID;
    if(defined($ENGINE->{'SESSION'}->{'searchURL'}) && $ENGINE->{'SESSION'}->{'searchURL'} ne '')
    {
        $inquiry = $ENGINE->{'SESSION'}->{'searchURL'};
    }

    my $summary = '';
    my $policy_info = '';
    my $details = '';
    my $monetary_ent = '';
    my $file_act = '';
    my $med_wksht = '';
    my $manual_pol = '';
    my $dis_payments = '';

    my $menuHTML = '<div class="menu hideprint" style="margin:0.4em 0em 0.4em 11em;text-decoration:none;color:black;">';
#'<a href="#" onclick="load(\'Claims_Inquiry\');"'.($selected{'Claims_Inquiry'} || '').'>Search</a> '.
#'<a href="#" onclick="load(\'Claims_Info\')"'.($selected{'Claims_Info'} || '').'>Summary</a> ';

#    if($ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} eq 'P' && $ENGINE->{'claimGeneral'}->{'MANUAL_OR_WHAT'} ne 'M' && $ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'Internal' && $ENGINE->{'AUTH'}->{'Claims_Access'} eq 'I')
#    {
#        $menuHTML .= '<a href="#" id="searchButton" onclick="load(\'Claims_Inquiry\');"'.($selected{'Claims_Inquiry'} || '').'>Search</a> ';
#    }
    if($ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} eq 'P' && $ENGINE->{'claimGeneral'}->{'MANUAL_OR_WHAT'} eq 'M' && $ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'Internal' && $ENGINE->{'AUTH'}->{'Claims_Access'} eq 'I')
    {
#        $menuHTML .= '<a href="#" id="searchButton" onclick="load(\'Claims_Inquiry\');"'.($selected{'Claims_Inquiry'} || '').'>Search</a> ';
        $details = '<li><a href="#" id="detailesButton" onclick="load(\'Claims_Details\');"'.($selected{'Claims_Details'} || '').'>Details</a></li> ';
        $manual_pol = '<li><a href="#" id="manualPolicyButton" onclick="load(\'Claims_ManualPolicy\');"'.($selected{'Claims_ManualPolicy'} || ' class="menu_button"').'>Manual&nbsp;Policy</a></li> ';
    }
    elsif($ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} eq 'P' && $ENGINE->{'claimGeneral'}->{'MANUAL_OR_WHAT'} ne 'M')
    {
#        $menuHTML .= '<a href="#" onclick="load(\'Claims_Details\');"'.($selected{'Claims_Details'} || '').'>Details</a> ';
    }
    elsif($ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} eq 'P' && $ENGINE->{'claimGeneral'}->{'MANUAL_OR_WHAT'} eq 'M')
    {
        $details = '<li><a href="#" id="detailesButton" onclick="load(\'Claims_Details\');"'.($selected{'Claims_Details'} || ' class="menu_button"').'>Details</a></li> ';
        $manual_pol = '<li><a href="#" id="manualPolicyButton" onclick="load(\'Claims_ManualPolicy\');"'.($selected{'Claims_ManualPolicy'} || ' class="menu_button"').'>Manual&nbsp;Policy</a></li> ';
    }
    else
    {
#            $menuHTML .= '<a href="#" id="searchButton" onclick="load(\'Claims_Inquiry\');"'.($selected{'Claims_Inquiry'} || '').'>Search</a> ';
            $summary = '<li><a href="#" id="summaryButton" onclick="load(\'Claims_Info\');"'.($selected{'Claims_Info'}||' class="menu_button"').'>Summary</a></li> ';

            if($ENGINE->{'claimGeneral'}->{'PURGED_IND'} !~ /Y|R/)
            {$policy_info .= '<li><a href="#" id="policyInfoButton" onclick="load(\'Claims_PolicyInfo\');"'.($selected{'Claims_PolicyInfo'}||' class="menu_button"').'>Policy</a></li> ';}

            $details = '<li><a href="#" id="detailesButton" onclick="load(\'Claims_Details\');"'.($selected{'Claims_Details'} || ' class="menu_button"').'>Details</a></li> ';

            if($ENGINE->{'claimGeneral'}->{'MANUAL_OR_WHAT'} eq 'M' && $ENGINE->{'claimGeneral'}->{'PURGED_IND'} !~ /Y|R/ && $ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'Internal')
            {
                $manual_pol = '<li><a href="#" id="manualPolicyButton" onclick="load(\'Claims_ManualPolicy\');"'.($selected{'Claims_ManualPolicy'} || ' class="menu_button"').'>Manual&nbsp;Policy</a></li> ';
            }
            if($ENGINE->{'claimGeneral'}->{'PURGED_IND'} !~ /Y|R/)
            {
                $monetary_ent = '<li><a href="#" id="monetaryButton" onclick="load(\'Claims_Monetary\');"'.($selected{'Claims_Monetary'} || ' class="menu_button"').'>Monetary</a></li> ';

                if(($ENGINE->{'AUTH'}->{'internalUser'} eq 'YES' && defined($ENGINE->{'AUTH'}->{'Claims_Access'}) && $ENGINE->{'AUTH'}->{'Claims_Access'} eq 'A')
                   || ($ENGINE->{'AUTH'}->{'internalUser'} eq 'YES' && defined($ENGINE->{'AUTH'}->{'Claims_FileActive'}) && $ENGINE->{'AUTH'}->{'Claims_FileActive'} eq 'I')
                   || ($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'LawFirm' && defined($ENGINE->{'AUTH'}->{'Claims_FileActive'}) && $ENGINE->{'AUTH'}->{'Claims_FileActive'} eq 'I'))
                  { $file_act = '<li><a href="#" id="fileActivityButton" onclick="load(\'Claims_FileActivity\');"'.($selected{'Claims_FileActivity'} || ' class="menu_button"').'>File&nbsp;Activity</a></li> '; }
            }
            if($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} !~ /105|200|205|600|605/ &&
               $ENGINE->{'claimGeneral'}->{'MANUAL_OR_WHAT'} ne 'G' &&
               !defined($ENGINE->{'AUTH'}->{'Claims_CashEntry'}) &&
               $ENGINE->{'AUTH'}->{'IMTOnline_UserType'} ne 'LawFirm' &&
               $ENGINE->{'AUTH'}->{'IMTOnline_UserType'} ne 'Agent' &&
               $ENGINE->{'AUTH'}->{'Claims_Access'} eq 'A')
            { $med_wksht = '<li><a href="#" id="medPaymentsButton" onclick="load(\'Claims_MedPayments\');"'.($selected{'Claims_MedPayments'} || ' class="menu_button"').'>Med Worksheet</a></li> '; }
            elsif($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /600|605/ && !defined($ENGINE->{'AUTH'}->{'Claims_CashEntry'}) && $ENGINE->{'AUTH'}->{'IMTOnline_UserType'} ne 'LawFirm' && $ENGINE->{'AUTH'}->{'IMTOnline_UserType'} ne 'Agent' && $ENGINE->{'AUTH'}->{'Claims_Access'} eq 'A')
            { $dis_payments = '<li><a href="#" id="disPaymentsButton" onclick="load(\'Claims_DisPayments\');"'.($selected{'Claims_DisPayments'} || ' class="menu_button"').'>Disability&nbsp;Payments</a></li> '; }
            #this if to allow law firms to see MedPayments and DisPayments screen.
            elsif($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} !~ /105|200|205|600|605/ &&
               $ENGINE->{'claimGeneral'}->{'MANUAL_OR_WHAT'} ne 'G' &&
               !defined($ENGINE->{'AUTH'}->{'Claims_CashEntry'}) &&
               $ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'LawFirm' &&
               $ENGINE->{'AUTH'}->{'Claims_Access'} eq 'I')
            { $med_wksht = '<li><a href="#" id="medPaymentsButton" onclick="load(\'Claims_MedPayments\');"'.($selected{'Claims_MedPayments'} || ' class="menu_button"').'>Med Worksheet</a></li> '; }
            elsif($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /600|605/ && !defined($ENGINE->{'AUTH'}->{'Claims_CashEntry'}) && $ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'LawFirm'  && $ENGINE->{'AUTH'}->{'Claims_Access'} eq 'I')
            { $dis_payments = '<li><a href="#" id="disPaymentsButton" onclick="load(\'Claims_DisPayments\');"'.($selected{'Claims_DisPayments'} || ' class="menu_button"').'>Disability&nbsp;Payments</a></li> '; }
    }
    my $save = '';
    if ($load eq 'Claims_Monetary')
        {
               $save = '<span class="save"><a href="#" class="primary" id="save" onclick="load(\'Claims_Monetary\');">Save</a></span>';
        }
        elsif($load eq 'Claims_FileActivity')
        {
               $save = '<span class="save"><a href="#" class="primary" id="save" onclick="load(\'Claims_FileActivity\');">Save</a></span>';
        }
        elsif($load eq 'Claims_MedPayments')
        {
               $save = '<span class="save"><a href="#" class="primary" id="save" onclick="load(\'Claims_MedPayments\');">Save</a></span>';
        }
        elsif($load eq 'Claims_DisPayments')
        {
               $save = '<span class="save"><a href="#" class="primary" id="save" onclick="load(\'Claims_DisPayments\');">Save</a></span>';
        }
        elsif($load eq 'Claims_PolicyInfo'
               || $load eq 'Claims_Info'
               || $load eq 'Claims_FileManager')
        {
                #do nothing for policy info and summary screens
        }
        else
        {
               $save = '<span class="save"><a href="#" class="primary" id="save" onclick="load(\'Claims_Details\');">Save</a></span>';
        }

    my $claim_num = $ENGINE->{'CGI'}->{'claim_number'}[0] || $ENGINE->{'claimGeneral'}->{'IMT_CLAIM_NO'} || 'unknown';
    my $ul = '';
    if($summary||$policy_info||$details||$manual_pol||$monetary_ent||
        $file_act||$med_wksht||$dis_payments)
        {
            $ul = <<EOF;
<ul class="scrollfix">
    $summary
    $policy_info
    $details
    $manual_pol
    $monetary_ent
    $file_act
    $med_wksht
    $dis_payments
    <li class="fixednav_addons">
        <div class="buttons">
            <span class="claimno"><a href="javascript:toggleDetails();" id="claimno">$claim_num</a></span>
            $save
        </div>

        <div class="textlinks">
            <span class="claimsearch"><a href="#" id="claimsearch">Claims Search</a></span>
            <span class="tracker"><a href="#" id="tracker">Tracker</a></span>
        </div>
    </li>
</ul>

EOF
        }
    else
    {
        $ul = <<HTML;
<ul class="scrollfix">
    <li class="fixednav_addons">
        <div class="buttons">
            <span class="claimno"><a href="javascript:toggleDetails();" id="claimno">$claim_num</a></span>
            $save
        </div>

        <div class="textlinks">
            <span class="claimsearch"><a href="#" id="claimsearch">Claims Search</a></span>
            <span class="tracker"><a href="#" id="tracker">Tracker</a></span>
        </div>
    </li>
</ul>

HTML
    }

    my $menuHTML = <<EOF;
<div class="container"><div class="row"><div class="main_nav">
     $ul
 </div></div><!--/main_nav--></div>
 <!--/header--></div>

EOF

    return $menuHTML;
}

sub getTop
{
    my $ENGINE = shift;
    my $action = $ENGINE->{'ACTION'};

    my $header = getHeader($ENGINE);
    my $menu = getMenu($ENGINE);
    my $claims_details = getClaimDetails($ENGINE);

    my $html = <<EOF;
$header
$menu
</div>
<div class="row">
$claims_details
<form name="mainform" id="mainform" action="$action" method="post">
EOF


    return $html;
}

sub getBottom
{
    my $ENGINE = shift;
    my $saveLoad = shift;
    my $sessID = $ENGINE->{'SESSION'}->{'sessionID'};
    my $claimid = $ENGINE->{'claimGeneral'}->{'CLAIM_ID'};
    my $last_save = $ENGINE->{'claimGeneral'}->{'LAST_SAVE'};
    my $desktop_or_mobile = $ENGINE->{'CGI'}->param('desktop_or_mobile') || '';

    my $footer = getFooter($ENGINE);

    my $html = <<EOF;
$footer
<div>
<input type="hidden" value="$sessID" name="sessionID"/>
<input type="hidden" value="$saveLoad" name="save" id="save"/>
<input type="hidden" value="$saveLoad" name="load" id="load"/>
<input type="hidden" value="$claimid" name="claimid" id="claimid"/>
<input type="hidden" value="$last_save" name="last_save" id="last_save"/>
<input type="hidden" value="" name="transactionCode" id="transactionCode"/>
<input type="hidden" value="$desktop_or_mobile" name="desktop_or_mobile" id="desktop_or_mobile"/>
<input type="hidden" value="N" name="wc_detail_jump" id="wc_detail_jump"/>
</div>
<!--/mainform--></form>
<!--/row--></div>
<!--/container--></div>
EOF

    return $html;
}

=item * getClaimDetails()

This function returns the Claims Details html that is used at the top of each page.

=cut
sub getClaimDetails
{
    my $ENGINE = shift;

    my $itemID = $ENGINE->{'claimGeneral'}->{'ITEM_ID'};
    my $claimid = $ENGINE->{'claimGeneral'}->{'CLAIM_ID'};
    my $policyNum = $ENGINE->{'claimGeneral'}->{'DISPLAY_POLICY_NUMBER'};
    my $lossDate = $ENGINE->{'claimGeneral'}->{'LOSS_DATE_TIME'};
    my $claim_num = $ENGINE->{'claimGeneral'}->{'IMT_CLAIM_NO'} || '';

    my $heading;
#    if($ENGINE->{'claimGeneral'}->{'MANUAL_OR_WHAT'} eq 'M')
#    {
    if($ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} !~ /P|M/)
    {
        my $load = $ENGINE->{'load'};
#        if($load =~ /Claims_Details/)
        if($load =~ /Claims_Monetary/)
        {
            undef ($ENGINE->{'SESSION'}->{'claimid'.$claimid}->{'claimheading'});
            undef ($ENGINE->{'SESSION'}->{'claimid'.$claimid}->{'firstAdjuster'});
            undef ($ENGINE->{'SESSION'}->{'claimid'.$claimid}->{'firstAdjusterEmail'});
            undef ($ENGINE->{'SESSION'}->{'claimid'.$claimid}->{'firstAdjusterPhone'});
            undef ($ENGINE->{'SESSION'}->{'claimid'.$claimid}->{'firstAdjusterFax'});
        }
#        elsif($load =~ /Claims_Details/)
#        { undef ($ENGINE->{'SESSION'}->{'claimid'.$claimid}->{'claimheading'}); }
    }
    if($ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} !~ /P/)
    {
        my $save = $ENGINE->{'save'};
#        if($save =~ /Claims_Details/)
        if(defined($save) && $save =~ /Claims_Vehicle|Claims_PropLiab|Claims_WorkComp/)
        { undef ($ENGINE->{'SESSION'}->{'claimid'.$claimid}->{'claimheading'}); }
    }

    my $policyLink = '';
    if (defined($itemID))
    {
       my $STH = $ENGINE->{'DBH'}->prepare(
          'SELECT STATUS
             FROM CLIENTDB.VERSION
            WHERE ITEM_ID = ?
              AND STATUS <> \'Z\'') || error($ENGINE,'General query prepare failed: '.$ENGINE->{'DBH'}->errstr);

       $STH->execute($itemID) || error($ENGINE,'General query execute failed: '.$ENGINE->{'DBH'}->errstr);

       my $result = $STH->fetchall_arrayref({});

       if (@$result > 0)
       {
          $ENGINE->{'SESSION'}->{'claimid'.$claimid}->{'policylink'} = '<a target="_blank" href="../Client/'.($ENGINE->{'AUTH'}->{'ClientEngine'} || 'ClientEngine.pl').'?load=ClientPView&amp;id='.$itemID.'">'.$policyNum.'</a>';
          if (check_for_platform_version({dbh => $ENGINE->{'DBH'}, item_id => $itemID})) {
            $ENGINE->{'SESSION'}->{'claimid'.$claimid}->{'policylink'} = '<a target="_blank" href="'.get_policy_info_page_url({policy_number => $policyNum}).'">'.$policyNum.'</a>';
          }
       }
       else
       {
          $ENGINE->{'SESSION'}->{'claimid'.$claimid}->{'policylink'} = $policyNum;
       }
    }
    else
    {
       $ENGINE->{'SESSION'}->{'claimid'.$claimid}->{'policylink'} = $policyNum;
    }
    $policyLink = $ENGINE->{'SESSION'}->{'claimid'.$claimid}->{'policylink'};

    my $claimStatus = getClaimStatus($ENGINE);

    $lossDate = $ENGINE->{'SESSION'}->{'claimid'.$claimid}->{'lossdate'} ||
     substr($lossDate,5,2).'/'.substr($lossDate,8,2).'/'.substr($lossDate,0,4);
    if($lossDate eq '01/01/9999')
      { $lossDate = 'Unknown'; }

    my $lineCode = $ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} || '';
    my $wcEMPResults = [];
    my $wcEMPName = '';
    if (($lineCode eq '600' || $lineCode eq '605') && $ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'Internal')
    {
        my $wcEMP = $ENGINE->{'DBH'}->prepare(
        "    SELECT P.CLAIM_ID
              ,G.POLICY_NUMBER ,P.PARTY_ID
               ,P.FIRST_NAME ,P.LAST_NAME
          FROM CLAIMDB.CLM_GENERAL G
          INNER JOIN CLAIMDB.CLM_PARTIES P
                    ON
                    G.CLAIM_ID = P.CLAIM_ID
          INNER JOIN CLAIMDB.CLM_PARTY_ROLES PR
                    ON
                    PR.PARTY_ID = P.PARTY_ID
                    AND
                    PR.CLAIM_ID = G.CLAIM_ID
          WHERE    G.CLAIM_ID = ?
                   AND G.CLAIM_STATUS <> 'P'
                   AND G.DATE_DELETED = '9999-01-01-01.00.00.000000'
                   AND P.DATE_DELETED = '9999-01-01-01.00.00.000000'
                   AND PR.ROLE = 'EM'
                   AND PR.DATE_DELETED = '9999-01-01-01.00.00.000000'")|| error($ENGINE,'WC Employee query prepare failed: '.$ENGINE->{'DBH'}->errstr);
         $wcEMP->execute($claimid) || error($ENGINE,'WC Employee query execute failed: '.$ENGINE->{'DBH'}->errstr);
         $wcEMPResults = $wcEMP->fetchall_arrayref({});

         if(@$wcEMPResults > 0)
         {
             $wcEMPResults->[0]->{'FIRST_NAME'} =~ s/&/&amp;/g;
             $wcEMPResults->[0]->{'FIRST_NAME'} =~ s/</&lt;/g;
             $wcEMPResults->[0]->{'LAST_NAME'} =~ s/&/&amp;/g;
             $wcEMPResults->[0]->{'LAST_NAME'} =~ s/</&lt;/g;
             $wcEMPName = '<li><label>Employee:</label><div>'.$wcEMPResults->[0]->{'FIRST_NAME'}.' '.$wcEMPResults->[0]->{'LAST_NAME'}.'</div></li>';
         }
    }



    my $insureds_html = '';
    my $insdQuery = $ENGINE->{'DBH'}->prepare('SELECT P.FIRST_NAME, P.LAST_NAME, P.BUSINESS_NAME FROM CLAIMDB.CLM_PARTIES AS P
INNER JOIN CLAIMDB.CLM_PARTY_ROLES AS R ON P.PARTY_ID = R.PARTY_ID AND R.DATE_DELETED = \'9999-01-01 01:00:00.000000\'
WHERE P.CLAIM_ID = ? AND R.ROLE = \'IN\' AND P.DATE_DELETED = \'9999-01-01 01:00:00.000000\' ORDER BY P.PARTY_ID') || error($ENGINE,'insured query prepare failed: '.$ENGINE->{'DBH'}->errstr);
    $insdQuery->execute($claimid) || error($ENGINE,'Insured query execute failed: '.$ENGINE->{'DBH'}->errstr);
    my $insdResults = $insdQuery->fetchall_arrayref({});
    my @insdNames;
    for my $r (@$insdResults)
    {
       for my $key ('FIRST_NAME','LAST_NAME','BUSINESS_NAME')
       {
          $r->{$key} =~ s/&/&amp;/g;
          $r->{$key} =~ s/</&lt;/g;
       }
       if($r->{'FIRST_NAME'} =~ /\w/ || $r->{'LAST_NAME'} =~ /\w/)
         { push(@insdNames,$r->{'FIRST_NAME'}.'&nbsp;'.$r->{'LAST_NAME'}); }
       elsif($r->{'BUSINESS_NAME'} =~ /\w/)
         { push(@insdNames,$r->{'BUSINESS_NAME'}); }
    }

    if(scalar @insdNames > 0){
        $insureds_html = '<li id="cd_insured"><label>Insured:</label><div>'.shift(@insdNames);
        for my $insd (@insdNames){
            $insureds_html .= '<br/>'.$insd;
        }
        $insureds_html .= '</div></li>';
    }

    my $partyInfoResults = ();
    if($lineCode !~ /600|605/)
    {
	     my $partyInfoQuery = $ENGINE->{'DBH'}->prepare("SELECT
	                                                     A.ADDRESS1,
	                                                     A.ADDRESS2,
	                                                     A.CITY,
	                                                     A.STATE,
	                                                     A.ZIP1_5,
	                                                     C.CONTACT_INFO
	                                                 FROM
	                                                     CLAIMDB.CLM_LOCATION AS A
	                                                 JOIN
	                                                     CLAIMDB.CLM_PARTY_CONTACT AS C ON A.PARTY_ID = C.PARTY_ID
	                                                 WHERE
	                                                     A.CLAIM_ID = ? AND
	                                                     A.LOC_TYPE = 'AD' AND
	                                                     C.TYPE = 'IP'");
	    $partyInfoQuery->execute($claimid) || error($ENGINE,'Party query execute failed: '.$ENGINE->{'DBH'}->errstr);
	    $partyInfoResults = $partyInfoQuery->fetchall_arrayref({});
    }
    elsif($lineCode =~ /600|605/)
    {
	     my $partyInfoQuery = $ENGINE->{'DBH'}->prepare("SELECT
	                                                     A.ADDRESS1,
	                                                     A.ADDRESS2,
	                                                     A.CITY,
	                                                     A.STATE,
	                                                     A.ZIP1_5,
	                                                     C.CONTACT_INFO
	                                                 FROM
	                                                     CLAIMDB.CLM_PARTIES P
	                                                 JOIN
	                                                     CLAIMDB.CLM_PARTY_ROLES R ON R.PARTY_ID = P.PARTY_ID
	                                                 JOIN
	                                                     CLAIMDB.CLM_LOCATION AS A ON A.PARTY_ID = P.PARTY_ID
	                                                 JOIN
	                                                     CLAIMDB.CLM_PARTY_CONTACT AS C ON A.PARTY_ID = C.PARTY_ID
	                                                 WHERE
	                                                     P.CLAIM_ID = ? AND
	                                                     A.LOC_TYPE = 'AD' AND
	                                                     R.ROLE = 'IP' AND
	                                                     C.TYPE = 'PH'");
	    $partyInfoQuery->execute($claimid) || error($ENGINE,'Party query execute failed: '.$ENGINE->{'DBH'}->errstr);
	    $partyInfoResults = $partyInfoQuery->fetchall_arrayref({});
    }

    my $partiesResults = Claims_Parties::getPartiesData($ENGINE);
#   die Data::Dumper::Dumper($ENGINE);
    my $basicPartyInfo = $partiesResults->{'basicPartyInfo'};
    my $primaryPhone = $basicPartyInfo->{'primaryPhone'} || '';
    my $secondaryPhone = $basicPartyInfo->{'secondaryPhone'} || '';
#    die Data::Dumper::Dumper($claimid,$partiesResults);
    my $street = '';
    my $city_state = '&nbsp';
    my $phone = $primaryPhone || $secondaryPhone;
    if(defined @$partyInfoResults[0]){
        $street = @$partyInfoResults[0]->{ADDRESS1}.' '.@$partyInfoResults[0]->{ADDRESS2};
        $city_state = @$partyInfoResults[0]->{CITY}.', '.@$partyInfoResults[0]->{STATE};
        $phone = @$partyInfoResults[0]->{CONTACT_INFO};
    }

#    my $insureds_address_html = '<li><label>Address:</label><span class="street">'.$street.'</span><span class="citystate">'.$city_state.'</span></li>';
    my $insureds_address_html = '<li id="cd_insd_address"><label>Address:</label><div>'.($partiesResults->{'insuredAddress'} || '&nbsp;').'</div></li>';
    my $insureds_phone_html = '<li id="cd_insd_phone"><label>Phone:</label><div>'.($phone||'&nbsp;').'</div></li>';

    my $adjuster_html = '';
    if(!defined($ENGINE->{'SESSION'}->{'claimid'.$claimid}->{'claimheading'}))
    {
        my $agencyNum = '';
        if(!defined($ENGINE->{'SESSION'}->{'claimid'.$claimid}->{'firstAdjuster'}))
        {
          #lwm.sql - 1172
            my $adjusterQuery = $ENGINE->{'DBH'}->prepare('
                SELECT
                    DATE_ASSIGNED, C.USER_KEY, c.date_completed
                    FROM CLAIMDB.CLM_REP_ASSIGNED AS C
                    WHERE CLAIM_ID = ?  AND
                    C.DATE_REMOVED = \'9999-01-01 01:00:00.000000\' and
                    c.date_completed = \'9999-01-01\'
                    ORDER BY CLM_REP_ASGN_ID FETCH FIRST 1 ROWS ONLY') || error($ENGINE,'Adjuster query prepare failed: '.$ENGINE->{'DBH'}->errstr);
            $adjusterQuery->execute($claimid) || error($ENGINE,'Adjuster query execute failed: '.$ENGINE->{'DBH'}->errstr);
            my $adjusterResults = $adjusterQuery->fetchall_arrayref({});
            my $adjuster = '';
            my $firstAdjusterName = 'Unknown';
            my $firstAdjusterEmail = '';
            my $repAssignedDate = '';
            if(scalar(@$adjusterResults) > 0)
            {
                my $firstAdjuster = shift(@$adjusterResults);
                my $firstUserKey = $firstAdjuster->{'USER_KEY'};

                my $user_key_data = fetch_user_key_data({
                  authorization => $ENGINE->{'AUTH'}->{'platform_access_token'},
                  user_key => $firstUserKey,
                  max_attempts => 2
                });

                my $hold_first_name = uc($user_key_data->{content}->{data}->[0]->{attributes}->{first_name});
                my $hold_last_Name = uc($user_key_data->{content}->{data}->[0]->{attributes}->{last_name});
                my $hold_dept_code = convert_dept_code({department => $user_key_data->{content}->{data}->[0]->{attributes}->{department}});
                my $hold_email = $user_key_data->{content}->{data}->[0]->{attributes}->{email};
                my $hold_bus_ext = $user_key_data->{content}->{data}->[0]->{attributes}->{other}->{business_phones}->[0];
                my $hold_fax_number = $user_key_data->{content}->{data}->[0]->{attributes}->{other}->{fax_number};
                my $hold_is_active = $user_key_data->{content}->{data}->[0]->{attributes}->{is_active};

                if(defined($hold_first_name) && $hold_first_name eq '' && defined($hold_last_Name) && $hold_last_Name gt '')
                  { $firstAdjusterName = $hold_last_Name; }
                else
                  { $firstAdjusterName = $hold_first_name.' '.$hold_last_Name; }

                $firstAdjusterName =~ s/&/&amp;/g;
                $firstAdjusterName =~ s/</&lt;/g;

                $ENGINE->{'SESSION'}->{'claimid'.$claimid}->{'firstAdjuster'} = $firstAdjusterName;

                if(defined($hold_is_active) && defined($hold_dept_code) && $hold_dept_code eq 'CLH')
                {
                    if($hold_email)
                    {
                        $firstAdjusterEmail = $hold_email;
                        $firstAdjusterEmail =~ s/&/&amp;/g;
                        $firstAdjusterEmail =~ s/</&lt;/g;
                    }
                    $ENGINE->{'SESSION'}->{'claimid'.$claimid}->{'firstAdjusterEmail'} = $firstAdjusterEmail;
                    $ENGINE->{'SESSION'}->{'claimid'.$claimid}->{'firstAdjusterPhone'} = 'Ph # (************* x'.$hold_bus_ext;
                    if ($hold_fax_number) {
                        $ENGINE->{'SESSION'}->{'claimid'.$claimid}->{'firstAdjusterFax'} = 'Fax # ('.substr($hold_fax_number,0,3).') '.substr($hold_fax_number,3,3).'-'.substr($hold_fax_number,6,4);
                    }

                }
            }
          }
        }
        my $firstAdjusterName = $ENGINE->{'SESSION'}->{'claimid'.$claimid}->{'firstAdjuster'} || 'NOT YET ASSIGNED';
        $firstAdjusterName =~ s/&/&amp;/g;
        $firstAdjusterName =~ s/</&lt;/g;
        my $firstAdjusterEmail = $ENGINE->{'SESSION'}->{'claimid'.$claimid}->{'firstAdjusterEmail'} || '';
        $firstAdjusterEmail =~ s/&/&amp;/g;
        $firstAdjusterEmail =~ s/</&lt;/g;
        my $firstAdjusterPhone = $ENGINE->{'SESSION'}->{'claimid'.$claimid}->{'firstAdjusterPhone'} || '';
        $firstAdjusterPhone =~ s/&/&amp;/g;
        $firstAdjusterPhone =~ s/</&lt;/g;
        my $firstAdjusterFax = $ENGINE->{'SESSION'}->{'claimid'.$claimid}->{'firstAdjusterFax'} || '';
        $firstAdjusterFax =~ s/&/&amp;/g;
        $firstAdjusterFax =~ s/</&lt;/g;
#    die Data::Dumper::Dumper($firstAdjusterEmail,$firstAdjusterPhone,$firstAdjusterFax);
    $adjuster_html = '<a href="#" data-toggle="modal" data-target="#adjusterModal">'.$firstAdjusterName.'</a>';
    if($firstAdjusterEmail ne '')
    {$firstAdjusterEmail = '<a href="mailto:'.$firstAdjusterEmail.'?subject=Regarding Claim Number '.$ENGINE->{'claimGeneral'}->{'IMT_CLAIM_NO'}.'">'.$firstAdjusterEmail.'</a>';}
    if($firstAdjusterName eq 'NOT YET ASSIGNED' || $firstAdjusterName eq 'NETWORK GLASS'){
        $adjuster_html = $firstAdjusterName;
    }

    my %branches = (21=>'Glass Claim',
10=>'Home Office',
20=>'Des Moines',
30=>'Cedar Rapids',
40=>'Mason City',
50=>'Sioux Falls',
60=>'Sioux City',
70=>'Omaha',
22=>'Automated Proclaim Glass Claim',
12=>'Bond Claim',
75=>'Quad Cities',
00=>'Unknown');
    my $branchInfo = 'Unknown';
    if(defined $ENGINE->{'claimGeneral'}->{'BRANCH'})
    {$branchInfo = uc($branches{$ENGINE->{'claimGeneral'}->{'BRANCH'}});}
    if($ENGINE->{'claimGeneral'}->{'BRANCH'} eq '00' && substr($ENGINE->{'claimGeneral'}->{'IMT_CLAIM_NO'},2,1) eq 'A')
    { $branchInfo = 'DES MOINES'; }

    my $agency_name = '';
    my $agency_address = '';
    my $agency_city = '';
    my $agency_state = '';
    my $agency_zip = '';
    my $agency_phone = '';
    my $agency_fax = '';
    my $agency_city_state = '';

    my @agencyInfo;
    my $agentEmail = '&nbsp;';
    my $agentName = '&nbsp;';

    if($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'Internal' ||
       $ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'Agent')
    {
       my $agencyNum = $ENGINE->{'claimGeneral'}->{'AGENCY_NO'};
    #            my $STH = $ENGINE->{'DBH'}->prepare('SELECT NAME,ADDRESS1,ADDRESS2,CITY,STATE,ZIP,PHONE,FAX FROM IMTINSDB.AGENTS WHERE NUM = ?') ||
    #                  error($ENGINE,'General query prepare failed: '.$ENGINE->{'DBH'}->errstr);
    #            $STH->execute($ENGINE->{'claimGeneral'}->{'AGENCY_NO'}) || error($ENGINE,'General query execute failed: '.$ENGINE->{'DBH'}->errstr);
    #            my $result = $STH->fetchall_arrayref({});
       my $agencyno = "$ENGINE->{'claimGeneral'}->{'AGENCY_NO'}";
       #1207 - agency data, does not need updated - 1325
       my $agencyPhoneQuery = $ENGINE->{'DBH'}->prepare('SELECT C.CONTACT_INFO,C.TYPE FROM GENSUPDB.USER_INFO G JOIN CLIENTDB.CLIENT CL ON CL.USER_KEY = G.USER_KEY
    JOIN CLIENTDB.CLIENT_HAS_CONTACT HC ON HC.CLIENT_ID = CL.CLIENT_ID JOIN CLIENTDB.CONTACT C ON C.CONTACT_ID = HC.CONTACT_ID WHERE G.USER_ID = ? AND C.TYPE IN (\'PW\',\'FX\')') ||
             error($ENGINE,'User Info query prepare failed: '.$ENGINE->{'DBH'}->errstr);
       $agencyPhoneQuery->execute($agencyno) || error($ENGINE,'User Info query execute failed: '.$ENGINE->{'DBH'}->errstr);
       my $agencyPhoneResults = $agencyPhoneQuery->fetchall_arrayref({});

        #1207 - agency data, does not need updated
        my $agencyAddressQuery = $ENGINE->{'DBH'}->prepare('SELECT G.USER_ID, Z.NAME, A.ADD_LINE1, A.ADD_LINE2, A.ADD_CITY, A.ADD_STATE, A.ADD_ZIP, A.ADD_TYPE
    FROM GENSUPDB.USER_INFO G JOIN AGENTDB.AGENCY AS Z ON G.USER_ID = Z.AGENCYNO JOIN CLIENTDB.CLIENT C ON C.USER_KEY = G.USER_KEY JOIN CLIENTDB.CLIENT_HAS_ADDRESS HA ON HA.CLIENT_ID = C.CLIENT_ID
    JOIN CLIENTDB.ADDRESS A ON A.ADDRESS_ID = HA.ADDRESS_ID WHERE A.ADD_TYPE IN (\'P\',\'E\') AND Z.AGENCYNO = ?') ||
             error($ENGINE,'User Info query prepare failed: '.$ENGINE->{'DBH'}->errstr);
       $agencyAddressQuery->execute($agencyno) || error($ENGINE,'User Info query execute failed: '.$ENGINE->{'DBH'}->errstr);
       my $agencyAddressResults = $agencyAddressQuery->fetchall_arrayref({});

       my $agentEmailQuery = $ENGINE->{'DBH'}->prepare('SELECT
        C.CONTACT_INFO, C.FIRST_NAME, C.LAST_NAME FROM CLAIMDB.CLM_PARTY_ROLES R JOIN CLAIMDB.CLM_PARTY_CONTACT C ON C.PARTY_ID = R.PARTY_ID
                WHERE R.CLAIM_ID = ? AND R.ROLE = \'SB\' AND C.TYPE = \'EM\'') ||
             error($ENGINE,'User Info query prepare failed: '.$ENGINE->{'DBH'}->errstr);
       $agentEmailQuery->execute($ENGINE->{'claimGeneral'}->{'CLAIM_ID'}) || error($ENGINE,'User Info query execute failed: '.$ENGINE->{'DBH'}->errstr);
       my $agentEmailResults = $agentEmailQuery->fetchall_arrayref({});
#       die Data::Dumper::Dumper($ENGINE->{'claimGeneral'}->{'CLAIM_ID'},@$agentEmailResults[0]->{'CONTACT_INFO'});

       if(defined(@$agentEmailResults[0]))
       {
            $agentEmail = '<a href="mailto:'.@$agentEmailResults[0]->{'CONTACT_INFO'}.'?subject=Regarding Claim Number '.$ENGINE->{'claimGeneral'}->{'IMT_CLAIM_NO'}.'">'.@$agentEmailResults[0]->{'CONTACT_INFO'}.'</a>';
            $agentName = @$agentEmailResults[0]->{'FIRST_NAME'}.' '.@$agentEmailResults[0]->{'LAST_NAME'};
       }
       if(@$agencyAddressResults > 0)
       {
           my $agencyPhoneNo = '';
           my $agencyFaxNo = '';
           my $x = 0;
           # added $x to only get the first 2 phone and fax numbers if there are more than 1 set
           for my $ap (@$agencyPhoneResults)
           {
               $x++;
               if($ap->{'TYPE'} eq 'PW')
               { $agencyPhoneNo = $ap->{'CONTACT_INFO'}; }
               elsif($ap->{'TYPE'} eq 'FX')
               { $agencyFaxNo = $ap->{'CONTACT_INFO'}; }
               if($x == 2)
               { last; }
           }
           for my $key ('NAME','ADD_LINE1','ADD_LINE2','ADD_CITY','ADD_STATE','ADD_ZIP')
           {
              $agencyAddressResults->[0]->{$key} =~ s/&/&amp;/g;
              $agencyAddressResults->[0]->{$key} =~ s/</&lt;/g;
           }

           $agency_name = $agencyAddressResults->[0]->{'NAME'};

           if(defined($agencyAddressResults->[0]->{'ADD_LINE1'}) && $agencyAddressResults->[0]->{'ADD_LINE1'} =~ /\w/)
             { $agency_address = $agencyAddressResults->[0]->{'ADD_LINE1'}; }
           if(defined($agencyAddressResults->[0]->{'ADD_LINE2'}) && $agencyAddressResults->[0]->{'ADD_LINE2'} =~ /\w/)
             { $agency_address .= ' '.$agencyAddressResults->[0]->{'ADD_LINE2'}; }
           if(defined($agencyAddressResults->[0]->{'ADD_CITY'}) && $agencyAddressResults->[0]->{'ADD_CITY'} =~ /\w/ &&
              defined($agencyAddressResults->[0]->{'ADD_STATE'}) && $agencyAddressResults->[0]->{'ADD_STATE'} =~ /\w/ &&
              defined($agencyAddressResults->[0]->{'ADD_ZIP'}) && $agencyAddressResults->[0]->{'ADD_ZIP'} =~ /\w/)
           { $agency_city = $agencyAddressResults->[0]->{'ADD_CITY'};
             $agency_state = $agencyAddressResults->[0]->{'ADD_STATE'};
             $agency_zip = $agencyAddressResults->[0]->{'ADD_ZIP'}; }
           if(defined($agencyPhoneNo) && $agencyPhoneNo =~ /\w/ && length($agencyPhoneNo) >= 10)
           { $agency_phone = $agencyPhoneNo; }
           if(defined($agencyFaxNo) && $agencyFaxNo =~ /\w/ && length($agencyFaxNo) >= 10)
           { $agency_fax = $agencyFaxNo; }

           $agency_city_state = $agency_city.', '.$agency_state.' '.$agency_zip;
       }
    }
    my $agency_html = '<a href="#" data-toggle="modal" data-target="#agencyModal">'.$agency_name.'</a>';
#    die Data::Dumper::Dumper('Claims_Misc line 1179',$agency_html,$agency_name);
    my $utility_bar = getUtilityBar($ENGINE);
    my $agencyNum = $ENGINE->{'claimGeneral'}->{'AGENCY_NO'};

    my $createLetters = '';
    if (defined $ENGINE->{'AUTH'}->{'Claims_Access'}
        && $ENGINE->{'AUTH'}->{'Claims_Access'} eq 'A')
    {
            my $path = File::Spec->catpath('d:', '/imtonline_media/print_files/New Claim Templates/', '');

            my $tempDIR = $path."Audit";
            my @auditList = dirList($ENGINE,$tempDIR);
            $tempDIR = $path."Auto";
            my @autoList = dirList($ENGINE,$tempDIR);
            $tempDIR = $path."General Form Letters";
            my @generalList = dirList($ENGINE,$tempDIR);
            $tempDIR = $path."Litigation";
            my @litigationList = dirList($ENGINE,$tempDIR);
            $tempDIR = $path."Medical";
            my @medicalList = dirList($ENGINE,$tempDIR);
            $tempDIR = $path."Medicare";
            my @medicareList = dirList($ENGINE,$tempDIR);
            $tempDIR = $path."Minnesota";
            my @minList = dirList($ENGINE,$tempDIR);
            $tempDIR = $path."Minnesota/Medical Authorizations";
            my @medAuthList = dirList($ENGINE,$tempDIR);
            $tempDIR = $path."Miscellaneous Forms";
            my @miscList = dirList($ENGINE,$tempDIR);
            $tempDIR = $path."Police-Fire Letters";
            my @policyList = dirList($ENGINE,$tempDIR);
            $tempDIR = $path."Property";
            my @propertyList = dirList($ENGINE,$tempDIR);
            $tempDIR = $path."Reinsurance";
            my @reinList = dirList($ENGINE,$tempDIR);
            $tempDIR = $path."Releases";
            my @releasesList = dirList($ENGINE,$tempDIR);
            $tempDIR = $path."Statements";
            my @statementsList = dirList($ENGINE,$tempDIR);
            $tempDIR = $path."Subrogation";
            my @subroList = dirList($ENGINE,$tempDIR);
            $tempDIR = $path."Work Comp";
            my @workCompList = dirList($ENGINE,$tempDIR);
            my $setIcon = '';

            my $outfiledir = '';
            $createLetters = '<ul class="file_directory">';
            $createLetters .= '<li id="dirList"><ul class="file_directory"><li><div class="icon fo"></div><a href="#createLettersHeadin" onclick="toggle(\'dirAudit\');"><b>Audit</b></a><br /></li><li style="display:none" id="dirAudit"><ul class="file_directory">';
            $outfiledir = 'a1';
            foreach my $auditList (@auditList)
            {
                if($auditList eq 'Thumbs.db')
                { next; }
                else
                {
                    $setIcon = getIcon($auditList);
                    $createLetters .= '<li>'.$setIcon.'<a href="outputFile.pl?outfiledir='.$outfiledir.';claimid='.$claimid.';outfilename='.$auditList.'"><b>'.$auditList.'</b></a></li>';
                }
            }
            $createLetters .= '</ul></li><li><div class="icon fo"></div><a href="#createLettersHeadin" onclick="toggle(\'dirAuto\');"><b>Auto</b></a><br /></li><li style="display:none" id="dirAuto"><ul class="file_directory">';
            $outfiledir = 'a2';
            foreach my $autoList (@autoList)
            {
                if($autoList eq 'Thumbs.db')
                { next; }
                else
                {
                    $setIcon = getIcon($autoList);
                    $createLetters .= '<li>'.$setIcon.'<b><a href="outputFile.pl?outfiledir='.$outfiledir.';claimid='.$claimid.';outfilename='.$autoList.'">'.$autoList.'</a></b></li>';
                }
            }
            $createLetters .= '</ul></li><li><div class="icon fo"></div><a href="#createLettersHeadin" onclick="toggle(\'dirGeneral\');"><b>General Form Letters</b></a><br /></li><li style="display:none" id="dirGeneral"><ul class="file_directory">';
            $outfiledir = 'a3';
            foreach my $generalList (@generalList)
            {
                if($generalList eq 'Thumbs.db')
                { next; }
                else
                {
                    $setIcon = getIcon($generalList);
                    $createLetters .= '<li>'.$setIcon.'<b><a href="outputFile.pl?outfiledir='.$outfiledir.';claimid='.$claimid.';outfilename='.$generalList.'">'.$generalList.'</a></b></li>';
                }
            }
            $createLetters .= '</ul></li><li><div class="icon fo"></div><a href="#createLettersHeadin" onclick="toggle(\'dirLitigation\');"><b>Litigation</b></a><br /></li><li style="display:none" id="dirLitigation"><ul class="file_directory">';
            $outfiledir = 'a4';
            foreach my $litigationList (@litigationList)
            {
                if($litigationList eq 'Thumbs.db')
                { next; }
                else
                {
                    $setIcon = getIcon($litigationList);
                    $createLetters .= '<li>'.$setIcon.'<b><a href="outputFile.pl?outfiledir='.$outfiledir.';claimid='.$claimid.';outfilename='.$litigationList.'">'.$litigationList.'</a></b></li>';
                }
            }
            $createLetters .= '</ul></li><li><div class="icon fo"></div><a href="#createLettersHeadin" onclick="toggle(\'dirMedical\');"><b>Medical</b></a><br /></li><li style="display:none" id="dirMedical"><ul class="file_directory">';
            $outfiledir = 'a5';
            foreach my $medicalList (@medicalList)
            {
                if($medicalList eq 'Thumbs.db')
                { next; }
                else
                {
                    $setIcon = getIcon($medicalList);
                    $createLetters .= '<li>'.$setIcon.'<b><a href="outputFile.pl?outfiledir='.$outfiledir.';claimid='.$claimid.';outfilename='.$medicalList.'">'.$medicalList.'</a></b></li>';
                }
            }
            $createLetters .= '</ul></li><li><div class="icon fo"></div><a href="#createLettersHeadin" onclick="toggle(\'dirMedicare\');"><b>Medicare</b></a><br /></li><li style="display:none" id="dirMedicare"><ul class="file_directory">';
            $outfiledir = 'a6';
            foreach my $medicareList (@medicareList)
            {
                if($medicareList eq 'Thumbs.db')
                { next; }
                else
                {
                    $setIcon = getIcon($medicareList);
                    $createLetters .= '<li>'.$setIcon.'<b><a href="outputFile.pl?outfiledir='.$outfiledir.';claimid='.$claimid.';outfilename='.$medicareList.'">'.$medicareList.'</a></b></li>';
                }
            }
            $createLetters .= '</ul></li><li><div class="icon fo"></div><a href="#createLettersHeadin" onclick="toggle(\'dirMinnesota\');"><b>Minnesota</b></a><br /></li><li style="display:none" id="dirMinnesota"><ul class="file_directory">';
            $createLetters .= '<li><div class="icon fo"></div><a href="#createLettersHeadin" onclick="toggle(\'dirMedicalAuthorizations\');"><b>Medical Authorizations</b></a><br /></li><li style="display:none" id="dirMedicalAuthorizations"><ul class="file_directory">';
            $outfiledir = 'a16';
            foreach my $medAuthList (@medAuthList)
            {
                if($medAuthList eq 'Thumbs.db')
                { next; }
                else
                {
                    $setIcon = getIcon($medAuthList);
                    $createLetters .= '<li>'.$setIcon.'<b><a href="outputFile.pl?outfiledir='.$outfiledir.';claimid='.$claimid.';outfilename='.$medAuthList.'">'.$medAuthList.'</a></b></li>';
                }
            }
            $createLetters .= '</ul></li>';
            $outfiledir = 'a7';
            foreach my $minList (@minList)
            {
                if($minList eq 'Thumbs.db')
                { next; }
                else
                {
                    $setIcon = getIcon($minList);
                    $createLetters .= '<li>'.$setIcon.'<b><a href="outputFile.pl?outfiledir='.$outfiledir.';claimid='.$claimid.';outfilename='.$minList.'">'.$minList.'</a></b></li>';
                }
            }
            $createLetters .= '</ul></li><li><div class="icon fo"></div><a href="#createLettersHeadin" onclick="toggle(\'dirMiscellaneousForms\');"><b>Miscellaneous Forms</b></a><br /></li><li style="display:none" id="dirMiscellaneousForms"><ul class="file_directory">';
            $outfiledir = 'a8';
            foreach my $miscList (@miscList)
            {
                if($miscList eq 'Thumbs.db')
                { next; }
                else
                {
                    $setIcon = getIcon($miscList);
                    $createLetters .= '<li>'.$setIcon.'<b><a href="outputFile.pl?outfiledir='.$outfiledir.';claimid='.$claimid.';outfilename='.$miscList.'">'.$miscList.'</a></b></li>';
                }
            }
            $createLetters .= '</ul></li><li><div class="icon fo"></div><a href="#createLettersHeadin" onclick="toggle(\'dirPoliceFireLetters\');"><b>Police-Fire Letters</b></a><br /></li><li style="display:none" id="dirPoliceFireLetters"><ul class="file_directory">';
            $outfiledir = 'a9';
            foreach my $policyList (@policyList)
            {
                if($policyList eq 'Thumbs.db')
                { next; }
                else
                {
                    $setIcon = getIcon($policyList);
                    $createLetters .= '<li style="padding-left:1em;height:17px;">'.$setIcon.'<b><a href="outputFile.pl?outfiledir='.$outfiledir.';claimid='.$claimid.';outfilename='.$policyList.'">'.$policyList.'</a></b></li>';
                }
            }
            $createLetters .= '</ul></li><li><div class="icon fo"></div><a href="#createLettersHeadin" onclick="toggle(\'dirProperty\');"><b>Property</b></a><br /></li><li style="display:none" id="dirProperty"><ul class="file_directory">';
            $outfiledir = 'a10';
            foreach my $propertyList (@propertyList)
            {
                if($propertyList eq 'Thumbs.db')
                { next; }
                else
                {
                    $setIcon = getIcon($propertyList);
                    $createLetters .= '<li>'.$setIcon.'<b><a href="outputFile.pl?outfiledir='.$outfiledir.';claimid='.$claimid.';outfilename='.$propertyList.'">'.$propertyList.'</a></b></li>';
                }
            }
            $createLetters .= '</ul></li><li><div class="icon fo"></div><a href="#createLettersHeadin" onclick="toggle(\'dirReinsurance\');"><b>Reinsurance</b></a><br /></li><li style="display:none" id="dirReinsurance"><ul class="file_directory">';
            $outfiledir = 'a11';
            foreach my $reinList (@reinList)
            {
                if($reinList eq 'Thumbs.db')
                { next; }
                else
                {
                    $setIcon = getIcon($reinList);
                    $createLetters .= '<li>'.$setIcon.'<b><a href="outputFile.pl?outfiledir='.$outfiledir.';claimid='.$claimid.';outfilename='.$reinList.'">'.$reinList.'</a></b></li>';
                }
            }
            $createLetters .= '</ul></li><li><div class="icon fo"></div><a href="#createLettersHeadin" onclick="toggle(\'dirReleases\');"><b>Releases</b></a><br /></li><li style="display:none" id="dirReleases"><ul class="file_directory">';
            $outfiledir = 'a12';
            foreach my $releasesList (@releasesList)
            {
                if($releasesList eq 'Thumbs.db')
                { next; }
                else
                {
                    $setIcon = getIcon($releasesList);
                    $createLetters .= '<li>'.$setIcon.'<b><a href="outputFile.pl?outfiledir='.$outfiledir.';claimid='.$claimid.';outfilename='.$releasesList.'">'.$releasesList.'</a></b></li>';
                }
            }
            $createLetters .= '</ul></li><li><div class="icon fo"></div><a href="#createLettersHeadin" onclick="toggle(\'dirStatements\');"><b>Statements</b></a><br /></li><li style="display:none" id="dirStatements"><ul class="file_directory">';
            $outfiledir = 'a13';
            foreach my $statementsList (@statementsList)
            {
                if($statementsList eq 'Thumbs.db')
                { next; }
                else
                {
                    $setIcon = getIcon($statementsList);
                    $createLetters .= '<li>'.$setIcon.'<b><a href="outputFile.pl?outfiledir='.$outfiledir.';claimid='.$claimid.';outfilename='.$statementsList.'" target="_blank">'.$statementsList.'</a></b></li>';
                }
            }
            $createLetters .= '</ul></li><li><div class="icon fo"></div><a href="#createLettersHeadin" onclick="toggle(\'dirSubrogation\');"><b>Subrogation</b></a><br /></li><li style="display:none" id="dirSubrogation"><ul class="file_directory">';
            $outfiledir = 'a14';
            foreach my $subroList (@subroList)
            {
                if($subroList eq 'Thumbs.db')
                { next; }
                else
                {
                    $setIcon = getIcon($subroList);
                    $createLetters .= '<li>'.$setIcon.'<b><a href="outputFile.pl?outfiledir='.$outfiledir.';claimid='.$claimid.';outfilename='.$subroList.'">'.$subroList.'</a></b></li>';
                }
            }
            $createLetters .= '</ul></li><li><div class="icon fo"></div><a href="#createLettersHeadin" onclick="toggle(\'dirWorkComp\');"><b>Work Comp</b></a><br /></li><li style="display:none" id="dirWorkComp"><ul class="file_directory">';
            $outfiledir = 'a15';
            foreach my $workCompList (@workCompList)
            {
                if($workCompList eq 'Thumbs.db')
                { next; }
                else
                {
                    $setIcon = getIcon($workCompList);
                    $createLetters .= '<li>'.$setIcon.'<b><a href="outputFile.pl?outfiledir='.$outfiledir.';claimid='.$claimid.';outfilename='.$workCompList.'">'.$workCompList.'</a></b></li>';
                }
            }
            $createLetters .= '</ul></li></ul></li></ul>';
    }
    my $unsub_hide = '';
    if($ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} eq 'P')
    {$unsub_hide = 'hidden';}
    my $branch_hide = '';
    if($branchInfo eq 'Unknown')
    {$branch_hide = 'class="hidden"';}

    #Get list of parties on claim and phone/email details for each
    my $partyQuery = $ENGINE->{'DBH'}->prepare
                ('SELECT distinct p.party_id, p.first_name, p.last_name, p.business_name, c.type, c.contact_info,
                                  c.first_name as contact_first_name, c.last_name as contact_last_name, c.precedence
                FROM CLAIMDB.CLM_PARTIES AS P
                    INNER JOIN
                            CLAIMDB.CLM_PARTY_ROLES AS R
                    ON
                            P.PARTY_ID = R.PARTY_ID
                            AND R.DATE_DELETED = \'9999-01-01 01:00:00.000000\'
                    LEFT JOIN
                        CLAIMDB.CLM_PARTY_CONTACT AS C
                    ON
                            P.PARTY_ID = C.PARTY_ID
                    WHERE P.CLAIM_ID = ?
                            AND R.ROLE IN (\'CL\',\'IP\')
                            AND P.DATE_DELETED = \'9999-01-01 01:00:00.000000\'')
                    || error($ENGINE,'Party query prepare failed: '.$ENGINE->{'DBH'}->errstr);

    $partyQuery->execute($claimid) || error($ENGINE,'Party query execute failed: '.$ENGINE->{'DBH'}->errstr);
    my $partyResults = $partyQuery->fetchall_arrayref({});
#    die Data::Dumper::Dumper($partyResults);
    my $cgi = $ENGINE->{'CGI'}->{'param'};
    my $errors = '';
    my $contact_info = {};
    for my $p (@$partyResults)
   {
       my $id = $p->{'PARTY_ID'};
       if((defined $p->{'TYPE'} && $p->{'TYPE'} =~ /IP/) && $p->{'PRECEDENCE'} eq '1')
       {$contact_info->{$id}->{'PHONE'} = $p->{'CONTACT_INFO'}}

#       if($p->{'TYPE'} =~ /IP/ && $p->{'PRECEDENCE'} eq '2' &&
#          $p->{'CONTACT_FIRST_NAME'} ne '')
#       {
#           $contact_info->{$id.'_2'}->{'PHONE'} = $p->{'CONTACT_INFO'};
#           $contact_info->{$id.'_2'}->{'FIRST_NAME'} = $p->{'CONTACT_FIRST_NAME'};
#           $contact_info->{$id.'_2'}->{'LAST_NAME'} = $p->{'CONTACT_LAST_NAME'};
#           next;
#       }

       if(defined $p->{'TYPE'} && $p->{'TYPE'} =~ /EM/)
       {$contact_info->{$id}->{'EMAIL'} = $p->{'CONTACT_INFO'}}
       if((!defined $contact_info->{$id}->{'BUSINESS_NAME'} ||
          (defined $contact_info->{$id}->{'BUSINESS_NAME'} && $contact_info->{$id}->{'BUSINESS_NAME'} eq '')) &&
          $p->{'BUSINESS_NAME'} ne '')
       {$contact_info->{$id}->{'BUSINESS_NAME'} = $p->{'BUSINESS_NAME'}}
       if((!defined $contact_info->{$id}->{'FIRST_NAME'} ||
          (defined $contact_info->{$id}->{'FIRST_NAME'} && $contact_info->{$id}->{'FIRST_NAME'} eq '')) &&
          (defined $p->{'CONTACT_FIRST_NAME'} && $p->{'CONTACT_FIRST_NAME'} ne '') && $p->{'FIRST_NAME'} eq '')
       {$contact_info->{$id}->{'FIRST_NAME'} = $p->{'CONTACT_FIRST_NAME'}}
       if((!defined $contact_info->{$id}->{'LAST_NAME'} ||
          (defined $contact_info->{$id}->{'LAST_NAME'} && $contact_info->{$id}->{'LAST_NAME'} eq '')) &&
          (defined $p->{'CONTACT_LAST_NAME'} && $p->{'CONTACT_LAST_NAME'} ne '') && $p->{'LAST_NAME'} eq '')
       {$contact_info->{$id}->{'LAST_NAME'} = $p->{'CONTACT_LAST_NAME'}}
       if((!defined $contact_info->{$id}->{'FIRST_NAME'} ||
          (defined $contact_info->{$id}->{'FIRST_NAME'} && $contact_info->{$id}->{'FIRST_NAME'} eq '')) &&
          $p->{'FIRST_NAME'} ne '')
       {$contact_info->{$id}->{'FIRST_NAME'} = $p->{'FIRST_NAME'}}
       if((!defined $contact_info->{$id}->{'LAST_NAME'} ||
          (defined $contact_info->{$id}->{'LAST_NAME'} && $contact_info->{$id}->{'LAST_NAME'} eq '')) &&
          $p->{'LAST_NAME'} ne '')
       {$contact_info->{$id}->{'LAST_NAME'} = $p->{'LAST_NAME'}}

        }
#    die Data::Dumper::Dumper($contact_info,$partyResults);
        my @sorted_info = sort{$contact_info->{$a}->{'FIRST_NAME'} cmp $contact_info->{$b}->{'FIRST_NAME'}} keys %$contact_info;
    for my $id (@sorted_info)
    {
       my $party = $contact_info->{$id};
       my $email = $party->{'EMAIL'};
       my $phone = $party->{'PHONE'};
       my $first = $party->{'FIRST_NAME'} || '';
       my $last = $party->{'LAST_NAME'} || '';
       my $party_name = $first.' '.$last;
       if(defined $party->{'BUSINESS_NAME'} && $party->{'BUSINESS_NAME'} ne '')
       {$party_name = $party->{'BUSINESS_NAME'}.' ('.$party_name.')';}
    }

    my $policy_coverages = '';
    if(!(defined $ENGINE->{'polCovsModalData'}))
    {getCovPartyMiscEndor($ENGINE)}
    $policy_coverages = $ENGINE->{'polCovsModalData'};
    my $policy_covs_help = $ENGINE->{'polCovsHelpData'};

#    die Data::Dumper::Dumper($mc_contact_list);
    my $html = <<HTML;
<!-- Modal -->
<div class="modal" id="policyCoveragesModal" tabindex="-1" role="dialog" aria-labelledby="policyCoveragesModal" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel">Policy Coverages</h4>
            </div>
            <div class="modal-body">
                $policy_coverages
            </div>
        </div>
    </div>
</div>
$policy_covs_help
<div class="modal" id="createLettersModal" tabindex="-1" role="dialog" aria-labelledby="createLettersModal" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel">Create Letters/Forms</h4>
            </div>
            $createLetters
        </div>
    </div>
</div>
<div class="modal" id="referencesModal" tabindex="-1" role="dialog" aria-labelledby="adjusterModal" aria-hidden="true">
          <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel">References</h4>
                </div>
                <div class="modal-body">
                              <ul class="references">

                        <li><a href="Auto%20Team%20Guidelines%20and%20Procedures.pdf">Auto Team Guidelines and Procedures</a></li>
                        <li><a href="Casualty%20Team%20Guidelines%20and%20Procedures.pdf">Casualty Team Guidelines and Procedures</a></li>
                        <li><a href="Phone%20Team%20Guidelines%20and%20Procedures.pdf">Phone Team Guidelines and Procedures</a></li>
                        <li><a href="Medical%20Payments%20Reference%20Guide.pdf">Medical Payments Reference Guide</a></li>
                        <li><a href="Property%20Team%20Guidelines%20and%20Procedures.pdf">Property Team Guidelines and Procedures</a></li>
                        <li><a href="Property%20Claims%20Reference%20Guide.pdf">Property Claims Reference Guide</a></li>
                        <li><a href="SIU%20Guidelines,%20Processes,%20and%20Procedures.pdf">SIU Guidelines, Processes and Procedures</a></li>
                        <li><a href="Work%20Comp%20Team%20Guidelines%20and%20Procedures.pdf">Work Comp Team Guidelines and Procedures</a></li>
                        <li><a href="Medicare%20Guidelines.pdf">Medicare Guidelines</a></li>
                        <li><a target="_blank" href="https://www.copart.com/seller">Auto Salvage Copart</a></li>
                        <li><a href="Claim%20File%20Folders%20-%20Auto.pdf">Claim File Folders - Auto</a></li>
                        <li><a href="Claim%20File%20Folders%20-%20Homeowners.pdf">Claim File Folders - Homeowners</a></li>
                        <li><a href="Claim%20File%20Folders%20-%20Work%20Comp.pdf">Claim File Folders - Work Comp</a></li>
                        <li><a target="_blank" href="http://www.claimspages.com/">Claims Pages</a></li>
                        <li><a target="_blank" href="http://www.capitalplanninginc.com/structured-settlements/">Capital Planning</a></li>
                        <li><a target="_blank" href="https://insource.nils.com/INsource/login.asp?Y=2">Compliance - OneSumX - NILS</a></li>
                        <li><a target="_blank" href="http://www.claimitred.com/">Contents - Claim-it-Red</a></li>
                        <li><a target="_blank" href="http://codeblueclaims.com/">Contents - CodeBlue</a></li>
                        <li><a target="_blank" href="https://submitnewclaim.claimtracker.com/">Contents - National Vendor</a></li>
                        <li><a target="_blank" href="http://www.judici.com/courts/court_list.jsp  ">Courts - Illinois</a></li>
                        <li><a target="_blank" href="http://www.iowacourts.state.ia.us/ESAWebApp/DefaultFrame">Courts - Iowa</a></li>
                        <li><a target="_blank" href="http://www.mncourts.gov/default.aspx?page=1927">Courts - Minnesota</a></li>
                        <li><a target="_blank" href="http://wcca.wicourts.gov/simpleCaseSearch.xsl;jsessionid=AF47316B73029A63C41F3AA0EE6F3197.render3?">Courts - Wisconsin</a></li>
                        <li><a target="_blank" href="http://www.verliance.com/placement-form">Deductible Recovery Request</a></li>

                 </ul>
                 <ul class="references">
                        <li><a target="_blank" href="https://www.armsweb.com/armslogon/logon?t=53140&rm= ">Enterprise ARMS</a></li>
                        <li><a target="_blank" href="http://www.rent-cars.com">Hertz Cars</a></li>
                        <li><a target="_blank" href="https://cpresolutions.com/referral/">Medicare Referral</a></li>
                        <li><a target="_blank" href="https://www.eagleview.com">Eagleview</a></li>
                        <li><a target="_blank" href="http://www.haagengineering.com/">HAAG Engineering</a></li>
                        <li><a target="_blank" href="http://accidentreports.iowa.gov/">ISP Crash Reports</a></li>
                        <li><a target="_blank" href="https://claimsearch.iso.com/index.asp  ">ISO ClaimSearch</a></li>
                        <li><a target="_blank" href="http://www.newcc.gov/">NE WC Court</a></li>
                        <li><a target="_blank" href="http://www.plrb.org/">PLRB</a></li>
                        <li><a href="SDIP%20Codes.pdf">SDIP Codes</a></li>
                        <li><a target="_blank" href="http://www.theodora.com/sic_index.html">SIC Index</a></li>
                        <li><a href="Subrogation%20Process%20and%20Contacts.pdf">Subrogation Process and Contacts</a></li>
                        <li><a href="Chain%20of%20Custody%20Form.pdf">Chain of Custody Form</a></li>
                        <li><a target="_blank" href="http://www.transwheel.com/">Transwheel</a></li>
                        <li><a href="Underwriting%20Memo%20Checklist.pdf ">Underwriting Memo Checklist</a></li>
                        <li><a target="_blank" href="http://www.wunderground.com/history/">Weather History</a></li>
                        <li><a href="Wording%20of%20Drafts.pdf">Wording of Drafts</a></li>
                        <li><a target="_blank" href="https://www.icd10data.com/ICD10CM/Codes">ICD10 Code Search</a></li>
                        <li><a target="_blank" href="https://xactimate.com/xor/sign-in">Xactimate</a></li>
                    </ul>
                        </div>
            </div>
          </div>
</div>

<div class="modal" id="adjusterModal" tabindex="-1" role="dialog" aria-labelledby="adjusterModal" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel">Contact Information for $firstAdjusterName</h4>
            </div>
            <div class="modal-body">
                <ul class="leftlabel_twocol">
                    <li id="cd_adj_email"><label>Email:</label><div>$firstAdjusterEmail</div></li>
                    <li id="cd_adj_phone"><label>Phone:</label><div>$firstAdjusterPhone</div></li>
                    <li id="cd_adj_fax"><label>Fax:</label><div>$firstAdjusterFax</div></li>
                </ul>
            </div>
        </div>
    </div>
</div>

<div class="modal" id="agencyModal" tabindex="-1" role="dialog" aria-labelledby="agencyModal" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel">Contact Information for $agency_name</h4>
            </div>
            <div class="modal-body">
                <ul class="leftlabel_twocol">
                    <li id="cd_agency_num"><label>Agency:</label><div>$agencyNum</div></li>
                    <li id="cd_agency_address"><label>Address:</label><span class="street"><div>$agency_address</div></span><span class="citystate">$agency_city_state</span></li>
                    <li id="cd_agent_name"><label>Agency Contact:</label><div><div>$agentName</div></div></li>
                    <li id="cd_agency_email"><label>Email:</label><div>$agentEmail</div></li>
                    <li id="cd_agency_phone"><label>Phone:</label><div>$agency_phone</div></li>
                    <li id="cd_agency_fax"><label>Fax:</label><div>$agency_fax</div></li>
                </ul>
            </div>
        </div>
    </div>
</div>
<div class="detailsbg">
    <fieldset class="threecol claim_details">
        <h2>Claim $claim_num</h2>
        <ul class="toplabel_threecol first">
            <li id="cd_policy"><label>Policy:</label><div>$policyLink</div></li>
            <li id="cd_loss_date"><label>Loss Date:</label><div>$lossDate</div></li>
            <li id="cd_status"><label>Status:</label><div>$claimStatus</div></li>
            $wcEMPName
        </ul>
        <ul class="toplabel_threecol second">
            $insureds_html
            $insureds_address_html
            $insureds_phone_html
        </ul>
        <ul class="toplabel_threecol third">
            <li id="cd_branch" $branch_hide><label>Branch:</label><div>$branchInfo</div></li>
            <li id="cd_adjuster_name"><label>Adjuster:</label><div>$adjuster_html<!--will open modal with details--></div></li>
            <li id="cd_agency_name"><label>Agency:</label><div>$agency_html<!--will open modal with details--></div></li>
        </ul>
        $utility_bar
    </fieldset>
</div>
HTML

    return $html;
}

=item * getUtilityBar()

This function returns the utility bar html that is used at the bottom of Claim Details.

=cut
sub getUtilityBar
{
    my $ENGINE = shift;
    my $UserType = $ENGINE->{'AUTH'}->{'IMTOnline_UserType'};
    my $sessID = $ENGINE->{'SESSION'}->{'sessionID'};
    my $claimid = $ENGINE->{'claimGeneral'}->{'CLAIM_ID'} || '';

    my $policyNum = $ENGINE->{'claimGeneral'}->{'POLICY_NUMBER'};
    my $lossDate = $ENGINE->{'claimGeneral'}->{'LOSS_DATE_TIME'};
    my $DBLossDate = substr($lossDate,0,10);
    my $policyNumLength = length($policyNum);
    if($policyNumLength gt 7)
    { $policyNum = substr($policyNum,2,7); }
    my $sth = $ENGINE->{'DBH'}->prepare('
        select  LINE_OF_BUSINESS,DATE_PROCESSED,
                POLICY_EFF_DATE,TYPE_OF_DOC,
                TOPIC_KEY
        from    GENSUPDB.MOBIUS
        where   POLICY_NUMBER = ? AND TYPE_OF_DOC IN (\'NEW BUSINESS\',\'AMENDMENT\',\'RENEWAL\')
AND POLICY_EFF_DATE <= ?') || error($ENGINE,$ENGINE->{'DBH'}->errstr);
    $sth->execute($policyNum,$DBLossDate) || error($ENGINE,$ENGINE->{'DBH'}->errstr);
    my $mobius = $sth->fetchall_arrayref({});
    @$mobius = reverse sort{$a->{'DATE_PROCESSED'} cmp $b->{'DATE_PROCESSED'}} @$mobius;

    my $decPages = '';
    my $maxDate = '';
    my $decForm = '';
    if((scalar @$mobius)==0) {
        $decPages = 'No print available |';
    }
    else {
        for my $m (@$mobius)
         {
            if($m->{'POLICY_EFF_DATE'}.'_'.$m->{'DATE_PROCESSED'} gt $maxDate)
            {
                $decPages = '<a href="#" onclick="var f = document.getElementById(\'decForm\');f.target=\'_blank\';f.submit()">Dec Page</a> |';
                $decForm = <<EOF;
                           <form id="decForm" method="post" action="../../agent_dashboard/policy-center/pd_mobiuspdf.pl"><div>
                           <input type="hidden" name="TOPIC1" value="$m->{'TOPIC_KEY'}" />
                           <input type="hidden" name="ACTG1" value="1" />
                           <input type="hidden" name="LINE_OF_BUSINESS1" value="$m->{'LINE_OF_BUSINESS'}" />
                           <input type="hidden" name="POLICY_EFF_DATE1" value="$m->{'POLICY_EFF_DATE'}" />
                           <input type="hidden" name="DEC_TYPE" value="3" /></div></form>
EOF
                $maxDate = $m->{'POLICY_EFF_DATE'}.'_'.$m->{'DATE_PROCESSED'};
            }

        }
    }

    my $loss_notice = '';
    my $load = $ENGINE->{'load'};
    if(!$ENGINE->{'READONLY'} && defined $ENGINE->{'claimGeneral'}->{'INITIATION_POINT'}
          && $ENGINE->{'claimGeneral'}->{'INITIATION_POINT'} ne 'CV' && $load ne 'Claims_Inquiry')
    {
        $loss_notice = getPrintLossNoticeButton($ENGINE);
    }
    elsif($ENGINE->{'READONLY'}
        && $ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} ne 'P'
        && defined $ENGINE->{'AUTH'}->{'Claims_CashEntry'}
        && $ENGINE->{'AUTH'}->{'Claims_CashEntry'} eq 'A'
        && $load eq 'Claims_Monetary')
    {
        $loss_notice = getPrintLossNoticeButton($ENGINE);
    }
    elsif($ENGINE->{'READONLY'}
            && $ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} ne 'P'
            && defined $ENGINE->{'AUTH'}->{'Claims_Access'}
            && $ENGINE->{'AUTH'}->{'Claims_Access'} eq 'A'
            && $load eq 'Claims_Monetary'
            && ($ENGINE->{'claimGeneral'}->{'REINSURANCE_IND'} eq 'P'
            || $ENGINE->{'claimGeneral'}->{'SUBRO_STATUS'} eq 'P'
            || $ENGINE->{'claimGeneral'}->{'SALV_STATUS'} eq 'P'
            || $ENGINE->{'claimGeneral'}->{'CLOSE_RECOVERABLE'} eq 'P'))
    {
        $loss_notice = getPrintLossNoticeButton($ENGINE);
    }
    elsif($ENGINE->{'READONLY'}
            && $ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} ne 'P'
            && $ENGINE->{'claimGeneral'}->{'INITIATION_POINT'} ne 'CV'
            && (defined $ENGINE->{'AUTH'}->{'Claims_Access'}
            && $ENGINE->{'AUTH'}->{'Claims_Access'} =~ /A|S/
            || defined $ENGINE->{'AUTH'}->{'Claims_Docs'}
            && $ENGINE->{'AUTH'}->{'Claims_Docs'} eq 'I')
            && $load ne 'Claims_Inquiry')
    {
        $loss_notice = getPrintLossNoticeButton($ENGINE);
    }

    my $doc_manager = '';
    my $refs = '';
    my $letters = '';
    if(defined $ENGINE->{'AUTH'}->{'Claims_Access'} && $ENGINE->{'AUTH'}->{'Claims_Access'} eq 'A') {
        $refs = '<a href="#" data-toggle="modal" data-target="#referencesModal">References</a> |';
        $letters = '<a href="#" data-toggle="modal" data-target="#createLettersModal">Letters&nbsp;/&nbsp;Forms</a> | ';
    }

    my $pol_covs = '<a href="#" data-toggle="modal" data-target="#policyCoveragesModal">Policy&nbsp;Coverages</a> | ';
    if($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /605|600/
        || $ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} eq 'P')
    {$pol_covs = '';}

    my $dec_link = $decPages;
    if(defined $ENGINE->{'claimGeneral'}->{'FGID'} && $ENGINE->{'claimGeneral'}->{'FGID'} != 0)
    {
        if (defined($ENGINE->{'AUTH'}->{'Claims_Access'}) && $ENGINE->{'AUTH'}->{'Claims_Access'} =~ 'A|I' && defined($ENGINE->{'AUTH'}->{'Claims_Docs'}) && $ENGINE->{'AUTH'}->{'Claims_Docs'} =~ 'A|I') {
            $doc_manager = '<a data-toggle="modal" data-keyboard="false" data-target="#documentManagerModal" title="use ctrl+d to access the document manager quickly">Documents</a> |';
        }
    }

    my $WC_Stat = '';
    if($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /605|600/)
    { $WC_Stat = '<a href="#" onclick="detJump();">NCCI</a> | '; }

    my $html = '';
    if ($ENGINE->{'AUTH'}->{'DEPT_CODE'} ne 'ACC' && $UserType eq 'Internal')
    {
        $html = <<EOF;
<div class="utilitybar">
    $doc_manager $WC_Stat $refs $letters $pol_covs $dec_link $loss_notice
</div>
$decForm
EOF
    }
    elsif($UserType eq 'Agent')
    {
        $html = <<EOF;
<div class="utilitybar agent">
<a href="#" id="addAttachmentButton" onclick="addAttachment(this,$claimid,$sessID);">Add Attachment</a> $loss_notice | $decPages
</div>
$decForm
EOF
    }
    elsif($UserType eq 'LawFirm')
    {
        if ($doc_manager eq '' && $loss_notice eq '')
        {$loss_notice .= ' |';}

        $html = <<EOF;
<div class="utilitybar lawfirm">
$doc_manager $loss_notice $decPages
</div>
$decForm
EOF
    }

    return $html;
}

=item * getClaimDetails()

This function returns the Claims Central html that is used on the left side of each page.

=cut
sub getClaimsCentral
{
    my $ENGINE = shift;

    my $error = $ENGINE->{'error'};

    my $sessID = $ENGINE->{'SESSION'}->{'sessionID'};
    my $action = $ENGINE->{'ACTION'};

    my $load = $ENGINE->{'load'};
    my $claimid = $ENGINE->{'claimGeneral'}->{'CLAIM_ID'};
    my %selected = ($load=>' style="background-color:#f6fafd"');

    my $policyNum = $ENGINE->{'claimGeneral'}->{'DISPLAY_POLICY_NUMBER'};
    my $claimNum = $ENGINE->{'claimGeneral'}->{'IMT_CLAIM_NO'};
    my $lossDate = $ENGINE->{'claimGeneral'}->{'LOSS_DATE_TIME'};
    my $itemID = $ENGINE->{'claimGeneral'}->{'ITEM_ID'};
    my $subroStatus = $ENGINE->{'claimGeneral'}->{'SUBRO_STATUS'} || '';
    my $salvStatus = $ENGINE->{'claimGeneral'}->{'SALV_STATUS'} || '';
    my $reinStatus = $ENGINE->{'claimGeneral'}->{'REINSURANCE_IND'} || '';
#107156 sjs 5/10/2012   add lineCode and wcEMP to display wc employee in white small box in upper left hand corner of claim.
    my $lineCode = $ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} || '';

# 109542 sjs add if statement to avoid multiple executes for lines not 600 or 605.
    my $wcEMPResults = [];
    if ($lineCode eq '600' || $lineCode eq '605')
    {
    my $wcEMP = $ENGINE->{'DBH'}->prepare(
    "    SELECT P.CLAIM_ID
          ,G.POLICY_NUMBER ,P.PARTY_ID
           ,P.FIRST_NAME ,P.LAST_NAME
      FROM CLAIMDB.CLM_GENERAL G
 INNER JOIN CLAIMDB.CLM_PARTIES P
           ON
           G.CLAIM_ID = P.CLAIM_ID
 INNER JOIN CLAIMDB.CLM_PARTY_ROLES PR
           ON
           PR.PARTY_ID = P.PARTY_ID
           AND
           PR.CLAIM_ID = G.CLAIM_ID
 WHERE    G.CLAIM_ID = ?
          AND G.CLAIM_STATUS <> 'P'
          AND G.DATE_DELETED = '9999-01-01-01.00.00.000000'
          AND P.DATE_DELETED = '9999-01-01-01.00.00.000000'
          AND PR.ROLE = 'EM'
          AND PR.DATE_DELETED = '9999-01-01-01.00.00.000000'")|| $error->($ENGINE);
         $wcEMP->execute($claimid) || error($ENGINE);
         $wcEMPResults = $wcEMP->fetchall_arrayref({});
        }


    my $claimStatus = getClaimStatus($ENGINE);

#    if($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /301|302|331|332/ && $policyNum =~ /^12|^14|^26|^40|^48/)
#      { $policyNum =~ s/^12|^14|^26|^40|^48//g; }

    $lossDate = $ENGINE->{'SESSION'}->{'claimid'.$claimid}->{'lossdate'} ||
     substr($lossDate,5,2).'/'.substr($lossDate,8,2).'/'.substr($lossDate,0,4);
    if($lossDate eq '01/01/9999')
      { $lossDate = 'Unknown'; }

    my $policyLink = '';
#    if(!defined($ENGINE->{'SESSION'}->{'claimid'.$claimid}->{'policylink'}))
#    {
    if (defined($itemID))
    {
       my $STH = $ENGINE->{'DBH'}->prepare(
          'SELECT STATUS
             FROM CLIENTDB.VERSION
            WHERE ITEM_ID = ?
              AND STATUS <> \'Z\'') || error($ENGINE,'General query prepare failed: '.$ENGINE->{'DBH'}->errstr);

       $STH->execute($itemID) || error($ENGINE,'General query execute failed: '.$ENGINE->{'DBH'}->errstr);

       my $result = $STH->fetchall_arrayref({});

       if (@$result > 0)
       {
          $ENGINE->{'SESSION'}->{'claimid'.$claimid}->{'policylink'} = '<a target="_blank" href="../Client/'.($ENGINE->{'AUTH'}->{'ClientEngine'} || 'ClientEngine.pl').'?load=ClientPView&amp;id='.$itemID.'">'.$policyNum.'</a>';
          if (check_for_platform_version({dbh => $ENGINE->{'DBH'}, item_id => $itemID})) {
            $ENGINE->{'SESSION'}->{'claimid'.$claimid}->{'policylink'} = '<a target="_blank" href="'.get_policy_info_page_url({policy_number => $policyNum}).'">'.$policyNum.'</a>';
          }
       }
       else
       {
          $ENGINE->{'SESSION'}->{'claimid'.$claimid}->{'policylink'} = $policyNum;
       }
    }
    else
    {
       $ENGINE->{'SESSION'}->{'claimid'.$claimid}->{'policylink'} = $policyNum;
    }
#    }
    $policyLink = $ENGINE->{'SESSION'}->{'claimid'.$claimid}->{'policylink'};

    my $helpSelected = '';
    my $helpDisplay = 'style="display:none"';
    my $errSelected = ' style="background-color:#f6fafd;"';
    my $errDisplay = '';
    my $errHTML = '<ul id="errorList" style="list-style-type: circle;">';
    my $documentSelected = 'background-color:#f6fafd;';
    my $documentDisplay = '';
    if(scalar(keys %{$ENGINE->{'errors'}}) > 0)
    {
        $helpSelected = '';
        $documentDisplay = 'display:none';
        $documentSelected = '';
        for my $key (keys %{$ENGINE->{'errors'}})
        {                                  # href="javascript:///"
            $errHTML .= '<li style="list-style-type: circle;" onclick="errFocus(\''.$key.'\')">'.join('</li><li style="list-style-type: circle;" onclick="errFocus(\''.$key.'\')">',@{$ENGINE->{'errors'}->{$key}}).'</li>';
        }
    }

    my $closeArray = $ENGINE->{'close_errors'};
    if( defined $ENGINE->{'close_errors'}
            && scalar(@{$ENGINE->{'close_errors'}}) > 0)
    {
        $helpSelected = '';
        $documentDisplay = 'display:none';
        $documentSelected = '';
        for my $c (@$closeArray)
        {
            $errHTML .= '<li style="list-style-type: circle;" onclick="errFocusScreen(\''.$c->{'screen'}.'\')">'.join('</li><li onclick="errFocusScreen(\''.$c->{'screen'}.'\')">',$c->{'msg'}).'</li>';
        }
    }

    #fields for submit errors
    my $submitArray = $ENGINE->{'submit_errors'};
    my $storeScreen = '';
    my $screenWords = '';

    if( defined $ENGINE->{'submit_errors'}
            && scalar(@{$ENGINE->{'submit_errors'}}) > 0)
    {
        $helpSelected = '';
        $documentDisplay = 'display:none';
        $documentSelected = '';
        for my $e (@$submitArray)
        {                                  # href="javascript:///"
            #see if the screen has changed
                if ($e->{'screen'} ne $storeScreen)
                {
                     $storeScreen = $e->{'screen'};
                     $screenWords = $e->{'screen'};
                     if ($e->{'screen'} eq 'Claims_Details'
                             || $e->{'screen'} eq 'Claims_PropLiab')
                     {
                         $screenWords = 'Details Errors:';
                     }
                     $errHTML .= '<div style="color:black; text-align:center; text-decoration:underline;"> ' . $screenWords . '</div>';
#                     $errHTML .= '<div style="color:black"><b> ' . $e->{'screen'} . '</div>';

                }

            $errHTML .= '<li style="list-style-type: circle;" onclick="errFocus(\''.$e->{'field'}.'\')">'.join('</li><li onclick="errFocus(\''.$e->{'field'}.'\')">',$e->{'msg'}).'</li>';
        }
    }

    #if both the screen errors and submit errs are empty
    #then set the display variables appropriately.  Submit errs
    #do not always exist, so special checking is done for their
    #existence
    if( defined $ENGINE->{'close_errors'}
            && scalar(@{$ENGINE->{'close_errors'}}) > 0)
    {

    }
    elsif(scalar(keys %{$ENGINE->{'errors'}}) == 0)
    {
            if (defined $ENGINE->{'submit_errors'}
                    && scalar(@{$ENGINE->{'submit_errors'}}) == 0)
            {
                    $errSelected = '';
                    $errDisplay = 'display:none';
            }
            elsif (!defined $ENGINE->{'submit_errors'})
            {
                    $errSelected = '';
                    $errDisplay = 'display:none';
            }
    }

#    else
#      { $errSelected = ''; $errDisplay = 'display:none'; }
    $errHTML .= '</ul>';

    my $docs = '';#'<a href="indlink.pl?claim='.$claimNum.'">Go to Indicium</a>';


    ## Show the Documents tab in Claims Central within the claim if the 3rd character in the claim number is the letter "R".
    ## Also show it if the claim was entered via the Internet (claimdb.clm_general.initiation_point <> "CV").
    ## Otherwise, don't show that tab in Claims Central.
    ##   As per email from Kathy dated 8/21/2010
    ## Also hide if the user does not have claims admin access - #104526 DRS
    my $claimClosedDate = $ENGINE->{'claimGeneral'}->{'CLAIM_CLOSED_DATE'};
    my $hideDocumentMenu = '';
#    if($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} ne 'Internal')
#    if($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} ne 'Internal' ||
#       ($ENGINE->{'claimGeneral'}->{'INITIATION_POINT'} eq 'CV' && substr($claimNum,2,1) ne 'R') ||
#       !defined($ENGINE->{'AUTH'}->{'Claims_Access'}) ||
#       $ENGINE->{'AUTH'}->{'Claims_Access'} ne 'A')
#    {
#        $hideDocumentMenu = 'display:none;';
#        $documentDisplay = 'display:none';
#        $helpSelected = 'background-color:#f6fafd;';
#        $helpDisplay = '';
#    }
#    else
#    {
#        if(defined($ENGINE->{'claimGeneral'}->{'FGID'}))
#        {
#            my %files = %{getFileGroup($ENGINE,{'FGID'=>$ENGINE->{'claimGeneral'}->{'FGID'}})};
#            $docs = returnFileTree($ENGINE,\%files,0,'');
#            $docs .= '<div style="text-align:center;padding-top:1em"><a href="/imtonline/IMTFiles.pl?group='.$ENGINE->{'claimGeneral'}->{'FGID'}.'">Download files as archive</a></div>'
#        }
##        if(defined($ENGINE->{'AUTH'}->{'Claims_Access'}) && $ENGINE->{'AUTH'}->{'Claims_Access'} eq 'A')
##        {
#            $docs .= '<div style="text-align:center;padding-top:1em;margin-bottom:0.2em"><a href="#" onclick="load(\'Claims_FileManager\');return false;">Manage files</a></div>';
##        }
#    }
#    print $ENGINE->{'AUTH'}->{'Claims_Docs'};
    if($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} =~ /Internal|LawFirm/ &&

# I commentted out these 2 line so we could load the remaining Keyfile documents to the open converted claims.
#       (($ENGINE->{'claimGeneral'}->{'INITIATION_POINT'} eq 'CV' && substr($claimNum,2,1) eq 'R')
#       || $ENGINE->{'claimGeneral'}->{'INITIATION_POINT'} ne 'CV') &&

       ((defined $ENGINE->{'AUTH'}->{'Claims_Access'} &&
       $ENGINE->{'AUTH'}->{'Claims_Access'} eq 'A')

       || (defined $ENGINE->{'AUTH'}->{'Claims_Access'}
       && $ENGINE->{'AUTH'}->{'Claims_Access'} eq 'I'
       && defined $ENGINE->{'AUTH'}->{'Claims_Docs'} && $ENGINE->{'AUTH'}->{'Claims_Docs'} eq 'I')))
    {
        if(defined($ENGINE->{'claimGeneral'}->{'FGID'}))
        {
            if(defined($ENGINE->{'AUTH'}->{'Claims_Access'}) && $ENGINE->{'AUTH'}->{'Claims_Access'} eq 'A')
            {
                $docs .= '<div style="text-align:center;padding-top:1em;margin-bottom:0.2em"><a href="#" onclick="load(\'Claims_FileManager\');return false;">Manage files</a></div><br>';
            }
            my %files = %{getFileGroup($ENGINE,{'FGID'=>$ENGINE->{'claimGeneral'}->{'FGID'}})};
            $docs .= returnFileTree($ENGINE,\%files,0,'');
            $docs .= '<div style="text-align:center;padding-top:1em"><a href="/imtonline/IMTFiles.pl?group='.$ENGINE->{'claimGeneral'}->{'FGID'}.'">Download files as archive</a></div>'
        }
        if(defined($ENGINE->{'AUTH'}->{'Claims_Access'}) && $ENGINE->{'AUTH'}->{'Claims_Access'} eq 'A')
        {
            $docs .= '<div style="text-align:center;padding-top:1em;margin-bottom:0.2em"><a href="#" onclick="load(\'Claims_FileManager\');return false;">Manage files</a></div>';
        }
    }
    else
    {
        $hideDocumentMenu = 'display:none;';
        $documentDisplay = 'display:none';
        $helpSelected = 'background-color:#f6fafd;';
        $helpDisplay = '';
    }

    my $insName = '';
    my $wcEMPName = '';
        if($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'Internal')
    {
        my $insuredQuery = $ENGINE->{'DBH'}->prepare("SELECT FIRST_NAME, LAST_NAME, BUSINESS_NAME FROM CLAIMDB.CLM_PARTIES AS P
    INNER JOIN CLAIMDB.CLM_PARTY_ROLES AS R ON R.PARTY_ID = P.PARTY_ID AND R.ROLE = 'IN' AND R.DATE_DELETED = '9999-01-01-01.00.00.000000'
    WHERE P.CLAIM_ID = ? AND P.PARTY_SEQ = 1 AND P.DATE_DELETED = '9999-01-01-01.00.00.000000'") || $error->($ENGINE);

        $insuredQuery->execute($claimid) || error($ENGINE);
        my $insuredResults = $insuredQuery->fetchall_arrayref({});



        if(@$insuredResults > 0)
        {
            if(defined($insuredResults->[0]->{'BUSINESS_NAME'}) && $insuredResults->[0]->{'BUSINESS_NAME'} =~ /\w/)
            {
                $insuredResults->[0]->{'BUSINESS_NAME'} =~ s/&/&amp;/g;
                $insuredResults->[0]->{'BUSINESS_NAME'} =~ s/</&lt;/g;
                $insName = $insuredResults->[0]->{'BUSINESS_NAME'};
            }
            elsif($insuredResults->[0]->{'FIRST_NAME'} =~ /\w/ && $insuredResults->[0]->{'LAST_NAME'} =~ /\w/)
            {
                $insuredResults->[0]->{'LAST_NAME'} =~ s/&/&amp;/g;
                $insuredResults->[0]->{'LAST_NAME'} =~ s/</&lt;/g;
                $insName = $insuredResults->[0]->{'LAST_NAME'};
            }
            if($lineCode =~ /600|605/)
            {
               if(@$wcEMPResults > 0)
               {
                  $wcEMPResults->[0]->{'FIRST_NAME'} =~ s/&/&amp;/g;
                  $wcEMPResults->[0]->{'FIRST_NAME'} =~ s/</&lt;/g;
                  $wcEMPResults->[0]->{'LAST_NAME'} =~ s/&/&amp;/g;
                  $wcEMPResults->[0]->{'LAST_NAME'} =~ s/</&lt;/g;
                  $wcEMPName = $wcEMPResults->[0]->{'FIRST_NAME'}.' '.$wcEMPResults->[0]->{'LAST_NAME'};
               }
            }


        }
        $insName = '<div style="font-size:0.8em;text-align:center">'.$insName.'<br />'.$wcEMPName.'</div>';
    }
    my $hideReferencesMenu = '';
    if($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} ne 'Internal')
    {
        $hideReferencesMenu = 'display:none;';
    }
#div style="padding:0.1em;font-size:0.8em">
#<div style="float:left;width:6em">Policy:</div>$policyNum<br />
#<div style="float:left;width:6em">Claim:</div>$claimNum<br />
#<div style="float:left;width:6em">Loss Date:</div>$lossDate<br />
#<div style="float:left;width:6em">Status:</div>$claimStatus<br />
#/div>                     style="visibility:hidden"              style="z-index:-1"
#<div class="ccTop" id="ccTop1" style="border-right:0;border-bottom:0;" onclick="ccSwap(1)">Notes</div>
#<div class="ccTop" id="ccTop3" style="border-right:0;" onclick="ccSwap(3)">Messages</div>
#<div class="ccTop" id="ccTop4" style="border-right:0;" onclick="ccSwap(4)">Tasks</div>
    my $cc = <<EOF;
<div class="claimscentral hideprint" >
<div style="background-color:white;margin-top:0.2em;border:1px solid #6699CC;width:100%;">
$insName
<table class="claiminfo"><tr><th>Policy:</th><td>$policyLink</td></tr>
<tr><th>Claim:</th><td>$claimNum</td></tr>
<tr><th>Loss Date:</th><td>$lossDate</td></tr>
<tr><th>Status:</th><td>$claimStatus</td></tr></table>
<div class="legend hideprint" style="padding-bottom:0.2em;font-size:0.8em">Claims Central</div>
<div class="hideprint" style="padding:0.2em;font-size:0.8em">
<div class="ccTop" id="ccTop0" style="border-right:0;border-bottom:0;$documentSelected$hideDocumentMenu" onclick="ccSwap(0)">Documents</div>
<div class="ccTop" id="ccTop2" style="border-bottom:0;$helpSelected" onclick="ccSwap(2)">Help</div><br />
<div class="ccTop" id="ccTop5" $errSelected onclick="ccSwap(5)">Errors</div>
<div class="ccTop" id="ccTop6" style="border-top:0; $hideReferencesMenu"  onclick="ccSwap(6)">References</div><br />
<div style="padding-top:0.2em;clear:both;">
<div style="overflow:auto;$documentDisplay" id="cc0">
<!--Documents-->
$docs
</div>
<div style="display:none" id="cc1">
<!--Notes-->
</div>
<div $helpDisplay id="cc2">
<!--Help-->
Click on any <span style="border-bottom:1px dashed #4b7ead;">underlined</span> word(s) to view field definition
</div>
<div style="display:none" id="cc3">
<!--Messages-->
</div>
<div style="display:none" id="cc4">
<!--Tasks-->
</div>
<div  style="color:#aa0000;font-weight:bold;$errDisplay" id="cc5">
<!--Errors-->
$errHTML
</div>

<div  style="display:none; overflow:auto; height:400px;" id="cc6">
<!--Link-->
<div><a target="_blank" href="Claim%20Documents%20Manual.pdf">CLAIM DOCUMENTS MANUAL</a></div>
<div><a target="_blank" href="Claims%20Guidelines%20Procedures.pdf">CLAIMS GUIDELINES &amp; PROCEDURES</a></div>
<div><a target="_blank" href="Medicare%20Guidelines.pdf">MEDICARE GUIDELINES</a></div>
<div><a target="_blank" href="Quality%20Audit%20Guidelines.pdf">Quality Audit GUIDELINES</a></div>
<div><a target="_blank" href="http://www.oakwood.com/cms/insurance.html">ALE � Oakwood Housing Solutions</a></div>
<div><a target="_blank" href="http://www.temporaryaccommodations.net/index.htm">ALE � Temp Accommodations</a></div>
<div><a target="_blank" href="https://www.arbfile.org/webapp/pgStatic/content/index.jsp">ARBITRATION FORUMS</a></div>
<div><a target="_blank" href="http://www.ianetwork.net/login/start-claim.html">AUTO APPRAISAL ASSIGNMENT</a></div>
<div><a target="_blank" href="http://www.metrosalvagepool.com/">AUTO SALVAGE Metro Salvage pool</a></div>
<div><a target="_blank" href="Crash%20Report%20Overlay%20-%20Illinois.pdf">Crash Report Overlay - Illinois</a></div>
<div><a target="_blank" href="Crash%20Report%20Overlay%20-%20Iowa.pdf">Crash Report Overlay - Iowa</a></div>
<div><a target="_blank" href="Crash%20Report%20Overlay%20-%20Nebraska.pdf">Crash Report Overlay - Nebraska</a></div>
<div><a target="_blank" href="Crash%20Report%20Overlay%20-%20South%20Dakota.pdf">Crash Report Overlay - South Dakota</a></div>
<div><a target="_blank" href="Claim%20File%20Folders%20-%20Auto.pdf">CLAIM FILE FOLDERS - AUTO</a></div>
<div><a target="_blank" href="Claim%20File%20Folders%20-%20Homeowners.pdf">CLAIM FILE FOLDERS - HOMEOWNERS</a></div>
<div><a target="_blank" href="http://www.claimspages.com/">CLAIMS PAGES</a></div>
<div><a target="_blank" href="http://www.capitalplanninginc.com/index.htm">CAPITAL PLANNING</a></div>
<div><a target="_blank" href="https://www.cob.cms.hhs.gov/MSPRP">CMS Portal</a></div>
<div><a target="_blank" href="CMS%20Reporting%20Examples.pdf">CMS Reporting Examples</a></div>
<div><a target="_blank" href="https://insource.nils.com/INsource/login.asp?Y=2">Compliance - OneSumX � NILS</a></div>
<div><a target="_blank" href="http://www.claimitred.com/">CONTENTS - Claim-it-Red</a></div>
<div><a target="_blank" href="CodeBlue Contents Service Request.doc">CONTENTS - CodeBlue</a></div>
<div><a target="_blank" href="https://submitnewclaim.claimtracker.com/">CONTENTS - National Vendor</a></div>
<div><a target="_blank" href="http://www.judici.com/courts/court_list.jsp  ">COURTS - Illinois</a></div>
<div><a target="_blank" href="http://www.iowacourts.state.ia.us/ESAWebApp/DefaultFrame">COURTS - Iowa</a></div>
<div><a target="_blank" href="http://www.mncourts.gov/default.aspx?page=1927">COURTS - Minnesota</a></div>
<div><a target="_blank" href="http://wcca.wicourts.gov/simpleCaseSearch.xsl;jsessionid=AF47316B73029A63C41F3AA0EE6F3197.render3?">COURTS - Wisconsin</a></div>
<div><a target="_blank" href="Deductible%20Recovery%20Request.pdf">Deductible Recovery Request</a></div>
<div><a target="_blank" href="https://www.eagleview.com">Eagleview</a></div>
<div><a target="_blank" href="https://www.armsweb.com/armslogon/logon?t=53140&rm= ">ENTERPRISE ARMS</a></div>
<div><a target="_blank" href="http://www.haagengineering.com/">HAAG ENGINEERING</a></div>
<div><a target="_blank" href="http://www.rent-cars.com">HERTZ CARS</a></div>
<div><a target="_blank" href="http://www.ICD9data.com">ICD9 DIAGNOSTIC CODE SEARCH</a></div>
<div><a target="_blank" href="http://www.state.il.us/agency/iic/">ILLINOIS IC</a></div>
<div><a target="_blank" href="http://www.iowaworkforce.org/wc/">IOWA IC</a></div>
<div><a target="_blank" href="http://accidentreports.iowa.gov/">ISP CRASH REPORTS</a></div>
<div><a target="_blank" href="https://claimsearch.iso.com/index.asp  ">ISO CLAIMSEARCH</a></div>
<div><a target="_blank" href="Large%20Loss%20Property%20Reference%20Guide.pdf">Large Loss Property Reference Guide</a></div>
<div><a target="_blank" href="http://www.romingerlegal.com/state/iowa.html ">LEGAL RESEARCH</a></div>
<div><a target="_blank" href="https://www.mymitchell.com/Login.aspx?ReturnUrl=%2fWorkBench.aspx">MITCHELL WCTL</a></div>
<div><a target="_blank" href="http://www.naiia.com/ ">NATL ASSOC OF IND ADJUSTERS</a></div>
<div><a target="_blank" href="http://www.newcc.gov/">NE WC COURT</a></div>
<div><a target="_blank" href="http://www.plrb.org/">PLRB</a></div>
<div><a target="_blank" href="http://dol.sd.gov/workerscomp/default.aspx">SD DEPT OF LABOR</a></div>
<div><a target="_blank" href="SDIP%20Codes.pdf">SDIP CODES</a></div>
<div><a target="_blank" href="http://www.theodora.com/sic_index.html">SIC INDEX</a></div>
<div><a href="Subrogation%20Process%20and%20Contacts.pdf">Subrogation Process and Contacts</a></div>
<div><a target="_blank" href="Navigation_Tips%20Client%20View.pdf">Trover Client View Guide</a></div>
<div><a target="_blank" href="TransPaC PowerPoint Presentation.pptx">Trover PowerPoint</a></div>
<div><a target="_blank" href="http://www.transwheel.com/">TRANSWHEEL</a></div>
<div><a target="_blank" href="Underwriting%20Memo%20Checklist.pdf ">Underwriting Memo Checklist</a></div>
<div><a target="_blank" href="http://www.wunderground.com/history/">WEATHER HISTORY</a></div>
<div><a target="_blank" href="http://dwd.wisconsin.gov/dwd/publications/wc/WKC_1_P_09/3_Chapter_102.pdf">WI LAW BOOK</a></div>
<div><a target="_blank" href="http://www.wisconsin.gov/wc/">WISCONSIN DWD</a></div>
<div><a target="_blank" href="Wording%20of%20Drafts.pdf">Wording of Drafts</a></div>

</div>

</div>

</div>
</div>
</div>
EOF

    return $cc;
}

sub getClaimStatus
{
    my $ENGINE = shift;

    my $claimStatus = $ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'};
    my $subroStatus = $ENGINE->{'claimGeneral'}->{'SUBRO_STATUS'} || '';
    my $salvStatus = $ENGINE->{'claimGeneral'}->{'SALV_STATUS'} || '';
    my $reinStatus = $ENGINE->{'claimGeneral'}->{'REINSURANCE_IND'} || '';
    my $recoverableStatus = $ENGINE->{'claimGeneral'}->{'CLOSE_RECOVERABLE'} || '';

    if($claimStatus eq 'A')
    {
        $claimStatus = 'OPEN';
        if($recoverableStatus eq 'P')
        {$claimStatus = 'Pending Recoverable Depreciation'}
    }
    elsif($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'Internal' &&
       ($subroStatus eq 'P' || $salvStatus eq 'P' || $reinStatus eq 'P'))
    {
        $claimStatus = 'Closed Pending ';
        if($subroStatus eq 'P')
          { $claimStatus .= 'Subrogation, '; }
        if($salvStatus eq 'P')
          { $claimStatus .= 'Salvage, '; }
        if($reinStatus eq 'P')
          { $claimStatus .= 'Reinsurance, '; }
        chop($claimStatus);
        chop($claimStatus);
    }
    elsif($subroStatus eq 'P')
      { $claimStatus = 'Closed Pending Subrogation'; }
    elsif($claimStatus eq 'P')
      { $claimStatus = 'Unsubmitted'; }
    elsif($claimStatus eq 'M')
      { $claimStatus = 'Submitted'; }
    elsif($claimStatus eq 'CWP')
      { $claimStatus = 'CLOSED<br />W/O<br />PAYMENT'; }
    elsif($claimStatus =~ /^C/)
      { $claimStatus = 'CLOSED'; }
    elsif($claimStatus eq 'TR')
      { $claimStatus = 'Transmitted'; }
        elsif($claimStatus eq 'ER')
      { $claimStatus = 'Glass Error'; }
        elsif($claimStatus eq 'NA')
      { $claimStatus = 'Glass Ready'; }

    if($ENGINE->{'claimGeneral'}->{'PURGED_IND'} eq 'Y'
            || $ENGINE->{'claimGeneral'}->{'PURGED_IND'} eq 'R')
          { $claimStatus =~ s/CLOSED/ARCHIVED/; }

    return uc($claimStatus);
}

=item * getClaimHeading()

This function returns the html used to build the information displayed at the top of each claim page.
It displays like:


    Insured:    JIM SMITH                   Agency:     IMT INSURANCE
                JANE SMITH                  969001      7825 MILLS CIVIC PARKWAY
    Adjuster:   JIM TODD                                WEST DES MOINES, IA 50266
                <EMAIL>                Ph # (*************
    Branch:     Des Moines                              Fax # (800) 289-4392

=cut
sub getClaimHeading
{
    my $ENGINE = shift;
    my $claimid = $ENGINE->{'claimGeneral'}->{'CLAIM_ID'};

    my $heading;
#    if($ENGINE->{'claimGeneral'}->{'MANUAL_OR_WHAT'} eq 'M')
#    {
    if($ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} !~ /P|M/)
    {
        my $load = $ENGINE->{'load'};
#        if($load =~ /Claims_Details/)
        if($load =~ /Claims_Monetary/)
        {
            undef ($ENGINE->{'SESSION'}->{'claimid'.$claimid}->{'claimheading'});
            undef ($ENGINE->{'SESSION'}->{'claimid'.$claimid}->{'firstAdjuster'});
            undef ($ENGINE->{'SESSION'}->{'claimid'.$claimid}->{'firstAdjusterEmail'});
            undef ($ENGINE->{'SESSION'}->{'claimid'.$claimid}->{'firstAdjusterPhone'});
            undef ($ENGINE->{'SESSION'}->{'claimid'.$claimid}->{'firstAdjusterFax'});
        }
#        elsif($load =~ /Claims_Details/)
#        { undef ($ENGINE->{'SESSION'}->{'claimid'.$claimid}->{'claimheading'}); }
    }
    if($ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} !~ /P/)
    {
        my $save = $ENGINE->{'save'};
#        if($save =~ /Claims_Details/)
        if(defined($save) && $save =~ /Claims_Vehicle|Claims_PropLiab|Claims_WorkComp/)
        { undef ($ENGINE->{'SESSION'}->{'claimid'.$claimid}->{'claimheading'}); }
    }
#    }
    if(!defined($ENGINE->{'SESSION'}->{'claimid'.$claimid}->{'claimheading'}))
    {
        my $agencyNum = '';
        if(!defined($ENGINE->{'SESSION'}->{'claimid'.$claimid}->{'firstAdjuster'}))
        {
          #lwm.sql - 2595
            my $adjusterQuery = $ENGINE->{'DBH'}->prepare('
            SELECT
            DATE_ASSIGNED, C.USER_KEY, c.date_completed
            FROM CLAIMDB.CLM_REP_ASSIGNED AS C
            WHERE CLAIM_ID = ?  AND
            C.DATE_REMOVED = \'9999-01-01 01:00:00.000000\' and
            c.date_completed = \'9999-01-01\'
            ORDER BY CLM_REP_ASGN_ID FETCH FIRST 1 ROWS ONLY') || error($ENGINE,'Adjuster query prepare failed: '.$ENGINE->{'DBH'}->errstr);
            $adjusterQuery->execute($claimid) || error($ENGINE,'Adjuster query execute failed: '.$ENGINE->{'DBH'}->errstr);
            my $adjusterResults = $adjusterQuery->fetchall_arrayref({});
            my $adjuster = '';
            my $firstAdjusterName = 'Unknown';
            my $firstAdjusterEmail = '';
            my $repAssignedDate = '';
            if(scalar(@$adjusterResults) > 0)
            {
                my $firstAdjuster = shift(@$adjusterResults);
                my $firstUserKey = $firstAdjuster->{'USER_KEY'};

                my $user_key_data = fetch_user_key_data({
                  authorization => $ENGINE->{'AUTH'}->{'platform_access_token'},
                  user_key => $firstUserKey,
                  max_attempts => 2
                });

                my $hold_first_name = uc($user_key_data->{content}->{data}->[0]->{attributes}->{first_name});
                my $hold_last_Name = uc($user_key_data->{content}->{data}->[0]->{attributes}->{last_name});
                my $hold_dept_code = convert_dept_code({department => $user_key_data->{content}->{data}->[0]->{attributes}->{department}});
                my $hold_email = $user_key_data->{content}->{data}->[0]->{attributes}->{email};
                my $hold_bus_ext = $user_key_data->{content}->{data}->[0]->{attributes}->{other}->{business_phones}->[0];
                my $hold_fax_number = $user_key_data->{content}->{data}->[0]->{attributes}->{other}->{fax_number};
                my $hold_is_active = $user_key_data->{content}->{data}->[0]->{attributes}->{is_active};

                if(defined($hold_first_name) && $hold_first_name eq '' && defined($hold_last_Name) && $hold_last_Name gt '')
                  { $firstAdjusterName = $hold_last_Name; }
                else
                  { $firstAdjusterName = $hold_first_name.' '.$hold_last_Name; }

                $firstAdjusterName =~ s/&/&amp;/g;
                $firstAdjusterName =~ s/</&lt;/g;

                $ENGINE->{'SESSION'}->{'claimid'.$claimid}->{'firstAdjuster'} = $firstAdjusterName;

                if(defined($hold_is_active) && defined($hold_dept_code) && $hold_dept_code eq 'CLH')
                {
                    if($hold_email)
                    {
                        $firstAdjusterEmail = $hold_email;
                        $firstAdjusterEmail =~ s/&/&amp;/g;
                        $firstAdjusterEmail =~ s/</&lt;/g;
                    }
                    $ENGINE->{'SESSION'}->{'claimid'.$claimid}->{'firstAdjusterEmail'} = $firstAdjusterEmail;
                    $ENGINE->{'SESSION'}->{'claimid'.$claimid}->{'firstAdjusterPhone'} = 'Ph # (************* x'.$hold_bus_ext;
                    if ($hold_fax_number) {
                        $ENGINE->{'SESSION'}->{'claimid'.$claimid}->{'firstAdjusterFax'} = 'Fax # ('.substr($hold_fax_number,0,3).') '.substr($hold_fax_number,3,3).'-'.substr($hold_fax_number,6,4);
                    }

                }
            }
        }

        my $firstAdjusterName = $ENGINE->{'SESSION'}->{'claimid'.$claimid}->{'firstAdjuster'} || '';
        $firstAdjusterName =~ s/&/&amp;/g;
        $firstAdjusterName =~ s/</&lt;/g;
        my $firstAdjusterEmail = $ENGINE->{'SESSION'}->{'claimid'.$claimid}->{'firstAdjusterEmail'} || '';
        $firstAdjusterEmail =~ s/&/&amp;/g;
        $firstAdjusterEmail =~ s/</&lt;/g;
        my $firstAdjusterPhone = $ENGINE->{'SESSION'}->{'claimid'.$claimid}->{'firstAdjusterPhone'} || '';
        $firstAdjusterPhone =~ s/&/&amp;/g;
        $firstAdjusterPhone =~ s/</&lt;/g;
        my $firstAdjusterFax = $ENGINE->{'SESSION'}->{'claimid'.$claimid}->{'firstAdjusterFax'} || '';
        $firstAdjusterFax =~ s/&/&amp;/g;
        $firstAdjusterFax =~ s/</&lt;/g;

        my $insdQuery = $ENGINE->{'DBH'}->prepare('SELECT P.FIRST_NAME, P.LAST_NAME, P.BUSINESS_NAME FROM CLAIMDB.CLM_PARTIES AS P
INNER JOIN CLAIMDB.CLM_PARTY_ROLES AS R ON P.PARTY_ID = R.PARTY_ID AND R.DATE_DELETED = \'9999-01-01 01:00:00.000000\'
WHERE P.CLAIM_ID = ? AND R.ROLE = \'IN\' AND P.DATE_DELETED = \'9999-01-01 01:00:00.000000\' ORDER BY P.PARTY_ID') || error($ENGINE,'insured query prepare failed: '.$ENGINE->{'DBH'}->errstr);
        $insdQuery->execute($claimid) || error($ENGINE,'SDIP query execute failed: '.$ENGINE->{'DBH'}->errstr);
        my $insdResults = $insdQuery->fetchall_arrayref({});
        my @insdNames;
        for my $r (@$insdResults)
        {
            for my $key ('FIRST_NAME','LAST_NAME','BUSINESS_NAME')
            {
               $r->{$key} =~ s/&/&amp;/g;
               $r->{$key} =~ s/</&lt;/g;
            }
            if($r->{'FIRST_NAME'} =~ /\w/ || $r->{'LAST_NAME'} =~ /\w/)
              { push(@insdNames,$r->{'FIRST_NAME'}.'&nbsp;'.$r->{'LAST_NAME'}); }
            elsif($r->{'BUSINESS_NAME'} =~ /\w/)
              { push(@insdNames,$r->{'BUSINESS_NAME'}); }
        }

        my @agencyInfo;
        if($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'Internal')
        {
            $agencyNum = $ENGINE->{'claimGeneral'}->{'AGENCY_NO'};
#            my $STH = $ENGINE->{'DBH'}->prepare('SELECT NAME,ADDRESS1,ADDRESS2,CITY,STATE,ZIP,PHONE,FAX FROM IMTINSDB.AGENTS WHERE NUM = ?') ||
#                  error($ENGINE,'General query prepare failed: '.$ENGINE->{'DBH'}->errstr);
#            $STH->execute($ENGINE->{'claimGeneral'}->{'AGENCY_NO'}) || error($ENGINE,'General query execute failed: '.$ENGINE->{'DBH'}->errstr);
#            my $result = $STH->fetchall_arrayref({});
            my $agencyno = "$ENGINE->{'claimGeneral'}->{'AGENCY_NO'}";
            #1207 - agency data, does not need updated - 2660
            my $agencyPhoneQuery = $ENGINE->{'DBH'}->prepare('SELECT C.CONTACT_INFO,C.TYPE FROM GENSUPDB.USER_INFO G JOIN CLIENTDB.CLIENT CL ON CL.USER_KEY = G.USER_KEY
JOIN CLIENTDB.CLIENT_HAS_CONTACT HC ON HC.CLIENT_ID = CL.CLIENT_ID JOIN CLIENTDB.CONTACT C ON C.CONTACT_ID = HC.CONTACT_ID WHERE G.USER_ID = ? AND C.TYPE IN (\'PW\',\'FX\')') ||
                  error($ENGINE,'User Info query prepare failed: '.$ENGINE->{'DBH'}->errstr);
            $agencyPhoneQuery->execute($agencyno) || error($ENGINE,'User Info query execute failed: '.$ENGINE->{'DBH'}->errstr);
            my $agencyPhoneResults = $agencyPhoneQuery->fetchall_arrayref({});

            #1207 - agency data, does not need updated
             my $agencyAddressQuery = $ENGINE->{'DBH'}->prepare('SELECT G.USER_ID, Z.NAME, A.ADD_LINE1, A.ADD_LINE2, A.ADD_CITY, A.ADD_STATE, A.ADD_ZIP, A.ADD_TYPE
FROM GENSUPDB.USER_INFO G JOIN AGENTDB.AGENCY AS Z ON G.USER_ID = Z.AGENCYNO JOIN CLIENTDB.CLIENT C ON C.USER_KEY = G.USER_KEY JOIN CLIENTDB.CLIENT_HAS_ADDRESS HA ON HA.CLIENT_ID = C.CLIENT_ID
JOIN CLIENTDB.ADDRESS A ON A.ADDRESS_ID = HA.ADDRESS_ID WHERE A.ADD_TYPE IN (\'P\',\'E\') AND Z.AGENCYNO = ?') ||
                  error($ENGINE,'User Info query prepare failed: '.$ENGINE->{'DBH'}->errstr);
            $agencyAddressQuery->execute($agencyno) || error($ENGINE,'User Info query execute failed: '.$ENGINE->{'DBH'}->errstr);
            my $agencyAddressResults = $agencyAddressQuery->fetchall_arrayref({});

            if(@$agencyAddressResults > 0)
            {
                my $agencyPhoneNo = '';
                my $agencyFaxNo = '';
                my $x = 0;
                # added $x to only get the first 2 phone and fax numbers if there are more than one set.
                for my $ap (@$agencyPhoneResults)
                {
                    $x++;
                    if($ap->{'TYPE'} eq 'PW')
                    { $agencyPhoneNo = $ap->{'CONTACT_INFO'}; }
                    elsif($ap->{'TYPE'} eq 'FX')
                    { $agencyFaxNo = $ap->{'CONTACT_INFO'}; }
                    if($x == 2)
                    { last; }
                }
                for my $key ('NAME','ADD_LINE1','ADD_LINE2','ADD_CITY','ADD_STATE','ADD_ZIP')
                {
                   $agencyAddressResults->[0]->{$key} =~ s/&/&amp;/g;
                   $agencyAddressResults->[0]->{$key} =~ s/</&lt;/g;
                }
                push(@agencyInfo,$agencyAddressResults->[0]->{'NAME'});
                if(defined($agencyAddressResults->[0]->{'ADD_LINE1'}) && $agencyAddressResults->[0]->{'ADD_LINE1'} =~ /\w/)
                  { push(@agencyInfo,$agencyAddressResults->[0]->{'ADD_LINE1'}); }
                if(defined($agencyAddressResults->[0]->{'ADD_LINE2'}) && $agencyAddressResults->[0]->{'ADD_LINE2'} =~ /\w/)
                  { push(@agencyInfo,$agencyAddressResults->[0]->{'ADD_LINE2'}); }
                if(defined($agencyAddressResults->[0]->{'ADD_CITY'}) && $agencyAddressResults->[0]->{'ADD_CITY'} =~ /\w/ &&
                   defined($agencyAddressResults->[0]->{'ADD_STATE'}) && $agencyAddressResults->[0]->{'ADD_STATE'} =~ /\w/ &&
                   defined($agencyAddressResults->[0]->{'ADD_ZIP'}) && $agencyAddressResults->[0]->{'ADD_ZIP'} =~ /\w/)
                { push(@agencyInfo,$agencyAddressResults->[0]->{'ADD_CITY'}.', '.$agencyAddressResults->[0]->{'ADD_STATE'}.' '.$agencyAddressResults->[0]->{'ADD_ZIP'}); }
                if(defined($agencyPhoneNo) && $agencyPhoneNo =~ /\w/ && length($agencyPhoneNo) >= 10)
#                  { push(@agencyInfo,'Ph # ('.substr($result->[0]->{'PHONE'},0,3).') '.substr($result->[0]->{'PHONE'},3,3).'-'.substr($result->[0]->{'PHONE'},6,4)); }
                  { push(@agencyInfo,'Ph # '.$agencyPhoneNo); }
                if(defined($agencyFaxNo) && $agencyFaxNo =~ /\w/ && length($agencyFaxNo) >= 10)
#                  { push(@agencyInfo,'Fax # ('.substr($result->[0]->{'FAX'},0,3).') '.substr($result->[0]->{'FAX'},3,3).'-'.substr($result->[0]->{'FAX'},6,4)); }
                  { push(@agencyInfo,'Fax # '.$agencyFaxNo); }
    #            if($result->[0]->{'EMAIL'} =~ /\w/)
    #              { push(@agencyInfo,$result->[0]->{'EMAIL'}); }
            }
        }
        my %branches = (21=>'Glass Claim',
10=>'Home Office',
20=>'Des Moines',
30=>'Cedar Rapids',
40=>'Mason City',
50=>'Sioux Falls',
60=>'Sioux City',
70=>'Omaha',
22=>'Automated Proclaim Glass Claim',
12=>'Bond Claim',
75=>'Quad Cities',
00=>'Unknown');
        my $branchInfo = $branches{$ENGINE->{'claimGeneral'}->{'BRANCH'}} || 'Unknown';
        if($ENGINE->{'claimGeneral'}->{'BRANCH'} eq '00' && substr($ENGINE->{'claimGeneral'}->{'IMT_CLAIM_NO'},2,1) eq 'A')
          { $branchInfo = 'Des Moines'; }


        $heading .= '<table style="margin-top:0.5em;"><tr>';
        if(scalar(@insdNames)>0)
        {
            my $name = shift(@insdNames);
            $heading .= '<th>Insured:</th><td>'.$name.'</td>';
        }
        elsif($firstAdjusterName =~ /\w/)
        {
            $heading .= '<th>Adjuster:</th><td>'.$firstAdjusterName.'</td>';
            $firstAdjusterName = '';
        }
        elsif($branchInfo =~ /\w/)
        {
            $heading .= '<tr><th>Branch:</th><td>'.$branchInfo.'</td>';
            $branchInfo = '';
        }
        else
          { $heading .= '<th></th><td></td>'; }

        if(scalar(@agencyInfo)>0)
        {
            my $name = shift(@agencyInfo);
            $heading .= '<th>Agency:</th><td>'.$name.'</td>';
        }
        elsif($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'Internal')
        {
            $heading .= '<th>Agency:</th><td>'.$agencyNum.'</td>';
            $agencyNum = '';
        }
        $heading .= '</tr>';

        my $insdName;
        my $agency;
        while($insdName = shift(@insdNames))
        {
            $agency = shift(@agencyInfo) || '';
            $heading .= '<tr><th></th><td>'.$insdName.'</td><th>'.$agencyNum.'</th><td>'.$agency.'</td></tr>';
            $agencyNum = '';
        }

        if($firstAdjusterName =~ /\w/)
        {
            $agency = shift(@agencyInfo) || '';
            $heading .= '<tr><th>Adjuster:</th><td>'.$firstAdjusterName.'</td><th>'.$agencyNum.'</th><td>'.$agency.'</td></tr>';
            $firstAdjusterName = '';
            $agencyNum = '';
        }
        if($firstAdjusterEmail =~ /\w/)
        {
            $agency = shift(@agencyInfo) || '';
            $heading .= '<tr><th></th><td><a href="mailto:'.$firstAdjusterEmail.'?subject=Regarding Claim Number '.$ENGINE->{'claimGeneral'}->{'IMT_CLAIM_NO'}.'">'.$firstAdjusterEmail.'</a></td><th>'.$agencyNum.'</th><td>'.$agency.'</td></tr>';
            $firstAdjusterName = '';
            $agencyNum = '';
        }
        if($firstAdjusterPhone =~ /\w/)
        {
            $agency = shift(@agencyInfo) || '';
            $heading .= '<tr><th></th><td>'.$firstAdjusterPhone.'</td><th>'.$agencyNum.'</th><td>'.$agency.'</td></tr>';
            $firstAdjusterName = '';
            $agencyNum = '';
        }
        if($firstAdjusterFax =~ /\w/)
        {
            $agency = shift(@agencyInfo) || '';
            $heading .= '<tr><th></th><td>'.$firstAdjusterFax.'</td><th>'.$agencyNum.'</th><td>'.$agency.'</td></tr>';
            $firstAdjusterName = '';
            $agencyNum = '';
        }
        if($branchInfo =~ /\w/ && $branchInfo ne 'Unknown')
        {
            $agency = shift(@agencyInfo) || '';
            $heading .= '<tr><th>Branch:</th><td>'.$branchInfo.'</td><th>'.$agencyNum.'</th><td>'.$agency.'</td></tr>';
            $agencyNum = '';
        }
        elsif($branchInfo =~ /\w/ && $branchInfo eq 'Unknown')
        {
            $agency = shift(@agencyInfo) || '';
            $heading .= '<tr><th></th><td></td><th>'.$agencyNum.'</th><td>'.$agency.'</td></tr>';
            $agencyNum = '';
        }

        while($agency = shift(@agencyInfo))
        {
            $heading .= '<tr><th></th><td></td><th>'.$agencyNum.'</th><td>'.$agency.'</td></tr>';
            $agencyNum = '';
        }
        $heading .= '</table>';


#        $heading = <<EOF;
#<table style="margin-top:0.5em">
#<tr><th>Insured:</th><td>JOE SMITH</td><th>Agency:</th><td>My Insurance Agency</td></tr>
#<tr><th>Adjuster:</th><td>$firstAdjusterName</td><td></td><td>123 Main Street Des Moines, IA 50308</td></tr>
#<tr><th></th><td><a href="mailto:$firstAdjusterEmail">$firstAdjusterEmail</a></td><td></td><td>Ph # 515-327-2767</td></tr>
#<tr><th></th><td>Ph # 515-327-2758</td><td></td><td>Fax # 515-327-2892</td></tr>
#<tr><th></th><td></td><td></td><td><a href="mailto:fake\@email.com">fake\@email.com</a></td></tr>
#</table>
#EOF
        $ENGINE->{'SESSION'}->{'claimid'.$claimid}->{'claimheading'} = $heading;
    }
    else
      { $heading = $ENGINE->{'SESSION'}->{'claimid'.$claimid}->{'claimheading'}; }



#<table  style="float:left;margin-top:0.5em">
#<tr><th>Insured:</th><td>JOE SMITH</td></tr>
#<tr><th>Adjuster:</th><td>$firstAdjusterName</td></tr>
#<tr><th></th><td><a href="mailto:$firstAdjusterEmail">$firstAdjusterEmail</a></td></tr>
#<tr><th></th><td>Ph # 515-327-2758</td></tr>
#</table>
#<table style="min-width:20em;margin-top:0.5em;text-transform:uppercase;">
#<tr><th>Agency:</th><td>My Insurance Agency</td></tr>
#<tr><td></td><td>123 Main Street Des Moines, IA 50308</td></tr>
#<tr><td></td><td>Ph # 515-327-2767</td></tr>
#<tr><td></td><td>Fax # 515-327-2892</td></tr>
#</table>

    return $heading;
}


=item * getCoveredItemDescQuery()

This function returns a prepared query depending on line code for description rows of a covered item.

=cut
sub getCoveredItemDescQuery
{
    my $ENGINE = shift;

    my $itemDescQuery = '';
    my %IMTLineCodeScreen = getIMTLineCodeScreen();
    my $screen = $IMTLineCodeScreen{$ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'}};
    if($screen eq 'VEH')
    {
        if($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} eq '052')
        {
            $itemDescQuery = $ENGINE->{'DBH'}->prepare('SELECT B.BOAT_YEAR, B.HORSE_POWER, B.MAKE, B.MODEL, B.SERIAL_NUMBER, B.BOAT_LENGTH FROM CLAIMDB.CLM_COMMON_STAT AS C
    INNER JOIN CLAIMDB.CLM_BOAT AS B ON C.CLM_COMMON_STAT_ID = B.CLM_COMMON_STAT_ID AND B.DATE_DELETED = \'9999-01-01 01:00:00.000000\'
    WHERE C.CLAIM_ID = ? AND C.LOCATION_NO = ? AND C.UNIT_NO = ? AND C.DATE_DELETED = \'9999-01-01 01:00:00.000000\'') || error($ENGINE,'Boat query prepare failed: '.$ENGINE->{'DBH'}->errstr);
        }
        else
        {
            $itemDescQuery = $ENGINE->{'DBH'}->prepare('SELECT VIN, YEAR, MODEL FROM CLAIMDB.CLM_VEHICLE
    WHERE CLAIM_ID = ? AND UNIT_NO = ? AND DATE_DELETED = \'9999-01-01 01:00:00.000000\'') || error($ENGINE,'Vehicle query prepare failed: '.$ENGINE->{'DBH'}->errstr);
        }
    }
#    elsif($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /300|301|302|330|331|332/)
#    {
#        $itemDescQuery = $ENGINE->{'DBH'}->prepare('SELECT ACRES, SECTION, TOWNSHIP_WORDAGE, RANGE, COUNTY_WORDS, STATE FROM CLAIMDB.CLM_PROPERTY_LEGAL
#    WHERE CLAIM_ID = ? AND LOCATION_NO = ? AND DATE_DELETED = \'9999-01-01 01:00:00.000000\'') || error($ENGINE,'Farm query prepare failed: '.$ENGINE->{'DBH'}->errstr);
#    }
    else
    {
        $itemDescQuery = $ENGINE->{'DBH'}->prepare('SELECT C.LOCATION_NO, L.CLM_COMMON_STAT_ID, L.ADDRESS1, L.ADDRESS2, L.CITY, L.FL_ACRES, L.FL_COUNTY_WORDS, L.FL_RANGE, L.FL_SECTION, L.FL_TOWNSHIP_WORDAGE, L.STATE, L.ZIP1_5, L.ZIP6_12 FROM CLAIMDB.CLM_COMMON_STAT AS C
    INNER JOIN CLAIMDB.CLM_LOCATION AS L ON C.CLM_COMMON_STAT_ID = L.CLM_COMMON_STAT_ID AND L.LOC_TYPE IN (\'AD\',\'AC\',\'PR\') AND L.DATE_DELETED = \'9999-01-01 01:00:00.000000\'
    WHERE C.CLAIM_ID = ? AND C.LOCATION_NO = ? AND C.UNIT_NO = ? AND C.DATE_DELETED = \'9999-01-01 01:00:00.000000\'') || error($ENGINE,'Location query prepare failed: '.$ENGINE->{'DBH'}->errstr);
    }

    return $itemDescQuery;
}

=item * getCoveredItemDesc()

This function returns a formatted description of a covered item when given location and unit numbers with the query prepared by getCoveredItemDescQuery.

=cut
sub getCoveredItemDesc
{
    my $ENGINE = shift;
    my $loc = shift;
    my $unit = shift;
    my $descQuery = shift;
    my $itemDesc = '';

    my %IMTLineCodeScreen = getIMTLineCodeScreen();
    my $screen = $IMTLineCodeScreen{$ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'}};
    if($screen eq 'VEH')
    {
        if($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} eq '052')
        {
            $descQuery->execute($ENGINE->{'claimGeneral'}->{'CLAIM_ID'},$loc,$unit) || error($ENGINE,'Boat query execute failed: '.$ENGINE->{'DBH'}->errstr);
            my $vehResults = $descQuery->fetchall_arrayref({});
            if(scalar(@$vehResults) > 0)
            {
                for my $v (@$vehResults)
                {
                    $itemDesc .= $v->{'BOAT_YEAR'}.' '.($v->{'MAKE'} || '').' '.($v->{'MODEL'} || '').' #'.($v->{'SERIAL_NUMBER'} || '').' '.$v->{'BOAT_LENGTH'}.'ft. '.$v->{'HORSE_POWER'}.'HP<br />';
                }
                $itemDesc =~ s/<br \/>$//;
            }
        }
        else
        {
                my $vehVardataQuery = $ENGINE->{'DBH'}->prepare(
                    "SELECT VARDATA, DATA_TYPE
                       FROM CLAIMDB.CLM_VEH_VARDATA
                      WHERE CLM_COMMON_STAT_ID IN
                            (SELECT CLM_COMMON_STAT_ID
                               FROM CLAIMDB.CLM_VEHICLE
                              WHERE CLAIM_ID = ?
                                AND DATE_DELETED = \'9999-01-01 01:00:00.000000\')
                        AND DATA_TYPE IN (\'HIREDAUTO\',\'EMPLOYER\',\'GLOC\')
                        AND DATE_DELETED = \'9999-01-01 01:00:00.000000\'") || error($ENGINE,'CLM_VEHICLE/CLM_VEH_VARDATA query prepare failed: '.$ENGINE->{'DBH'}->errstr);

                $vehVardataQuery->execute($ENGINE->{'claimGeneral'}->{'CLAIM_ID'}) || error($ENGINE,'CLM_VEH_VARDATA query execute failed: '.$ENGINE->{'DBH'}->errstr);

                my $vehVardataResults = $vehVardataQuery->fetchall_arrayref({});

            $descQuery->execute($ENGINE->{'claimGeneral'}->{'CLAIM_ID'},$unit) || error($ENGINE,'Vehicle query execute failed: '.$ENGINE->{'DBH'}->errstr);
            my $vehResults = $descQuery->fetchall_arrayref({});
            if(scalar(@$vehResults) > 0)
            {
                my $VINQuery = $ENGINE->{'DBH'}->prepare('SELECT MAKE, MODEL FROM WADB.VIN WHERE VIN_MAKE_CODE = ? AND VIN_VARIABLE = ? AND VIN_CHECK = ? AND VIN_YEAR = ?') || error($ENGINE,'VIN query prepare failed: '.$ENGINE->{'DBH'}->errstr);
                for my $v (@$vehResults)
                {
                    my $desc = '';
                    my $vin = $v->{'VIN'};
                    if(length($vin) >= 10)
                    {
                    $vin = convertVIN($v->{'VIN'});
                    $VINQuery->execute(substr($vin,0,3),substr($vin,3,5),substr($vin,8,1),substr($vin,9,1)) || error($ENGINE,'VIN query execute failed: '.$ENGINE->{'DBH'}->errstr);
                    my $VINResults = $VINQuery->fetchall_arrayref({});
                    if(scalar(@$VINResults) > 0)
                      { $desc = $VINResults->[0]->{'MAKE'}.' '.$VINResults->[0]->{'MODEL'}.' '.$v->{'VIN'}; }
                    }
                    else
                    {
                        #if vehicle is garage location, hired auto or e&o.
                        for my $vr (@$vehVardataResults)
                        {
                            $vr->{'VARDATA'} =~ s/\s*$//;
                            if(defined($vr->{'DATA_TYPE'}) && $vr->{'DATA_TYPE'} eq 'GLOC' && (substr($unit,0,2) eq '10'))
                            { $desc = 'GLOC '.$vr->{'VARDATA'}; }
                            elsif(defined($vr->{'DATA_TYPE'}) && $vr->{'DATA_TYPE'} eq 'EMPLOYER' && (substr($unit,0,2) eq '20'))
                            { $desc = $vr->{'VARDATA'}.' EMPLOYER NON OWNED'; }
                            elsif(defined($vr->{'DATA_TYPE'}) && $vr->{'DATA_TYPE'} eq 'HIREDAUTO' && (substr($unit,0,2) eq '30'))
                            { $desc = $vr->{'VARDATA'}.' HIRED AUTO'; }
#                            $desc .= $vr->{'VARDATA'};
                        }
                    }
                    $itemDesc .= $v->{'YEAR'}.' '.($desc||($v->{'MODEL'}.' '.$v->{'VIN'})).'<br />';
                }
                $itemDesc =~ s/<br \/>$//;
            }
        }
    }
#    elsif($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /300|301|302|330|331|332/)
#    {
#        $descQuery->execute($ENGINE->{'claimGeneral'}->{'CLAIM_ID'},$loc) || error($ENGINE,'Farm property query execute failed: '.$ENGINE->{'DBH'}->errstr);
#        my $propertyResults = $descQuery->fetchall_arrayref({});

#        for my $p (@$propertyResults)
#        {
#            if(defined($p->{'ACRES'}) && $p->{'ACRES'} =~ /\w/)
#              { $itemDesc .= $p->{'ACRES'}.' '; }
#            if(defined($p->{'RANGE'}) && $p->{'RANGE'} =~ /\w/)
#              { $itemDesc .= $p->{'RANGE'}.' '; }
#            if(defined($p->{'SECTION'}) && $p->{'SECTION'} =~ /\w/)
#              { $itemDesc .= $p->{'SECTION'}.' '; }
#            if(defined($p->{'TOWNSHIP_WORDAGE'}) && $p->{'TOWNSHIP_WORDAGE'} =~ /\w/)
#              { $itemDesc .= $p->{'TOWNSHIP_WORDAGE'}.' '; }
#            if(defined($p->{'COUNTY_WORDS'}) && $p->{'COUNTY_WORDS'} =~ /\w/)
#              { $itemDesc .= $p->{'COUNTY_WORDS'}.' COUNTY '; }
#            if(defined($p->{'STATE'}) && $p->{'STATE'} =~ /\w/)
#              { $itemDesc .= $p->{'STATE'}.' '; }
#            chop($itemDesc);
#            $itemDesc .= '<br />';
#        }
#        $itemDesc =~ s/<br \/>$//;
#    }
    else
    {
        $descQuery->execute($ENGINE->{'claimGeneral'}->{'CLAIM_ID'},$loc,$unit) || error($ENGINE,'Location query execute failed: '.$ENGINE->{'DBH'}->errstr);
        my $propertyResults = $descQuery->fetchall_arrayref({});

        for my $p (@$propertyResults)
        {
            if(defined($p->{'FL_ACRES'}) && $p->{'FL_ACRES'} =~ /\w/ && $p->{'FL_ACRES'} > 0)
            {
                if($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /810|814|816/)
                { $itemDesc .= 'Location: '.$p->{'LOCATION_NO'}.' '; }
                else
                { $itemDesc .= 'Location: '.$p->{'LOCATION_NO'}.' Acres: '.$p->{'FL_ACRES'}.' '; }
                if(defined($p->{'FL_SECTION'}) && $p->{'FL_SECTION'} =~ /\w/)
                  { $itemDesc .= ' Section: '.$p->{'FL_SECTION'}.' '; }
                if(defined($p->{'FL_RANGE'}) && $p->{'FL_RANGE'} =~ /\w/)
                  { $itemDesc .= ' Range: '.$p->{'FL_RANGE'}.' '; }
                if(defined($p->{'FL_TOWNSHIP_WORDAGE'}) && $p->{'FL_TOWNSHIP_WORDAGE'} =~ /\w/)
                  { $itemDesc .= ' Township: '.$p->{'FL_TOWNSHIP_WORDAGE'}.' '; }
                if(defined($p->{'FL_COUNTY_WORDS'}) && $p->{'FL_COUNTY_WORDS'} =~ /\w/)
                  { $itemDesc .= ' County: '.$p->{'FL_COUNTY_WORDS'}.' '; }
            }
            else
            {
                if($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /580|810|814|816/ && (defined($unit) && $unit =~ /98|99/))
                  { $itemDesc .= 'Class Code: '.$p->{'ADDRESS1'}.' '; }
                elsif(defined($p->{'ADDRESS1'}) && $p->{'ADDRESS1'} =~ /\w/)
#                  { $itemDesc .= 'Location: '.$p->{'LOCATION_NO'}.' Address: '.$p->{'ADDRESS1'}.' '; }
                  { $itemDesc .= 'Location: '.$p->{'LOCATION_NO'}.' '.$p->{'ADDRESS1'}.' '; }
                if(defined($p->{'ADDRESS2'}) && $p->{'ADDRESS2'} =~ /\w/ && defined($unit) && $unit =~ /98|99/)
                  { $itemDesc .= $p->{'ADDRESS2'}; }
                  else
                  { $itemDesc .= $p->{'ADDRESS2'}.' '; }
                if(defined($p->{'CITY'}) && $p->{'CITY'} =~ /\w/)
#                  { $itemDesc .= ' City: '.$p->{'CITY'}.' '; }
                  { $itemDesc .= $p->{'CITY'}.' '; }
            }

            if(defined($p->{'STATE'}) && $p->{'STATE'} =~ /\w/)
#              { $itemDesc .= ' State: '.$p->{'STATE'}.' '; }
              { $itemDesc .= $p->{'STATE'}.' '; }
            if(defined($p->{'ZIP1_5'}) && $p->{'ZIP1_5'} =~ /\w/)
              { $itemDesc .= $p->{'ZIP1_5'}.'-'; }
            if(defined($p->{'ZIP6_12'}) && $p->{'ZIP6_12'} =~ /\w/ && $p->{'ZIP6_12'} ne '0000')
              { $itemDesc .= $p->{'ZIP6_12'}.' '; }
            if(defined($unit) && $unit !~ /98|99/)
            { chop($itemDesc); }
            $itemDesc .= '<br />';
        }
        $itemDesc =~ s/<br \/>$//;
    }

    return $itemDesc;
}

=item * getCauseOfLoss()

This function returns the cause of loss descriptions along with a pipe delimited list of loss codes.

=cut
sub getCauseOfLoss
{
    my $ENGINE = shift;
    my $causeOfLoss = '';
    my $lossCodes = '';
    my %lossCodeDescs = ();
    my %causeOfLossDescs = ();
    my %losscodeLOB = getLossCodeLOB();
    my %causeOfLossLOB = getCauseOfLossLOB();
    my $cashQuery = $ENGINE->{'DBH'}->prepare("SELECT LOSS_CODE, IMT_CAUSE_OF_LOSS FROM CLAIMDB.CLM_CASH AS C
WHERE C.CLAIM_ID = ? AND LOSS_CODE not in (\'79\',\'80\',\'81\',\'82\') AND C.DATE_DELETED = '9999-01-01 01:00:00.000000'") || error($ENGINE,'Cash query prepare failed: '.$ENGINE->{'DBH'}->errstr);
    $cashQuery->execute($ENGINE->{'claimGeneral'}->{'CLAIM_ID'}) || error($ENGINE,'Cash query execute failed: '.$ENGINE->{'DBH'}->errstr);
    my $cashResults = $cashQuery->fetchall_arrayref({});

    my $reserveQuery = $ENGINE->{'DBH'}->prepare("SELECT LOSS_CODE, IMT_CAUSE_OF_LOSS FROM CLAIMDB.CLM_RESERVES AS R
WHERE R.CLAIM_ID = ? AND LOSS_CODE not in (\'79\',\'80\',\'81\',\'82\') AND R.DATE_DELETED = '9999-01-01 01:00:00.000000'") || error($ENGINE,'Reserve query prepare failed: '.$ENGINE->{'DBH'}->errstr);
    $reserveQuery->execute($ENGINE->{'claimGeneral'}->{'CLAIM_ID'}) || error($ENGINE,'Reserve query execute failed: '.$ENGINE->{'DBH'}->errstr);
    my $reserveResults = $reserveQuery->fetchall_arrayref({});

    my $lossCodeQuery = $ENGINE->{'DBH'}->prepare("SELECT LOSSCODES_DESC FROM GENSUPDB.LOSSCODES WHERE LOSSCODES_IMTCODE = ?
AND (LOSSCODES_LOB = 'ADJ' OR LOSSCODES_LOB = ?)
AND LOSSCODES_EFF_DATE = (SELECT MAX(LOSSCODES_EFF_DATE) FROM GENSUPDB.LOSSCODES WHERE LOSSCODES_IMTCODE = ?
AND (LOSSCODES_LOB = 'ADJ' OR LOSSCODES_LOB = ?))");

    my @results = (@$cashResults,@$reserveResults);
    for my $c (@results)
    {
        my $show = 0;
        if(!defined($lossCodeDescs{$c->{'LOSS_CODE'}}))
        {
            $lossCodeQuery->execute($c->{'LOSS_CODE'},
    $losscodeLOB{$ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'}},
    $c->{'LOSS_CODE'},
    $losscodeLOB{$ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'}}) || error($ENGINE,'Amount paid query execute failed: '.$ENGINE->{'DBH'}->errstr);
            my $lossCodeResults = $lossCodeQuery->fetchall_arrayref({});
            if(scalar(@$lossCodeResults) > 0)
              { $lossCodeDescs{$c->{'LOSS_CODE'}} = $lossCodeResults->[0]->{'LOSSCODES_DESC'}; }
            else
              { $lossCodeDescs{$c->{'LOSS_CODE'}} = ''; }
            $show = 1;
            $lossCodes .= $c->{'LOSS_CODE'}.'|';
        }
        if(!defined($causeOfLossDescs{$c->{'IMT_CAUSE_OF_LOSS'}}))
        {
            $lossCodeQuery->execute($c->{'IMT_CAUSE_OF_LOSS'},
    $causeOfLossLOB{$ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'}},
    $c->{'IMT_CAUSE_OF_LOSS'},
    $causeOfLossLOB{$ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'}}) || error($ENGINE,'Amount paid query execute failed: '.$ENGINE->{'DBH'}->errstr);
            my $lossCodeResults = $lossCodeQuery->fetchall_arrayref({});
            if(scalar(@$lossCodeResults) > 0)
              { $causeOfLossDescs{$c->{'IMT_CAUSE_OF_LOSS'}} = $lossCodeResults->[0]->{'LOSSCODES_DESC'}; }
            else
              { $causeOfLossDescs{$c->{'IMT_CAUSE_OF_LOSS'}} = ''; }
            $show = 1;
        }
        if($show)
        {
            my $separator = '';
            if($lossCodeDescs{$c->{'LOSS_CODE'}} && $causeOfLossDescs{$c->{'IMT_CAUSE_OF_LOSS'}})
              { $separator = ' - '; }
            $causeOfLoss .= $lossCodeDescs{$c->{'LOSS_CODE'}}.$separator.$causeOfLossDescs{$c->{'IMT_CAUSE_OF_LOSS'}}.'<br />';

        }
    }
    $causeOfLoss =~ s/<br \/>$//;
    return $causeOfLoss,$lossCodes;
}

=item * screenDataSync()

This function copies data from one array of hashes into the other depending on
whether a matching row can be found and the field exists. This function is necessary to fill out
partial rows with data from the database for update purposes and also for when the screen must redisplay
with errors.
For instance:



    my $db = [{'id'=>0,'val'=>'a','val2'=>'b'},
              {'id'=>1,'val'=>'a','val2'=>'b'},
              {'id'=>2,'val'=>'a','val2'=>'b'},
              {'id'=>3,'val'=>'a','val2'=>'b'},
              {'id'=>4,'val'=>'a','val2'=>'b'},
             ];
    my $screen = [{'id'=>0,'val'=>'a'},
                  {'id'=>1,'val'=>'a','val2'=>''},
                  {'id'=>2,'DELETE'=>1},
                  {'id'=>4,'val'=>undef},
                 ];

    screenDataSync($ENGINE,$db,$scren,'id');

    Would result in $screen being:
    [{'id'=>0,'val'=>'a','val2'=>'b'},
     {'id'=>1,'val'=>'a','val2'=>''},
     {'id'=>2,'DELETE'=>1},
     {'id'=>3,'val'=>'a','val2'=>'b','NOUPDATE'=>1},
     {'id'=>4,'val'=>undef,'val2'=>'b'},
    ]

Notice that the entire row with id 3 was added and marked 'NOUPDATE',
also notice that the row with id 4 had an undef value for val and this was not overwritten with 'a'.


=cut
sub screenDataSync
{
    my $ENGINE = shift;
    my $dbResults = shift;
    my $screenData = shift;
    my $keyField = shift;

    if(!defined($screenData))
    {
        my @c = caller(); my $ctext = ' '.$c[1].' line '.$c[2];
        print 'WARNING: screenDataSync called with undefined screenData reference by '.$ctext."\n";
    }

    for my $d (@$dbResults)
    {
        my $found = 0;

        for my $s (@{$screenData})
        {
            ## If the keyfield is not defined or the row is marked 'NOUPDATE', then skip this row since we can't match it.
            #print '<br>'.'keyfield = '.$s->{$keyField} . ' and ' . $d->{$keyField};
            if(!defined($s->{$keyField}) || $s->{'NOUPDATE'})
              { next; }

            if($s->{$keyField} eq $d->{$keyField})
            {
                $found = 1;

                ## Don't bother copying over the fields if the row is going to be deleted
                if($s->{'DELETE'})
                  { last; }

# Added the elsif below for something in the FileActivity screen.
# If it caused problems we can remove.
                for my $k (keys %$d)
                {
                    if(!exists($s->{$k}))
                      { $s->{$k} = $d->{$k}; }
                    elsif(defined($s->{$k}) && defined($d->{$k}))
                      {
                        if($s->{$k} ne $d->{$k})
                        {
                           $s->{'YESUPDATE'} = 'Y';
                        }
                      }
                }

                last;
            }
        }

        ## If we didn't find the database row, add it to the screen data, but mark it 'NOUPDATE'
        if(!$found)
        {
            $d->{'NOUPDATE'} = 1;
            push(@{$screenData},$d);
        }
    }

    return $screenData;
}


=item * getFooter()

This function returns the navigation menu used at the bottom of each page.

=cut
sub getFooter
{
    my $ENGINE = shift;

    my $sessID = $ENGINE->{'SESSION'}->{'sessionID'};
    my $action = $ENGINE->{'ACTION'};

    my $load = $ENGINE->{'load'};
    my $claimid = $ENGINE->{'claimGeneral'}->{'CLAIM_ID'} || '';
    my $userID = $ENGINE->{'AUTH'}->{'user_key'};
    my %selected = ($load=>' style="background-color:#f6fafd"');
    my $inquiry = $action.'?load=Claims_Inquiry&amp;sessionID='.$sessID;
    if(defined($ENGINE->{'SESSION'}->{'searchURL'}) && $ENGINE->{'SESSION'}->{'searchURL'} ne '')
    {
        $inquiry = $ENGINE->{'SESSION'}->{'searchURL'};
    }

    #code to force user to print the draft no matter what screen he is
    #navigating to - if he just tried to print a draft, that is!
    my $printDraftScript = '';
    if (defined $ENGINE->{'NewDraft'}->{'fileName'}
        && $ENGINE->{'NewDraft'}->{'fileName'}  gt '')
    {

    $printDraftScript = <<EOF;

    <script type="text/javascript">
        printTheDraft($claimid,$sessID,"$ENGINE->{'NewDraft'}->{'fileName'}");
    </script>

EOF

    }

    my $detailsScreen = $ENGINE->{'CGI'}->param('save')||'';
    my $footerHTML = '';
    my $loss_notice = '';
    my $add_attach = '';
    my $sub_glass = '';
    my $sub_claim = '';
    my $save_exit = '';
    my $save = '';
    my $wc_state_form = '';
    my $restore = '';
    my $required_msg = '';

    if(!$ENGINE->{'READONLY'} || ($load eq 'Claims_Monetary' && 
       (defined $ENGINE->{'AUTH'}->{'Claims_CashEntry'} && $ENGINE->{'AUTH'}->{'Claims_CashEntry'} eq 'A')))
    {
        $footerHTML .= '<div class="menu hideprint" style="margin:0.4em 0em 0.4em 11em;text-decoration:none;color:black;text-align:center;">';

        ## Add print loss notice button if the file exists.
#        if(defined $ENGINE->{'claimGeneral'}->{'INITIATION_POINT'}
#           && $ENGINE->{'claimGeneral'}->{'INITIATION_POINT'} ne 'CV')
#        {
#                if ($load ne 'Claims_Inquiry')
#                {
#                    $loss_notice = getPrintLossNoticeButton($ENGINE);
#            }
#        }
#         if($ENGINE->{'AUTH'}->{'Claims_Access'} ne 'I')
#         {$add_attach = '<li><a href="#" class="primary" id="addAttachmentButton" onclick="addAttachment(this,'.$claimid.','.$sessID.');">Add Attachments</a></li>';}
#        $footerHTML .= '<div class="attachmentDiv">Add Attachments<input style="cursor:pointer;z-index:1;opacity:0;position:absolute;top:0;bottom:0;right:0;margin:0;" type="file" name="attachFile" /></div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; ';
#        $footerHTML .= '<div class="attachmentDiv">Add Attachments: <!--<input onchange="addAttachment(this)" type="file" name="attachFile" />--></div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; ';
        my %IMTLineCodeScreen = getIMTLineCodeScreen();
        my $screen = 'Claims_Inquiry';
        if (defined $ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'})
        {
            $screen = $IMTLineCodeScreen{$ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'}};
        }
        if(defined $ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'}
                && $ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} eq 'P')
        {
            #This if sees if the user can submit a claim.
            if(($ENGINE->{'AUTH'}->{'Claims_Access'} eq 'S' ||
            (defined($ENGINE->{'AUTH'}->{'Claims_Submit'}) && $ENGINE->{'AUTH'}->{'Claims_Submit'} eq 'A')) &&
            (defined($ENGINE->{'glassClaimSubmitted'}) && $ENGINE->{'glassClaimSubmitted'} eq 'Y'))
            { $sub_glass = '<li><a href="#" class="primary" style="background-color: green;" id="submitGlassButton" onclick="ajaxGlassRequest('.$claimid.','.$userID.');">Transmit Glass</a></li> '; }
#            elsif(defined($ENGINE->{'claimGeneral'}->{'MANUAL_OR_WHAT'}) && $ENGINE->{'claimGeneral'}->{'MANUAL_OR_WHAT'} eq 'G' && defined($ENGINE->{'claimGeneral'}->{'SUBMIT_TO_IMT_DATE'}) && $ENGINE->{'claimGeneral'}->{'SUBMIT_TO_IMT_DATE'} ne '9999-01-01 01:00:00.000000')
#            { $footerHTML .= '<a href="#" style="background-color: green;" id="submitGlassButton" onclick="ajaxGlassRequest('.$claimid.','.$userID.');">Transmit Glass</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; '; }
            elsif((defined($ENGINE->{'AUTH'}->{'Claims_Submit'}) && $ENGINE->{'AUTH'}->{'Claims_Submit'} eq 'A')||
                  $ENGINE->{'AUTH'}->{'Claims_Access'} eq 'S')
            { $sub_claim = '<li><a href="#" class="primary" id="submitClaimButton" onclick="load(\'Claims_Submit\');">Submit Claim</a></li> '; }
            $save_exit = '<li><a href="#" class="primary" id="saveExitButton" onclick="load(\'Claims_Inquiry\');">Save &amp; Exit</a></li> ';
#            if($screen eq 'VEH')
#              { $footerHTML .= '<a href="#" onclick="SubmitClaim(\'Claims_Vehicle\');">Submit Claim</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; '; }
#            elsif($screen eq 'PROP')
#              { $footerHTML .= '<a href="#" onclick="SubmitClaim(\'Claims_PropLiab\');">Submit Claim</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; '; }
#            elsif($screen eq 'WC')
#              { $footerHTML .= '<a href="#" onclick="SubmitClaim(\'Claims_WorkComp\');">Submit Claim</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; '; }
        }

        #for monetary entries, we want a save button.  For Details we want
        #a save and exit button
        if ($load eq 'Claims_Monetary')
        {
                $save = '<li><a href="#" class="primary" id="saveButton" onclick="load(\'Claims_Monetary\');">Save</a></li>';
        }
        elsif($load eq 'Claims_FileActivity')
        {
                $save = '<li><a href="#" class="primary" id="saveButton" onclick="load(\'Claims_FileActivity\');">Save</a></li>';
        }
        elsif($load eq 'Claims_MedPayments')
        {
                $save = '<li><a href="#" class="primary" id="saveButton" onclick="load(\'Claims_MedPayments\');">Save</a></li>';
        }
        elsif($load eq 'Claims_DisPayments')
        {
                $save = '<li><a href="#" class="primary" id="saveButton" onclick="load(\'Claims_DisPayments\');">Save</a></li>';
        }
        elsif($load eq 'Claims_PolicyInfo'
                || $load eq 'Claims_Info'
                || $load eq 'Claims_FileManager')
        {
                 #do nothing for policy info and summary screens
        }
        elsif ($load eq 'Claims_Inquiry')
        {
                $add_attach = '';
        }
        else
        {
                $save = '<li><a href="#" class="primary" id="saveButton" onclick="load(\'Claims_Details\');">Save</a></li>';
        }

        if (defined $ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'}
                && $ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} gt ''
                && $ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /600|605/
                && defined $ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'}
                && $ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} ne 'P'
                && $ENGINE->{'claimGeneral'}->{'ACCIDENT_STATE'} ne 'MN'
                && ($load eq 'Claims_Details' || $load eq 'Claims_WorkComp'))
##                && $load ne 'Claims_Inquiry')
        {
                if(!$ENGINE->{'READONLY'})
                    {
                #$footerHTML .= '&nbsp;&nbsp;&nbsp;<a href="#" id="wcStatePrint" onclick="load(\'Claims_State_Form\');">Print State Forms</a>';
                    #$footerHTML .= '<a id="wcStatePrint" name="wcStatePrint" href="../IMTFiles.pl?file=84556">Generate State First Loss Notice</a>';
                #$footerHTML .= '<a id="wcStatePrint" name="wcStatePrint" href="Claims_State_Form.pl?sessionID='.$sessID.',load='.$load.',save='.$load.',claimid='.$claimid. '>Generate State First Loss Notice</a>';
                                $wc_state_form .= '<li><a href="#" class="primary" id="wcStatePrint" onclick="generateState();">Generate State WC Form</a></li>';

                    }
        }
        #return $footerHTML.'</div>';
        $footerHTML.='</div>';
    }
    elsif($ENGINE->{'READONLY'}
            && $ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} ne 'P'
            && defined $ENGINE->{'AUTH'}->{'Claims_CashEntry'}
            && $ENGINE->{'AUTH'}->{'Claims_CashEntry'} eq 'A'
            && $load eq 'Claims_Monetary')
    {
        $footerHTML = '<div class="menu hideprint" style="margin:0.4em 0em 0.4em 11em;text-decoration:none;color:black;text-align:center;">';
#        if($load eq 'Claims_Monetary')
#        {
#                $loss_notice = getPrintLossNoticeButton($ENGINE);
#            $save = '<li><a href="#" class="primary" id="saveButton" onclick="load(\'Claims_Monetary\');">Save</a></li>';
#        }

        #return $footerHTML.'</div>';
        $footerHTML.='</div>';
    }
    elsif($ENGINE->{'READONLY'}
            && $ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} ne 'P'
            && defined $ENGINE->{'AUTH'}->{'Claims_Access'}
            && $ENGINE->{'AUTH'}->{'Claims_Access'} eq 'A'
            && $load eq 'Claims_Monetary'
            && ($ENGINE->{'claimGeneral'}->{'REINSURANCE_IND'} eq 'P'
            || $ENGINE->{'claimGeneral'}->{'SUBRO_STATUS'} eq 'P'
            || $ENGINE->{'claimGeneral'}->{'SALV_STATUS'} eq 'P'
            || $ENGINE->{'claimGeneral'}->{'CLOSE_RECOVERABLE'} eq 'P'))
    {
        $footerHTML = '<div class="menu hideprint" style="margin:0.4em 0em 0.4em 11em;text-decoration:none;color:black;text-align:center;">';
        if($load eq 'Claims_Monetary')
        {
            #    $loss_notice = getPrintLossNoticeButton($ENGINE);
            $save = '<li><a href="#" class="primary" id="saveButton" onclick="load(\'Claims_Monetary\');">Save</a></li>';
        }

        #return $footerHTML.'</div>';
        $footerHTML.='</div>';
    }
    elsif($ENGINE->{'READONLY'}
            && $ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} ne 'P'
            && $ENGINE->{'claimGeneral'}->{'INITIATION_POINT'} ne 'CV')
    {
        $footerHTML = '<div class="menu hideprint" style="margin:0.4em 0em 0.4em 11em;text-decoration:none;color:black;text-align:center;">';

#        if(($ENGINE->{'AUTH'}->{'Claims_Access'} eq 'S' ||
#           $ENGINE->{'AUTH'}->{'Claims_Access'} eq 'A')
#                && $load ne 'Claims_Inquiry')
#        {
#            $add_attach = '<li><a href="#" class="primary" onclick="addAttachment(this,'.$claimid.','.$sessID.');">Add Attachments</a></li>';
#        }

#        if ($load ne 'Claims_Inquiry')
#        {
#                $loss_notice = getPrintLossNoticeButton($ENGINE);
#        }
        if($load eq 'Claims_FileActivity')
        {
            if($ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} =~ /C/ && $ENGINE->{'AUTH'}->{'Claims_Access'} eq 'A')
            {
                $save = '<li><a href="#" class="primary" id="saveButton" onclick="load(\'Claims_FileActivity\');">Save</a></li>';
            }
        }
        $footerHTML.='</div>';
    }
    elsif($ENGINE->{'READONLY'}
            && $ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} ne 'P'
            && $ENGINE->{'claimGeneral'}->{'INITIATION_POINT'} eq 'CV')
    {
        $footerHTML = '<div class="menu hideprint" style="margin:0.4em 0em 0.4em 11em;text-decoration:none;color:black;text-align:center;">';
        if($load eq 'Claims_FileActivity')
        {
            if($ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} =~ /C/ && $ENGINE->{'AUTH'}->{'Claims_Access'} eq 'A')
            {
                $save = '<li><a href="#" class="primary" id="saveButton" onclick="load(\'Claims_FileActivity\');">Save</a></li>';
            }
        }
        $footerHTML.='</div>';
    }
    elsif($ENGINE->{'READONLY'})
    {
        $footerHTML .= '<div class="menu hideprint" style="margin:0.4em 0em 0.4em 11em;text-decoration:none;color:black;text-align:center;">';
        if(defined($ENGINE->{'claimGeneral'}->{'MANUAL_OR_WHAT'}) && $ENGINE->{'claimGeneral'}->{'MANUAL_OR_WHAT'} eq 'G' && defined($ENGINE->{'claimGeneral'}->{'SUBMIT_TO_IMT_DATE'}) && $ENGINE->{'claimGeneral'}->{'SUBMIT_TO_IMT_DATE'} ne '9999-01-01 01:00:00.000000')
        { $sub_glass = '<li><a href="#" class="primary" style="background-color: green;" id="submitGlassButton" onclick="ajaxGlassRequest('.$claimid.','.$userID.');">Transmit Glass</a></li>'; }
         $footerHTML.='</div>';
    }

    #special case for purged claims
    if($ENGINE->{'AUTH'}->{'internalUser'} eq 'YES'
             && defined $ENGINE->{'claimGeneral'}->{'PURGED_IND'}
            && $ENGINE->{'claimGeneral'}->{'PURGED_IND'} eq 'Y'
            && defined $ENGINE->{'AUTH'}->{'TITLE_KEY'} && $ENGINE->{'AUTH'}->{'TITLE_KEY'} =~ /^1$|^2$|^3$|^8$|^9$|^10$|^11$|^12$|^13$|^14$|^15$|^16$|^17$|^18$|^19$|^20$|^22$/)
    {   #build the restore claim from archive button
        $footerHTML = '<div class="menu hideprint" style="margin:0.4em 0em 0.4em 11em;text-decoration:none;color:black;text-align:center;">';
        if($load eq 'Claims_Info')
        {
            if($ENGINE->{'AUTH'}->{'Claims_Access'} eq 'A')
            {
                $restore= '<li><a href="#" class="primary" name="reinstateButton" id="reinstateButton" onclick="load3(\'Claims_Info\');">Restore Archived Claim</a></li>';
            }
        }
        $footerHTML.='</div>';
    }

    #add the message about required fields
    if (($load eq 'Claims_Details'
        || $load eq 'Claims_WorkComp'
        || $load eq 'Claims_Vehicle'
        || $load eq 'Claims_PropLiab')
        && (defined $ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'}
                   && $ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} eq 'P'
                   && !$ENGINE->{'READONLY'}))
    {
        $required_msg= '<li class="required">Fields with * are required to submit claim </li>';
    }

        #add the draft print into the footer
    $footerHTML .= $printDraftScript;
#    die Data::Dumper::Dumper($ENGINE->{'claimGeneral'}->{'CLM_STORM_ID'},
#        $ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'},
#        $ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'},
#        $ENGINE->{'AUTH'}->{'TITLE_KEY'},
#        $ENGINE->{'CGI'}->param('load'));

#     my $google_analytics = '';
#     if((!defined $ENGINE->{'claimGeneral'}->{'CLM_STORM_ID'}) &&
#         $ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} eq '010' &&
#         $ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} eq 'M' &&
#         $ENGINE->{'AUTH'}->{'TITLE_KEY'} =~ /^1$|^10$|^12$/ &&
#         $ENGINE->{'CGI'}->param('load') eq 'Claims_Details')
#     {
#         $google_analytics = <<EOF;
# <script>
#   (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
#   (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
#   m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
#   })(window,document,'script','https://www.google-analytics.com/analytics.js','ga');
#
#   ga('create', 'UA-101456211-1', 'auto');
#   ga('send', 'pageview');
#
# </script>
# EOF
#     }

#    die Data::Dumper::Dumper($load,$loss_notice,$add_attach,$sub_glass,$sub_claim,$save_exit,$save,$wc_state_form,$restore,$required_msg);
    my $footerHTML = '';
    if($load ne 'Claims_Inquiry')
        {
    $footerHTML = <<EOF;
<div class="primary_actions">
<ul>
$add_attach
$sub_glass
$sub_claim
$save_exit
$save
$wc_state_form
$restore
$required_msg
</ul>
</div>
$printDraftScript
EOF
    }
    return $footerHTML;
}

sub getPrintLossNoticeButton
{
    my $ENGINE = shift;

    use IMT::Files qw(:all);

    my $FGID = $ENGINE->{'claimGeneral'}->{'FGID'};

    my $lossSelectSTH = $ENGINE->{'DBH'}->prepare('SELECT FID, COMMENT FROM GENSUPDB.IMTFILES
WHERE FGID = ? AND OWNER = ? AND TYPE = ? AND FILE_NAME = ?')  || $ENGINE->{'error'}->($ENGINE);

    $lossSelectSTH->execute($FGID,UNKNOWN_USER,FILE,'Loss Notice.pdf') || $ENGINE->{'error'}->($ENGINE);

    my $result = $lossSelectSTH->fetchall_arrayref({});

    #107295  2/28/2012 akc
    #added commit to help prevent table locks
    $ENGINE->{'DBH'}->commit();

    if(scalar(@$result)>0)
    {
        my $download_url = '../IMTFiles.pl?file='.$result->[0]->{'FID'};
        if($result->[0]->{'COMMENT'} =~ /^AWS/ && length($result->[0]->{'COMMENT'}) == 40)
        {$download_url = '/imtonline/Claims/Claims_Doc_AWS_Download.pl?key='.$result->[0]->{'COMMENT'};}

        if((defined $ENGINE->{'CGI'}->{'transactionCode'} && $ENGINE->{'CGI'}->{'transactionCode'}[0] eq 'submit') 
           && (defined $ENGINE->{'CGI'}->{'quarterlyReportSubmit'} && $ENGINE->{'CGI'}->{'quarterlyReportSubmit'} ne 'Y'))
        {return '<a class="btn btn-default" href="'.$download_url.'" id="printLossButton">Print Loss Notice</a>';}
        else
        {return '<a href="'.$download_url.'" id="printLossButton">Loss Notice</a>';}
    }
    else
    {
        return '';
    }
}

=item * editSubmitClaimData()

This function edits when Submit button is pressed.

=cut
sub editSubmitClaimData
{

    my $ENGINE = shift;

    my $sessID = $ENGINE->{'SESSION'}->{'sessionID'};
    my $action = $ENGINE->{'ACTION'};

    my %errors = ();
    my $return = 1;
    my $claimid = $ENGINE->{'claimGeneral'}->{'CLAIM_ID'};

#    my $transactionCode = $ENGINE->{'CGI'}->param('transactionCode')||'';
#    if(defined($transactionCode) && $transactionCode eq 'submit')
#    {
#        my $generalData = {};
#        $generalData->{'CLAIM_ID'} = $claimid;
#        $generalData->{'CLAIM_STATUS'} = 'M';
#        $generalData->{'USER_KEY'} = $ENGINE->{'AUTH'}->{'user_key'};
#        push(@{$ENGINE->{'claimMisc'}->{'CLM_GENERAL'}},$generalData);

#        my %lobTasks = getLOBTask();
#        my $tasksArray = $lobTasks{$ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'}};
#        for my $t (@$tasksArray)
#        {
#            createTaskList($ENGINE,$t);
###            if($t eq 'ALG')
###            { $activityToBeCompleted .= createAllClaimsExGlass($ENGINE); }
###            elsif($t eq 'AWG')
###            { $activityToBeCompleted .= createAllClaimsExWCGlass($ENGINE); }
###            elsif($t eq 'AEG')
###            { $activityToBeCompleted .= createAutoClaimsExGlass($ENGINE); }
###            elsif($t eq 'LAG')
###            { $activityToBeCompleted .= createLiabAutoClaimsExGlass($ENGINE); }
###            elsif($t eq 'PLC')
###            { $activityToBeCompleted .= createPropLiabClaims($ENGINE); }
###            elsif($t eq 'PRC')
###            { $activityToBeCompleted .= createPropClaims($ENGINE); }
###            elsif($t eq 'WCC')
###            { $activityToBeCompleted .= createWorkCompClaims($ENGINE); }
#        }
#    }

#    my %keys = getTablePrimaryKeyName();
##    my $error = $ENGINE->{'error'};

#    push(@{$ENGINE->{'claimMisc'}->{'OLD_CLM_GENERAL'}},$ENGINE->{'claimGeneral'});
#    if(!defined($ENGINE->{'claimMisc'}->{'CLM_GENERAL'}))
#      { $ENGINE->{'claimMisc'}->{'CLM_GENERAL'} =[] }

#    screenDataSync($ENGINE,$ENGINE->{'claimMisc'}->{'OLD_CLM_GENERAL'},$ENGINE->{'claimMisc'}->{'CLM_GENERAL'},$keys{'CLM_GENERAL'});

#    $ENGINE->{'claimMisc'}->{'OLD_CLM_TASK_LIST'} =[];
#    if(!defined($ENGINE->{'claimMisc'}->{'CLM_TASK_LIST'}))
#      { $ENGINE->{'claimMisc'}->{'CLM_TASK_LIST'} =[]; }

#    screenDataSync($ENGINE,$ENGINE->{'claimMisc'}->{'OLD_CLM_TASK_LIST'},$ENGINE->{'claimMisc'}->{'CLM_TASK_LIST'},$keys{'CLM_TASK_LIST'});
#    if(defined($ENGINE->{'errors'}))
#    {
#        for my $key (keys %errors)
#        {
#            push(@{$ENGINE->{'errors'}->{$key}},@{$errors{$key}});
#        }
#    }
#    else
#     { $ENGINE->{'errors'} = \%errors; }
    return $return;
}

=item * storeSubmitClaimData()

This function saves data when the Submits button is pressed.

=cut
sub storeSubmitClaimData
{

    my $ENGINE = shift;
#    my $error = $ENGINE->{'error'};

#    my $sessID = $ENGINE->{'SESSION'}->{'sessionID'};
#    my $action = $ENGINE->{'ACTION'};

#    my $name = $ENGINE->{'AUTH'}->{'name'};
#    my $claimid = $ENGINE->{'claimGeneral'}->{'CLAIM_ID'};

#    #############
#    # General   #
#    #############
#    my $generalUpdate = $ENGINE->{'DBH'}->prepare('UPDATE CLAIMDB.CLM_GENERAL SET CLAIM_STATUS = ?,USER_KEY = ?
#    WHERE CLAIM_ID = ?') || $error->($ENGINE);


#    for my $n (@{$ENGINE->{'claimMisc'}->{'CLM_GENERAL'}})
#    {
#        $generalUpdate->execute($n->{'CLAIM_STATUS'},$n->{'USER_KEY'},$claimid) || $error->($ENGINE);

#        if($ENGINE->{'claimGeneral'}->{'MANUAL_OR_WHAT'} eq 'G')
#        {# warn "will send to alliance here";
#           # use Claims_GlassInterface qw(sendGlassClaim);
#            #sendGlassClaim($ENGINE);
#        }
#        elsif($n->{'CLAIM_STATUS'} eq 'M')                                                                  #UNASSIGNED_DM_BASKET_ID UNASSIGNED_SC_BASKET_ID UNASSIGNED_SF_BASKET_ID        check which branch...
#        {
#            my $basket = UNASSIGNED_DM_BASKET_ID;
#            if($ENGINE->{'claimGeneral'}->{'BRANCH'} eq '20')
#            {
#                $basket = UNASSIGNED_DM_BASKET_ID;
#            }
#            elsif($ENGINE->{'claimGeneral'}->{'BRANCH'} eq '50')
#            {
#                $basket = UNASSIGNED_SF_BASKET_ID;
#            }
#            elsif($ENGINE->{'claimGeneral'}->{'BRANCH'} eq '60')
#            {
#                $basket = UNASSIGNED_SC_BASKET_ID;
#            }
#            insert_notification($ENGINE,{'claim_id'=>$claimid,'baskets'=>$basket,'type'=>CLAIM_NTF_TYPE(),'msg_id'=>UNASSIGNED_CLAIM(),'comment'=>''});
#        }
#    }

#    ###############
#    # Task List   #
#    ###############
#    my $taskListInsert = $ENGINE->{'DBH'}->prepare(
#        "SELECT CLM_TASK_ID
#           FROM FINAL TABLE
#                (INSERT INTO CLAIMDB.CLM_TASK_LIST
#                   (CLM_TASK_ID,CLAIM_ID,TASKS_ID,TASK_ORDER_NUMBER,TASK_USER_KEY,PRIVATE_OR_PUBLIC)
#         VALUES (NEXT VALUE FOR CLAIMDB.CLM_TASK_ID_SEQ,?,?,?,?,?))")  || $error->($ENGINE);


#    for my $n (@{$ENGINE->{'claimMisc'}->{'CLM_TASK_LIST'}})
#    {
#        $taskListInsert->execute($claimid,$n->{'TASKS_ID'},$n->{'TASK_ORDER_NUMBER'},$n->{'TASK_USER_KEY'},$n->{'PRIVATE_OR_PUBLIC'}) || $error->($ENGINE);
#    }
}

=item * createTaskList()

This function saves data when the Submits button is pressed.

=cut
sub createTaskList
{

    my $ENGINE = shift;
    my $task = shift;
    my $error = $ENGINE->{'error'};

    my $sessID = $ENGINE->{'SESSION'}->{'sessionID'};
    my $action = $ENGINE->{'ACTION'};

    my $name = $ENGINE->{'AUTH'}->{'name'};
    my $claimid = $ENGINE->{'claimGeneral'}->{'CLAIM_ID'};

    # CLM_TASKS query
    my $tasksQuery = $ENGINE->{'DBH'}->prepare("SELECT TASKS_ID, TASK_ORDER FROM CLAIMDB.TASKS
    WHERE TASK_LINE_OF_BUSINESS = ?  AND
    PRIVATE_OR_PUBLIC <> '' ORDER BY TASK_ORDER") || $error->($ENGINE,'Tasks query prepare failed: '.$ENGINE->{'DBH'}->errstr);
    $tasksQuery->execute($task) || $error->($ENGINE,'Tasks query execute failed: '.$ENGINE->{'DBH'}->errstr);
    my $tasksResults = $tasksQuery->fetchall_arrayref({});

    for my $t (@$tasksResults)
    {
        if($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /010|011|012|015|016/ && $t->{'TASKS_ID'} == 22)
        {
                my $taskListData = {};
                $taskListData->{'CLAIM_ID'} = $claimid;
                $taskListData->{'TASKS_ID'} = $t->{'TASKS_ID'};
                $taskListData->{'TASK_ORDER_NUMBER'} = $t->{'TASK_ORDER'};
                $taskListData->{'TASK_USER_KEY'} = $ENGINE->{'AUTH'}->{'user_key'};
                $taskListData->{'PRIVATE_OR_PUBLIC'} = 'L';
                push(@{$ENGINE->{'claimMisc'}->{'CLM_TASK_LIST'}},$taskListData);
        }
        if($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /010|011|012|015|016|030|031|051|052/ && $t->{'TASKS_ID'} =~ /^3$|^6$|^7$|^8$|^9$/)
        {
                my $taskListData = {};
                $taskListData->{'CLAIM_ID'} = $claimid;
                $taskListData->{'TASKS_ID'} = $t->{'TASKS_ID'};
                $taskListData->{'TASK_ORDER_NUMBER'} = $t->{'TASK_ORDER'};
                $taskListData->{'TASK_USER_KEY'} = $ENGINE->{'AUTH'}->{'user_key'};
                $taskListData->{'PRIVATE_OR_PUBLIC'} = 'L';
                push(@{$ENGINE->{'claimMisc'}->{'CLM_TASK_LIST'}},$taskListData);
        }
        if($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /010|011|012|015|016|030|031|051|052/ && $t->{'TASKS_ID'} =~ /^11$|^12$/)
        {
                my $taskListData = {};
                $taskListData->{'CLAIM_ID'} = $claimid;
                $taskListData->{'TASKS_ID'} = $t->{'TASKS_ID'};
                $taskListData->{'TASK_ORDER_NUMBER'} = $t->{'TASK_ORDER'};
                $taskListData->{'TASK_USER_KEY'} = $ENGINE->{'AUTH'}->{'user_key'};
                $taskListData->{'PRIVATE_OR_PUBLIC'} = 'L';
                push(@{$ENGINE->{'claimMisc'}->{'CLM_TASK_LIST'}},$taskListData);
        }
        if($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /100|112|113|120|300|301|302|330|331|332|350|360|575|580|810|814|816/ && $ENGINE->{'claimGeneral'}->{'PROP_OR_LIAB'} =~ /L|B/ && $t->{'TASKS_ID'} =~ /^11$|^12$/)
        {
                my $taskListData = {};
                $taskListData->{'CLAIM_ID'} = $claimid;
                $taskListData->{'TASKS_ID'} = $t->{'TASKS_ID'};
                $taskListData->{'TASK_ORDER_NUMBER'} = $t->{'TASK_ORDER'};
                $taskListData->{'TASK_USER_KEY'} = $ENGINE->{'AUTH'}->{'user_key'};
                $taskListData->{'PRIVATE_OR_PUBLIC'} = 'L';
                push(@{$ENGINE->{'claimMisc'}->{'CLM_TASK_LIST'}},$taskListData);
        }
        if($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /804/ && $t->{'TASKS_ID'} =~ /^11$|^12$/)
        {
                my $taskListData = {};
                $taskListData->{'CLAIM_ID'} = $claimid;
                $taskListData->{'TASKS_ID'} = $t->{'TASKS_ID'};
                $taskListData->{'TASK_ORDER_NUMBER'} = $t->{'TASK_ORDER'};
                $taskListData->{'TASK_USER_KEY'} = $ENGINE->{'AUTH'}->{'user_key'};
                $taskListData->{'PRIVATE_OR_PUBLIC'} = 'L';
                push(@{$ENGINE->{'claimMisc'}->{'CLM_TASK_LIST'}},$taskListData);
        }
        #For Quarterly Reserve Report.
        if($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /010|011|012|015|016|030|031|051|052|100|112|113|120|300|301|302|330|331|332|350|360|575|580|804|810|814|816/ && $t->{'TASKS_ID'} =~ /^13$/)
        {
                my $taskListData = {};
                $taskListData->{'CLAIM_ID'} = $claimid;
                $taskListData->{'TASKS_ID'} = $t->{'TASKS_ID'};
                $taskListData->{'TASK_ORDER_NUMBER'} = $t->{'TASK_ORDER'};
                $taskListData->{'TASK_USER_KEY'} = $ENGINE->{'AUTH'}->{'user_key'};
                $taskListData->{'PRIVATE_OR_PUBLIC'} = 'L';
                push(@{$ENGINE->{'claimMisc'}->{'CLM_TASK_LIST'}},$taskListData);
        }
        if($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /100|105|112|113|120|200|205|300|301|302|303|330|331|332|350|360|575|580|804|810|814|816/ && $t->{'TASKS_ID'} =~ /^14$|^15$/)
        {
                my $taskListData = {};
                $taskListData->{'CLAIM_ID'} = $claimid;
                $taskListData->{'TASKS_ID'} = $t->{'TASKS_ID'};
                $taskListData->{'TASK_ORDER_NUMBER'} = $t->{'TASK_ORDER'};
                $taskListData->{'TASK_USER_KEY'} = $ENGINE->{'AUTH'}->{'user_key'};
                $taskListData->{'PRIVATE_OR_PUBLIC'} = 'L';
                push(@{$ENGINE->{'claimMisc'}->{'CLM_TASK_LIST'}},$taskListData);
        }
        if($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /100|105|112|113|120|200|205|575|580|810|814|816/ && $ENGINE->{'claimGeneral'}->{'PROP_OR_LIAB'} =~ /P|B/ && $t->{'TASKS_ID'} =~ /^5$|^16$/)
        {
                my $taskListData = {};
                $taskListData->{'CLAIM_ID'} = $claimid;
                $taskListData->{'TASKS_ID'} = $t->{'TASKS_ID'};
                $taskListData->{'TASK_ORDER_NUMBER'} = $t->{'TASK_ORDER'};
                $taskListData->{'TASK_USER_KEY'} = $ENGINE->{'AUTH'}->{'user_key'};
                $taskListData->{'PRIVATE_OR_PUBLIC'} = 'L';
                push(@{$ENGINE->{'claimMisc'}->{'CLM_TASK_LIST'}},$taskListData);
        }
        #Work Comp Memo.
        if($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /600|605/ && $t->{'TASKS_ID'} =~ /^19$/)
        {
                my $taskListData = {};
                $taskListData->{'CLAIM_ID'} = $claimid;
                $taskListData->{'TASKS_ID'} = $t->{'TASKS_ID'};
                $taskListData->{'TASK_ORDER_NUMBER'} = $t->{'TASK_ORDER'};
                $taskListData->{'TASK_USER_KEY'} = $ENGINE->{'AUTH'}->{'user_key'};
                $taskListData->{'PRIVATE_OR_PUBLIC'} = 'L';
                push(@{$ENGINE->{'claimMisc'}->{'CLM_TASK_LIST'}},$taskListData);
        }
        if($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /600|605/ && $ENGINE->{'claimGeneral'}->{'PROP_OR_LIAB'} eq 'D' && $t->{'TASKS_ID'} =~ /^18$|^20$/)
        {
                my $taskListData = {};
                $taskListData->{'CLAIM_ID'} = $claimid;
                $taskListData->{'TASKS_ID'} = $t->{'TASKS_ID'};
                $taskListData->{'TASK_ORDER_NUMBER'} = $t->{'TASK_ORDER'};
                $taskListData->{'TASK_USER_KEY'} = $ENGINE->{'AUTH'}->{'user_key'};
                $taskListData->{'PRIVATE_OR_PUBLIC'} = 'L';
                push(@{$ENGINE->{'claimMisc'}->{'CLM_TASK_LIST'}},$taskListData);
        }
        if($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /010|011|012|015|016|030|031|051|052/ && $ENGINE->{'claimGeneral'}->{'ACCIDENT_STATE'} eq 'IL' && $t->{'TASKS_ID'} == 10)
        {
                my $taskListData = {};
                $taskListData->{'CLAIM_ID'} = $claimid;
                $taskListData->{'TASKS_ID'} = $t->{'TASKS_ID'};
                $taskListData->{'TASK_ORDER_NUMBER'} = $t->{'TASK_ORDER'};
                $taskListData->{'TASK_USER_KEY'} = $ENGINE->{'AUTH'}->{'user_key'};
                $taskListData->{'PRIVATE_OR_PUBLIC'} = 'L';
                push(@{$ENGINE->{'claimMisc'}->{'CLM_TASK_LIST'}},$taskListData);
        }
        if($t->{'TASKS_ID'} =~ /^1$|^2$/)
        {
                my $taskListData = {};
                $taskListData->{'CLAIM_ID'} = $claimid;
                $taskListData->{'TASKS_ID'} = $t->{'TASKS_ID'};
                $taskListData->{'TASK_ORDER_NUMBER'} = $t->{'TASK_ORDER'};
                $taskListData->{'TASK_USER_KEY'} = $ENGINE->{'AUTH'}->{'user_key'};
                $taskListData->{'PRIVATE_OR_PUBLIC'} = 'L';
                push(@{$ENGINE->{'claimMisc'}->{'CLM_TASK_LIST'}},$taskListData);
        }
        if($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} !~ /200|205/ && $t->{'TASKS_ID'} =~ /^4$/)
        {
                my $taskListData = {};
                $taskListData->{'CLAIM_ID'} = $claimid;
                $taskListData->{'TASKS_ID'} = $t->{'TASKS_ID'};
                $taskListData->{'TASK_ORDER_NUMBER'} = $t->{'TASK_ORDER'};
                $taskListData->{'TASK_USER_KEY'} = $ENGINE->{'AUTH'}->{'user_key'};
                $taskListData->{'PRIVATE_OR_PUBLIC'} = 'L';
                push(@{$ENGINE->{'claimMisc'}->{'CLM_TASK_LIST'}},$taskListData);
        }
#        if($t->{'TASKS_ID'} !~ /10|22/)
#        {
#                my $taskListData = {};
#                $taskListData->{'CLAIM_ID'} = $claimid;
#                $taskListData->{'TASKS_ID'} = $t->{'TASKS_ID'};
#                $taskListData->{'TASK_ORDER_NUMBER'} = $t->{'TASK_ORDER'};
#                $taskListData->{'TASK_USER_KEY'} = $ENGINE->{'AUTH'}->{'user_key'};
#                $taskListData->{'PRIVATE_OR_PUBLIC'} = 'L';
#                push(@{$ENGINE->{'claimMisc'}->{'CLM_TASK_LIST'}},$taskListData);
#        }
    }

}

=item * createTaskList()

This function rolls back database changes and puts an error on the claims
engine error hash if a draft type of transaction has an error in the draft
master or irs master interface programs.

=cut
sub draftIntfceErr
{
    my $ENGINE = shift;
    my $errInd = shift;
    my $progErr = shift;
    my $screen = shift;
    my %errors = ();
    my $workMess = '';
#105858 10/30/2012 if 'help desk' in error message change to include help desk at 18002743531  (changed four error messages in this program)
    #first, roll back any changes since the transaction did not work
    $ENGINE->{'DBH'}->rollback()
                || die("failed to rollback: ".$ENGINE->{'DBH'}->errstr);

    if ($errInd =~ /X/)
    {
        push(@{$errors{'noKey'}},'Digital payment successfully transmitted, but 1099 info has not been submitted to the IRS database. Please contact ext 605 with payment info.');
                $workMess .= 'Insert to IRS Master Failed! May need to void electronic payment.';
    }

    #duplicate draft number error message
    if ($errInd =~ /D/)
    {
        push(@{$errors{'draftNo100'}},'<li><div class="errorInfo">Draft Number is duplicate on draft master.  Enter unique draft number or please contact IMT Claim Help Desk at 1-800-274-3531, ext 605.</div></li>');
                $workMess = 'DRAFTS HARDCODE ROLLBACK!  Draft Number is duplicate on draft master.  Enter unique draft number or please contact IMT Claim Help Desk at 1-800-274-3531, ext 605.';
    }
    elsif ($errInd =~ /U/)
    {
            #unavailable vsam draft master or irs master error
        push(@{$errors{'noKey'}},'Draft and IRS files unavailable.  Try again in 5 minutes.');
                $workMess = 'DRAFTS HARDCODE ROLLBACK!  Draft and IRS files unavailable.  Try again in 5 minutes.';
    }
    elsif ($errInd =~ /V/)
    {
        for my $key (keys %{$progErr})
        {
            push(@{$errors{$key}},$progErr->{$key});
        }
    }
    else
    {
            #just plain old interface program error
        push(@{$errors{'noKey'}},'Draft and IRS interface error.  Please contact IMT Claim Help Desk at 1-800-274-3531, ext 605 with this information: ' . $progErr);
                $workMess = 'DRAFTS HARDCODE ROLLBACK!  Draft and IRS interface error.  Please contact IMT Claim Help Desk at 1-800-274-3531, ext 605 with this information: ' . $progErr;
    }

    #load any errors
    $ENGINE->{'errors'} = \%errors;

    #reload the money screen with err messages
    $ENGINE->{'load'} = $screen;

    $workMess = 'Claim id: ' . $ENGINE->{'claimGeneral'}->{'CLAIM_ID'}
            . '   ErrorInd: ' .  $errInd
            . '   ProgErr: ' . Data::Dumper::Dumper($progErr)
            . '   Screen: ' . $screen
            . '   WorkMess: ' . $workMess;

    #toss the error regarding a rollback onto the log
    local *ERRFILE;
    my @date = localtime;
    my $min = $date[1];
    my $hour = $date[2];
    my $day = $date[3];
    my $mon = $date[4] + 1;
    my $year = $date[5] + 1900;
    my $rand = int(rand(1000000));
    my $path = File::Spec->catpath('d:', '/cgilogs/Claims/', $year.'-'.$mon.'-'.$day.'-Error.log');
    open(*ERRFILE,'>>', $path)  || die('Failure in error logging subroutine in the claims system, please contact IMT if this problem continues');

    print ERRFILE $year,'-',$mon,'-',$day,' ',$hour,':',$min," Reference number: ROLLBACK!!! \n";
    print ERRFILE $workMess,"\n\n";
    close(*ERRFILE);

}

sub calculateAge
{
    my $ENGINE = shift;
    my $birthDate = shift;
    my $workDate = shift;
    my $age = 0;

    if( defined($birthDate) && $birthDate gt '' )
    {
        my $b_month = substr($birthDate,5,2);
        my $b_day = substr($birthDate,8,2);
        my $b_year = substr($birthDate,0,4);
        my $e_month = substr($workDate,5,2);
        my $e_day = substr($workDate,8,2);
        my $e_year = substr($workDate,0,4);

        if( $b_year > 0 )
        {
            if( length($b_day) < 2 )
            {
                $b_day = "0".$b_day;
            }
            my $a_year = $e_year - $b_year;
            my $a_month = $e_month - $b_month;
            my $a_day = $e_day - $b_day;

            if( $e_month < $b_month)
            {
                $a_year--;
                $a_month = 12 + $a_month;
            }
            if( $e_day < $b_day )
            {
                $a_month--;
                $a_day = 31 + $a_day;
                if( $a_month < 0 )
                {
                    $a_month = 11;
                    $a_year--;
                }
            }
            $age = $a_year;
        }
    }

    return $age;

}


sub deleteLienholderMortgagee
{
   my $ENGINE = shift;
   my $claimID = shift;
   my $commonStatID = shift;
   my $error = $ENGINE->{'error'};

   my $locationQuery = $ENGINE->{'DBH'}->prepare
      ('SELECT G.CLAIM_STATUS,
               L.PARTY_ID
          FROM CLAIMDB.CLM_LOCATION AS L
    INNER JOIN CLAIMDB.CLM_GENERAL AS G
            ON G.CLAIM_ID = L.CLAIM_ID
         WHERE L.CLAIM_ID = ?
           AND L.CLM_COMMON_STAT_ID = ?
           AND L.DATE_DELETED = \'9999-01-01 01:00:00.000000\'
           AND L.LOC_TYPE IN (\'LH\',\'MG\')') || $error->($ENGINE,'Location query prepare failed: '.$ENGINE->{'DBH'}->errstr);

   $locationQuery->execute($claimID,$commonStatID) || $error->($ENGINE,'Location query execute failed: '.$ENGINE->{'DBH'}->errstr);

   my $locationResults = $locationQuery->fetchall_arrayref({});

   for my $l (@$locationResults)
   {
      if (defined($l->{'PARTY_ID'} && $l->{'PARTY_ID'} > 0))
      {
         if ($l->{'CLAIM_STATUS'} eq 'P')
         {
            # DELETE CLM_LOCATION ROW
            my $locationDelete = $ENGINE->{'DBH'}->prepare
               ('DELETE FROM CLAIMDB.CLM_LOCATION
                  WHERE CLM_COMMON_STAT_ID = ?
                    AND CLAIM_ID = ?
                    AND PARTY_ID = ?')  || $error->($ENGINE,'Location Delete query prepare failed: '.$ENGINE->{'DBH'}->errstr);

            $locationDelete->execute($commonStatID,$claimID,$l->{'PARTY_ID'}) || $error->($ENGINE,'Location Delete query execute failed: '.$ENGINE->{'DBH'}->errstr);

            # DELETE CLM_PARTIES ROW
            my $partiesDelete = $ENGINE->{'DBH'}->prepare
               ('DELETE FROM CLAIMDB.CLM_PARTIES
                  WHERE CLAIM_ID = ?
                    AND PARTY_ID = ?')  || $error->($ENGINE,'Parties Delete query prepare failed: '.$ENGINE->{'DBH'}->errstr);

            $partiesDelete->execute($claimID,$l->{'PARTY_ID'}) || $error->($ENGINE,'Parties Delete query execute failed: '.$ENGINE->{'DBH'}->errstr);

            # DELETE CLM_PARTY_ROLES ROW
            my $partyRolesDelete = $ENGINE->{'DBH'}->prepare
               ('DELETE FROM CLAIMDB.CLM_PARTY_ROLES
                  WHERE CLAIM_ID = ?
                    AND PARTY_ID = ?
                    AND ROLE IN (\'LH\',\'MG\')')  || $error->($ENGINE,'Party Roles Delete query prepare failed: '.$ENGINE->{'DBH'}->errstr);

            $partyRolesDelete->execute($claimID,$l->{'PARTY_ID'}) || $error->($ENGINE,'Party Roles Delete query execute failed: '.$ENGINE->{'DBH'}->errstr);
         }
         else
         {
            # SET DATE_DELETED ON CLM_LOCATION ROW
            my $locationUpdate = $ENGINE->{'DBH'}->prepare
               ('UPDATE CLAIMDB.CLM_LOCATION
                    SET DATE_DELETED = CURRENT TIMESTAMP
                  WHERE CLM_COMMON_STAT_ID = ?
                    AND CLAIM_ID = ?
                    AND PARTY_ID = ?')  || $error->($ENGINE,'Location Update query prepare failed: '.$ENGINE->{'DBH'}->errstr);

            $locationUpdate->execute($commonStatID,$claimID,$l->{'PARTY_ID'}) || $error->($ENGINE,'Location Update query execute failed: '.$ENGINE->{'DBH'}->errstr);

            # SET DATE_DELETED ON CLM_PARTIES ROW
            my $partiesUpdate = $ENGINE->{'DBH'}->prepare
               ('UPDATE CLAIMDB.CLM_PARTIES
                    SET DATE_DELETED = CURRENT TIMESTAMP
                  WHERE CLAIM_ID = ?
                    AND PARTY_ID = ?')  || $error->($ENGINE,'Parties Update query prepare failed: '.$ENGINE->{'DBH'}->errstr);

            $partiesUpdate->execute($claimID,$l->{'PARTY_ID'}) || $error->($ENGINE,'Parties Update query execute failed: '.$ENGINE->{'DBH'}->errstr);

            # SET DATE_DELETED ON CLM_PARTY_ROLES ROW
            my $partyRolesUpdate = $ENGINE->{'DBH'}->prepare
               ('UPDATE CLAIMDB.CLM_PARTY_ROLES
                    SET DATE_DELETED = CURRENT TIMESTAMP
                  WHERE CLAIM_ID = ?
                    AND PARTY_ID = ?
                    AND ROLE IN (\'LH\',\'MG\')')  || $error->($ENGINE,'Party Roles Update query prepare failed: '.$ENGINE->{'DBH'}->errstr);

            $partyRolesUpdate->execute($claimID,$l->{'PARTY_ID'}) || $error->($ENGINE,'Party Roles Update query execute failed: '.$ENGINE->{'DBH'}->errstr);
         }
      }
   }
}

sub dirList
{
    my $ENGINE = shift;
    my $filePath = shift;
    my $error = $ENGINE->{'error'};
#    local *TEMPLATEDIR;
    opendir(TEMPLATEDIR, $filePath) || $error->($ENGINE,'Can not open directory '.$filePath);
    my @fileNames = sort {$a cmp $b } readdir(TEMPLATEDIR);
    closedir(TEMPLATEDIR);
    my @dirArray = ();

    foreach my $name (@fileNames)
    {
        $name =~ s/&/&amp;/g;
        $name =~ s/</&lt;/g;
#        get info on the file
        my @statInfo = stat("$filePath/$name");
#        is it a directory
        if(-d(_))
        {
#            print("$filePath/$name (dir)"."\n");
            if($name ne "." && $name ne "..")
            {
#              print("$filePath/$name (dir)"."\n");
#              print("$name (dir)"."\n");
#              push(@dirArray,$name);

#              dirList("$filePath/$name",$ENGINE);
            }
        }
        else
        {
#            print("$filePath/$name"."\n");
#            print($name."\n");
            push(@dirArray,$name);
        }
    }
    return @dirArray;
#    print 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxarray '."@dirArray";
#    print join("\n",@fileNames)."\n";
#    print 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'.$fileNames[2];
}

sub createLabelHelp
{
    my $args = shift;
    my $label = $args->{'label'};
    my $help = $args->{'help'};
    my $data = $args->{'data'} || '';
    my $id = $args->{'id'};
    my $div_info = $args->{'div_info'}|| '';
    my $help_expand = $args->{'help_expand'}||'';
    my $help_hide = 'hidden';
    if($help_expand)
    {$help_hide = '';}

    my $html = <<EOF;
<label>
    <span>$label</span>
    <a class="help" onclick="toggleHelp(\'$id\')">&nbsp;&nbsp;</a>
</label>
<div class="helptext $help_hide" id="$id">
    $help
</div>
<div $div_info >$data</div>
EOF

}

sub updateLastSave
{
    my $ENGINE = shift;
#    die 'we\'ve been hit!';
    my $lastSaveUpdateSTH = $ENGINE->{'DBH'}->prepare('SELECT LAST_SAVE FROM FINAL TABLE (UPDATE CLAIMDB.CLM_GENERAL SET LAST_SAVE = CURRENT TIMESTAMP
WHERE CLAIM_ID = ?)') || $ENGINE->{'DBH'}->errstr;

    # Set the last save time for this successful save
    $lastSaveUpdateSTH->execute($ENGINE->{'claimGeneral'}->{'CLAIM_ID'}) || $ENGINE->{'DBH'}->errstr;
    my $lastsaveResult = $lastSaveUpdateSTH->fetchrow_hashref() || $ENGINE->{'DBH'}->errstr;

    $ENGINE->{'claimGeneral'}->{'LAST_SAVE'} = $lastsaveResult->{'LAST_SAVE'};
}

sub setFlagshipResend
{
        my $type = shift;
    my $data = shift;
        my $ENGINE = shift;
        my $error = $ENGINE->{'error'};
    my $claim_id = $ENGINE->{'claimGeneral'}->{'CLAIM_ID'};
    my $party_id = $data->{'PARTY_ID'};
    my $status = 'RS';

    my $origFSReport = $ENGINE->{'CGI'}->param('orig_FSreport'.$data->{'PARTY_ID'});
    my $origFSMedical = $ENGINE->{'CGI'}->param('orig_FSmedical'.$data->{'PARTY_ID'});

    my $partyQuery = $ENGINE->{'DBH'}->prepare('SELECT FS_REFERRAL_STATUS, FS_READY_REPORT, FS_MEDICAL_READY FROM CLAIMDB.CLM_PARTIES
WHERE PARTY_ID = ?  AND CLAIM_ID = ? AND DATE_DELETED = \'9999-01-01 01:00:00.000000\'') || error($ENGINE,'Party query prepare failed: '.$ENGINE->{'DBH'}->errstr);

    $partyQuery->execute($party_id,$claim_id) || error($ENGINE,'Party query execute failed: '.$ENGINE->{'DBH'}->errstr);
    my $partyResults = $partyQuery->fetchall_arrayref({});

    my $FS_Referral_Status = '';
    for my $p (@$partyResults)
    {
        $FS_Referral_Status = $p->{'FS_REFERRAL_STATUS'};
        if(defined($p->{'FS_REFERRAL_STATUS'}) && $p->{'FS_REFERRAL_STATUS'} eq 'CM')
        {
            if((defined($origFSReport) && $origFSReport eq 'Y') && (defined($origFSMedical) && $origFSMedical eq 'Y') )
            {
                $FS_Referral_Status = 'CM';
            }
            elsif((defined($origFSReport) && $origFSReport eq 'Y') && (defined($origFSMedical) && $origFSMedical eq 'N') )
            {
                $FS_Referral_Status = 'CL';
            }
            elsif((defined($origFSMedical) && $origFSMedical eq 'Y') && (defined($origFSReport) && $origFSReport eq 'N') )
            {
                $FS_Referral_Status = 'CD';
            }
        }
    }

    my $partyUpdate = $ENGINE->{'DBH'}->prepare('UPDATE CLAIMDB.CLM_PARTIES SET
                                                    FLAGSHIP_STATUS = ?,
                                                    FS_REFERRAL_STATUS = ?
                                                 WHERE PARTY_ID = ? AND CLAIM_ID = ?') || $error->($ENGINE);
    $partyUpdate->execute($status,$FS_Referral_Status,$party_id,$claim_id) || $error->($ENGINE);

    return;
}

sub getCovPartyMiscEndor
{
    my $ENGINE = shift;
    my $claimid = $ENGINE->{'claimGeneral'}->{'CLAIM_ID'};
    my $policyNum = $ENGINE->{'claimGeneral'}->{'POLICY_NUMBER'};
    my $UserType = $ENGINE->{'AUTH'}->{'IMTOnline_UserType'};

    my ($package, $filename, $line) = caller;
    my $covs_only = 0;
    if($filename eq 'Claims_Misc.pm')
    {$covs_only = 1;}

    my $covHTML = '';
    my $modalCovHTML = '';
    my $polCovHTML = '';
    my $endorsements = '';
    my $partiesData = '';

    my $lossDate = $ENGINE->{'claimGeneral'}->{'LOSS_DATE_TIME'};
    my $DBLossDate = substr($lossDate,0,10);

    my $prefix = '';
    my $prefix2 = '';
    # Also look for 300 & 330 to add the policy prefix.
    if($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /300|301|302|330|331|332/)
      { $policyNum =~ s/^12|^14|^26|^40|^48//g; $prefix = ' OR E.POLICY_PREFIX = \'LB\''; }


    my %IMTLineCodeScreen = getIMTLineCodeScreen();
    my $screen = $IMTLineCodeScreen{$ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'}};


    if($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /110|111|112|113/)
    {
        if($ENGINE->{'claimGeneral'}->{'POL_EFF_DATE'} lt '1997-09-01')
          { $prefix = ' OR E.POLICY_PREFIX = \'HO\''; }
        elsif($ENGINE->{'claimGeneral'}->{'POL_EFF_DATE'} lt '2004-01-01')
          { $prefix = ' OR E.POLICY_PREFIX = \'HO01\''; }
        else
          { $prefix = ' OR E.POLICY_PREFIX = \'HO02\''; }
    }
    elsif($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /120/)
    {
        if($ENGINE->{'claimGeneral'}->{'POL_EFF_DATE'} lt '1997-09-01')
          { $prefix = ' OR E.POLICY_PREFIX = \'MH\''; }
        elsif($ENGINE->{'claimGeneral'}->{'POL_EFF_DATE'} lt '2004-01-01')
          { $prefix = ' OR E.POLICY_PREFIX = \'MH01\''; }
        else
          { $prefix = ' OR E.POLICY_PREFIX = \'MH02\''; }
    }
    elsif($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /100/)
    { $prefix = ' OR E.POLICY_PREFIX = \'DP01\''; }
    elsif($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /575/ && $ENGINE->{'claimGeneral'}->{'SYSTEM_IND'} eq 'N')
    { $prefix = ' OR E.POLICY_PREFIX = \'IBO\''; $prefix2 = ' OR E.LOB = \'IBO\'';}

    my %stateHash = getStateAbbrevNumeric();
    my $covState = $stateHash{$ENGINE->{'claimGeneral'}->{'POLICY_STATE'}} || '';

    my %policyPrefix;
    if($ENGINE->{'claimGeneral'}->{'COMPANY_NO'} eq '01')
     { %policyPrefix = getIMTPolicyPrefix(); }
    else
     { %policyPrefix = getWadenaPolicyPrefix(); }

    my $numRateState = '(';
    my $alphaRateState = '(';
    if($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} eq '015' && $ENGINE->{'claimGeneral'}->{'SYSTEM_IND'} eq 'N')
    {
        my $rateStateQuery = $ENGINE->{'DBH'}->prepare('SELECT DISTINCT RATE_STATE FROM CLAIMDB.CLM_COMMON_STAT
WHERE CLAIM_ID = ?  AND DATE_DELETED = \'9999-01-01 01:00:00.000000\'') || error($ENGINE,'Common Stat query prepare failed: '.$ENGINE->{'DBH'}->errstr);

        $rateStateQuery->execute($ENGINE->{'claimGeneral'}->{'CLAIM_ID'}) || error($ENGINE,'Common Stat query execute failed: '.$ENGINE->{'DBH'}->errstr);
        my $rateStateresults = $rateStateQuery->fetchall_arrayref({});

        if(@$rateStateresults > 0)
        {
                for my $s (@$rateStateresults)
                {
                    my $state = $s->{'RATE_STATE'};
                    $alphaRateState .= "'$state',";
                    my $num = $stateHash{$state};
                    $numRateState .= "'$num',";
                }
        }
        else
        {
            $numRateState .= "'$covState',";
            my $alpState = $ENGINE->{'claimGeneral'}->{'POLICY_STATE'};
            $alphaRateState .= "'$alpState',";
        }
    }
    else
    {
        $numRateState .= "'$covState',";
        my $alpState = $ENGINE->{'claimGeneral'}->{'POLICY_STATE'};
        $alphaRateState .= "'$alpState',";
    }
    chop($alphaRateState);
    chop($numRateState);
    $alphaRateState .= ')';
    $numRateState .= ')';

                                                          # E.EFF_DATE <= ? AND E.OBS_DATE > ?
    my $covsQuery = '';
    if($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /015|112|113|120|575|810|814|816/ && $ENGINE->{'claimGeneral'}->{'SYSTEM_IND'} eq 'N')
    {
    $covsQuery = $ENGINE->{'DBH'}->prepare('SELECT CE.COVERAGE,CE.CLASS,CE.SUB_COV,CE.COVERAGE_ID,CE.COV_OR_ENDORSE,CE.IMT_COVERAGE, CE.LOCATION_NO, CE.UNIT_NO,
CE.LIMIT1,CE.LIMIT2,CE.EDITION_DATE,CE.ITEM_ID,CE.EDITION_DATE AS ED_DATE,E.DESC_DISPLAY AS END_DESCRIPTION,E.EDITION_DATE, D.DEDUCTIBLE, D.TYPE_OF_DEDUCTIBLE, COV.IMT_DESCRIPT
FROM CLAIMDB.CLM_COVS_ENDORSES AS CE
LEFT JOIN GENSUPDB.NEW_ENDORSEMENTS AS E ON E.ENDOR_NUMBER = CE.COVERAGE AND (E.LOB = ?'.$prefix2.') AND E.EDITION_DATE = CE.EDITION_DATE AND (E.STATE = ? OR E.STATE= ?)
LEFT JOIN CLAIMDB.CLM_DEDUCTIBLES AS D ON CE.COVERAGE_ID = D.COVERAGE_ID AND D.DATE_DELETED = \'9999-01-01 01:00:00.000000\'
LEFT JOIN GENSUPDB.COVERAGES AS COV ON (COV.IMT_COVERAGE = CE.COVERAGE OR CE.COVERAGE = \'\') AND CE.IMT_COVERAGE = COV.IMT_COV_CODE AND COV.EFFECTIVE_DATE <= ? AND COV.OBSOLETE_DATE > ?
AND (COV.STATE IN '.$numRateState.' OR COV.STATE IN '.$alphaRateState.') AND COV.COMPANY_CODE = ? AND COV.IMT_LINE_CODE = ?
WHERE CE.CLAIM_ID = ?  AND CE.DATE_DELETED = \'9999-01-01 01:00:00.000000\'') || error($ENGINE,'Covs endorses query prepare failed: '.$ENGINE->{'DBH'}->errstr);
    }
    else
    {
    $covsQuery = $ENGINE->{'DBH'}->prepare('SELECT CE.COVERAGE,CE.CLASS,CE.SUB_COV,CE.COVERAGE_ID,CE.COV_OR_ENDORSE,CE.IMT_COVERAGE, CE.LOCATION_NO, CE.UNIT_NO,
CE.LIMIT1,CE.LIMIT2,CE.EDITION_DATE,CE.ITEM_ID,CE.EDITION_DATE AS ED_DATE,E.DESCRIPTION AS END_DESCRIPTION,E.EDITION_DATE, D.DEDUCTIBLE, D.TYPE_OF_DEDUCTIBLE, COV.IMT_DESCRIPT
FROM CLAIMDB.CLM_COVS_ENDORSES AS CE
LEFT JOIN GENSUPDB.ENDORSEMENTS AS E ON E.FORMNUMBER = CE.COVERAGE AND (E.POLICY_PREFIX = ?'.$prefix.') AND E.EDITION_DATE = CE.EDITION_DATE AND (E.STATE = ? OR E.STATE= ?)
LEFT JOIN CLAIMDB.CLM_DEDUCTIBLES AS D ON CE.COVERAGE_ID = D.COVERAGE_ID AND D.DATE_DELETED = \'9999-01-01 01:00:00.000000\'
LEFT JOIN GENSUPDB.COVERAGES AS COV ON (COV.IMT_COVERAGE = CE.COVERAGE OR CE.COVERAGE = \'\') AND CE.IMT_COVERAGE = COV.IMT_COV_CODE AND COV.EFFECTIVE_DATE <= ? AND COV.OBSOLETE_DATE > ?
AND (COV.STATE IN '.$numRateState.' OR COV.STATE IN '.$alphaRateState.') AND COV.COMPANY_CODE = ? AND COV.IMT_LINE_CODE = ?
WHERE CE.CLAIM_ID = ?  AND CE.DATE_DELETED = \'9999-01-01 01:00:00.000000\'') || error($ENGINE,'Covs endorses query prepare failed: '.$ENGINE->{'DBH'}->errstr);
    }
    #print "policy effective date: ".$ENGINE->{'claimGeneral'}->{'POL_EFF_DATE'};
    #print '15238904712390847 policy prefix is: '.$policyPrefix{$ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'}}.$prefix;
    $covsQuery->execute($policyPrefix{$ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'}},
$ENGINE->{'claimGeneral'}->{'POLICY_STATE'},
$covState,
$ENGINE->{'claimGeneral'}->{'POL_EFF_DATE'},
$ENGINE->{'claimGeneral'}->{'POL_EFF_DATE'},
$ENGINE->{'claimGeneral'}->{'COMPANY_NO'},
$ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'},
$claimid) || error($ENGINE,'Flag query execute failed: '.$ENGINE->{'DBH'}->errstr);
    my $covsResults = $covsQuery->fetchall_arrayref({});

    my @covs = sort({$a->{'COVERAGE'} cmp $b->{'COVERAGE'}} @$covsResults);
    #my @covs = sort({$a->{'IMT_DESCRIPT'} cmp $b->{'IMT_DESCRIPT'}} @$covsResults);


    my %formsFolders;
    if($ENGINE->{'claimGeneral'}->{'COMPANY_NO'} eq '01' && $ENGINE->{'claimGeneral'}->{'SYSTEM_IND'} ne 'N')
     { %formsFolders = getIMTFormsFolder(); }
    elsif($ENGINE->{'claimGeneral'}->{'COMPANY_NO'} eq '01' && $ENGINE->{'claimGeneral'}->{'SYSTEM_IND'} eq 'N' && $ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /015/)
     { %formsFolders = getWadenaISOFormsFolder(); }
    elsif($ENGINE->{'claimGeneral'}->{'COMPANY_NO'} eq '02' && $ENGINE->{'claimGeneral'}->{'SYSTEM_IND'} eq 'N'  && $ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /575|015/)
     { %formsFolders = getWadenaISOFormsFolder(); }
    else
     { %formsFolders = getWadenaFormsFolder(); }
    my $formsFolder = $formsFolders{$ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'}};

    # This loop looks for endorsement descriptions that are not defined.  These are endorsements that are for a diffenent state then
    #the policy state.  Commercial lines could have endorsements that are for a different state the the policy state.
    my $endQuery = '';
    if($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /015|112|113|120|575|810|814|816/)
    {
        $endQuery = $ENGINE->{'DBH'}->prepare('SELECT DISTINCT E.ENDOR_NUMBER, E.DESC_DISPLAY AS DESCRIPTION FROM GENSUPDB.NEW_ENDORSEMENTS AS E
WHERE E.ENDOR_NUMBER = ? AND (E.LOB = ?'.$prefix2.')') || error($ENGINE,'GENSUPDB ENDORSEMENT query prepare failed: '.$ENGINE->{'DBH'}->errstr);
    }
    else
    {
        $endQuery = $ENGINE->{'DBH'}->prepare('SELECT DISTINCT E.FORMNUMBER, E.DESCRIPTION FROM GENSUPDB.ENDORSEMENTS AS E
WHERE E.FORMNUMBER = ? AND (E.POLICY_PREFIX = ?'.$prefix.')') || error($ENGINE,'GENSUPDB ENDORSEMENT query prepare failed: '.$ENGINE->{'DBH'}->errstr);
    }
    for my $cov (@covs)
    {
#        if(defined($c->{'END_DESCRIPTION'}) && $c->{'END_DESCRIPTION'} eq '')
        if(!defined($cov->{'END_DESCRIPTION'}))
        {
            $endQuery->execute($cov->{'COVERAGE'},$policyPrefix{$ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'}}) || error($ENGINE,'GENSUPDB ENDORSEMENT query execute failed: '.$ENGINE->{'DBH'}->errstr);
            my $endResults = $endQuery->fetchall_arrayref({});
            $cov->{'END_DESCRIPTION'} = $endResults->[0]->{'DESCRIPTION'};
            if(defined($cov->{'ED_DATE'}))
            {
                my $year = substr($cov->{'ED_DATE'},0,4);
                my $month = substr($cov->{'ED_DATE'},5,2);
#                if(substr($ENGINE->{'claimGeneral'}->{'POLICY_NUMBER'},0,2) eq 'AP')
#                {
#                    if(defined($cov->{'COVERAGE'}) && $cov->{'COVERAGE'} =~ /PP0174|PP1367/ && $year eq '2011')
#                    { $month = '99'; }
#                }

                my $pathYM = File::Spec->catpath('d:',
                                                 '/imtonline_media/pdf_dump/'.$formsFolder.'/',
                                                 $cov->{'COVERAGE'}.$year.$month.'.pdf');
                my $pathY  = File::Spec->catpath('d:',
                                                 '/imtonline_media/pdf_dump/'.$formsFolder.'/',
                                                 $cov->{'COVERAGE'}.$year.'.pdf');

                if(-e $pathYM)
                  { $cov->{'COVERAGE'} = '<a onclick="this.target=\'_blank\'" href="../../getfile.pl?file='.$formsFolder.'/'.$cov->{'COVERAGE'}.$year.$month.'">'.$cov->{'COVERAGE'}.'</a>'; }
                elsif(-e $pathY)
                  { $cov->{'COVERAGE'} = '<a onclick="this.target=\'_blank\'" href="../../getfile.pl?file='.$formsFolder.'/'.$cov->{'COVERAGE'}.$year.'">'.$cov->{'COVERAGE'}.'</a>'; }
            }
        }
    }

    my %coverages = ();
    my %endorsementForms = ();
    my %typeOfDeducts = getTypeOfDeducts();
    my $i = 0;
    my $percentSign = '';
    my $dollarSign = '';
    my %subCovDesc = getSubCovDesc();
    my %CASSymbolDef = getCASSymbolDesc();

    my $CASSymbolQuery = '';
    my $FarmClassQuery = '';
    my %coverageIDs = ();
    my $numEndorsements = 0;
    my $cas_help_modals = '';
    if($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /015|804/)
    {
        $CASSymbolQuery = $ENGINE->{'DBH'}->prepare('SELECT COVERAGE_SYMBOL FROM CLAIMDB.CLM_CAS_SYMBOLS
WHERE COVERAGE_ID = ?  AND DATE_DELETED = \'9999-01-01 01:00:00.000000\'') || error($ENGINE,'CAS symbols query prepare failed: '.$ENGINE->{'DBH'}->errstr);
    }
    elsif($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /300|301|302|330|331|332/)
    {
        $FarmClassQuery = $ENGINE->{'DBH'}->prepare('SELECT DESCRIPT FROM FARMDB.CLASS_CODES
WHERE CLASS = ? AND EFF_DATE <= ? AND EXPIRE >= ?') || error($ENGINE,'Farm class query prepare failed: '.$ENGINE->{'DBH'}->errstr);
    }
    for my $c (@covs)
    {
        if($c->{'COV_OR_ENDORSE'} eq 'E')
        {
            $numEndorsements++;
            if(!defined($endorsementForms{$c->{'COVERAGE'}}))
            {
                my $class = '';
                if($i%2 == 0)
                  { $class = ' class="altRow" '; }
                $i++;
                $endorsementForms{$c->{'COVERAGE'}} = 1;
                if($c->{'COVERAGE'} =~ /HO0420|DP0411/)
                  { $dollarSign = ''; $percentSign = '%'; }
                else
                  { $dollarSign = '$'; $percentSign = ''; }
                if(defined($c->{'EDITION_DATE'}))
                {
                    my $year = substr($c->{'EDITION_DATE'},0,4);
                    my $month = substr($c->{'EDITION_DATE'},5,2);
#                    if(substr($ENGINE->{'claimGeneral'}->{'POLICY_NUMBER'},0,2) eq 'AP')
#                    {
#                        if(defined($c->{'COVERAGE'}) && $c->{'COVERAGE'} =~ /PP0174|PP1367/ && $year eq '2011')
#                        { $month = '99'; }
#                    }

                my $pathYM = File::Spec->catpath('d:',
                                                 '/imtonline_media/pdf_dump/'.$formsFolder.'/',
                                                 $c->{'COVERAGE'}.$year.$month.'.pdf');
                my $pathY  = File::Spec->catpath('d:',
                                                 '/imtonline_media/pdf_dump/'.$formsFolder.'/',
                                                 $c->{'COVERAGE'}.$year.'.pdf');

                    if(-e $pathYM)
                      { $c->{'COVERAGE'} = '<a onclick="this.target=\'_blank\'" href="../../getfile.pl?file='.$formsFolder.'/'.$c->{'COVERAGE'}.$year.$month.'">'.$c->{'COVERAGE'}.'</a>'; }
                    elsif(-e $pathY)
                      { $c->{'COVERAGE'} = '<a onclick="this.target=\'_blank\'" href="../../getfile.pl?file='.$formsFolder.'/'.$c->{'COVERAGE'}.$year.'">'.$c->{'COVERAGE'}.'</a>'; }
                }
                my $limit = '';
                if(defined($c->{'LIMIT1'}) && $c->{'LIMIT1'} > 0)
                  { $limit .= $dollarSign.CommaFormatted($c->{'LIMIT1'}).$percentSign; }
                if(defined($c->{'LIMIT1'}) && $c->{'LIMIT1'} > 0 && defined($c->{'LIMIT2'}) && $c->{'LIMIT2'} > 0)
                  { $limit .= '/'; }
                if(defined($c->{'LIMIT2'}) && $c->{'LIMIT2'} > 0)
                  { $limit .= $dollarSign.CommaFormatted($c->{'LIMIT2'}).$percentSign; }

                if($limit eq ''){
                    $limit = '&nbsp';
                }

                my $editionDate = '';
                if(defined($c->{'EDITION_DATE'}) && $c->{'EDITION_DATE'} ne '9999-01-01')
                  { $editionDate .= substr($c->{'EDITION_DATE'},5,2).'/'.substr($c->{'EDITION_DATE'},8,2).'/'.substr($c->{'EDITION_DATE'},0,4); }
                elsif(defined($c->{'ED_DATE'}) && $c->{'ED_DATE'} ne '9999-01-01')
                  { $editionDate .= substr($c->{'ED_DATE'},5,2).'/'.substr($c->{'ED_DATE'},8,2).'/'.substr($c->{'ED_DATE'},0,4); }
                else
                  { $editionDate = 'Unknown'; }
                #this if is used to override the endoresement description of CA9944.  SVC 120675
                if($c->{'COVERAGE'} eq 'CA9944')
                { $c->{'END_DESCRIPTION'} = 'Loss Payable Clause'; }
                $endorsements .= '<li'.$class.'><span class="first">'.$c->{'COVERAGE'}.'</span><span class="second">'.$editionDate.'</span><span class="third">'.$limit.'</span><span class="fourth">'.($c->{'END_DESCRIPTION'}||$c->{'DESC_DISPLAY'}||'Description not found').'</span></li>';
            }
        }
        elsif($c->{'COV_OR_ENDORSE'} eq 'C' && !($c->{'IMT_COVERAGE'} =~ /50/)
                && !($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /300|301|302|330|331|332/ && $c->{'IMT_COVERAGE'} =~ /51/))
        {
            my $loc = $c->{'LOCATION_NO'} || 0;
            my $unit = $c->{'UNIT_NO'} || 0;
            my $CASSymbols = '';
            my $FarmClassDesc = '';
            if($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /015|804/)
            {
                $CASSymbolQuery->execute($c->{'COVERAGE_ID'}) || error($ENGINE,'CAS symbols query execute failed: '.$ENGINE->{'DBH'}->errstr);
                my $CASresults = $CASSymbolQuery->fetchall_arrayref({});
                for my $symbol (@$CASresults)
                {
                    my $help_words = $CASSymbolDef{$symbol->{'COVERAGE_SYMBOL'}}||'';
                    $CASSymbols .= <<EOF;
<span class="left">$symbol->{'COVERAGE_SYMBOL'}</span>
<a class="help" href="#" data-toggle="modal" data-target="#cas$symbol->{'COVERAGE_SYMBOL'}$c->{'COVERAGE_ID'}">&nbsp;&nbsp;</a>
EOF

                    $cas_help_modals .= <<EOF;
<div class="modal" id="cas$symbol->{'COVERAGE_SYMBOL'}$c->{'COVERAGE_ID'}" tabindex="-1" role="dialog" aria-labelledby="cas$symbol->{'COVERAGE_SYMBOL'}$c->{'COVERAGE_ID'}" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog" id="CASSymbolsModal">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            </div>
            <div class="modal-body">
                <p>$help_words</p>
            </div>
        </div>
    </div>
</div>
EOF

                }
                chop($CASSymbols);
            }
            if($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /300|301|302|330|331|332/ && $c->{'IMT_COVERAGE'} =~ /06/)
            {
                $FarmClassQuery->execute($c->{'CLASS'},$DBLossDate,$DBLossDate) || error($ENGINE,'Farm class query execute failed: '.$ENGINE->{'DBH'}->errstr);
                my $FarmClassresults = $FarmClassQuery->fetchall_arrayref({});
                for my $desc (@$FarmClassresults)
                  { $FarmClassDesc = '<br /><span style="padding-left:2em">'.$desc->{'DESCRIPT'}.'</span>'; }
            }

            # For certain coverage/line code combinations, we actually change the key to combine the descriptions
            my $key = $c->{'IMT_COVERAGE'}.'_'.$c->{'COVERAGE_ID'};
            if($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /100/ && $c->{'IMT_COVERAGE'} =~ /01|02|03|04|10/)
              { $key = $c->{'IMT_COVERAGE'}.'_'.$c->{'COVERAGE'}; }
            elsif($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /105/ && $c->{'IMT_COVERAGE'} =~ /01|02|03|06|10|14|15|13|12/)
              { $key = $c->{'IMT_COVERAGE'}.'_'.$c->{'COVERAGE'}; }
            elsif($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /300|301|302|330|331|332/ && $c->{'IMT_COVERAGE'} =~ /06/)
              { $key = $c->{'IMT_COVERAGE'}; }

            # If the coverage for the right location/unit isn't defined, add it.
            if(!defined($coverages{$loc}->{$unit}->{$key}))
              { $coverages{$loc}->{$unit}->{$key} = {'LIMIT1'=>$c->{'LIMIT1'},'LIMIT2'=>$c->{'LIMIT2'},'IMT_DESCRIPT'=>$c->{'IMT_DESCRIPT'},'CAS_SYMBOLS'=>$CASSymbols,'IMT_COVERAGE'=>$c->{'IMT_COVERAGE'},'COVERAGE'=>$c->{'COVERAGE'},'SUB_COV'=>$c->{'SUB_COV'}}; }
            # Make sure not to add the sub_cov or class desc twice for the same coverage row.
            if(!defined($coverageIDs{$c->{'COVERAGE_ID'}}))
            {
                # For dwelling fire, add the sub coverage descriptions to the coverages
                if($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /100/ && $c->{'IMT_COVERAGE'} =~ /01|02|03|04|10/ && $c->{'SUB_COV'} =~ /421|422/ && defined($subCovDesc{$c->{'SUB_COV'}}))
                  { $coverages{$loc}->{$unit}->{$key}->{'IMT_DESCRIPT'} .= '<br /><span style="padding-left:2em">'.$subCovDesc{$c->{'SUB_COV'}}.'</span>'; }
               # # For commercial, add the sub coverage descriptions to the coverages
               # if($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /105/ && $c->{'IMT_COVERAGE'} =~ /01|02|03|06|10/ && defined($subCovDesc{$c->{'SUB_COV'}}))
               #   { $coverages{$loc}->{$unit}->{$key}->{'IMT_DESCRIPT'} .= '<br /><span style="padding-left:2em">'.$subCovDesc{$c->{'SUB_COV'}}.'</span>'; }
                # For farm liability add the class descriptions to the coverage.
                if($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /300|301|302|330|331|332/ && $c->{'IMT_COVERAGE'} =~ /06/)
                  { $coverages{$loc}->{$unit}->{$key}->{'IMT_DESCRIPT'} .= $FarmClassDesc; }
                $coverageIDs{$c->{'COVERAGE_ID'}} = 1;
            }
            if(defined($c->{'DEDUCTIBLE'}) && defined($c->{'TYPE_OF_DEDUCTIBLE'}))
            {
                push(@{$coverages{$loc}->{$unit}->{$key}->{'DEDUCTIBLES'}},'$'.CommaFormatted($c->{'DEDUCTIBLE'}).' '.$typeOfDeducts{$c->{'TYPE_OF_DEDUCTIBLE'}});
                if(!defined($coverages{$loc}->{$unit}->{$key}->{'DEDUCTIBLES'}))
                  {  $coverages{$loc}->{$unit}->{$key}->{'DEDUCTIBLES'} = ['test1']; }
            }
        }
    }

    my $endMessage = '';
    if($ENGINE->{'claimGeneral'}->{'INITIATION_POINT'} eq 'CV' && $numEndorsements == 16)
    { $endMessage = '<span style="color:red;font-weight:bold">More Endorsements May Exist. Check Policy.</span>'; }
    $endorsements = <<EOF;
<fieldset>
    <h2>Endorsements</h2>
<span class="info">Forms obsolete prior to April 1, 2008 may be unavailable for online viewing.</span>
    <ul class="endorsements">
        <li><span class="head first">Form</span><span class="head second">Ed Date</span><span class="head third">Limits</span><span class="head fourth">Description</span></li>
        $endorsements
    </ul>
$endMessage
</fieldset>
EOF

    if($screen ne 'WC')
    {

        my $itemDescQuery = getCoveredItemDescQuery($ENGINE);
        my %policyFormDesc = (1=>'Form 1 - Basic',
2=>'Form 2 - Broad',
3=>'Form 3 - Special',
4=>'Form 4 - Contents Broad',
5=>'Form 5 - Comprehensive',
6=>'Form 6 - Unit-Owners');
        my %itemDescription = ();
        my $unitNum = '';
        my $limit1 = 0;
        my $limit2 = 0;
        my $unitDesc = ' Unit ';
        my $HR = '';
        if($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /105|575|580/)
        { $unitDesc = ' Building '; }
        elsif($screen eq 'VEH')
        {
            $unitDesc = ' Vehicle ';
            if ($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} eq '052')
            {$unitDesc = ' Boat ';}
            if ($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} eq '030')
            {$unitDesc = ' Unit ';}
        }

        my $coverage_html;
        for my $loc (sort {$a <=> $b} keys %coverages)
        {   my @loc_covs;
            for my $unit (sort {$a <=> $b} keys %{$coverages{$loc}})
            {
                my $i = 1;
                $unitNum = '';
                if(!defined($itemDescription{$loc.'_'.$unit}))
                  { $itemDescription{$loc.'_'.$unit} = getCoveredItemDesc($ENGINE,$loc,$unit,$itemDescQuery); }
                if($unit > 0)
                  { $unitNum = $unitDesc.$unit; }
                if($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /810|814|816/)
                { push(@loc_covs,'<ul class="leftlabel_twocol"><li class="coverage_details"><span>'.$itemDescription{$loc.'_'.$unit}.'</span></li>');}
                else
                { push(@loc_covs,'<ul class="leftlabel_twocol"><li class="coverage_details"><span>'.$itemDescription{$loc.'_'.$unit}.$unitNum.'</span></li>'); }
                if($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /100|110|111|112|113|120/)
                {
                    $i++;
                    my $cov_line;
                    $cov_line->{'desc'} = $policyFormDesc{$ENGINE->{'claimGeneral'}->{'POLICY_FORM'}};
                    push(@loc_covs,$cov_line);
                }
#                $first_line .= '<li>';
                for my $cov (sort keys %{$coverages{$loc}->{$unit}})
                {
                    if($coverages{$loc}->{$unit}->{$cov}->{'IMT_DESCRIPT'})
                    {
                        my $class = '';
#                        if($i%2 == 0)
#                          { $class = ' class="altRow" '; }
#                        $i++;
                        my $cov_line;
                        my $desc = '';
                        $cov_line->{'text'} = $coverages{$loc}->{$unit}->{$cov}->{'IMT_DESCRIPT'};
                        if($coverages{$loc}->{$unit}->{$cov}->{'COVERAGE'} eq 'EMPLB' ||
                           ($coverages{$loc}->{$unit}->{$cov}->{'COVERAGE'} eq 'FLLEX' && $ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /300|301|302|330|331|332/))
                          { $desc = 'Included in CSL'; }
                        elsif($coverages{$loc}->{$unit}->{$cov}->{'COVERAGE'} eq 'FMEMP')
                          { $desc = 'Included in Medical Payments Premises'; }
                        elsif($coverages{$loc}->{$unit}->{$cov}->{'COVERAGE'} eq 'BUSIN' && $ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /575/)
                          { $desc = 'Actual Loss Sustained'; }
                        elsif($coverages{$loc}->{$unit}->{$cov}->{'COVERAGE'} eq 'DWELL' && $coverages{$loc}->{$unit}->{$cov}->{'LIMIT1'} == 0 && $coverages{$loc}->{$unit}->{$cov}->{'LIMIT2'} == 0)
                          { $desc = 'N/A'; }
                        elsif($coverages{$loc}->{$unit}->{$cov}->{'COVERAGE'} eq 'BLDG' && $coverages{$loc}->{$unit}->{$cov}->{'LIMIT1'} == 0 && $coverages{$loc}->{$unit}->{$cov}->{'LIMIT2'} == 0)
                          { $desc = 'N/A'; }
                        elsif($coverages{$loc}->{$unit}->{$cov}->{'COVERAGE'} eq 'OS' && $coverages{$loc}->{$unit}->{$cov}->{'LIMIT1'} == 0 && $coverages{$loc}->{$unit}->{$cov}->{'LIMIT2'} == 0)
                          { $desc = 'N/A'; }
                        elsif($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /810|814|816/ && $ENGINE->{'claimGeneral'}->{'SYSTEM_IND'} eq 'N' && $coverages{$loc}->{$unit}->{$cov}->{'LIMIT1'} == 0 && $coverages{$loc}->{$unit}->{$cov}->{'LIMIT2'} == 0)
                        { $desc = 'Included'; }
                        elsif($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /015/ && $ENGINE->{'claimGeneral'}->{'SYSTEM_IND'} eq 'N' && $coverages{$loc}->{$unit}->{$cov}->{'LIMIT1'} == 0 && $coverages{$loc}->{$unit}->{$cov}->{'LIMIT2'} == 0 && $coverages{$loc}->{$unit}->{$cov}->{'COVERAGE'} eq 'MEDPM')
                        { $desc = 'Excluded';  }
                        elsif($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /015/ && $ENGINE->{'claimGeneral'}->{'SYSTEM_IND'} eq 'N' && $coverages{$loc}->{$unit}->{$cov}->{'LIMIT1'} == 0 && $coverages{$loc}->{$unit}->{$cov}->{'LIMIT2'} == 0 && $coverages{$loc}->{$unit}->{$cov}->{'COVERAGE'} =~ /UM|UNDUM/)
                        { $desc = 'Rejected';  }
                        else
                        {
                            $limit1 = $coverages{$loc}->{$unit}->{$cov}->{'LIMIT1'} || 0;
                            $limit2 = $coverages{$loc}->{$unit}->{$cov}->{'LIMIT2'} || 0;
                            if($limit1 > 0)
                              { $desc .= '$'.CommaFormatted($limit1); }
                            if($limit1 > 0 && $limit2 > 0)
                              { $desc .= '/'; }
                            if($limit2 > 0)
                              { $desc .= '$'.CommaFormatted($coverages{$loc}->{$unit}->{$cov}->{'LIMIT2'}); }
                            if($limit1 > 0 || $limit2 > 0)
                              { $desc .= ' Limit'; }
 #sjs  108204
 #SJS 110690 adding propdwell literal
                                                         my $propdwell='';

                             if($ENGINE->{'claimGeneral'}->{'INITIATION_POINT'} eq 'CV' || $ENGINE->{'claimGeneral'}->{'POLICY_FORM'} eq '4')
                             {
                                  $propdwell='PP';
                             }
                             else
                             {
                                  $propdwell='DWELL';
                             }
                             if($ENGINE->{'claimGeneral'}->{'POLICY_FORM'} eq '4' &&
                             $coverages{$loc}->{$unit}->{$cov}->{'COVERAGE'} eq 'PP')
                                 {
                                  my $form4Query = $ENGINE->{'DBH'}->prepare('SELECT SD.DEDUCTIBLE
                                      FROM
                                         CLAIMDB.CLM_COVS_ENDORSES SE
                                      INNER JOIN
                                         CLAIMDB.CLM_DEDUCTIBLES SD
                                         ON
                                         SD.COVERAGE_ID = SE.COVERAGE_ID
                                      WHERE
                                        SE.COVERAGE = ?
                                           AND
                                      SE.CLAIM_ID = ?') || error($ENGINE,'form 4 deductible query failed: '.$ENGINE->{'DBH'}->errstr);
                                      $form4Query->execute($propdwell,$claimid) || error($ENGINE,$ENGINE->{'DBH'}->errstr);
                                   my $form4rslt = $form4Query->fetchall_arrayref({});

                               $desc .= ' $'.CommaFormatted($form4rslt->[0]->{'DEDUCTIBLE'}).' All Peril Deductible';
                              }
                               else
                              {
# #sjs 108204
                               if (defined($coverages{$loc}->{$unit}->{$cov}->{'DEDUCTIBLES'}))
                               { $desc .= ' '.join(' Deductible, ',@{$coverages{$loc}->{$unit}->{$cov}->{'DEDUCTIBLES'}}).' Deductible'; }
                              }
                        }
                        if($coverages{$loc}->{$unit}->{$cov}->{'CAS_SYMBOLS'} ne '')
                          { $cov_line->{'cas'} = $coverages{$loc}->{$unit}->{$cov}->{'CAS_SYMBOLS'}; }
                        $cov_line->{'desc'} = $desc;
                        push(@loc_covs,$cov_line);
                    }
                }
            }

            $coverage_html->{$loc} = [@loc_covs];
        }

        my $partyQuery = $ENGINE->{'DBH'}->prepare
                ('SELECT *
                FROM CLAIMDB.CLM_PARTIES AS P
                    INNER JOIN
                            CLAIMDB.CLM_PARTY_ROLES AS R
                    ON
                            P.PARTY_ID = R.PARTY_ID
                            AND R.DATE_DELETED = \'9999-01-01 01:00:00.000000\'
                    LEFT JOIN
                            CLAIMDB.CLM_LOCATION AS L
                    ON
                            P.PARTY_ID = L.PARTY_ID
                            AND L.DATE_DELETED = \'9999-01-01 01:00:00.000000\'
                    WHERE P.CLAIM_ID = ?
                            AND R.ROLE IN (\'LH\',\'MG\')
                            AND P.DATE_DELETED = \'9999-01-01 01:00:00.000000\'')
                    || error($ENGINE,'Party query prepare failed: '.$ENGINE->{'DBH'}->errstr);
        $partyQuery->execute($claimid) || error($ENGINE,'Party query execute failed: '.$ENGINE->{'DBH'}->errstr);
        my $partyResults = $partyQuery->fetchall_arrayref({});

        my $partyOptions = '';
        $i = 0;
#104971 sjs 10/16/2012 remove financial interest heading if none.
        my $financialInt = '';
        my $i = 0;
        for my $p (@$partyResults)
        {
            $i++;
            my $class = '';
            if($i%2 == 0)
            { $class = 'altRow'; }

            my $street = '';
            my $cityState = '';
            my $mortgage = '';
            my $name = $p->{'BUSINESS_NAME'};
            if($p->{'FIRST_NAME'} && $p->{'LAST_NAME'})
              { $name = $p->{'FIRST_NAME'}.' '.$p->{'LAST_NAME'}; }
            $name =~ s/&/&amp;/g;
            $name =~ s/</&lt;/g;
            if(!defined($itemDescription{$p->{'MORT_LOC_NO'}.'_'.$p->{'MORT_LOC_NO'}}))
              { $itemDescription{$p->{'MORT_LOC_NO'}.'_'.$p->{'MORT_LOC_NO'}} = getCoveredItemDesc($ENGINE,$p->{'MORT_LOC_NO'},$p->{'MORT_LOC_NO'},$itemDescQuery); }
#            $partiesData .= '<li>'.$itemDescription{$p->{'MORT_LOC_NO'}.'_'.$p->{'MORT_LOC_NO'}}.'</li>';
#            $partiesData .= '<li><b>'.$name.'</li>';
            if(defined($p->{'ADDRESS1'}) && $p->{'ADDRESS1'} ne '')
              { $street .= $p->{'ADDRESS1'}; }
            if(defined($p->{'ADDRESS2'}) && $p->{'ADDRESS2'} ne '')
              { $street .= $p->{'ADDRESS2'}; }
            if(defined($p->{'CITY'}) && defined($p->{'STATE'}) && defined($p->{'ZIP1_5'}))
              { $cityState .= $p->{'CITY'}.', '.$p->{'STATE'}.' '.$p->{'ZIP1_5'}; }
            if($p->{'MORTGAGE_ITEM'} =~ /\w/)
              { $mortgage .= '<li><label>Mortgage Item: </label><div>'.$p->{'MORTGAGE_ITEM'}.'</div></li>'; }

              $partiesData .= <<EOF;
<ul class="toplabel_onecol $class">
    <li class="bold">$itemDescription{$p->{'MORT_LOC_NO'}.'_'.$p->{'MORT_LOC_NO'}}</li>
    <li class="bold">$name</li>
    <li class="bold"><span class="street">$street</span><span class="citystate">$cityState</span></li>
    $mortgage
</ul>
EOF

              $financialInt = 'Financial Interests';
        }

#die Data::Dumper::Dumper(%$coverage_html);

    for my $loc (keys %$coverage_html){
        my $covs = $coverage_html->{$loc};
#        die Data::Dumper::Dumper(@covs);
        $covHTML .= shift @$covs;
        if($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /015|804/)
        {$covHTML .= '<li><span class="threecol_first bold">Coverage</span><span class="threecol_second bold">Limit</span><span class="threecol_third bold">Symbol</span></li>';}
        my $k = 1;
        for my $line (@$covs){
            $k++;

            if(ref($line) eq "HASH")
            {
                my $class = '';
                if($k%2 == 0)
                { $class = ' class="altRow" '; }
                my $cas = '';
                   $cas = $line->{'cas'} if(defined $line->{'cas'});
                if (defined $line->{'text'} && $cas ne ''){
                    $covHTML .= <<EOF;
<li $class>
   <span class="threecol_first">
      $line->{'text'}
   </span>
   <span class="threecol_second">
      $line->{'desc'}
   </span>
   <span class="threecol_third">
      $cas
   </span>
</li>
EOF
                }
                elsif(defined $line->{'text'})
                {
                    $covHTML .= <<EOF;
<li $class>
    <span class="first">
        $line->{'text'}
    </span>
    <span class="second">
        $line->{'desc'}
    </span>
</li>
EOF
                }
                else{
                    $covHTML .= '<li '.$class.'><span>'.$line->{'desc'}.$cas.'</span></li>';
                }
            }
            else
            {
                $covHTML .= '</ul>';
                $covHTML .= $line;
                $k = 1;
            }
        }
        $covHTML .= '</ul>';
    }

        $covHTML = <<EOF;
<fieldset class="nofix">
    <h2>Coverages</h2>
      $covHTML
      <!--$cas_help_modals -->
</fieldset>
EOF

#die Data::Dumper::Dumper($covHTML);

        if($partiesData || $financialInt)
        {
        $partiesData = <<EOF;
<fieldset>
    <h2>$financialInt</h2>
$partiesData
</fieldset>
EOF
        }
    }

    # Misc info
    my $descQuery = $ENGINE->{'DBH'}->prepare('SELECT VARDATA, LINE_CNTR, DATA_TYPE FROM CLAIMDB.CLM_VARDATA WHERE CLAIM_ID = ? AND DATA_TYPE IN (\'MISC_INFO\') AND DATE_DELETED = \'9999-01-01 01:00:00.000000\'') || error($ENGINE,'Amount paid query prepare failed: '.$ENGINE->{'DBH'}->errstr);
    $descQuery->execute($claimid) || error($ENGINE,'Amount paid query execute failed: '.$ENGINE->{'DBH'}->errstr);
    my $descResults = $descQuery->fetchall_arrayref({});
    my @sortedDescResults = sort({$a->{'LINE_CNTR'} <=> $b->{'LINE_CNTR'}} @$descResults);
    my $miscInfo = '';
    for my $d (@$descResults)
    {
        if($d->{'DATA_TYPE'} eq 'MISC_INFO')
          { $miscInfo .= '<li class="bold">'.$d->{'VARDATA'}.'</li>'; }
    }
    #chop($miscInfo);
    $miscInfo =~ s/<br \/>$//;
    if($miscInfo ne '')
      { $miscInfo = <<EOF;
<fieldset>
    <h2>Miscellaneous Information</h2>
    <ul>
        $miscInfo
    </ul>
</fieldset>
EOF
        }

#die Data::Dumper::Dumper($covHTML);
    my $html = <<EOF;
$covHTML
$partiesData
$miscInfo
$endorsements
EOF

    $ENGINE->{'polCovsModalData'} = $covHTML;
    $ENGINE->{'polCovsHelpData'} = $cas_help_modals;
    $ENGINE->{'policyScreenData'} = $html;
#    if($covs_only)
#    {return $covHTML;}
#    else
#    {return $html;}
}

sub validateEmail
{
        my $email = shift;

    my $valid = 1;

        my $username = qr/[a-zA-Z0-9_+]([a-zA-Z0-9_+.-]*[a-zA-Z0-9_+])?/;
    my $domain   = qr/[a-zA-Z0-9.-]+/;
    my $tld = qr/[a-zA-Z0-9]+/;
    my $regex = $email =~ /^$username\@$domain\.$tld$/;
    if(!$regex)
    {$valid = 0;}

    return $valid;
}

sub platformAuthError
{
    my $ENGINE = shift;
    my $err_code = shift;
    my $auth_error_msg = shift;

    local *ERRFILE;
    my @date = localtime;
    my $min = $date[1];
    my $hour = $date[2];
    my $day = $date[3];
    my $mon = $date[4] + 1;
    my $year = $date[5] + 1900;
    my $rand = int(rand(1000000));
    my $path = File::Spec->catpath('d:', '/cgilogs/Claims/', $year.'-'.$mon.'-'.$day.'-Error.log');
    open(*ERRFILE,'>>', $path)  || die('Failure in Claims_Misc error logging subroutine, please contact IMT if this problem continues');

    my ( $package, $filename, $line ) = caller;

    print ERRFILE $package, $filename, $line."\n";
    print ERRFILE $err_code."\n";
    print ERRFILE $auth_error_msg."\n";

    close(*ERRFILE);

    return;

}




=back

=head1 AUTHOR

Dave Schwenker (<EMAIL>)

=head1 COPYRIGHT

Copyright ???

This library is intended for use at IMT Insurance only.

=head1 SEE ALSO

perl(1).

=cut

1;
