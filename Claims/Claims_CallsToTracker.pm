
#!/usr/local/bin/perl
package Claims_CallsToTracker;

require 5.000;
use strict;
use vars qw($VERSION @ISA @EXPORT_OK);


use Exporter;

@ISA = qw(Exporter);
@EXPORT_OK = qw(claimISOClose claimQuarterlyNotify assignClaimNotify closeClaimNotify reOpenClaimNotify claimUpdateNotify closeClaimPending);

$VERSION = '0.01';

use Claims_Error qw(error);
use Claims_TrackerAPI qw(:NTF_TYPES :MSG_IDS close_notification insert_notification);
use GetCovLossLists qw(GetCoveragesList GetLosscodeList GetCauseOfLossList);
use IMT::CommaFormatted qw(CommaFormatted);
use Claims_Constants qw(SPECIALIZATION_DATE);
use Common::Platform::Users::usersCommon qw(fetch_user_key_data fetch_title_key_data);
use Common::Platform::Users::User_Obj;

sub assignClaimNotify
{

        my $ENGINE = shift;
    my %errors = ();
        my $claimid = $ENGINE->{'claimGeneral'}->{'CLAIM_ID'};
    my $error = $ENGINE->{'error'};

    #retrieve the claim reps on this claim
    my $getRepsQuery = $ENGINE->{'DBH'}->prepare
        ("SELECT USER_KEY
            FROM CLAIMDB.CLM_REP_ASSIGNED
            WHERE CLAIM_ID = ?
            and DATE_REMOVED = '9999-01-01-01.00.00.000000'
            AND DATE_COMPLETED = '9999-01-01'
            ORDER BY CLM_REP_ASGN_ID")
        || $error->($ENGINE,'CLM_REP_ASSIGNED prepare failed: '.$ENGINE->{'DBH'}->errstr);
    $getRepsQuery->execute($claimid)
        || $error->($ENGINE,'CLM_REP_ASSIGNED query execute failed: '.$ENGINE->{'DBH'}->errstr);
    my $newReps = $getRepsQuery->fetchall_arrayref({})
        || $error->($ENGINE,'CLM_REP_ASSIGNED query fetch  failed: '.$ENGINE->{'DBH'}->errstr);

    #save first adjuster.
    my $holdFirstAdjuster = $newReps->[0]->{'USER_KEY'};

    #close the unassigned claim notification
    close_notification($ENGINE,{'type'=>CLAIM_NTF_TYPE,
                                                            'msg_id'=>UNASSIGNED_CLAIM,
                                'claim_id'=>$claimid});
    #close the unassigned claim notification for Marked claims
    close_notification($ENGINE,{'type'=>MARKED_CLAIM_NTF_TYPE,
                                                            'msg_id'=>UNASSIGNED_CLAIM,
                                'claim_id'=>$claimid});

    my @userKeys = ();
    my $addNewRep = 'N';

    #loop thru adjusters and build notifications for all of them
    #since this was in Manager status and is now changing to assigned
    if (defined $ENGINE->{'Monetary'}->{'originalStatus'}
            && $ENGINE->{'Monetary'}->{'originalStatus'} eq 'M')
    {
            #set up to notify all adjusters assigned to the claim
            if(scalar(@$newReps) > 0)
        {
                         for my $rep (@{$newReps})
                    {
                    push (@userKeys, $rep->{'USER_KEY'});
                    }
        }
    }
    else
    {
                #loop thru the new reps and build the 'IN' for sql read
                my $userKeyIN = '(';
                my $cntr = 0;
            if(scalar(@$newReps) > 0)
        {
                         for my $rep (@{$newReps})
                    {
                            if ($cntr == 0)
                            {
                            $userKeyIN .= $rep->{'USER_KEY'};
                    }
                    else
                    {
                        $userKeyIN .= ', ' . $rep->{'USER_KEY'};
                    }
                    $cntr++;
                    }
        }
        $userKeyIN .= ')';
        if($userKeyIN eq '()')
        { $userKeyIN = '(0)'; }

            #determine which users already have a notification of this claim
        my $findExistingNotes = $ENGINE->{'DBH'}->prepare
                ("SELECT B.NAME,
                           B.USER_KEY,
                           N.MESSAGE_ID
                    FROM
                        CLAIMDB.CLM_BASKET B
                    INNER JOIN
                        CLAIMDB.CLM_NTF_HAS_BASKET H
                    ON B.BASKET_ID = H.BASKET_ID
                    INNER JOIN
                        CLAIMDB.CLM_NOTIFICATION N
                    ON N.NOTIFICATION_ID = H.NOTIFICATION_ID
                  WHERE
                    N.CLAIM_ID = ?
                    AND B.USER_KEY in $userKeyIN
                    AND N.MESSAGE_ID = 2
                    AND COMPLETION_DATE IS NULL")
                        || $error->($ENGINE,'Existing notifications prepare failed: '.$ENGINE->{'DBH'}->errstr);

        $findExistingNotes->execute($claimid)
            || $error->($ENGINE,'Existing notifications query execute failed: '.$ENGINE->{'DBH'}->errstr);
        my $oldReps = $findExistingNotes->fetchall_arrayref({})
            || $error->($ENGINE,'Existing notifications query fetch  failed: '.$ENGINE->{'DBH'}->errstr);

            #set up to notify only new adjusters assigned to the claim
            if(scalar(@$oldReps) > 0)
        {
                         for my $rep (@{$newReps})
                    {
                            my $foundRep = 'N';
                            #check to see if the newRep is on the old rep list
                for my $oldRep (@{$oldReps})
                {
                        if ($rep->{'USER_KEY'} == $oldRep->{'USER_KEY'})
                        {
                                $foundRep = 'Y';
                             next;  #skip out
                               }
                }
                if ($foundRep eq 'N')
                {
                         push (@userKeys, $rep->{'USER_KEY'});
                         $addNewRep = 'Y';
                }
                    }
        }
        else
        {
                         for my $rep (@{$newReps})
                    {
                    push (@userKeys, $rep->{'USER_KEY'});
                    }
        }

    }

    #if we have any users on the array, call the tracker API
#    if(scalar(@userKeys) > 0)
#    {
#        insert_notification($ENGINE,{'type'=>ALERT_NTF_TYPE,
#                                'msg_id'=>ASSIGNED_CLAIM,
#                                'claim_id'=>$claimid,
#                                'comment'=>'New Claim Assignment',
#                                'users'=>\@userKeys});
#    }

    if(substr($ENGINE->{'claimGeneral'}->{'SUBMIT_TO_IMT_DATE'},0,10) lt SPECIALIZATION_DATE)
    {
	    if(scalar(@userKeys) > 0)
	    {
	        for my $user (@userKeys)
	        {
	                insert_notification($ENGINE,{'type'=>ALERT_NTF_TYPE,
	                                        'msg_id'=>ASSIGNED_CLAIM,
	                                        'claim_id'=>$claimid,
	                                        'comment'=>'New Claim Assignment',
	                                        'users'=>$user});
	            if ($addNewRep eq 'Y')
	            {
	                    my $alertsQuery = $ENGINE->{'DBH'}->prepare
	                        ("SELECT A.*, V.*
	                            FROM
	                                CLAIMDB.CLM_ALERTS A
	                            INNER JOIN
	                                CLAIMDB.CLM_VARDATA V
	                            ON V.CLM_ALERT_ID = A.CLM_ALERT_ID
	                          WHERE
	                            A.CLAIM_ID = ?
	                            AND END_DATE = '9999-01-01'
	                            AND USER_KEY = ?
	                            AND ALERTS_ID IN (5,7,9,10)
	                            AND A.DATE_DELETED = '9999-01-01-01.00.00.000000'")
	                                || $error->($ENGINE,'Alerts Vardata prepare failed: '.$ENGINE->{'DBH'}->errstr);
	                    $alertsQuery->execute($claimid,$holdFirstAdjuster)
	                        || $error->($ENGINE,'Alerts Vardata query execute failed: '.$ENGINE->{'DBH'}->errstr);
	                    my $alertResults = $alertsQuery->fetchall_arrayref({})
	                        || $error->($ENGINE,'Alerts Vardata query fetch  failed: '.$ENGINE->{'DBH'}->errstr);

	                    my $alertsInsert = $ENGINE->{'DBH'}->prepare(
	                       "SELECT CLM_ALERT_ID
	                          FROM FINAL TABLE
	                                 (INSERT INTO CLAIMDB.CLM_ALERTS
	                                    (CLM_ALERT_ID,
	                                     CLAIM_ID,
	                                     USER_KEY,
	                                     ALERTS_ID,
	                                     HOW_OFTEN,
	                                     COMPLETE,
	                                     START_DATE,
	                                     END_DATE,
	                                     DAY_TO_SEND,
	                                     ALERT_TYPE,
	                                     DATE_ADDED,
	                                     DUE_DATE,
	                                     DATE_DELETED)
	                        VALUES (NEXT VALUE FOR CLAIMDB.CLM_ALERT_ID_SEQ,?,?,?,?,?,?,?,?,?,CURRENT TIMESTAMP,?,?))")  || $error->($ENGINE);

	                    my $vardataInsert = $ENGINE->{'DBH'}->prepare(
	                       "SELECT CLM_VARDATA_ID
	                          FROM FINAL TABLE
	                                 (INSERT INTO CLAIMDB.CLM_VARDATA
	                                    (CLM_VARDATA_ID,
	                                     CLAIM_ID,
	                                     PARTY_ID,
	                                     CLM_CASH_ID,
	                                     CLM_ALERT_ID,
	                                     CLM_SALVAGE_ID,
	                                     CLM_MEMO_ID,
	                                     CLM_LITIGATION_ID,
	                                     COVERAGE_ID,
	                                     LINE_TYPE,
	                                     LINE_CNTR,
	                                     DATA_TYPE,
	                                     VARDATA,
	                                     DATE_DELETED)
	                        VALUES (NEXT VALUE FOR CLAIMDB.CLM_VARDATA_ID_SEQ,?,?,?,?,?,?,?,?,?,?,?,?,?))")  || $error->($ENGINE);

	                    for my $newAlerts (@{$alertResults})
	                    {
	#                    print ' claimid '.$claimid;
	#                    print ' user '.$user;
	#                    print ' alerts_id '.$newAlerts->{'ALERTS_ID'};
	#                    print ' how_often '.$newAlerts->{'HOW_OFTEN'};
	#                    print ' complete '.$newAlerts->{'COMPLETE'};
	#                    print ' start_date '.$newAlerts->{'START_DATE'};
	#                    print ' end_date '.$newAlerts->{'END_DATE'};
	#                    print ' day_to_send '.$newAlerts->{'DAY_TO_SEND'};
	#                    print ' alert_type '.$newAlerts->{'ALERT_TYPE'};
	#                    print ' date_added '.$newAlerts->{'DATE_ADDED'};
	#                    print ' due_date '.$newAlerts->{'DUE_DATE'};
	#                    print ' date_deleted '.$newAlerts->{'DATE_DELETED'};
	                        $alertsInsert->execute($claimid,
	                                                 $user,
	                                                     $newAlerts->{'ALERTS_ID'},
	                                                     $newAlerts->{'HOW_OFTEN'},
	                                                     $newAlerts->{'COMPLETE'},
	                                                     $newAlerts->{'START_DATE'},
	                                                     '9999-01-01',
	                                                     $newAlerts->{'DAY_TO_SEND'},
	                                                     $newAlerts->{'ALERT_TYPE'},
	#                                                     $newAlerts->{'DATE_ADDED'},
	                                                     $newAlerts->{'DUE_DATE'},
	                                                     $newAlerts->{'DATE_DELETED'}) || $error->($ENGINE);
	                        my $result = $alertsInsert->fetchall_arrayref({});
	                        if (scalar(@$result) < 1)
	                        {
	                           $error->($ENGINE);
	                        }

	                        my $insertedAlertID = $result->[0]->{'CLM_ALERT_ID'};

	                        $vardataInsert->execute($claimid,
	                                                     $newAlerts->{'PARTY_ID'}||undef,
	                                                     $newAlerts->{'CLM_CASH_ID'}||undef,
	                                                     $insertedAlertID,
	                                                     $newAlerts->{'CLM_SALVAGE_ID'}||undef,
	                                                     $newAlerts->{'CLM_MEMO_ID'}||undef,
	                                                     $newAlerts->{'LITIGATION_ID'}||undef,
	                                                     $newAlerts->{'COVERAGE_ID'}||undef,
	                                                     $newAlerts->{'LINE_TYPE'},
	                                                     $newAlerts->{'LINE_CNTR'},
	                                                     $newAlerts->{'DATA_TYPE'},
	                                                     $newAlerts->{'VARDATA'},
	                                                     $newAlerts->{'DATE_DELETED'}) || $error->($ENGINE);
	                        $vardataInsert->finish();
	                    }
	            }
	        }
	    }
    }
    else
    {
	    if(scalar(@userKeys) > 0)
	    {
	        for my $user (@userKeys)
	        {
	                insert_notification($ENGINE,{'type'=>ALERT_NTF_TYPE,
	                                        'msg_id'=>ASSIGNED_CLAIM,
	                                        'claim_id'=>$claimid,
	                                        'comment'=>'New Claim Assignment',
	                                        'users'=>$user});

                #get new users TITLE_KEY

                    #call platform with user_key
                my $user_key_data = fetch_user_key_data({
                  authorization => $ENGINE->{'AUTH'}->{'platform_access_token'},
                  user_key => $user,
                  max_attempts => 2
                });

	            my $title_key = $user_key_data->{content}->{data}->[0]->{attributes}->{other}->{title_key};

	            if ($addNewRep eq 'Y')
	            {
#                        my $alertsQuery = $ENGINE->{'DBH'}->prepare
#                            ("SELECT A.*, V.*
#                                FROM
#                                    CLAIMDB.CLM_ALERTS A
#                                INNER JOIN
#                                    CLAIMDB.CLM_VARDATA V
#                                ON V.CLM_ALERT_ID = A.CLM_ALERT_ID
#                              WHERE
#                                A.CLAIM_ID = ?
#                                AND END_DATE = '9999-01-01'
#                                AND USER_KEY = ?
#                                AND ALERTS_ID IN (5,9,10)
#                                AND A.DATE_DELETED = '9999-01-01-01.00.00.000000'")
#                                    || $error->($ENGINE,'Alerts Vardata prepare failed: '.$ENGINE->{'DBH'}->errstr);
#                        $alertsQuery->execute($claimid,$holdFirstAdjuster)
#                            || $error->($ENGINE,'Alerts Vardata query execute failed: '.$ENGINE->{'DBH'}->errstr);
#                        my $alertResults = $alertsQuery->fetchall_arrayref({})
#                            || $error->($ENGINE,'Alerts Vardata query fetch  failed: '.$ENGINE->{'DBH'}->errstr);

                        my $alertsInsert = $ENGINE->{'DBH'}->prepare(
                           "SELECT CLM_ALERT_ID
                              FROM FINAL TABLE
                                     (INSERT INTO CLAIMDB.CLM_ALERTS
                                        (CLM_ALERT_ID,
                                         CLAIM_ID,
                                         USER_KEY,
                                         ALERTS_ID,
                                         HOW_OFTEN,
                                         COMPLETE,
                                         START_DATE,
                                         END_DATE,
                                         DAY_TO_SEND,
                                         ALERT_TYPE,
                                         DATE_ADDED,
                                         DUE_DATE,
                                         DATE_DELETED)
                            VALUES (NEXT VALUE FOR CLAIMDB.CLM_ALERT_ID_SEQ,?,?,?,?,?,?,?,?,?,CURRENT TIMESTAMP,?,?))")  || $error->($ENGINE);

                        my $vardataInsert = $ENGINE->{'DBH'}->prepare(
                           "SELECT CLM_VARDATA_ID
                              FROM FINAL TABLE
                                     (INSERT INTO CLAIMDB.CLM_VARDATA
                                        (CLM_VARDATA_ID,
                                         CLAIM_ID,
                                         PARTY_ID,
                                         CLM_CASH_ID,
                                         CLM_ALERT_ID,
                                         CLM_SALVAGE_ID,
                                         CLM_MEMO_ID,
                                         CLM_LITIGATION_ID,
                                         COVERAGE_ID,
                                         LINE_TYPE,
                                         LINE_CNTR,
                                         DATA_TYPE,
                                         VARDATA,
                                         DATE_DELETED)
                            VALUES (NEXT VALUE FOR CLAIMDB.CLM_VARDATA_ID_SEQ,?,?,?,?,?,?,?,?,?,?,?,?,?))")  || $error->($ENGINE);

#                        for my $newAlerts (@{$alertResults})
#                        {
#    #                    print ' claimid '.$claimid;
#    #                    print ' user '.$user;
#    #                    print ' alerts_id '.$newAlerts->{'ALERTS_ID'};
#    #                    print ' how_often '.$newAlerts->{'HOW_OFTEN'};
#    #                    print ' complete '.$newAlerts->{'COMPLETE'};
#    #                    print ' start_date '.$newAlerts->{'START_DATE'};
#    #                    print ' end_date '.$newAlerts->{'END_DATE'};
#    #                    print ' day_to_send '.$newAlerts->{'DAY_TO_SEND'};
#    #                    print ' alert_type '.$newAlerts->{'ALERT_TYPE'};
#    #                    print ' date_added '.$newAlerts->{'DATE_ADDED'};
#    #                    print ' due_date '.$newAlerts->{'DUE_DATE'};
#    #                    print ' date_deleted '.$newAlerts->{'DATE_DELETED'};
#                            $alertsInsert->execute($claimid,
#                                                     $user,
#                                                         $newAlerts->{'ALERTS_ID'},
#                                                         $newAlerts->{'HOW_OFTEN'},
#                                                         $newAlerts->{'COMPLETE'},
#                                                         $newAlerts->{'START_DATE'},
#                                                         '9999-01-01',
#                                                         $newAlerts->{'DAY_TO_SEND'},
#                                                         $newAlerts->{'ALERT_TYPE'},
#    #                                                     $newAlerts->{'DATE_ADDED'},
#                                                         $newAlerts->{'DUE_DATE'},
#                                                         $newAlerts->{'DATE_DELETED'}) || $error->($ENGINE);
#                            my $result = $alertsInsert->fetchall_arrayref({});
#                            if (scalar(@$result) < 1)
#                            {
#                               $error->($ENGINE);
#                            }

#                            my $insertedAlertID = $result->[0]->{'CLM_ALERT_ID'};

#                            $vardataInsert->execute($claimid,
#                                                         $newAlerts->{'PARTY_ID'}||undef,
#                                                         $newAlerts->{'CLM_CASH_ID'}||undef,
#                                                         $insertedAlertID,
#                                                         $newAlerts->{'CLM_SALVAGE_ID'}||undef,
#                                                         $newAlerts->{'CLM_MEMO_ID'}||undef,
#                                                         $newAlerts->{'LITIGATION_ID'}||undef,
#                                                         $newAlerts->{'COVERAGE_ID'}||undef,
#                                                         $newAlerts->{'LINE_TYPE'},
#                                                         $newAlerts->{'LINE_CNTR'},
#                                                         $newAlerts->{'DATA_TYPE'},
#                                                         $newAlerts->{'VARDATA'},
#                                                         $newAlerts->{'DATE_DELETED'}) || $error->($ENGINE);
#                            $vardataInsert->finish();
#                        }
	                    #retrieve the claim reps on this user
	                     my $getAssignedQuery = $ENGINE->{'DBH'}->prepare
	                         ("SELECT DATE_ASSIGNED
	                             FROM CLAIMDB.CLM_REP_ASSIGNED
	                             WHERE CLAIM_ID = ?
	                             AND USER_KEY = ?
	                             and DATE_REMOVED = '9999-01-01-01.00.00.000000'")
	                         || $error->($ENGINE,'CLM_REP_ASSIGNED prepare failed: '.$ENGINE->{'DBH'}->errstr);
	                     $getAssignedQuery->execute($claimid,$user)
	                         || $error->($ENGINE,'CLM_REP_ASSIGNED query execute failed: '.$ENGINE->{'DBH'}->errstr);
	                     my $dateAssignedResults = $getAssignedQuery->fetchall_arrayref({})
	                         || $error->($ENGINE,'CLM_REP_ASSIGNED query fetch  failed: '.$ENGINE->{'DBH'}->errstr);

	                     #add new adjuster.
    					 my $holdDateAssigned = substr($dateAssignedResults->[0]->{'DATE_ASSIGNED'},0,10);
    					 my $BIReserve = 'N';
    					 my $transaction_date = '';
    					 my @assigned_date;
                         my $start_day_1 = 0;
                         my @date_between_days;
                         my $start_day_2 = 0;
                         my $new_Assigned_Year = 0;
                         my $new_Assigned_Month = 0;
                         my $new_Assigned_Day = 0;
                         my $new_Assigned_Date = '';
                         my $alertID = '';
                         my $alertType = '';
                         my $howOften = '';
                         my $vardata1 = '';
                         my $vardata2 = '';
                         my @disablilty_date;
                         my @disab_date_between_days;
                         my $new_Disab_Year = 0;
                         my $new_Disab_Month = 0;
                         my $new_Disab_Day = 0;
                         my @medical_date;
                         my @medical_date_between_days;
                         my $new_Medical_Year = 0;
                         my $new_Medical_Month = 0;
                         my $new_Medical_Day = 0;

                         #not Work Comp
                         if($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} !~ /600|605/)
                         {
	                         ($BIReserve,$transaction_date) = checkBI($ENGINE);
	                         @assigned_date = split(/-/,substr($holdDateAssigned,0,10));
	                         $start_day_1 = $assigned_date[2];
	                         use Date::Calc qw(Add_Delta_Days Delta_Days);
	                         @date_between_days = Add_Delta_Days(@assigned_date, -10);
	                         $start_day_2 = $date_between_days[2];
	                         $new_Assigned_Year = $date_between_days[0];
	                         $new_Assigned_Month = $date_between_days[1];
	                         $new_Assigned_Day = $date_between_days[2];
	                         $new_Assigned_Month = length($new_Assigned_Month)<2 ? '0'.$new_Assigned_Month : $new_Assigned_Month;
	                         $new_Assigned_Day = length($new_Assigned_Day)<2 ? '0'.$new_Assigned_Day : $new_Assigned_Day;
	                         $new_Assigned_Date = $new_Assigned_Year.'-'.$new_Assigned_Month.'-'.$new_Assigned_Day;
	                         $alertID = '5';
	                         $alertType = 'I';
	                         $howOften = 'M';
	                         $vardata1 = ' CLAIM UPDATE DUE ';
	                         $vardata2 = ' CLAIM UPDATE DUE TODAY';
#	                        if($title_key =~ /^1$|^3$|^9$|^10$|^11$|^12$|^13$|^14$|^19$|^20$|^24$|^29$|^30$/ && $BIReserve eq 'Y')
#	                        {
	    #                    print ' claimid '.$claimid;
	    #                    print ' user '.$user;
	    #                    print ' alerts_id '.$newAlerts->{'ALERTS_ID'};
	    #                    print ' how_often '.$newAlerts->{'HOW_OFTEN'};
	    #                    print ' complete '.$newAlerts->{'COMPLETE'};
	    #                    print ' start_date '.$newAlerts->{'START_DATE'};
	    #                    print ' end_date '.$newAlerts->{'END_DATE'};
	    #                    print ' day_to_send '.$newAlerts->{'DAY_TO_SEND'};
	    #                    print ' alert_type '.$newAlerts->{'ALERT_TYPE'};
	    #                    print ' date_added '.$newAlerts->{'DATE_ADDED'};
	    #                    print ' due_date '.$newAlerts->{'DUE_DATE'};
	    #                    print ' date_deleted '.$newAlerts->{'DATE_DELETED'};
	                            $alertsInsert->execute($claimid,
	                                                     $user,
	                                                         $alertID,
	                                                         $howOften,
	                                                         'Y',
	                                                         $new_Assigned_Date,
	                                                         '9999-01-01',
	                                                         $start_day_2,
	                                                         $alertType,
	    #                                                     $newAlerts->{'DATE_ADDED'},
	                                                         $new_Assigned_Date,
	                                                         '9999-01-01-01.00.00.000000') || $error->($ENGINE);
	                            my $result = $alertsInsert->fetchall_arrayref({});
	                            if (scalar(@$result) < 1)
	                            {
	                               $error->($ENGINE);
	                            }

	                            my $insertedAlertID = $result->[0]->{'CLM_ALERT_ID'};

	                            my $vardataDate = substr($holdDateAssigned,5,2).'/'.substr($holdDateAssigned,8,2).'/'.substr($holdDateAssigned,0,4);

	                            $vardataInsert->execute($claimid,
	                                                         undef,
	                                                         undef,
	                                                         $insertedAlertID,
	                                                         undef,
	                                                         undef,
	                                                         undef,
	                                                         undef,
	                                                         'R',
	                                                         1,
	                                                         'ALERT',
	                                                         $vardataDate.$vardata1,
	                                                         '9999-01-01-01.00.00.000000') || $error->($ENGINE);
	                            $vardataInsert->finish();
	#                            $transaction_date = $date_14_days[0].'-'.$date_14_days[1].'-'.$date_14_days[2];

	    #                    print ' claimid '.$claimid;
	    #                    print ' user '.$user;
	    #                    print ' alerts_id '.$newAlerts->{'ALERTS_ID'};
	    #                    print ' how_often '.$newAlerts->{'HOW_OFTEN'};
	    #                    print ' complete '.$newAlerts->{'COMPLETE'};
	    #                    print ' start_date '.$newAlerts->{'START_DATE'};
	    #                    print ' end_date '.$newAlerts->{'END_DATE'};
	    #                    print ' day_to_send '.$newAlerts->{'DAY_TO_SEND'};
	    #                    print ' alert_type '.$newAlerts->{'ALERT_TYPE'};
	    #                    print ' date_added '.$newAlerts->{'DATE_ADDED'};
	    #                    print ' due_date '.$newAlerts->{'DUE_DATE'};
	    #                    print ' date_deleted '.$newAlerts->{'DATE_DELETED'};
	                            $alertsInsert->execute($claimid,
	                                                     $user,
	                                                         $alertID,
	                                                         $howOften,
	                                                         'Y',
	                                                         $holdDateAssigned,
	                                                         '9999-01-01',
	                                                         $start_day_1,
	                                                         $alertType,
	    #                                                     $newAlerts->{'DATE_ADDED'},
	                                                         $holdDateAssigned,
	                                                         '9999-01-01-01.00.00.000000') || $error->($ENGINE);
	                            my $result = $alertsInsert->fetchall_arrayref({});
	                            if (scalar(@$result) < 1)
	                            {
	                               $error->($ENGINE);
	                            }

	                            my $insertedAlertID = $result->[0]->{'CLM_ALERT_ID'};

	                            my $vardataDate = substr($holdDateAssigned,5,2).'/'.substr($holdDateAssigned,8,2).'/'.substr($holdDateAssigned,0,4);

	                            $vardataInsert->execute($claimid,
	                                                         undef,
	                                                         undef,
	                                                         $insertedAlertID,
	                                                         undef,
	                                                         undef,
	                                                         undef,
	                                                         undef,
	                                                         'R',
	                                                         1,
	                                                         'ALERT',
	                                                         $vardataDate.$vardata2,
	                                                         '9999-01-01-01.00.00.000000') || $error->($ENGINE);
	                            $vardataInsert->finish();
#	                        }
                         }
                         #Work Comp
                         else
                         {
	                         if($ENGINE->{'claimGeneral'}->{'PROP_OR_LIAB'} eq 'M')
	                         {
                                 #for medical we need to start any new alerts_id 09 55 days from the current date so the new
                                 #completed 09 alerts get calculated correctly to start on the correct date.
	                             use Date::Calc qw(Add_Delta_Days Delta_Days);
#                                 @medical_date = split(/-/,substr($holdDateAssigned,0,10));
#                                 @medical_date_between_days = Add_Delta_Days(@medical_date, -55);
#                                 $new_Medical_Year = $medical_date_between_days[0];
#                                 $new_Medical_Month = $medical_date_between_days[1];
#                                 $new_Medical_Day = $medical_date_between_days[2];
#                                 $new_Medical_Month = length($new_Medical_Month)<2 ? '0'.$new_Medical_Month : $new_Medical_Month;
#                                 $new_Medical_Day = length($new_Medical_Day)<2 ? '0'.$new_Medical_Day : $new_Medical_Day;
#                                 $holdDateAssigned = $new_Medical_Year.'-'.$new_Medical_Month.'-'.$new_Medical_Day;
	                             @assigned_date = split(/-/,substr($holdDateAssigned,0,10));
	                             $start_day_1 = $assigned_date[2];
	                             @date_between_days = Add_Delta_Days(@assigned_date, -10);
	                             $start_day_2 = $date_between_days[2];
	                             $new_Assigned_Year = $date_between_days[0];
	                             $new_Assigned_Month = $date_between_days[1];
	                             $new_Assigned_Day = $date_between_days[2];
	                             $new_Assigned_Month = length($new_Assigned_Month)<2 ? '0'.$new_Assigned_Month : $new_Assigned_Month;
	                             $new_Assigned_Day = length($new_Assigned_Day)<2 ? '0'.$new_Assigned_Day : $new_Assigned_Day;
	                             $new_Assigned_Date = $new_Assigned_Year.'-'.$new_Assigned_Month.'-'.$new_Assigned_Day;
	                             $alertID = '9';
	                             $alertType = 'I';
	                             $howOften = 'B';
	                             $vardata1 = ' WORK COMP (MEDICAL) DUE ';
	                             $vardata2 = ' WORK COMP(MEDICAL) DUE TODAY';
	                         }
	                         elsif($ENGINE->{'claimGeneral'}->{'PROP_OR_LIAB'} eq 'D')
	                         {
                                 #for disability we need to start any new alerts_id 10 60 days from the current date so the new
                                 #completed 10 alerts get calculated correctly to start on the correct date.
	                             use Date::Calc qw(Add_Delta_Days Delta_Days);
	                             @disablilty_date = split(/-/,substr($holdDateAssigned,0,10));
	                             @disab_date_between_days = Add_Delta_Days(@disablilty_date, -60);
	                             $new_Disab_Year = $disab_date_between_days[0];
	                             $new_Disab_Month = $disab_date_between_days[1];
	                             $new_Disab_Day = $disab_date_between_days[2];
	                             $new_Disab_Month = length($new_Disab_Month)<2 ? '0'.$new_Disab_Month : $new_Disab_Month;
	                             $new_Disab_Day = length($new_Disab_Day)<2 ? '0'.$new_Disab_Day : $new_Disab_Day;
	                             $holdDateAssigned = $new_Disab_Year.'-'.$new_Disab_Month.'-'.$new_Disab_Day;
	                             @assigned_date = split(/-/,substr($holdDateAssigned,0,10));
	                             $start_day_1 = $assigned_date[2];
	                             @date_between_days = Add_Delta_Days(@assigned_date, -5);
	                             $start_day_2 = $date_between_days[2];
	                             $new_Assigned_Year = $date_between_days[0];
	                             $new_Assigned_Month = $date_between_days[1];
	                             $new_Assigned_Day = $date_between_days[2];
	                             $new_Assigned_Month = length($new_Assigned_Month)<2 ? '0'.$new_Assigned_Month : $new_Assigned_Month;
	                             $new_Assigned_Day = length($new_Assigned_Day)<2 ? '0'.$new_Assigned_Day : $new_Assigned_Day;
	                             $new_Assigned_Date = $new_Assigned_Year.'-'.$new_Assigned_Month.'-'.$new_Assigned_Day;
	                             $alertID = '10';
	                             $alertType = 'I';
	                             $howOften = 'T';
	                             $vardata1 = ' WORK COMP (DISABILITY) MEMO DUE ';
	                             $vardata2 = ' WORK COMP (DISABILITY) MEMO DUE TODAY';
	                         }
	    #                    print ' claimid '.$claimid;
	    #                    print ' user '.$user;
	    #                    print ' alerts_id '.$newAlerts->{'ALERTS_ID'};
	    #                    print ' how_often '.$newAlerts->{'HOW_OFTEN'};
	    #                    print ' complete '.$newAlerts->{'COMPLETE'};
	    #                    print ' start_date '.$newAlerts->{'START_DATE'};
	    #                    print ' end_date '.$newAlerts->{'END_DATE'};
	    #                    print ' day_to_send '.$newAlerts->{'DAY_TO_SEND'};
	    #                    print ' alert_type '.$newAlerts->{'ALERT_TYPE'};
	    #                    print ' date_added '.$newAlerts->{'DATE_ADDED'};
	    #                    print ' due_date '.$newAlerts->{'DUE_DATE'};
	    #                    print ' date_deleted '.$newAlerts->{'DATE_DELETED'};
	                            $alertsInsert->execute($claimid,
	                                                     $user,
	                                                         $alertID,
	                                                         $howOften,
	                                                         'Y',
	                                                         $new_Assigned_Date,
	                                                         '9999-01-01',
	                                                         $start_day_2,
	                                                         $alertType,
	    #                                                     $newAlerts->{'DATE_ADDED'},
	                                                         $new_Assigned_Date,
	                                                         '9999-01-01-01.00.00.000000') || $error->($ENGINE);
	                            my $result = $alertsInsert->fetchall_arrayref({});
	                            if (scalar(@$result) < 1)
	                            {
	                               $error->($ENGINE);
	                            }

	                            my $insertedAlertID = $result->[0]->{'CLM_ALERT_ID'};

	                            my $vardataDate = substr($holdDateAssigned,5,2).'/'.substr($holdDateAssigned,8,2).'/'.substr($holdDateAssigned,0,4);

	                            $vardataInsert->execute($claimid,
	                                                         undef,
	                                                         undef,
	                                                         $insertedAlertID,
	                                                         undef,
	                                                         undef,
	                                                         undef,
	                                                         undef,
	                                                         'R',
	                                                         1,
	                                                         'ALERT',
	                                                         $vardataDate.$vardata1,
	                                                         '9999-01-01-01.00.00.000000') || $error->($ENGINE);
	                            $vardataInsert->finish();
	#                            $transaction_date = $date_14_days[0].'-'.$date_14_days[1].'-'.$date_14_days[2];

	    #                    print ' claimid '.$claimid;
	    #                    print ' user '.$user;
	    #                    print ' alerts_id '.$newAlerts->{'ALERTS_ID'};
	    #                    print ' how_often '.$newAlerts->{'HOW_OFTEN'};
	    #                    print ' complete '.$newAlerts->{'COMPLETE'};
	    #                    print ' start_date '.$newAlerts->{'START_DATE'};
	    #                    print ' end_date '.$newAlerts->{'END_DATE'};
	    #                    print ' day_to_send '.$newAlerts->{'DAY_TO_SEND'};
	    #                    print ' alert_type '.$newAlerts->{'ALERT_TYPE'};
	    #                    print ' date_added '.$newAlerts->{'DATE_ADDED'};
	    #                    print ' due_date '.$newAlerts->{'DUE_DATE'};
	    #                    print ' date_deleted '.$newAlerts->{'DATE_DELETED'};
	                            $alertsInsert->execute($claimid,
	                                                     $user,
	                                                         $alertID,
	                                                         $howOften,
	                                                         'Y',
	                                                         $holdDateAssigned,
	                                                         '9999-01-01',
	                                                         $start_day_1,
	                                                         $alertType,
	    #                                                     $newAlerts->{'DATE_ADDED'},
	                                                         $holdDateAssigned,
	                                                         '9999-01-01-01.00.00.000000') || $error->($ENGINE);
	                            my $result = $alertsInsert->fetchall_arrayref({});
	                            if (scalar(@$result) < 1)
	                            {
	                               $error->($ENGINE);
	                            }

	                            my $insertedAlertID = $result->[0]->{'CLM_ALERT_ID'};

	                            my $vardataDate = substr($holdDateAssigned,5,2).'/'.substr($holdDateAssigned,8,2).'/'.substr($holdDateAssigned,0,4);

	                            $vardataInsert->execute($claimid,
	                                                         undef,
	                                                         undef,
	                                                         $insertedAlertID,
	                                                         undef,
	                                                         undef,
	                                                         undef,
	                                                         undef,
	                                                         'R',
	                                                         1,
	                                                         'ALERT',
	                                                         $vardataDate.$vardata2,
	                                                         '9999-01-01-01.00.00.000000') || $error->($ENGINE);
	                            $vardataInsert->finish();
                         }
	            }
	        }
	    }
    }

#'comment'=>'Claim assigned to you',

}

sub checkBI
{
    my $ENGINE = shift;
    my %errors = ();
    my $error = $ENGINE->{'error'};

    my $line = $ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'};
    my $hasMedical = 'N';
    my $hasBIUMUIM = 'N';
    my $hasBIMedical = 'N';
    my $lossType = '';
    my $lossCause = '';
    my $coverageID = 0;
    my $cov = 0;
    my $transaction_date = '';

    for my $r (@{$ENGINE->{'Monetary'}->{'CLM_RESERVES'}})
    {
        $lossType = $r->{'LOSS_CODE'};
        $lossCause = $r->{'IMT_CAUSE_OF_LOSS'} || '';
        $coverageID = $r->{'COVERAGE_ID'} || 0;
        $transaction_date = substr($r->{'TRANSACTION_DATE'},0,10);

        if ($coverageID)
        {
                #get the coverage code too, in case we need it
                for my $c (@{$ENGINE->{'Monetary'}->{'OLD_CLM_COVS_ENDORSES'}})
                {
                     if ($c->{'COVERAGE_ID'} == $coverageID)
                     {
                        $cov = $c->{'IMT_COVERAGE'};
                           }
                  }
        }

                ####This section is stolen from claims_fileActivity.pm
                ####function BIMEDTotals
                if($line =~ /010|011|012|030|031|051/)
                {
                    if($lossType eq '01')
                    {
                        if($lossCause =~ /001|058|059|060|061|062|063|064|065|067/)
                        {
                            $hasMedical = 'Y';
                        }
                    }
                    if($lossType eq '03')
                    {
                        if($lossCause =~ /099|058|059|060|061|062|063|064|065|067/)
                        {
                            $hasMedical = 'Y'
                        }
                    }
                    if($lossType eq '06')
                    {
                        if($lossCause =~ /099|058|059|060|061|062|063|064|065/)
                        {
                            $hasMedical = 'Y'
                        }
                    }
                    if($lossType =~ /09|10/)
                    {
                        if($lossCause =~ /099|058|059|060|064|065/)
                        {
                            $hasMedical = 'Y'
                        }
                    }
                    if($lossType =~ /L1/)
                    {
                        if($lossCause =~ /0L1/)
                        {
                            $hasMedical = 'Y'
                        }
                    }
                    if($lossType =~ /L2/)
                    {
                        if($lossCause =~ /0L2/)
                        {
                            $hasMedical = 'Y'
                        }
                    }
                }
                elsif($line =~ /112|113|120|110|111/)
                {
                    if($lossType eq '58'
                        || $lossType eq '73')
                    {
                        if($lossCause =~ /^070$|^124$|^125$|^126$|^127$|^128$|^129$|^130$|^131$|^132$|^134$|^199$/)
                        {
                            $hasBIMedical = 'Y'
                        }
                    }
                }
                elsif($line =~ /015|016/)
                {
                    if($lossType =~ /01|03|09|10|L3|L4/)
                    {
                        $hasBIMedical = 'Y'
                    }
                    if($cov eq '03')
                    {
                        if($lossType =~ /58|59|60|61|62|63|64|65|67/)
                        {
                            $hasBIMedical = 'Y'
                        }
                    }
                    if($cov eq '06')
                    {
                        if($lossType =~ /58|59|60|61|62|63|64|65/)
                        {
                            $hasBIMedical = 'Y'
                        }
                    }
                    if($cov =~ /09|10/)
                    {
                        if($lossType =~ /58|59|60|64|65/)
                        {
                            $hasBIMedical = 'Y'
                        }
                    }
                }
                elsif($line =~ /052/)
                {
                    if($lossType =~ /70|73/)
                    {
                        $hasBIMedical = 'Y'
                    }
                }
                elsif($line =~ /100/)
                {
                    if($lossType =~ /28|54|56|70|73/)
                    {
                        $hasBIMedical = 'Y'
                    }
                }
                elsif($line =~ /575|580/)
                {
                    if($lossType =~ /26|28|29|30|53|54|55|56/)
                    {
                        $hasBIMedical = 'Y'
                    }
                }
                elsif($line =~ /804/)
                {
                    if($lossType =~ /01|03|09|10|13|L3|L4/)
                    {
                        $hasBIMedical = 'Y'
                    }
                }
                elsif($line =~ /810|811|812|814|815|816/)
                {
                    if($lossType =~ /01|03|28|29|53|54|55|56/)
                    {
                        $hasBIMedical = 'Y'
                    }
                }
                elsif($line =~ /300|301|302|330|331|332/)
                {
                    if($lossType =~ /28|53|54|55|56|57|62|63|64|67|70|73|74|75|76/)
                    {
                        $hasBIMedical = 'Y'
                    }
                }
                elsif($line =~ /350|360/)
                {
                    if($lossType =~ /70/)
                    {
                        $hasBIMedical = 'Y'
                    }
                }


            #this section is stolen from function BIUMUIMTotals
            if($line =~ /010|011|012|030|031|051/)
            {
                if($lossType eq '01')
                {
                    if($lossCause =~ /001|058|059|060|061|062|063|064|065|067/)
                    {
                        $hasBIUMUIM = 'Y';
                        last;
                    }
                }
                if($lossType =~ /09|10/)
                {
                    if($lossCause =~ /099|058|059|060|064|065/)
                    {
                        $hasBIUMUIM = 'Y';
                        last;
                    }
                }
                if($lossType =~ /L1/)
                {
                    if($lossCause =~ /0L1/)
                    {
                        $hasBIUMUIM = 'Y';
                        last;
                    }
                }
                if($lossType =~ /L2/)
                {
                    if($lossCause =~ /0L2/)
                    {
                        $hasBIUMUIM = 'Y';
                        last;
                    }
                }
            }
            elsif($line =~ /112|113|120|110|111/)
            {
                if($lossType eq '58')
                    {
                        if($lossCause =~ /^070$|^124$|^125$|^126$|^127$|^128$|^129$|^130$|^131$|^132$|^134$|^199$/)
                        {
                        $hasBIUMUIM = 'Y';
                        last;
                    }
                }
            }
            elsif($line =~ /015|016/)
            {
                if($lossType =~ /01|09|10/)
                {
                    $hasBIUMUIM = 'Y';
                    last;
                }
                if($cov =~ /09|10/)
                {
                    if($lossType =~ /58|59|60|64|65/)
                    {
                        $hasBIUMUIM = 'Y';
                        last;
                    }
                }
            }
            elsif($line =~ /052/)
            {
                if($lossType =~ /70|72/)
                {
                    $hasBIUMUIM = 'Y';
                    last;
                }
            }
            elsif($line =~ /100/)
            {
                if($lossType =~ /28|54|70/)
                {
                    $hasBIUMUIM = 'Y';
                    last;
                }
            }
            elsif($line =~ /575|580/)
            {
                if ($lossType =~ /26|28|29|53|54/)
                {
                    $hasBIUMUIM = 'Y';
                    last;
                }
            }
            elsif($line =~ /804/)
            {
                if($lossType =~ /01|09|10|L3|L4/)
                {
                    $hasBIUMUIM = 'Y';
                    last;
                }
            }
            elsif($line =~ /810|811|812|814|815|816/)
            {
                if($lossType =~ /01|28|29|53|54/)
                {
                    $hasBIUMUIM = 'Y';
                    last;
                }
            }
            elsif($line =~ /300|301|302|330|331|332/)
            {
                if($lossType =~ /28|53|54|57|62|64|67|70|74/)
                {
                    $hasBIUMUIM = 'Y';
                    last;
                }
            }
            elsif($line =~ /350|360/)
            {
                if($lossType =~ /70/)
                {
                    $hasBIUMUIM = 'Y';
                    last;
                }
            }

                         #this section stolen from function medicaltotals
                         if($line =~ /010|011|012|030|031|051/)
                {
                  if($lossType eq '03')
                  {
                      if($lossCause =~ /099|058|059|060|061|062|063|064|065|067/)
                      {
                          $hasMedical = 'Y';
                      }
                  }
                }
                elsif($line =~ /112|113|120|110|111/)
                {
              if($lossType eq '73')
                  {
                          if($lossCause =~ /^124$|^125$|^126$|^127$|^128$|^129$|^130$|^131$|^132$|^199$/)
                    {
                        $hasMedical = 'Y';
                }
              }
                }
                elsif($line =~ /015|016/)
                {
                  if($lossType =~ /03/)
                  {
                      $hasMedical = 'Y';
                  }
                  if($cov eq '03')
                  {
                      if($lossType =~ /58|59|60|61|62|63|64|65|67/)
                      {
                          $hasMedical = 'Y';
                      }
                  }
                }
                elsif($line =~ /052/)
                {
                  if($lossType =~ /73/)
                  {
                      $hasMedical = 'Y';
                  }
                }
                elsif($line =~ /100/)
                {
                  if($lossType =~ /56|73/)
                  {
                      $hasMedical = 'Y';
                  }
                }
                elsif($line =~ /575|580/)
                {
                  if($lossType =~ /30|55|56/)
                  {
                      $hasMedical = 'Y';
                  }
                }
                elsif($line =~ /804/)
                {
                  if($lossType =~ /03|13/)
                  {
                      $hasMedical = 'Y';
                  }
                }
                elsif($line =~ /810|811|812|814|815|816/)
                {
                  if($lossType =~ /03|55|56/)
                  {
                      $hasMedical = 'Y';
                  }
                }
                elsif($line =~ /300|301|302|330|331|332/)
                {
                  if($lossType =~ /55|56|63|73|75|76/)
                  {
                      $hasMedical = 'Y';
                  }
                }

    }

    return ($hasBIUMUIM,$transaction_date);
}

sub closeClaimNotify
{

        my $ENGINE = shift;
    my %errors = ();
        my $claimid = $ENGINE->{'claimGeneral'}->{'CLAIM_ID'};
    my $error = $ENGINE->{'error'};

    #work fields
    my @userKeys = ();
    my @bossesKeys = ();


    #call function to close the claim for all adjusters assigned to it
    close_notification($ENGINE,{'type'=>ALERT_NTF_TYPE,
                                'msg_id'=>ASSIGNED_CLAIM,
                                'claim_id'=>$claimid});

    #call function to close the claim for all supervisors reopened claims.
    close_notification($ENGINE,{'type'=>ALERT_NTF_TYPE,
                                'msg_id'=>REOPENED_CLAIM,
                                'claim_id'=>$claimid});

    #call function to close the "adjuster portion reopened" notification.
    close_notification($ENGINE,{'type'=>ALERT_NTF_TYPE,
                                'msg_id'=>REOPENED_PORTION,
                                'claim_id'=>$claimid});

        #when claim is closed, always send tracker message to immediate supervisor
    #first, retrieve the claim reps on this claim and their bosses
    #lwm.sql - 951
    my $getRepsQuery = $ENGINE->{'DBH'}->prepare
        ("SELECT G.USERNAME
            FROM CLAIMDB.CLM_REP_ASSIGNED R
			JOIN GENSUPDB.USER_MAPPING G
            on G.LEGACY_USER_KEY = R.USER_KEY
            WHERE R.CLAIM_ID = ?
            AND R.DATE_REMOVED = '9999-01-01-01.00.00.000000'
            and date_completed = '9999-01-01'")
        || $error->($ENGINE,'CLM_REP_ASSIGNED2 prepare failed: '.$ENGINE->{'DBH'}->errstr);
    $getRepsQuery->execute($claimid)
        || $error->($ENGINE,'CLM_REP_ASSIGNED2 query execute failed: '.$ENGINE->{'DBH'}->errstr);
    my $repsAndBosses = $getRepsQuery->fetchall_arrayref({})
        || $error->($ENGINE,'CLM_REP_ASSIGNED2 query fetch  failed: '.$ENGINE->{'DBH'}->errstr);

    #start building the array of user bosses to send notification that
    #claims is closed to.
    if(scalar(@$repsAndBosses) > 0)
    {
        for my $repBoss (@{$repsAndBosses})
        {
			my $user_obj = User_Obj->new({
				filter => {
					username => $repBoss->{'USERNAME'},
				},
				auth_token => $ENGINE->{'AUTH'}->{'platform_access_token'}
			});

            my $supervisor_key = $user_obj->get_supervisor_legacy_key();

            if(defined($supervisor_key) && $supervisor_key gt '')
                { push (@userKeys, $supervisor_key); }
        }
    }

#    for my $x (@userKeys)
#    {
#        print '<br>before value: ' . $x;
#    }

    #remove any duplicates off the array of users and bosses by putting
    #it on a hash and then putting it back onto an array
    my %workHash = map {$_, 1} @userKeys;
    @userKeys = keys %workHash;

#    for my $x (@userKeys)
#    {
#        print '<br>after valueA: ' . $x;
#    }

    #if we have any users on the array, call the tracker API to
    #insert the normal claim closed messages.
    if(scalar(@userKeys) > 0
        && $ENGINE->{'AUTH'}->{'TITLE_KEY'} ne '18')
    {
        insert_notification($ENGINE,{'type'=>ALERT_NTF_TYPE,
                                'msg_id'=>CLOSED_CLAIM,
                                'claim_id'=>$claimid,
#                               'close'=>1,
                                'comment'=>'Closed claim',
                                'users'=>\@userKeys});
    }

##########################################################################
    #if more than $100,000 has been paid on this claim, send alerts
    #to VP, Director, Sr. Claims Manager, Regional Claims Manager,
    #District claims Manager too.
    #check payment amounts to see if > 100,000.
##########################################################################
    my $getTotPaid = $ENGINE->{'DBH'}->prepare
            ("SELECT SUM(C.PAYMENT_AMT) AS TOTAL_PAID
            FROM CLAIMDB.CLM_CASH C
            WHERE C.CLAIM_ID = ?
            AND C.TYPE = 'G'
            AND C.LOSS_CODE NOT IN ('79','80','81','82')
            AND C.DATE_DELETED = '9999-01-01-01.00.00.000000'")
        || $error->($ENGINE,'CLM_totalpaid prepare failed: '.$ENGINE->{'DBH'}->errstr);
    $getTotPaid->execute($claimid)
        || $error->($ENGINE,'CLM_totalpaid query execute failed: '.$ENGINE->{'DBH'}->errstr);
    my $totPaid = $getTotPaid->fetchall_arrayref({})
        || $error->($ENGINE,'CLM_totalpaid query fetch  failed: '.$ENGINE->{'DBH'}->errstr);

    #see if we got a result and if so, do we need to send alerts to the
    #big guns
    my $sendBigGuns = 'N';
    my $totDollsPaid = 0;
    if(scalar(@$totPaid)  > 0)
    {
        for my $t (@{$totPaid})
        {
                if (defined $t->{'TOTAL_PAID'}
                        && $t->{'TOTAL_PAID'} > 100000)
                {
                     $sendBigGuns = 'Y';
                     $totDollsPaid = '$'.CommaFormatted($t->{'TOTAL_PAID'});
                }
        }
    }

    my $getBigGuns = '';
    my $bigGuns = [];
    my $hold_title_key = '';
    if ($sendBigGuns eq 'Y')
    {
        if($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /600|605/)
        {
        	$hold_title_key = '1,3,9';
        }
        else
        {
                #Loop to get all the Distric Claim Managers, Regional Claim Managers, Sr Claims Manager, Director and VP.
        	$hold_title_key = '1,3,9,12,15';
        }

        #call platform with user_key
        my $user_key_data = fetch_title_key_data({
          authorization => $ENGINE->{'AUTH'}->{'platform_access_token'},
          title_key => $hold_title_key,
          max_attempts => 2
        });

        my $bossUsers = "'";
        $bossUsers .= join("','",( my @user = map {lc($_->{'attributes'}->{'username'})} @{$user_key_data->{content}->{data}}));
        $bossUsers .= "'";

        my $getUserMapping = $ENGINE->{'DBH'}->prepare
        ("SELECT LEGACY_USER_KEY
            FROM GENSUPDB.USER_MAPPING
               WHERE USERNAME IN ($bossUsers)")
        || $error->($ENGINE,'GENSUPDB.USER_MAPPING prepare failed: '.$ENGINE->{'DBH'}->errstr);
        $getUserMapping->execute()
            || $error->($ENGINE,'GENSUPDB.USER_MAPPING query execute failed: '.$ENGINE->{'DBH'}->errstr);
        my $userMappingResults = $getUserMapping->fetchall_arrayref({})
            || $error->($ENGINE,'GENSUPDB.USER_MAPPING query fetch  failed: '.$ENGINE->{'DBH'}->errstr);

        if(scalar(@$userMappingResults)  > 0)
        {
            for my $g (@{$userMappingResults})
            {
                push (@bossesKeys, $g->{'LEGACY_USER_KEY'});
            }
        }

        if(scalar(@bossesKeys) > 0)
        {
            insert_notification($ENGINE,{'type'=>ALERT_NTF_TYPE,
                                    'msg_id'=>CLOSED_CLAIM,
                                    'claim_id'=>$claimid,
    #                               'close'=>1,
                                    'users'=>\@bossesKeys,
                                    'comment'=>"Closed claim - total amount paid $totDollsPaid"});
        }
#        #loop thru the reps&bosses array of hashes,
#        # and build the 'IN' for sql read
#        my $userKeyIN = '(';
#        my $cntr = 0;
#        if(scalar(@$repsAndBosses) > 0)
#        {
#            for my $rep (@{$repsAndBosses})
#            {
#                if ($cntr == 0)
#                {
#                    $userKeyIN .= $rep->{'USER_KEY'};
#                }
#                else
#                {
#                    $userKeyIN .= ', ' . $rep->{'USER_KEY'};
#                }
#                $cntr++;
#            }
#        }
#        $userKeyIN .= ')';

#        print ' userkeyin '.$userKeyIN;
#        if ($userKeyIN eq '()')
#        {
#            #we don't want to send to the big guns
#        }
#        else
#        {
#            #find the bosses.  Going in 5 levels
#


#            #loop thru the results (there should be only 1, but looping just in case)
#            #and move the id's onto the array if they are big enough guns
#            if(scalar(@$bigGuns)  > 0)
#            {
#                for my $g (@{$bigGuns})
#                {

#                    if ($g->{'SUPER_TITLE'} =~ /^1$|^3$|^9$|^12$|^15$/)
#                    {
#                        #put him on the array
#                        push (@userKeys, $g->{'SUPER_USER'});
#                    }
#                    if ($g->{'SUPER_TITLE2'} =~ /^1$|^3$|^9$|^12$|^15$/)
#                    {
#                        #put him on the array
#                        push (@userKeys, $g->{'SUPER_USER2'});
#                    }
#                    if ($g->{'SUPER_TITLE3'} =~ /^1$|^3$|^9$|^12$|^15$/)
#                    {
#                        #put him on the array
#                        push (@userKeys, $g->{'SUPER_USER3'});
#                    }
#                    if ($g->{'SUPER_TITLE4'} =~ /^1$|^3$|^9$|^12$|^15$/)
#                    {
#                        #put him on the array
#                        push (@userKeys, $g->{'SUPER_USER4'});
#                    }
#                }
#            }

##    for my $x (@userKeys)
##    {
##        print '<br>before valueB: ' . $x;
##    }
#            #remove any duplicates off the array of users and bosses by putting
#            #it on a hash and then putting it back onto an array
#            my %workHash = map {$_, 1} @userKeys;
#            @userKeys = keys %workHash;

##    for my $x (@userKeys)
##    {
##        print '<br>before valueC: ' . $x;
##    }
#            #set up their notifications for large loss
#            if(scalar(@userKeys) > 0)
#            {
#                insert_notification($ENGINE,{'type'=>ALERT_NTF_TYPE,
#                                        'msg_id'=>CLOSED_CLAIM,
#                                        'claim_id'=>$claimid,
#    #                                   'close'=>1,
#                                        'users'=>\@userKeys,
#                                        'comment'=>"Closed claim - total amount paid $totDollsPaid"});
#            }
#        }
    }
#    else
#    {
#        #if we have any users on the array, call the tracker API to
#        #insert the normal claim closed messages.
#        if(scalar(@userKeys) > 0
#            && $ENGINE->{'AUTH'}->{'TITLE_KEY'} ne '18')
#        {
#            insert_notification($ENGINE,{'type'=>ALERT_NTF_TYPE,
#                                    'msg_id'=>CLOSED_CLAIM,
#                                    'claim_id'=>$claimid,
##                                   'close'=>1,
#                                    'comment'=>'Closed claim',
#                                    'users'=>\@userKeys});
#        }
#    }

##########################################################################
    ###############When closing a claim, they want to close alerts to
    ###############all adjuster-level people, and send a special alert to
    ###############Claims Tracker for people above adjuster level to let
    ###############them know they have open alerts within the claim.
##########################################################################
#lwm.sql - 1244
    my $getManualAlerts = $ENGINE->{'DBH'}->prepare
        ("SELECT A.USER_KEY,
                A.CLM_ALERT_ID,
                A.ALERT_TYPE
            FROM CLAIMDB.CLM_ALERTS A
            WHERE A.CLAIM_ID = ?
            AND A.COMPLETE = 'N'
            AND A.ALERT_TYPE IN ('F','I')
            AND A.DATE_DELETED = '9999-01-01-01.00.00.000000'")
        || $error->($ENGINE,'manualAlerts prepare failed: '.$ENGINE->{'DBH'}->errstr);
    $getManualAlerts->execute($claimid)
        || $error->($ENGINE,'manualAlerts query execute failed: '.$ENGINE->{'DBH'}->errstr);
    my $manualAlerts = $getManualAlerts->fetchall_arrayref({})
        || $error->($ENGINE,'manualAlerts query fetch  failed: '.$ENGINE->{'DBH'}->errstr);

    my @alertKeys = ();
    my @NotAdjusterKeys = ();
    #start building the array of users whose alerts won't be closed, and
    #the array of alerts that we want to close
    if(scalar(@$manualAlerts) > 0)
    {
        for my $manA (@{$manualAlerts})
        {

	            my $user_key_data = fetch_user_key_data({
	              authorization => $ENGINE->{'AUTH'}->{'platform_access_token'},
	              user_key => $manA->{'USER_KEY'},
	              max_attempts => 2
	            });

	            my $title_key = $user_key_data->{content}->{data}->[0]->{attributes}->{other}->{title_key};

                #this will get all the manual alerts assigned to an adjuster-level
                #person, AND get all the automatic alerts, regardless of the title
                #of the person the automatic alert is set for
                if ((defined $title_key
#                        && $manA->{'TITLE_KEY'} =~ /^10$|^14$|^16$|^17$|^18$|^19$|^20$|^2$/)
                        && $title_key =~ /^16$|^17$|^18$|^19$|^20$|^2$/)
                        || $manA->{'ALERT_TYPE'} eq 'I')
                {
                        push (@alertKeys, $manA->{'CLM_ALERT_ID'});
                }
                else
                {
                push (@NotAdjusterKeys, $manA->{'USER_KEY'});
                }
        }
    }

    #remove duplicates
    my %workHash1 = map {$_, 1} @NotAdjusterKeys;
        @NotAdjusterKeys = keys %workHash1;
        my %workHash2 = map {$_, 1} @alertKeys;
        @alertKeys = keys %workHash2;

        #build an IN statement for the update sql for the alerts that
        #need to close, and then update the alerts in the IN
        my $alertKeyIN = '(';
    my $cntr1 = 0;
    if(scalar(@alertKeys) > 0)
    {
        for my $alert (@alertKeys)
        {
            if ($cntr1 == 0)
            {
                $alertKeyIN .= $alert;
            }
            else
            {
                $alertKeyIN .= ', ' . $alert;
            }
            $cntr1++;
        }
            $alertKeyIN .= ')';

        #update the clm_alerts table to close out the pending alerts on
        #the claim
            my $updateManualAlerts = $ENGINE->{'DBH'}->prepare
                ("UPDATE CLAIMDB.CLM_ALERTS
                    SET COMPLETE = 'Y',
                    END_DATE = CURRENT DATE
                    WHERE CLAIM_ID = ?
                    AND CLM_ALERT_ID IN  $alertKeyIN
                    AND COMPLETE = 'N'")
                || $error->($ENGINE,'manualAlerts update prepare failed: '.$ENGINE->{'DBH'}->errstr);
            $updateManualAlerts->execute($claimid)
                || $error->($ENGINE,'manualAlerts update query execute failed: '.$ENGINE->{'DBH'}->errstr);
    }

    #now we need to add the Tracker notifications for alerts for NOT
    #adjusters that are on the claim.
    if(scalar(@NotAdjusterKeys) > 0)
    {
        insert_notification($ENGINE,{'type'=>ALERT_NTF_TYPE,
                                'msg_id'=>CLOSED_CLAIM,
                                'claim_id'=>$claimid,
#                                'close'=>1,
                                'comment'=>'Pending Alert On Closed claim',
                                'users'=>\@NotAdjusterKeys});
           }

           if($ENGINE->{'claimGeneral'}->{'SUIT_IND'} =~ /Y|L/)
           {
        #Loop to get all the Sr Claims Manager, Director and VP.

        $hold_title_key = '1,3,9';
        #call platform with user_key
        my $user_key_data = fetch_title_key_data({
          authorization => $ENGINE->{'AUTH'}->{'platform_access_token'},
          title_key => $hold_title_key,
          max_attempts => 2
        });

        my $bossUsers = "'";
        $bossUsers .= join("','",( my @user = map {lc($_->{'attributes'}->{'username'})} @{$user_key_data->{content}->{data}}));
        $bossUsers .= "'";

        my $getUserMapping = $ENGINE->{'DBH'}->prepare
        ("SELECT LEGACY_USER_KEY
            FROM GENSUPDB.USER_MAPPING
               WHERE USERNAME IN ($bossUsers)")
        || $error->($ENGINE,'GENSUPDB.USER_MAPPING prepare failed: '.$ENGINE->{'DBH'}->errstr);
        $getUserMapping->execute()
            || $error->($ENGINE,'GENSUPDB.USER_MAPPING query execute failed: '.$ENGINE->{'DBH'}->errstr);
        my $userMappingResults = $getUserMapping->fetchall_arrayref({})
            || $error->($ENGINE,'GENSUPDB.USER_MAPPING query fetch  failed: '.$ENGINE->{'DBH'}->errstr);

        my @lawBossesKeys = ();

        if(scalar(@$userMappingResults)  > 0)
        {
            for my $g (@{$userMappingResults})
            {
                push (@lawBossesKeys, $g->{'LEGACY_USER_KEY'});
            }
        }

        if(scalar(@lawBossesKeys) > 0)
        {
            insert_notification($ENGINE,{'type'=>ALERT_NTF_TYPE,
                                    'msg_id'=>CLOSED_CLAIM,
                                    'claim_id'=>$claimid,
    #                               'close'=>1,
                                    'users'=>\@lawBossesKeys,
                                    'comment'=>"Closed Suit/Law firm referral claim"});
        }
           }

    #close the Close Claim Pending notification for claims
    close_notification($ENGINE,{'type'=>ALERT_NTF_TYPE,
                                                            'msg_id'=>CLOSE_CLAIM_PEND,
                                'claim_id'=>$claimid});

    my $closeAlert = $ENGINE->{'DBH'}->prepare
        ("UPDATE CLAIMDB.CLM_ALERTS
                SET COMPLETE = 'Y',
                END_DATE = CURRENT DATE
            WHERE
                               CLAIM_ID = ?
                               AND ALERTS_ID = 16
                               AND COMPLETE = 'N'
                               AND DATE_DELETED = '9999-01-01-01.00.00.000000'")
        || $error->($ENGINE,'CLM_ALERT update prepare failed: '.$ENGINE->{'DBH'}->errstr);
    $closeAlert->execute($claimid)
        || $error->($ENGINE,'CLM_ALERT update execute failed: '.$ENGINE->{'DBH'}->errstr);

}

sub reOpenClaimNotify
{

    my $ENGINE = shift;
    my $user_keys = shift;
    my %errors = ();
    my $claimid = $ENGINE->{'claimGeneral'}->{'CLAIM_ID'};
    my $error = $ENGINE->{'error'};

    #work fields
    my @bossUserKeys = ();
    my @repUserKeys = ();
    my $repsAndBosses = '';

    if($user_keys ne '')
    {
      #lwm.sql - 1411
        my $getRepsQuery = $ENGINE->{'DBH'}->prepare
            ("SELECT G.USERNAME, R.USER_KEY
                FROM CLAIMDB.CLM_REP_ASSIGNED R
				JOIN GENSUPDB.USER_MAPPING G
            	on G.LEGACY_USER_KEY = R.USER_KEY
                WHERE R.CLAIM_ID = ?
                and R.user_key in ($user_keys)
                AND R.DATE_REMOVED = '9999-01-01-01.00.00.000000'")
            || $error->($ENGINE,'CLM_REP_ASSIGNED2 prepare failed: '.$ENGINE->{'DBH'}->errstr);
        $getRepsQuery->execute($claimid)
            || $error->($ENGINE,'CLM_REP_ASSIGNED2 query execute failed: '.$ENGINE->{'DBH'}->errstr);
        $repsAndBosses = $getRepsQuery->fetchall_arrayref({})
            || $error->($ENGINE,'CLM_REP_ASSIGNED2 query fetch  failed: '.$ENGINE->{'DBH'}->errstr);
    }
    else
    {
        #when claim is re-opened, always send tracker message to immediate supervisor
        #and to all the adjuster on the claim
        #lwm.sql - 1432
        my $getRepsQuery = $ENGINE->{'DBH'}->prepare
            ("SELECT G.USERNAME, R.USER_KEY
                FROM CLAIMDB.CLM_REP_ASSIGNED R
				JOIN GENSUPDB.USER_MAPPING G
            	on G.LEGACY_USER_KEY = R.USER_KEY
                WHERE R.CLAIM_ID = ?
                AND R.DATE_REMOVED = '9999-01-01-01.00.00.000000'")
            || $error->($ENGINE,'CLM_REP_ASSIGNED2 prepare failed: '.$ENGINE->{'DBH'}->errstr);
        $getRepsQuery->execute($claimid)
            || $error->($ENGINE,'CLM_REP_ASSIGNED2 query execute failed: '.$ENGINE->{'DBH'}->errstr);
        $repsAndBosses = $getRepsQuery->fetchall_arrayref({})
            || $error->($ENGINE,'CLM_REP_ASSIGNED2 query fetch  failed: '.$ENGINE->{'DBH'}->errstr);
    }

    if(scalar(@$repsAndBosses) > 0)
    {
        for my $repBoss (@{$repsAndBosses})
        {
			my $user_obj = User_Obj->new({
				filter => {
					username => $repBoss->{'USERNAME'},
				},
				auth_token => $ENGINE->{'AUTH'}->{'platform_access_token'}
			});

			my $supervisor_key = $user_obj->get_supervisor_legacy_key();
			push (@bossUserKeys, $supervisor_key);
			push (@repUserKeys, $repBoss->{'USER_KEY'});
        }
    }

    if(scalar(@repUserKeys) > 0)
    {
        for my $user (@repUserKeys)
        {
            insert_notification($ENGINE,{'type'=>ALERT_NTF_TYPE,
                                                            'msg_id'=>ASSIGNED_CLAIM,
                                'claim_id'=>$claimid,
                                'comment'=>'Reopened Claim Assignment',
                                'users'=>$user});
        }
    }

    #call function sending notification of reopen
    if(scalar(@bossUserKeys) > 0)
    {
        for my $boss (@bossUserKeys)
        {
                insert_notification($ENGINE,{'type'=>ALERT_NTF_TYPE,
                                        'msg_id'=>REOPENED_CLAIM,
                                        'claim_id'=>$claimid,
                                        'comment'=>'Reopened claim',
#                                        'close'=>1,
                                        'users'=>$boss});
        }
    }



#Reset Alerts when claim is reopened.
    my $alertsQuery = '';
    if($user_keys ne '')
    {
        $alertsQuery = $ENGINE->{'DBH'}->prepare
         ("SELECT *
         FROM CLAIMDB.CLM_ALERTS A
         INNER JOIN CLAIMDB.CLM_REP_ASSIGNED RA
           ON RA.CLAIM_ID = A.CLAIM_ID
           AND RA.USER_KEY = A.USER_KEY
           AND RA.DATE_REMOVED = '9999-01-01 01:00:00.000000'
         WHERE A.CLAIM_ID = ?
         AND A.ALERTS_ID IN (5,7,9,10)
         and a.user_key in ($user_keys)
         AND A.DATE_DELETED = '9999-01-01 01:00:00.000000' ORDER BY A.CLM_ALERT_ID")
         || $error->($ENGINE,'CLM_ALERTS prepare failed: '.$ENGINE->{'DBH'}->errstr);
    }
    else
    {
        $alertsQuery = $ENGINE->{'DBH'}->prepare
         ("SELECT *
         FROM CLAIMDB.CLM_ALERTS A
         INNER JOIN CLAIMDB.CLM_REP_ASSIGNED RA
           ON RA.CLAIM_ID = A.CLAIM_ID
           AND RA.USER_KEY = A.USER_KEY
           AND RA.DATE_REMOVED = '9999-01-01 01:00:00.000000'
         WHERE A.CLAIM_ID = ?
         AND A.ALERTS_ID IN (5,7,9,10)
         AND A.DATE_DELETED = '9999-01-01 01:00:00.000000' ORDER BY A.CLM_ALERT_ID")
         || $error->($ENGINE,'CLM_ALERTS prepare failed: '.$ENGINE->{'DBH'}->errstr);
    }
#    my $alertsQuery = $ENGINE->{'DBH'}->prepare
#        ("SELECT *
#        FROM CLAIMDB.CLM_ALERTS A
#        INNER JOIN CLAIMDB.CLM_REP_ASSIGNED RA
#          ON RA.CLAIM_ID = A.CLAIM_ID
#          AND RA.USER_KEY = A.USER_KEY
#          AND RA.DATE_REMOVED = '9999-01-01 01:00:00.000000'
#      WHERE A.END_DATE IN
#           (SELECT MAX(CA.END_DATE)
#              FROM CLAIMDB.CLM_ALERTS CA
#            WHERE CA.CLAIM_ID = ?
#            AND CA.ALERTS_ID IN (5,7,9,10)
#            AND CA.END_DATE <> '9999-01-01')
#        AND A.CLAIM_ID = ?
#        AND A.ALERTS_ID IN (5,7,9,10)
#        AND A.DATE_DELETED = '9999-01-01 01:00:00.000000' ORDER BY A.CLM_ALERT_ID")
#        || $error->($ENGINE,'CLM_ALERTS prepare failed: '.$ENGINE->{'DBH'}->errstr);
#    my $alertsQuery = $ENGINE->{'DBH'}->prepare
#        ("SELECT *
#        FROM CLAIMDB.CLM_ALERTS
#      WHERE END_DATE IN
#           (SELECT MAX(END_DATE)
#              FROM CLAIMDB.CLM_ALERTS
#            WHERE CLAIM_ID = ?
#            AND ALERTS_ID IN (5,7,9,10,11,12,13)
#            AND END_DATE <> '9999-01-01')
#        AND CLAIM_ID = ?
#        AND ALERTS_ID IN (5,7,9,10,11,12,13)
#        AND DATE_DELETED = '9999-01-01 01:00:00.000000'")
#        || $error->($ENGINE,'CLM_ALERTS prepare failed: '.$ENGINE->{'DBH'}->errstr);
    $alertsQuery->execute($claimid)
        || $error->($ENGINE,'CLM_ALERTS query execute failed: '.$ENGINE->{'DBH'}->errstr);
    my $alertsResults = $alertsQuery->fetchall_arrayref({})
        || $error->($ENGINE,'CLM_ALERTS query fetch  failed: '.$ENGINE->{'DBH'}->errstr);

    my $getDates = $ENGINE->{'DBH'}->prepare
       ("SELECT
         CURRENT TIMESTAMP AS CURRENT
           ,DATE(CURRENT TIMESTAMP + 20 DAYS) AS DATE20
           ,DATE(CURRENT TIMESTAMP + 30 DAYS) AS DATE30
           ,DATE(CURRENT TIMESTAMP + 76 DAYS) AS DATE76
           ,DATE(CURRENT TIMESTAMP + 90 DAYS) AS DATE90
           ,DATE(CURRENT TIMESTAMP + 50 DAYS) AS DATE50
           ,DATE(CURRENT TIMESTAMP + 60 DAYS) AS DATE60
           ,DATE(CURRENT TIMESTAMP + 25 DAYS) AS DATE25
           ,DATE(CURRENT TIMESTAMP + 3 DAYS) AS DATE3
        FROM SYSIBM.SYSDUMMY1")  || $error->($ENGINE,'System Date prepare failed: '.$ENGINE->{'DBH'}->errstr);
    $getDates->execute()
        || $error->($ENGINE,'System Date query execute failed: '.$ENGINE->{'DBH'}->errstr);
    my $getDatesResults = $getDates->fetchall_arrayref({})
        || $error->($ENGINE,'System Date query fetch  failed: '.$ENGINE->{'DBH'}->errstr);

    my $currentDate = $getDatesResults->[0]->{'CURRENT'};
    my $date20 = $getDatesResults->[0]->{'DATE20'};
    my $date30 = $getDatesResults->[0]->{'DATE30'};
    my $date76 = $getDatesResults->[0]->{'DATE76'};
    my $date90 = $getDatesResults->[0]->{'DATE90'};
    my $date50 = $getDatesResults->[0]->{'DATE50'};
    my $date60 = $getDatesResults->[0]->{'DATE60'};
    my $date25 = $getDatesResults->[0]->{'DATE25'};
#    my $date3 = $getDatesResults->[0]->{'DATE3'};
    my $alert5 = 'N';
    my $alert7 = 'N';
    my $alert9 = 'N';
    my $alert10 = 'N';
    my $date30Refor = '';
    my $date90Refor = '';
    my $date60Refor = '';
#    my $date3Refor = '';

    for my $al (@$alertsResults)
    {
# No longer need this code. When reopening claim start claim updates again.
#        if(defined($al->{'ALERTS_ID'}) && $al->{'ALERTS_ID'} eq 5)
#        {
#            my $found = 'N';
#            for my $al (@$alertsResults)
#            {
#                if(defined($al->{'ALERTS_ID'}) && $al->{'ALERTS_ID'} eq 7)
#                { $found = 'Y'; }
#            }
#            if($found eq 'Y')
#            { next; }
#        }
        my $newDueDate = '';
        if(defined($al->{'ALERTS_ID'}) && $al->{'ALERTS_ID'} == 5 && $alert5 eq 'N')
        {
            $newDueDate = $date20;
            $alert5 = 'Y';
            $date30Refor = substr($date30,5,2).'/'.substr($date30,8,2).'/'.substr($date30,0,4);
        }
        elsif(defined($al->{'ALERTS_ID'}) && $al->{'ALERTS_ID'} == 5 && $alert5 eq 'Y')
        {
            $newDueDate = $date30;
            $alert5 = 'N';
            $date30Refor = substr($date30,5,2).'/'.substr($date30,8,2).'/'.substr($date30,0,4);
        }
        if(defined($al->{'ALERTS_ID'}) && $al->{'ALERTS_ID'} == 7 && $alert7 eq 'N')
        {
            $newDueDate = $date76;
            $alert7 = 'Y';
            $date90Refor = substr($date90,5,2).'/'.substr($date90,8,2).'/'.substr($date90,0,4);
        }
        elsif(defined($al->{'ALERTS_ID'}) && $al->{'ALERTS_ID'} == 7 && $alert7 eq 'Y')
        {
            $newDueDate = $date90;
            $alert7 = 'N';
            $date90Refor = substr($date90,5,2).'/'.substr($date90,8,2).'/'.substr($date90,0,4);
        }
        if(defined($al->{'ALERTS_ID'}) && $al->{'ALERTS_ID'} == 9 && $alert9 eq 'N')
        {
            $newDueDate = $date50;
            $alert9 = 'Y';
            $date60Refor = substr($date60,5,2).'/'.substr($date60,8,2).'/'.substr($date60,0,4);
        }
        elsif(defined($al->{'ALERTS_ID'}) && $al->{'ALERTS_ID'} == 9 && $alert9 eq 'Y')
        {
            $newDueDate = $date60;
            $alert9 = 'N';
            $date60Refor = substr($date60,5,2).'/'.substr($date60,8,2).'/'.substr($date60,0,4);
        }
        if(defined($al->{'ALERTS_ID'}) && $al->{'ALERTS_ID'} == 10 && $alert10 eq 'N')
        {
            $newDueDate = $date25;
            $alert10 = 'Y';
            $date30Refor = substr($date30,5,2).'/'.substr($date30,8,2).'/'.substr($date30,0,4);
        }
        elsif(defined($al->{'ALERTS_ID'}) && $al->{'ALERTS_ID'} == 10 && $alert10 eq 'Y')
        {
            $newDueDate = $date30;
            $alert10 = 'N';
            $date30Refor = substr($date30,5,2).'/'.substr($date30,8,2).'/'.substr($date30,0,4);
        }
#        elsif(defined($al->{'ALERTS_ID'}) && $al->{'ALERTS_ID'} == 11)
#        { $newDueDate = $date30; }
#        elsif(defined($al->{'ALERTS_ID'}) && $al->{'ALERTS_ID'} == 12)
#        { $newDueDate = $date30; }
#        elsif(defined($al->{'ALERTS_ID'}) && $al->{'ALERTS_ID'} == 13)
#        {
#            $newDueDate = $date3;
#            $date3Refor = substr($date3,5,2).'/'.substr($date3,8,2).'/'.substr($date3,0,4);
#        }

#        if($currentDate ge $al->{'DUE_DATE'})
#        {
            my $updateAlerts = $ENGINE->{'DBH'}->prepare
                ("UPDATE CLAIMDB.CLM_ALERTS
                    SET COMPLETE = 'N',
                    END_DATE = '9999-01-01',
                    DUE_DATE = ?
                    WHERE CLM_ALERT_ID = ?")
                || $error->($ENGINE,'Alerts update prepare failed: '.$ENGINE->{'DBH'}->errstr);
            $updateAlerts->execute($newDueDate,$al->{'CLM_ALERT_ID'})
                || $error->($ENGINE,'Alerts update query execute failed: '.$ENGINE->{'DBH'}->errstr);
#            }
#        elsif(defined($al->{'ALERTS_ID'}) && $al->{'ALERTS_ID'} eq 11 || $al->{'ALERTS_ID'} eq 12 || $al->{'ALERTS_ID'} eq 13)
#        {
#            my $updateAlerts = $ENGINE->{'DBH'}->prepare
#                ("UPDATE CLAIMDB.CLM_ALERTS
#                    SET COMPLETE = 'Y',
#                    END_DATE = '9999-01-01',
#                    DUE_DATE = ?
#                    WHERE CLM_ALERT_ID = ?")
#                || $error->($ENGINE,'Alerts update prepare failed: '.$ENGINE->{'DBH'}->errstr);
#            $updateAlerts->execute($newDueDate,$al->{'CLM_ALERT_ID'})
#                || $error->($ENGINE,'Alerts update query execute failed: '.$ENGINE->{'DBH'}->errstr);
#        }
#        else
#        {
#            my $updateAlerts = $ENGINE->{'DBH'}->prepare
#                ("UPDATE CLAIMDB.CLM_ALERTS
#                    SET COMPLETE = 'N',
#                    END_DATE = '9999-01-01'
#                    WHERE CLM_ALERT_ID = ?")
#                || $error->($ENGINE,'Alerts update prepare failed: '.$ENGINE->{'DBH'}->errstr);
#            $updateAlerts->execute($al->{'CLM_ALERT_ID'})
#                || $error->($ENGINE,'Alerts update query execute failed: '.$ENGINE->{'DBH'}->errstr);
#        }

        my $selectVardata = $ENGINE->{'DBH'}->prepare
            ("SELECT VARDATA
                FROM CLAIMDB.CLM_VARDATA
                WHERE CLM_ALERT_ID = ?")
            || $error->($ENGINE,'CLM_VARDATA prepare failed: '.$ENGINE->{'DBH'}->errstr);
        $selectVardata->execute($al->{'CLM_ALERT_ID'})
            || $error->($ENGINE,'CLM_VARDATA query execute failed: '.$ENGINE->{'DBH'}->errstr);
        my $vardataResults = $selectVardata->fetchall_arrayref({})
            || $error->($ENGINE,'CLM_VARDATA query fetch  failed: '.$ENGINE->{'DBH'}->errstr);
        my $varData = $vardataResults->[0]->{'VARDATA'};

        my $newVarData = '';
        if(defined($al->{'ALERTS_ID'}) && $al->{'ALERTS_ID'} == 5)
        { $newVarData =  $date30Refor.' '.substr($varData,11); }
        elsif(defined($al->{'ALERTS_ID'}) && $al->{'ALERTS_ID'} == 7)
        { $newVarData =  $date90Refor.' '.substr($varData,11); }
        elsif(defined($al->{'ALERTS_ID'}) && $al->{'ALERTS_ID'} == 9)
        { $newVarData =  $date60Refor.' '.substr($varData,11); }
        elsif(defined($al->{'ALERTS_ID'}) && $al->{'ALERTS_ID'} == 10)
        { $newVarData =  $date30Refor.' '.substr($varData,11); }
#        elsif(defined($al->{'ALERTS_ID'}) && $al->{'ALERTS_ID'} == 13)
#        { $newVarData =  $date3Refor.' '.substr($varData,11); }

        my $updateVardata = $ENGINE->{'DBH'}->prepare
            ("UPDATE CLAIMDB.CLM_VARDATA
                SET VARDATA = ?
                WHERE CLM_ALERT_ID = ?")
            || $error->($ENGINE,'Vardata update prepare failed: '.$ENGINE->{'DBH'}->errstr);
        $updateVardata->execute($newVarData,$al->{'CLM_ALERT_ID'})
            || $error->($ENGINE,'Vardata update query execute failed: '.$ENGINE->{'DBH'}->errstr);
    }

    #close the Close Claim Pending notification for claims
    close_notification($ENGINE,{'type'=>ALERT_NTF_TYPE,
                                                            'msg_id'=>CLOSE_CLAIM_PEND,
                                'claim_id'=>$claimid});

    my $closeAlert = $ENGINE->{'DBH'}->prepare
        ("UPDATE CLAIMDB.CLM_ALERTS
                SET COMPLETE = 'Y',
                END_DATE = CURRENT DATE
            WHERE
                               CLAIM_ID = ?
                               AND ALERTS_ID = 16
                               AND COMPLETE = 'N'
                               AND DATE_DELETED = '9999-01-01-01.00.00.000000'")
        || $error->($ENGINE,'CLM_ALERT update prepare failed: '.$ENGINE->{'DBH'}->errstr);
    $closeAlert->execute($claimid)
        || $error->($ENGINE,'CLM_ALERT update execute failed: '.$ENGINE->{'DBH'}->errstr);

}

#this function is for the Claims Update function on the File Activity
#screen.
sub claimUpdateNotify
{

        my $ENGINE = shift;
    my %errors = ();
        my $claimid = $ENGINE->{'claimGeneral'}->{'CLAIM_ID'};
    my $error = $ENGINE->{'error'};

    #work fields
    my @userKeys = ();

    #when Claim Update on File Activity is complete, notify adjuster's immediate supervisor. Only
    #notify the supervisor of the person who did the Claim Update.

    #retrieve the boss' id from auth
    my $userSupervisorKey = $ENGINE->{'AUTH'}->{'SUPERVISOR_KEY'};
    push (@userKeys, $userSupervisorKey);

    #read the clm_notification table for the specific notification we
    #want to close for the specific adjuster.  We are doing this because if
    #there is more than one adjuster, they only want to close the notification
    #for the one who filled out the Claim Update and leave the other open.
    my $holdUserKeyIn = '';
    #This will close the specific adjusters and their supervisors claim update.
    if(defined($userSupervisorKey) && $userSupervisorKey > 0)
    { $holdUserKeyIn = '('.$ENGINE->{'AUTH'}->{'user_key'}.','.$userSupervisorKey.')'; }
    else
    { $holdUserKeyIn = '('.$ENGINE->{'AUTH'}->{'user_key'}.')'; }
    my $getNotification = $ENGINE->{'DBH'}->prepare
        ("SELECT N.NOTIFICATION_ID
                    FROM
                        CLAIMDB.CLM_BASKET B
                    INNER JOIN
                        CLAIMDB.CLM_NTF_HAS_BASKET H
                    ON B.BASKET_ID = H.BASKET_ID
                    INNER JOIN
                CLAIMDB.CLM_NOTIFICATION N
                    ON N.NOTIFICATION_ID = H.NOTIFICATION_ID
                  WHERE
                    N.CLAIM_ID = ?
            AND B.USER_KEY IN " . $holdUserKeyIn . " AND N.MESSAGE_ID = 5
                    AND COMPLETION_DATE IS NULL")
        || $error->($ENGINE,'CLM_NOTIFY prepare failed: '.$ENGINE->{'DBH'}->errstr);
    $getNotification->execute($claimid)
        || $error->($ENGINE,'CLM_NOTIFY query execute failed: '.$ENGINE->{'DBH'}->errstr);
    my $notify = $getNotification->fetchall_arrayref({})
        || $error->($ENGINE,'CLM_NOTIFY query fetch  failed: '.$ENGINE->{'DBH'}->errstr);

    #we want to close notifications for the one adjuster, but he might
    #be behind and have multiple notifications, we we want to close all
    #his notifications.
    if(scalar(@$notify) > 0)
    {
        for my $x (@{$notify})
        {
                #call function to close Claim Update for single adjuster that
                #completed the claim update. Do not close for all adjusters
                close_notification($ENGINE,{'type'=>ALERT_NTF_TYPE,
                                    'msg_id'=>COVERAGE_UPDATE_DUE,
                                    'ntf_id'=>$x->{'NOTIFICATION_ID'},
                                    'claim_id'=>$claimid});
        }
    }

    #call function sending notification about claim update complete to
    #adjusters boss
#    if(scalar(@userKeys) > 0)
    if(defined($userSupervisorKey) && $userSupervisorKey > 0)
    {
        my $adjuster_name = $ENGINE->{'AUTH'}->{'name'}.' ';
        if(substr($ENGINE->{'claimGeneral'}->{'SUBMIT_TO_IMT_DATE'},0,10) lt SPECIALIZATION_DATE)
        {$adjuster_name = '';}
            insert_notification($ENGINE,{'type'=>ALERT_NTF_TYPE,
                                'msg_id'=>COVERAGE_UPDATE_COMPLETED,
                                'claim_id'=>$claimid,
                                'comment'=> $adjuster_name.'Claim Update Complete',
#                               'close'=>1,
                                'users'=>$userSupervisorKey});
    }

    #mark alert on file activity screen as complete for adjuster who
    #did the update only.  So if there are 2 adjusters assigned, only
    #update the ONE
    my $closeAlert = $ENGINE->{'DBH'}->prepare
        ("UPDATE CLAIMDB.CLM_ALERTS
                SET COMPLETE = 'Y'
            WHERE
                               CLAIM_ID = ?
                               AND ALERTS_ID = 5
                               AND COMPLETE = 'N'
                               AND USER_KEY = ?
                               AND DATE_DELETED = '9999-01-01-01.00.00.000000'")
        || $error->($ENGINE,'CLM_ALERT update prepare failed: '.$ENGINE->{'DBH'}->errstr);
    $closeAlert->execute($claimid,$ENGINE->{'AUTH'}->{'user_key'})
        || $error->($ENGINE,'CLM_ALERT update execute failed: '.$ENGINE->{'DBH'}->errstr);


}


sub claimQuarterlyNotify
{
        my $ENGINE = shift;
    my %errors = ();
        my $claimid = $ENGINE->{'claimGeneral'}->{'CLAIM_ID'};
    my $error = $ENGINE->{'error'};

    #work fields
    my @userKeys = ();

    #when quarterly report is completed, notify the supervisor of the
    #all adjusters who are on the claim.

    #retrieve the boss' id from auth
    my $userSupervisorKey = $ENGINE->{'AUTH'}->{'SUPERVISOR_KEY'};
    push (@userKeys, $userSupervisorKey);

    #read the clm_notification table for the specific notification we
    #want to close for the specific adjuster.  We are doing this because if
    #there is more than one adjuster, they only want to close the notification
    #for the one who filled out the Claim Update and leave the other open.
    my $holdUserKeyIn = '';
    #This will close the specific adjusters and their supervisors claim update.
    if(defined($userSupervisorKey) && $userSupervisorKey > 0)
    { $holdUserKeyIn = '('.$ENGINE->{'AUTH'}->{'user_key'}.','.$userSupervisorKey.')'; }
    else
    { $holdUserKeyIn = '('.$ENGINE->{'AUTH'}->{'user_key'}.')'; }
    my $getNotification = $ENGINE->{'DBH'}->prepare
        ("SELECT N.NOTIFICATION_ID, N.MESSAGE_ID
                    FROM
                        CLAIMDB.CLM_BASKET B
                    INNER JOIN
                        CLAIMDB.CLM_NTF_HAS_BASKET H
                    ON B.BASKET_ID = H.BASKET_ID
                    INNER JOIN
                CLAIMDB.CLM_NOTIFICATION N
                    ON N.NOTIFICATION_ID = H.NOTIFICATION_ID
                  WHERE
                    N.CLAIM_ID = ?
            AND B.USER_KEY IN " . $holdUserKeyIn . " AND N.MESSAGE_ID IN (7,9,10)
                    AND COMPLETION_DATE IS NULL")
        || $error->($ENGINE,'CLM_NOTIFY prepare failed: '.$ENGINE->{'DBH'}->errstr);
    $getNotification->execute($claimid)
        || $error->($ENGINE,'CLM_NOTIFY query execute failed: '.$ENGINE->{'DBH'}->errstr);
    my $notify = $getNotification->fetchall_arrayref({})
        || $error->($ENGINE,'CLM_NOTIFY query fetch  failed: '.$ENGINE->{'DBH'}->errstr);

    if(scalar(@$notify) > 0)
    {
            if($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /600|605/ && $ENGINE->{'claimGeneral'}->{'PROP_OR_LIAB'} eq 'M')
            {
                for my $x (@{$notify})
                {
                    if($x->{'MESSAGE_ID'} == 9)
                    {
                        #call function to close Work Comp Medical Memo for all adjusters that
                        #are on the claim
                        close_notification($ENGINE,{'type'=>ALERT_NTF_TYPE,
                                                    'msg_id'=>WC_MEDICAL_MEMO,
                                                    'ntf_id'=>$x->{'NOTIFICATION_ID'},
                                                    'claim_id'=>$claimid});
                    }
                }
            }
            elsif($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /600|605/ && $ENGINE->{'claimGeneral'}->{'PROP_OR_LIAB'} eq 'D')
            {
            for my $x (@{$notify})
            {
                if($x->{'MESSAGE_ID'} == 10)
                {
                        #call function to close Work Comp Disability Memo for all adjusters that
                        #are on the claim
                        close_notification($ENGINE,{'type'=>ALERT_NTF_TYPE,
                                                    'msg_id'=>WC_DISABILITY_MEMO,
                                                    'ntf_id'=>$x->{'NOTIFICATION_ID'},
                                                    'claim_id'=>$claimid});
                    }
                }
            }
            else
            {
            for my $x (@{$notify})
            {
                if($x->{'MESSAGE_ID'} == 7)
                {
                        #call function to close Quarterly report for all adjusters that
                        #are on the claim
                        close_notification($ENGINE,{'type'=>ALERT_NTF_TYPE,
                                                    'msg_id'=>QUARTERLY_REPORT_DUE,
                                                    'ntf_id'=>$x->{'NOTIFICATION_ID'},
                                                    'claim_id'=>$claimid});
                    }
                }
            }
    }


    if($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} !~ /600|605/)
    {
            #call function sending notification about Quarterly Report complete to
            #adjusters bosses
            if(scalar(@userKeys) > 0)
            {
                my $adjuster_name = $ENGINE->{'AUTH'}->{'name'}.' ';
                if(substr($ENGINE->{'claimGeneral'}->{'SUBMIT_TO_IMT_DATE'},0,10) lt SPECIALIZATION_DATE)
                {$adjuster_name = '';}
                insert_notification($ENGINE,{'type'=>ALERT_NTF_TYPE,
                                        'msg_id'=>QUARTERLY_REPORT_COMPLETED,
                                        'claim_id'=>$claimid,
        #                                'close'=>1,
                                        'comment'=> $adjuster_name.'Quarterly Report Complete',
                                        'users'=>\@userKeys});
            }
    }

    my $alertID = 7;
    if($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /600|605/)
    {
        $alertID = 9;
        my $wc_type = '(Medical)';
        if($ENGINE->{'claimGeneral'}->{'PROP_OR_LIAB'} eq 'D')
        {
            $wc_type = '(Disability)';
            $alertID = 10;
        }
		my $adjuster_name = $ENGINE->{'AUTH'}->{'name'}.' ';
		if(substr($ENGINE->{'claimGeneral'}->{'SUBMIT_TO_IMT_DATE'},0,10) lt SPECIALIZATION_DATE)
		{$adjuster_name = '';}
        insert_notification($ENGINE,{'type'=>ALERT_NTF_TYPE,
                                    'msg_id'=>WC_MEMO_COMPLETED,
                                    'claim_id'=>$claimid,
                                    'comment'=> $adjuster_name.'Work Comp Memo '.$wc_type.' Completed',
                                    'users'=>\@userKeys});
    }
    #mark alert on file activity screen as complete for adjuster who
    #did the update only.  So if there are 2 adjusters assigned, only
    #update the ONE
#    my $closeAlert = $ENGINE->{'DBH'}->prepare
#        ("UPDATE CLAIMDB.CLM_ALERTS
#            SET COMPLETE = 'Y'
#            WHERE
#                CLAIM_ID = ?
#                AND ALERTS_ID = 7
#                AND COMPLETE = 'N'
#                AND USER_KEY in $userKeyIN
#                AND DATE_DELETED = '9999-01-01-01.00.00.000000'")
#        || $error->($ENGINE,'CLM_ALERT2 update prepare failed: '.$ENGINE->{'DBH'}->errstr);
    my $closeAlert = $ENGINE->{'DBH'}->prepare
        ("UPDATE CLAIMDB.CLM_ALERTS
                SET COMPLETE = 'Y'
            WHERE
                               CLAIM_ID = ?
                               AND ALERTS_ID = ?
                               AND COMPLETE = 'N'
                               AND USER_KEY = ?
                               AND DATE_DELETED = '9999-01-01-01.00.00.000000'")
        || $error->($ENGINE,'CLM_ALERT2 update prepare failed: '.$ENGINE->{'DBH'}->errstr);
    $closeAlert->execute($claimid,$alertID,$ENGINE->{'AUTH'}->{'user_key'})
        || $error->($ENGINE,'CLM_ALERT2 update execute failed: '.$ENGINE->{'DBH'}->errstr);


}

sub claimISOClose
{

        my $ENGINE = shift;
    my $whichOneToClose = shift;

    my %errors = ();
        my $claimid = $ENGINE->{'claimGeneral'}->{'CLAIM_ID'};
    my $error = $ENGINE->{'error'};

    #defaulting to ISO Property
    my $workID = ISO_PROPERTY;

    if ($whichOneToClose eq 'auto')
    {
             $workID = ISO_AUTO;
    }

    if ($whichOneToClose eq 'injury')
    {
             $workID = ISO_INJURY;
    }

        #call function to close ISO report notification for claim
    close_notification($ENGINE,{'type'=>ALERT_NTF_TYPE,
                                                            'msg_id'=>$workID,
                                'claim_id'=>$claimid});

    #mark alert on file activity screen as complete for adjuster who
    #did the update only.  So if there are 2 adjusters assigned, only
    #update the ONE
    my $closeAlert = $ENGINE->{'DBH'}->prepare
        ("UPDATE CLAIMDB.CLM_ALERTS
                SET COMPLETE = 'Y'
            WHERE
                               CLAIM_ID = ?
                               AND ALERTS_ID = ?
                               AND COMPLETE = 'N'
                               AND USER_KEY = ?
                               AND DATE_DELETED = '9999-01-01-01.00.00.000000'")
        || $error->($ENGINE,'CLM_ALERT2 update prepare failed: '.$ENGINE->{'DBH'}->errstr);
    $closeAlert->execute($claimid,$workID,$ENGINE->{'AUTH'}->{'user_key'})
        || $error->($ENGINE,'CLM_ALERT2 update execute failed: '.$ENGINE->{'DBH'}->errstr);

}

sub closeClaimPending
{
    my $ENGINE = shift;
    my %errors = ();
    my $claimid = $ENGINE->{'claimGeneral'}->{'CLAIM_ID'};
    my $error = $ENGINE->{'error'};

#lwm.sql - 2097
    #retrieve the claim reps on this claim
    my $getRepsQuery = $ENGINE->{'DBH'}->prepare
        ("SELECT G.USERNAME, R.USER_KEY
            FROM CLAIMDB.CLM_REP_ASSIGNED AS R
			JOIN GENSUPDB.USER_MAPPING G
            on G.LEGACY_USER_KEY = R.USER_KEY
            WHERE CLAIM_ID = ?
            and DATE_REMOVED = '9999-01-01-01.00.00.000000'
            and date_completed = '9999-01-01'
            ORDER BY CLM_REP_ASGN_ID")
        || $error->($ENGINE,'CLM_REP_ASSIGNED prepare failed: '.$ENGINE->{'DBH'}->errstr);
    $getRepsQuery->execute($claimid)
        || $error->($ENGINE,'CLM_REP_ASSIGNED query execute failed: '.$ENGINE->{'DBH'}->errstr);
    my $newReps = $getRepsQuery->fetchall_arrayref({})
        || $error->($ENGINE,'CLM_REP_ASSIGNED query fetch  failed: '.$ENGINE->{'DBH'}->errstr);

    #save first adjuster.
    my @userKey = ();
    push(@userKey,$newReps->[0]->{'USER_KEY'});

	my $user_obj = User_Obj->new({
		filter => {
			username => $newReps->[0]->{'USERNAME'},
		},
		auth_token => $ENGINE->{'AUTH'}->{'platform_access_token'}
	});

    my $supervisor_key = $user_obj->get_supervisor_legacy_key();

    if(defined($supervisor_key) && $supervisor_key gt '')
    { push(@userKey,$supervisor_key); }

    my ($curDay, $curMonth, $curYear) = (localtime)[3,4,5];
    $curYear = $curYear+1900;
    $curDay = length($curDay)<2 ? '0'.$curDay : $curDay;
    $curMonth = $curMonth+1;
    $curMonth = length($curMonth)<2 ? '0'.$curMonth : $curMonth;
    my $currentDate = $curMonth.'/'.$curDay.'/'.$curYear;

    my $dueDate = $curYear.'-'.$curMonth.'-'.$curDay;
    if($ENGINE->{'Monetary'}->{'CLM_GENERAL'}->[0]->{'CLOSE_RECOVERABLE'} eq 'P')
    {
        my @today = (localtime)[5,4,3];
                    $today[0] += 1900;
                    $today[1]++;
        my @addThirtyDays = Add_Delta_Days(@today, 30);
        $addThirtyDays[1] = length($addThirtyDays[1])<2 ? '0'.$addThirtyDays[1] : $addThirtyDays[1];
        $addThirtyDays[2] = length($addThirtyDays[2])<2 ? '0'.$addThirtyDays[2] : $addThirtyDays[2];
        $dueDate = $addThirtyDays[0].'-'.$addThirtyDays[1].'-'.$addThirtyDays[2];
    }

    my $alertsInsert = $ENGINE->{'DBH'}->prepare(
       "SELECT CLM_ALERT_ID
          FROM FINAL TABLE
                 (INSERT INTO CLAIMDB.CLM_ALERTS
                    (CLM_ALERT_ID,
                     CLAIM_ID,
                     USER_KEY,
                     ALERTS_ID,
                     HOW_OFTEN,
                     COMPLETE,
                     START_DATE,
                     END_DATE,
                     DAY_TO_SEND,
                     ALERT_TYPE,
                     DATE_ADDED,
                     DUE_DATE)
        VALUES (NEXT VALUE FOR CLAIMDB.CLM_ALERT_ID_SEQ,?,?,?,?,?,CURRENT DATE,?,?,?,CURRENT TIMESTAMP,?))")  || $error->($ENGINE);

    my $vardataInsert = $ENGINE->{'DBH'}->prepare(
       "SELECT CLM_VARDATA_ID
          FROM FINAL TABLE
                 (INSERT INTO CLAIMDB.CLM_VARDATA
                    (CLM_VARDATA_ID,
                     CLAIM_ID,
                     CLM_ALERT_ID,
                     LINE_TYPE,
                     LINE_CNTR,
                     DATA_TYPE,
                     VARDATA)
        VALUES (NEXT VALUE FOR CLAIMDB.CLM_VARDATA_ID_SEQ,?,?,?,?,?,?))")  || $error->($ENGINE);

        my $workId = CLOSE_CLAIM_PEND;
        for my $user (@userKey){
            $alertsInsert->execute($claimid,
                                         $user,
                                         $workId,
                                         'M',
                                         'N',
                                         '9999-01-01',
                                         $curDay,
                                         'I',
                                         $dueDate) || $error->($ENGINE);
            my $result = $alertsInsert->fetchall_arrayref({});
            if (scalar(@$result) < 1)
            {
               $error->($ENGINE);
            }

            my $insertedAlertID = $result->[0]->{'CLM_ALERT_ID'};

            my $alertWording = "Review for pending payment received";
            if($ENGINE->{'Monetary'}->{'CLM_GENERAL'}->[0]->{'CLOSE_RECOVERABLE'} eq 'P')
            {$alertWording = "Review Claim - Pending Recoverable Depreciation"}

            $vardataInsert->execute($claimid,
                                         $insertedAlertID,
                                         'D',
                                         1,
                                         'ALERT',
                                         $alertWording) || $error->($ENGINE);
            $vardataInsert->finish();
        }
}

1;
