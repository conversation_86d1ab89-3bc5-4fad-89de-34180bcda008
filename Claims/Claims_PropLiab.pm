#!/usr/local/bin/perl
package Claims_PropLiab;

require 5.000;
use strict;
use vars qw($VERSION @ISA @EXPORT_OK);

use Exporter;

@ISA = qw(Exporter);
@EXPORT_OK = qw(loadScreen);

$VERSION = '0.01';

use IMT::stringParsers qw(parseTimeString parseDateString parsePhoneString);
use Claims_Parties qw(getPartiesData editPartiesData storePartiesData);
use Claims_Misc qw(getHeader getMenu getClaimsCentral getClaimHeading getCauseOfLoss validateClaimID screenDataSync
                   getFooter editSubmitClaimData storeSubmitClaimData createSelect createCheckBox createTextInput
                   deleteLienholderMortgagee getClaimDetails createLabelHelp updateLastSave);
use Claims_Error qw(error);
use Claims_Constants qw(getPartyRoleCodes getStateAbbrev getTablePrimaryKeyName VARDATA_LENGTH VEH_VARDATA_LENGTH getIMTPolicyPrefix getCPPackageCodes getLiabRateCodes getTheftRiskCodes stateCountryCodes);
use Claims_Authority qw(getAuthorityData editAuthorityData storeAuthorityData);
use Claims_Veh_Total_Loss qw(getVehTotalLossData editVehTotalLossData storeVehTotalLossData);
use Claims_TransHist qw(transHistory);
use Claims_VARDATA qw(VARDATAsave VARDATAcreateTextArea VARDATAcreateTextInput VARDATAreadInput);
use Claims_OtherInfo qw(getOtherInfoData editOtherInfoData storeOtherInfoData);
use PolicyInterface qw(PolicyInterface);
use Claims_TrackerAPI qw(:NTF_TYPES :MGMT_BASKETS :MSG_IDS);
use GetCovLossLists qw(GetCoveragesList GetLosscodeList GetCauseOfLossList);
use Claims_Validation qw(CLM_PROPERTY_STAT__CONST_CODE CLM_PROPERTY_STAT__CF_OCCUPANCY_CODE CLM_PROPERTY_STAT__OCCUP_SIZE_LOC CLM_PROPERTY_STAT__DF_FIRE_DETECT
                         CLM_PROPERTY_STAT__DF_BURGLR_ALARM CLM_PROPERTY_STAT__DF_PRIMARY_HEAT CLM_PROPERTY_STAT__CF_CRIME_OFFP CLM_PROPERTY_STAT__DF_SMOKE_DETECT
                         CLM_PROPERTY_STAT__MH_TIE_DOWN CLM_PROPERTY_STAT__CF_LEGALIAB_TYPCOD CLM_PROPERTY_STAT__PROTECTIVE_DEV CLM_PROPERTY_STAT__CF_EC_SYMBOL
                         CLM_PROPERTY_STAT__CF_EXTRAEXP_MTHLIM CLM_PROPERTY_STAT__CF_MANUAL_RATE_IND CLM_PROPERTY_STAT__CF_SPRINKLEAK_SUS
                         CLM_PROPERTY_STAT__HO_PELLET_HEATING CLM_PROPERTY_STAT__LOSS_SETTLEMENT_TYPE CLM_PROPERTY_STAT__PROTECT_CODE CLM_PROPERTY_STAT__DF_RESIDENCE_TYPE
                         CLM_PROPERTY_STAT__BOART_RATE_IDENT CLM_PROPERTY_STAT__BOART_TYPE_OF_PAK CLM_PARTIES__ENTITY_CODE CLM_PROPERTY_STAT__HO_NUM_UNITS
                         CLM_PROPERTY_STAT__BOART_AAIS_FORM CLM_LIAB_STAT__PK_PROG_CODE CLM_BOND_INFO__BOND_TYPE CLM_LIAB_STAT__UMBRELLA_FH CLM_ASSIGNMENT_FIELDS__STORM_TYPE
                         CLM_ASSIGNMENT_FIELDS__SEVERITY_OF_DAMAGES);

use Claims::Claims_XactAnalysis qw (type_of_loss_mapping cause_of_loss_mapping create_XactObj show_XactAnalysis check_button_pressed);
use JSON;

my %iUpkeep = (
' '=>'',
'C'=>'Clean',
'A'=>'Average (some stains, some wall dings)',
'P'=>'Poor (carpet stains, paint peeling, holes in wall)',
'U'=>'Unknown');
my %eUpkeep = (
' '=>'',
'G'=>'Good',
'N'=>'Needs Minor Maintenance',
'P'=>'Poor',
'U'=>'Unknown');
my %yUpkeep = (
' '=>'',
'G'=>'Good (Well kept, no junk lying around)',
'N'=>'Needs Improvement (some minor issues)',
'P'=>'Poor (lawn not cared for, junk lying around)',
'U'=>'Unknown');
my %yesNo = (
'Y'=>'Yes',
'N'=>'No',
'U'=>'Unknown');
sub loadScreen
{

   my $ENGINE = shift;
   my $error = $ENGINE->{'error'};
   my $sessID = $ENGINE->{'SESSION'}->{'sessionID'};
   my $action = $ENGINE->{'ACTION'};

   my $name = $ENGINE->{'AUTH'}->{'name'};
   my $policyNumber = $ENGINE->{'claimGeneral'}->{'POLICY_NUMBER'};
   my $claimid = $ENGINE->{'claimGeneral'}->{'CLAIM_ID'};
   my $LineCode = $ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'};
   my $UserType = $ENGINE->{'AUTH'}->{'IMTOnline_UserType'};
   my $ManualOrWhat = $ENGINE->{'claimGeneral'}->{'MANUAL_OR_WHAT'};
   my $lossDate10 = substr($ENGINE->{'claimGeneral'}->{'LOSS_DATE_TIME'},0,10);
   my $policyLocationSelect = '';
   $ENGINE->{'loading'} = 'Claims_PropLiab';
   my $focusSet = '';

   # Also look for 300 & 330 to add the policy prefix.
   my $policyNum = $policyNumber;
   if($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /300|301|302|330|331|332/)
   { $policyNum =~ s/^12|^14|^26|^40|^48//g; }

     # Policy print (Mobius)
        my $decPages = '<div style="height:1.4em;"></div>';;
        my $maxDate = '';
        my $decForm = '';
    if(defined $ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} && $ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} eq 'P')
    {
            my $policyNumLength = length($policyNum);
            if($policyNumLength gt 7)
            { $policyNum = substr($policyNum,0,7); }
            my $sth = $ENGINE->{'DBH'}->prepare('
                select  LINE_OF_BUSINESS,DATE_PROCESSED,
                        POLICY_EFF_DATE,TYPE_OF_DOC,
                        TOPIC_KEY
                from    GENSUPDB.MOBIUS
                where   POLICY_NUMBER = ? AND TYPE_OF_DOC IN (\'NEW BUSINESS\',\'AMENDMENT\',\'RENEWAL\')
        AND POLICY_EFF_DATE < ?') || error($ENGINE,$ENGINE->{'DBH'}->errstr);
            $sth->execute($policyNum,$lossDate10) || error($ENGINE,$ENGINE->{'DBH'}->errstr);
            my $mobius = $sth->fetchall_arrayref({});
            @$mobius = reverse sort{$a->{'DATE_PROCESSED'} cmp $b->{'DATE_PROCESSED'}} @$mobius;

            if((scalar @$mobius)==0) {
                $decPages = '<div style="height:1.7em;">No print available.</div>';
            }
            else {
                for my $m (@$mobius)
                 {
                    if($m->{'POLICY_EFF_DATE'}.'_'.$m->{'DATE_PROCESSED'} gt $maxDate)
                    {
                        $decPages = '<input type="button" onclick="var f = document.getElementById(\'decForm\');f.target=\'_blank\';f.submit()" value="Show Dec Page" />';
        #                $decForm = '<form id="decForm" method="post" action="../policydocs/pd_mobiuspdf.pl"><div>'.
                        $decForm = '<form id="decForm" method="post" action="../../agent_dashboard/policy-center/pd_mobiuspdf.pl"><div>'.
                                   '<input type="hidden" name="TOPIC1" value="'.$m->{'TOPIC_KEY'}.'" />'.
                                   '<input type="hidden" name="ACTG1" value="1" />'.
                                   '<input type="hidden" name="LINE_OF_BUSINESS1" value="'.$m->{'LINE_OF_BUSINESS'}.'" />'.
                                   '<input type="hidden" name="POLICY_EFF_DATE1" value="'.$m->{'POLICY_EFF_DATE'}.'" />'.
                                   '<input type="hidden" name="DEC_TYPE" value="3" /></div></form>';
                        $maxDate = $m->{'POLICY_EFF_DATE'}.'_'.$m->{'DATE_PROCESSED'};
                    }

                }
            }
    }

    my $agentRed = '';
    #make the red * variable for Agents only
    if ($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} ne 'Internal'
            && defined $ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'}
                   && $ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} eq 'P'
                   && !$ENGINE->{'READONLY'})
    {
            $agentRed = '<span style="color:red;">*</span>';
    }

    my $agentRedAst = '';
   if (defined $ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'}
                   && $ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} eq 'P'
                   && !$ENGINE->{'READONLY'})
   {
            $agentRedAst = '<span style="color:red">*</span>';
   }

   my $screenErrors = 'N';
   if (scalar(keys %{$ENGINE->{'errors'}}) == 0)
   {
      undef $ENGINE->{'PropLiab'};
      undef $ENGINE->{'PropLiab'}->{'propLiabErrors'};
      undef $ENGINE->{'locRef'};
   }

   if(defined $ENGINE->{'PropLiab'}->{'propLiabErrors'} && $ENGINE->{'PropLiab'}->{'propLiabErrors'} eq 'Y')
   { $screenErrors = 'Y'; }

#   die Data::Dumper::Dumper($ENGINE);
   if (!$ENGINE->{'READONLY'})
   {
       if (!defined($ENGINE->{'locRef'}))
       {
         my $errInd = '';
         my $lossDate = '';
         my $lossDateChange = $ENGINE->{'CGI'}->param('lossDateChange')||'';
         if(defined($lossDateChange) && $lossDateChange eq 'Y' && !(scalar(keys %{$ENGINE->{'errors'}}) > 0))
         {
            $lossDate = $ENGINE->{'CGI'}->param('lossDate')||'';
            if ($lossDate =~ /^(\d\d)(\d\d)(\d\d|\d\d\d\d)$/ || $lossDate =~ /^(\d\d?)[^0-9](\d\d?)[^0-9](\d\d|\d\d\d\d)$/)
            {
               my $day = length($2)<2 ? '0'.$2 : $2;
               my $month = length($1)<2 ? '0'.$1 : $1;
               my $year = length($3)<3 ? 2000 + $3 : $3;
               if (length($year)>4)
               {
                  $year = substr($year,0,4);
               }
               $lossDate = $month.'/'.$day.'/'.$year;
            }
         }
         else
         {
            $lossDate = $ENGINE->{'claimGeneral'}->{'LOSS_DATE_TIME'};
            if (length($lossDate) >= 10)
            {
               $lossDate = substr($lossDate,5,2).'/'.substr($lossDate,8,2).'/'.substr($lossDate,0,4);
            }
         }

         my $PIReturn = PolicyInterface($ENGINE,{'polNo'=>$ENGINE->{'claimGeneral'}->{'POLICY_NUMBER'},'lossDate'=>$lossDate,'claimid'=>$claimid,'trans'=>'PROPLIST'});
         $errInd = $PIReturn->{'errInd'};

         $ENGINE->{'locRef'} = $PIReturn->{'locRef'};
         my $progErr = $PIReturn->{'progErr'};
       }
       elsif(!defined($ENGINE->{'locRef'}) && (scalar(keys %{$ENGINE->{'errors'}}) > 0))
       {
           my $errInd = '';
           my $lossDate = '';
            $lossDate = $ENGINE->{'claimGeneral'}->{'LOSS_DATE_TIME'};
            if (length($lossDate) >= 10)
            {
               $lossDate = substr($lossDate,5,2).'/'.substr($lossDate,8,2).'/'.substr($lossDate,0,4);
            }

            my $PIReturn = PolicyInterface($ENGINE,{'polNo'=>$ENGINE->{'claimGeneral'}->{'POLICY_NUMBER'},'lossDate'=>$lossDate,'claimid'=>$claimid,'trans'=>'PROPLIST'});
            $errInd = $PIReturn->{'errInd'};

            $ENGINE->{'locRef'} = $PIReturn->{'locRef'};
            my $progErr = $PIReturn->{'progErr'};
       }

        if (defined($ENGINE->{'locRef'}))
        {
                #loop thru locations from the policy system and put
                #the unit and location
                #number data into a common field name.
            for my $Loc (@{$ENGINE->{'locRef'}})
            {

                #for some reason we don't have the same field name for the location
                #and unit number coming from different interfaces.  These should
                #have been named the same in the interface.pm files but weren't
                #for some reason.
                if (defined $Loc->{'PropNum'}
                    && $Loc->{'PropNum'} gt '')
                {
                    $Loc->{'location_no'} = $Loc->{'PropNum'};
                }
                elsif (defined $Loc->{'locNo'}
                    && $Loc->{'locNo'} gt '')
                {
                    $Loc->{'location_no'} = $Loc->{'locNo'};
                }
                elsif (defined $Loc->{'PropNum'}
                    && $Loc->{'PropNum'} gt '')
                {
                    $Loc->{'location_no'} = $Loc->{'PropNum'};
                }
                elsif (defined $Loc->{'LocNum'}
                    && $Loc->{'LocNum'} gt '')
                {
                    $Loc->{'location_no'} = $Loc->{'LocNum'};
                }
                else
                {
                    $Loc->{'location_no'}   = 0;
                }

                #get the building number
                if (defined $Loc->{'BldNum'}
                    && $Loc->{'BldNum'} gt '')
                {
                    $Loc->{'building_no'} = $Loc->{'BldNum'};
                }
                elsif (defined $Loc->{'bldNum'}
                    && $Loc->{'bldNum'} gt '')
                {
                    $Loc->{'building_no'} = $Loc->{'bldNum'};
                }
                else
                {
                    $Loc->{'building_no'} = 0;
                }

                if (defined $Loc->{'Subline'}
                    && $Loc->{'Subline'} gt '')
                {
                    $Loc->{'subline_code'} = $Loc->{'Subline'};
                }
                else
                {
                    $Loc->{'subline_code'} = '';
                }

                #format the location, building and subline fields
                $Loc->{'subline_code'} =~ s/\s*$//;
                $Loc->{'building_no'} =~ s/\s*$//;
                $Loc->{'location_no'} =~ s/\s*$//;

                #set the default onclaim indicator
                $Loc->{'ONCLAIM'} = 'N';

            }
        }

   }

   $ENGINE->{'head'} = <<EOF;
<style type="text/css">th { vertical-align:top }</style><script type="text/javascript" src="../Common/popupCalendar2909.js"></script><script type="text/javascript" src="Claims.js"></script>
<script type="text/javascript" src="claim_scripts_Monetary.js"></script>
<script type="text/javascript" src="Claims_Parties.js?v=1.0.1"></script>
<script type="text/javascript" src="Claims_OtherInfo.js"></script>
<script type="text/javascript" src="Claims_PropLiab.js"></script>
<script type="text/javascript" src="lossDatePopupCalendar2909.js"></script>
<script type="text/javascript">
</script>
EOF

   my $partiesResults = getPartiesData($ENGINE);
   $ENGINE->{'head'} .= $partiesResults->{'partiesHead'};

   #The loop below is looking to see if there are any close errors and add them to the $ENGINE->{'errors'}.
   my %params = $ENGINE->{'CGI'}->Vars();

   my %errorsNew = ();
   my $closeErrorSW = 'N';
   for my $key (keys %params)
   {
       if($key =~ /^ZZ/)
       {
           $closeErrorSW = 'Y';
           my @closeErrorArray = split('_', $ENGINE->{'CGI'}->param($key));
           push(@{$errorsNew{$closeErrorArray[0]}},$closeErrorArray[1]);
       }
   }
   if($closeErrorSW eq 'Y')
   { $ENGINE->{'errors'} = \%errorsNew; }

   $ENGINE->{'output'} .= getHeader($ENGINE);
   $ENGINE->{'output'} .= getMenu($ENGINE);
   $ENGINE->{'output'} .= "<!--/header--></div>";
   $ENGINE->{'output'} .= '<div class="row">' . getClaimDetails($ENGINE) . '</div>';
   my @sortedVardataResults = ();
   if (scalar(keys %{$ENGINE->{'errors'}}) > 0 && $closeErrorSW eq 'N')
   {
      if (defined($ENGINE->{'PropLiab'}->{'CLM_VARDATA'}))
      {
         @sortedVardataResults = sort({$a->{'LINE_CNTR'} <=> $b->{'LINE_CNTR'}} @{$ENGINE->{'PropLiab'}->{'CLM_VARDATA'}});
      }
   }
   else
   {
      my $descQuery = $ENGINE->{'DBH'}->prepare(
         'SELECT *
            FROM CLAIMDB.CLM_VARDATA
           WHERE CLAIM_ID = ?
             AND DATA_TYPE IN (\'DESCRIPT\',\'DETAILS\',\'LOCATION\',\'LOSSSTOTH\')
             AND DATE_DELETED = \'9999-01-01 01:00:00.000000\'') || $error->($ENGINE,'Variable data query prepare failed: '.$ENGINE->{'DBH'}->errstr);

      $descQuery->execute($claimid) || $error->($ENGINE,'Variable data query execute failed: '.$ENGINE->{'DBH'}->errstr);

      my $descResults = $descQuery->fetchall_arrayref({});

      @sortedVardataResults = sort({$a->{'LINE_CNTR'} <=> $b->{'LINE_CNTR'}} @$descResults);
   }

#   my $lossLocationVardata = VARDATAcreateTextInput($ENGINE,{'attributes'=>{'name'=>'lossLocation','size'=>'100'},'vardata'=>\@sortedVardataResults,'keys'=>{'LINE_TYPE'=>'L','DATA_TYPE'=>'LOCATION'}});
   my $lossLocationHTML = VARDATAcreateTextInput($ENGINE,{'attributes'=>{'name'=>'lossLocation','id'=>'lossLocation', 'size'=>'50','maxlength'=>'50'},'vardata'=>\@sortedVardataResults,'keys'=>{'LINE_TYPE'=>'L','DATA_TYPE'=>'LOCATION'}});

   my $lossDesc = VARDATAcreateTextInput($ENGINE,{'attributes'=>{'id'=>'lossDescription','name'=>'lossDescription','maxlength'=>'132'},'vardata'=>\@sortedVardataResults,'keys'=>{'LINE_TYPE'=>'D','DATA_TYPE'=>'DESCRIPT'}});

   my $lossDetail = VARDATAcreateTextArea($ENGINE,{'attributes'=>{'name'=>'lossDetail','id'=>'lossDetail','cols'=>'40','rows'=>'5'},'vardata'=>\@sortedVardataResults,'keys'=>{'LINE_TYPE'=>'D','DATA_TYPE'=>'DETAILS'}});

   my $showLossDetail = 'display:none';
   my $loss_desc_err = '';
   if($ENGINE->{'errors'}->{'lossDetail'})
   {
        $showLossDetail = 'display:';
        $loss_desc_err = $ENGINE->{'errors'}->{'lossDetail'}[0];
   }
   if($lossDetail =~ /Unknown/)
   {$lossDetail = '';}

   my $homePolicy = '';
   my $liabPolicy = '';
   my $businPolicy = '';
   my $glPolicy = '';
   my $cfPolicy = '';
   my $arPolicy = '';
   my $imPolicy = '';
   my $dpPolicy = '';
   my $upPolicy = '';
   my $ucPolicy = '';

   my $policyPrefix = substr($policyNumber,0,2);
   my $policyPrefix3 = substr($policyNumber,0,3);

   if ($policyPrefix eq 'HM' or $policyPrefix eq 'MB' or $policyPrefix eq 'HO')
   {
      $homePolicy = 'Y';
   }
   elsif ($policyPrefix3 eq 'WHM' or $policyPrefix3 eq 'WMH')
   {
      $homePolicy = 'Y';
   }
   elsif ($policyPrefix eq 'FL' or $policyPrefix eq 'CP' or $policyPrefix =~ /^(\d\d)/)
   {
      $liabPolicy = 'Y';
   }
   elsif ($policyPrefix eq 'BO' or $policyPrefix eq 'WO')
   {
      $businPolicy = 'Y'
   }
   elsif ($policyPrefix eq 'GL')
   {
      $glPolicy = 'Y'
   }
   elsif ($policyPrefix eq 'CF')
   {
      $cfPolicy = 'Y'
   }
   elsif ($policyPrefix eq 'AR')
   {
      $arPolicy = 'Y'
   }
   elsif ($policyPrefix eq 'IP' or $policyPrefix eq 'IC')
   {
      $imPolicy = 'Y'
   }
   elsif ($policyPrefix eq 'DP')
   {
             $dpPolicy = 'Y';
   }
   elsif ($policyPrefix eq 'UP')
   {
             $upPolicy = 'Y';
   }
   elsif ($policyPrefix eq 'UC')
   {
             $ucPolicy = 'Y';
   }

   my $lossLocationHeading = '';
   my $lochelp = '';
   if($homePolicy eq 'Y')
   { $lossLocationHeading = $agentRedAst.'Loss Location';  $lochelp = 'Describe where the loss occurred.  If the exact address is known please enter it.  If not known please be as descriptive as possible.  Example: White house on NE corner of 5th and Grand, Des Moines.' }
   else
   { $lossLocationHeading = 'Enter Loss Location if not on policy'; $lochelp = 'This is required if a location was not selected from the Select Loss Location From Policy field.' }

   my $moreDetailsButton = '';
   my $detailButtonValue = '';
   my $switchFunction = '';

   if(!$ENGINE->{'READONLY'})
   {
      my $showDetails = '';
      $switchFunction = 'switchDetail()';
      for my $v (@sortedVardataResults)
      {
         if (defined($v->{'DATA_TYPE'}) && $v->{'DATA_TYPE'} eq 'DETAILS' && defined($v->{'DATE_DELETED'}) && $v->{'DATE_DELETED'} eq '9999-01-01 01:00:00.000000')
         {
            $showDetails = 'Y';
         }
      }

      if ($showDetails eq 'Y')
      {
         $detailButtonValue = 'View Details';
      }
      else
      {
         $detailButtonValue = 'View Details';
      }
   }
   else
   {
      $switchFunction = 'switchDetailRead()';
      $detailButtonValue = 'View Details';
   }

   if($lossDetail ne '')
   {
       $moreDetailsButton = <<EOF;
<a class="showhide" onclick="toggle('detailText');$switchFunction" name="showDetButton" id="showDetButton">$detailButtonValue</a>
EOF
   }


   my %occupancyCodes = ();
   if ($LineCode =~ /100/)
   {
      %occupancyCodes = CLM_PROPERTY_STAT__CF_OCCUPANCY_CODE();
   }

   my %primaryHeatCodes = ();
   if ($LineCode =~ /100/)
   {
      %primaryHeatCodes = CLM_PROPERTY_STAT__DF_PRIMARY_HEAT();
   }

   my %smokeDetectorCodes = ();
   if ($LineCode =~ /100/)
   {
      %smokeDetectorCodes = CLM_PROPERTY_STAT__DF_SMOKE_DETECT();
   }

   my %fireDetectorCodes = ();
   if ($LineCode =~ /100/)
   {
      %fireDetectorCodes = CLM_PROPERTY_STAT__DF_FIRE_DETECT();
   }

   my %burglarAlarmCodes = ();
   if ($LineCode =~ /100/)
   {
      %burglarAlarmCodes = CLM_PROPERTY_STAT__DF_BURGLR_ALARM();
   }

   my %vacantCodes = ();
   if ($LineCode =~ /105/)
   {
      %vacantCodes = CLM_PROPERTY_STAT__OCCUP_SIZE_LOC();
   }

   my %crimeOffPremCodes = ();
   if ($LineCode =~ /105/)
   {
      %crimeOffPremCodes = CLM_PROPERTY_STAT__CF_CRIME_OFFP();
   }

   my %ecSymbolCodes = ();
   if ($LineCode =~ /105/)
   {
      %ecSymbolCodes = CLM_PROPERTY_STAT__CF_EC_SYMBOL();
   }

   my %pctMonthlyLmtCodes = ();
   if ($LineCode =~ /105/)
   {
      %pctMonthlyLmtCodes = CLM_PROPERTY_STAT__CF_EXTRAEXP_MTHLIM();
   }

   my %manualRateCodes = ();
   if ($LineCode =~ /105/)
   {
      %manualRateCodes = CLM_PROPERTY_STAT__CF_MANUAL_RATE_IND();
   }

   my %eqSprinklerLeakCodes = ();
   if ($LineCode =~ /105/)
   {
      %eqSprinklerLeakCodes = CLM_PROPERTY_STAT__CF_SPRINKLEAK_SUS();
   }

   my %fireLegalCodes = ();
   if ($LineCode =~ /105/)
   {
      %fireLegalCodes = CLM_PROPERTY_STAT__CF_LEGALIAB_TYPCOD();
   }

   my %protectDeviceCodes = ();
   if ($LineCode =~ /105|580/)
   {
      %protectDeviceCodes = CLM_PROPERTY_STAT__PROTECTIVE_DEV($lossDate10);
   }

   my %CPPackageCodes = ();
   if ($LineCode =~ /105/)
   {
      %CPPackageCodes = getCPPackageCodes();
   }

   my %CPTheftRiskCodes = ();
   if ($LineCode =~ /105/)
   {
      %CPTheftRiskCodes = getTheftRiskCodes();
   }

   my %pelletHeatCodes = ();
   if ($LineCode =~ /112|113/)
   {
      %pelletHeatCodes = CLM_PROPERTY_STAT__HO_PELLET_HEATING();
   }

   my %numUnitsCodes = ();
   if ($LineCode =~ /112|113/)
   {
      %numUnitsCodes = CLM_PROPERTY_STAT__HO_NUM_UNITS();
   }

   my %protectCodes = ();
   if ($LineCode =~ /100|105|110|111|112|113|200|205|580/)
   {
      %protectCodes = CLM_PROPERTY_STAT__PROTECT_CODE($LineCode);
   }

   my %residenceTypeCodes = ();
   if ($LineCode =~ /100|110|111|112|113/)
   {
      %residenceTypeCodes = CLM_PROPERTY_STAT__DF_RESIDENCE_TYPE();
   }

   my %lossSettleCodes = ();
   if ($LineCode =~ /112|113|120/)
   {
      %lossSettleCodes = CLM_PROPERTY_STAT__LOSS_SETTLEMENT_TYPE();
   }

   my %mhTieDownCodes = ();
   if ($LineCode =~ /120/)
   {
      %mhTieDownCodes = CLM_PROPERTY_STAT__MH_TIE_DOWN();
   }

   my %constCodes = ();
   if ($LineCode =~ /100|105|112|113|200|205|575|580/)
   {
      %constCodes = CLM_PROPERTY_STAT__CONST_CODE($LineCode);
   }

   my %liabRateCodes = ();
   if ($LineCode =~ /300|301|302|330|331|332/)
   {
      %liabRateCodes = getLiabRateCodes();
   }

   my %umbrella_FHCodes = ();
   if ($LineCode =~ /350/)
   {
      %umbrella_FHCodes = CLM_LIAB_STAT__UMBRELLA_FH();
   }

   my %ratingIDCodes = ();
   if ($LineCode =~ /810|814|816/)
   {
      %ratingIDCodes = CLM_PROPERTY_STAT__BOART_RATE_IDENT();
   }

#   my %bondTypeCodes = ();
#   if ($LineCode =~ /550/)
#   {
#      %bondTypeCodes = CLM_BOND_INFO__BOND_TYPE();
#   }

   my %packageCodes = ();
   if ($LineCode =~ /575/)
   {
      %packageCodes = CLM_PROPERTY_STAT__BOART_TYPE_OF_PAK();
   }

   my %entityCodes = ();
   if ($LineCode =~ /810|814|816/)
   {
      %entityCodes = CLM_PARTIES__ENTITY_CODE();
   }

   my %AAISForm = ();
   if ($LineCode =~ /575|580/)
   {
      %AAISForm = CLM_PROPERTY_STAT__BOART_AAIS_FORM();
   }

   my %GLPackageCodes = ();
   if ($LineCode =~ /810|814|816/)
   {
      %GLPackageCodes = CLM_LIAB_STAT__PK_PROG_CODE();
   }

    my %stormType = ();
    if($LineCode !~ /300|301|302|330|331|332|810|814|816/)
    {
        %stormType = CLM_ASSIGNMENT_FIELDS__STORM_TYPE();
    }

    my %severityOfDamages = ();
    if($LineCode !~ /300|301|302|330|331|332|810|814|816/)
    {
        %severityOfDamages = CLM_ASSIGNMENT_FIELDS__SEVERITY_OF_DAMAGES();
    }


   my $partiesData = $partiesResults->{'partiesData'};
   my $insuredInfo = $partiesResults->{'insuredInfo'};
   my $contactInfo = $partiesResults->{'contactInfo'};
   my $insuredOne = $partiesResults->{'insuredOne'};
   my $insuredTwo = $partiesResults->{'insuredTwo'};
   my $insuredAddress = $partiesResults->{'insuredAddress'};
   my $witnessData = $partiesResults->{'witnessData'};
   my $witnesses = $partiesResults->{'witnesses'};
   my $manufacturerData = $partiesResults->{'manufacturerData'};
   my $otherInfoDataA = $partiesResults->{'otherInfoDataA'};
   my %driverNames = %{$partiesResults->{'driverNames'}};
   my %driverNumbers = %{$partiesResults->{'driverNumbers'}};
   my $otherInfoResults = getOtherInfoData($ENGINE);
   my $otherInfoData1 = $otherInfoResults->{'otherInfoData1'};
   my $otherInfoData2 = $otherInfoResults->{'otherInfoData2'};
#   my $otherInfoData3 = $otherInfoResults->{'otherInfoData3'};

   $insuredInfo =  '<div><b>'.$insuredOne.$insuredTwo.$insuredAddress.'</b></div>';

   my $claimLossDate = '';
   my $lossDateChange = $ENGINE->{'CGI'}->param('lossDateChange')||'';

   if (defined($lossDateChange) && $lossDateChange eq 'Y')
   {
      $claimLossDate = $ENGINE->{'CGI'}->param('lossDate')||'';
   }
   else
   {
      $claimLossDate = substr($ENGINE->{'claimGeneral'}->{'LOSS_DATE_TIME'},5,2).'/'.substr($ENGINE->{'claimGeneral'}->{'LOSS_DATE_TIME'},8,2).'/'.substr($ENGINE->{'claimGeneral'}->{'LOSS_DATE_TIME'},0,4);
      if ($claimLossDate eq '01/01/9999')
      {
         $claimLossDate = 'Unknown';
      }
   }

   my ($curDay, $curMonth, $curYear) = (localtime)[3,4,5];
   $curYear = $curYear+1900;
   $curDay = length($curDay)<2 ? '0'.$curDay : $curDay;
   $curMonth = $curMonth+1;
   $curMonth = length($curMonth)<2 ? '0'.$curMonth : $curMonth;
   my $currentDate = $curMonth.'/'.$curDay.'/'.$curYear;

   my $printLossDate = '';
   if (scalar(keys %{$ENGINE->{'errors'}}) > 0 && $closeErrorSW eq 'N')
   {
      $printLossDate = $ENGINE->{'CGI'}->param('reportDate')||'';
   }
   else
   {
          $printLossDate = substr($ENGINE->{'claimGeneral'}->{'REPORTED_DATE'},5,2).'/'.substr($ENGINE->{'claimGeneral'}->{'REPORTED_DATE'},8,2).'/'.substr($ENGINE->{'claimGeneral'}->{'REPORTED_DATE'},0,4);
          if ($printLossDate eq '01/01/9999')
          {
             $printLossDate = $currentDate;
          }
   }

   my $accidentState = '';
   my $accidentStateSelect = '';

   if ($ManualOrWhat eq 'M')
   {
      $accidentState = $ENGINE->{'claimGeneral'}->{'ACCIDENT_STATE'} || $ENGINE->{'claimGeneral'}->{'POLICY_STATE'};
      # Initializing select variable to ACCIDENT_STATE value so it will display correctly for closed claims
      $accidentStateSelect = $ENGINE->{'claimGeneral'}->{'ACCIDENT_STATE'} || $ENGINE->{'claimGeneral'}->{'POLICY_STATE'};
   }
   else
   {
      $accidentState = $ENGINE->{'claimGeneral'}->{'ACCIDENT_STATE'};
      # Initializing select variable to ACCIDENT_STATE value so it will display correctly for closed claims
      $accidentStateSelect = $ENGINE->{'claimGeneral'}->{'ACCIDENT_STATE'};
   }

   my $lossStateOtherText = '';
   my $stateOtherStyle = '';

#   my $bondTypeType = '';
#   my $bondType = '';
#   my $bondInfoId = '';
#   my $displayBond = 'none';
#   if($LineCode =~ /550/)
#   {
#      my $bondQuery = $ENGINE->{'DBH'}->prepare(
#         'SELECT *
#            FROM CLAIMDB.CLM_BOND_INFO
#           WHERE CLAIM_ID = ?
#             AND DATE_DELETED = \'9999-01-01 01:00:00.000000\'') || $error->($ENGINE,'Bond Info data query prepare failed: '.$ENGINE->{'DBH'}->errstr);

#      $bondQuery->execute($claimid) || $error->($ENGINE,'Bond Info data query execute failed: '.$ENGINE->{'DBH'}->errstr);

#      my $bondResults = $bondQuery->fetchall_arrayref({});
#      $displayBond = '';
#      for my $b (@$bondResults)
#      {
#         if (defined($b->{'BOND_TYPE'}) && $b->{'BOND_TYPE'} gt '')
#         {
#            $bondType = $b->{'BOND_TYPE'};
#            for my $key (keys %bondTypeCodes)
#            {
#                if($bondType eq $b->{'BOND_TYPE'})
#                {
#                    $bondTypeType = $bondTypeCodes{$key};
#                    $bondInfoId = $b->{'CLM_BOND_INFO_ID'};
#                }
#            }
#         }
#      }
#   }
   my $stormClaim = '';
   my $propLiab = '';
   my $claimType = '';
   my $badClaimType = '';
   my $badClaimStorm = '';
   my $stormHTML = '';
   my $badassignCov = '';
   my $badassignTypeOfLoss = '';
   my $badassignCauseOfLoss = '';
   my $addCovBut = '';
   my $storm_type = '';
   my $coverageIn = '';
   my $lossGroup = '';
   my $lossCode = '';
   my $causeOfLoss = '';
   my $showCauseOfLoss = 0;
   my $assignCovHTML = '';
   my $severityOfDamage = '';
   my $sevDamageHTML = '';
   my $badSeverityDamage = '';
   my $scheduledItemHTML = '';
   my $badScheduledItem = '';
   my $suitFiledHTML = '';
   my $schedItem = '';
   my $suitFiled = '';
   my $badSuitFiled = '';
   my $missPropImj = '';
   my $storemTypeAssignmentID = '';

   my $assignmentQuery = $ENGINE->{'DBH'}->prepare(
      'SELECT STORM_TYPE, SEVERITY_OF_DAMAGE, ASSIGNMENT_ID, SCHEDULED_ITEM, SUIT_FILED,ACCIDENT_CITY,ACCIDENT_ZIP,ACCIDENT_STATE,ACCIDENT_COUNTY
      FROM CLAIMDB.CLM_ASSIGNMENT_FIELDS
      WHERE CLAIM_ID = ?
      AND PARTY_ID IS NULL AND COMMON_STAT_ID IS NULL') || $error->($ENGINE, 'Assignment fields query prepare failed: ' . $ENGINE->{'DBH'}->errstr);

   $assignmentQuery->execute($claimid) || $error->($ENGINE, 'Assignment fields query execute failed: ' . $ENGINE->{'DBH'}->errstr);
   my $assignmentResultsCL = $assignmentQuery->fetchall_arrayref({});

   foreach my $key (%{$ENGINE->{'errors'}})
   {
       if($key eq 'propLiabRadio')
       { $badClaimType = $ENGINE->{'errors'}->{$key}[0]; }
       if(substr($key,0,9) eq 'stormType')
       { $badClaimStorm = $ENGINE->{'errors'}->{$key}[0];}
       if($key eq 'severityOfDamage')
       { $badSeverityDamage = $ENGINE->{'errors'}->{$key}[0]; }
       if($key eq 'schedItemRadio')
       { $badScheduledItem = $ENGINE->{'errors'}->{$key}[0]; }
       if($key eq 'suitFiledRadio')
       { $badSuitFiled = $ENGINE->{'errors'}->{$key}[0]; }
       if($key eq 'missPropInj')
       { $missPropImj = $ENGINE->{'errors'}->{$key}[0]; }
   }

   # DISPLAY Severity of Damages and Does this Involve Scheduled Items'
   #also shows and hides the Coverage, Type of Loss and Cause of Loss.
   my $assignmentStyle = '';
   if ($ENGINE->{'claimGeneral'}->{'PROP_OR_LIAB'} =~ /P|B/ || $ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /300|301|302|330|331|332|810|814|816/)
   {
      $assignmentStyle = '"display"';
   }
   else
   {
      $assignmentStyle = '"display:none"';
   }

   my $assigCovStyle = '';

   if ($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /112|113|120|100/)
   {
       if($ENGINE->{'claimGeneral'}->{'PROP_OR_LIAB'} =~ /P|B/)
       {
           $assigCovStyle = '"display"';
       }
       else
       {
           $assigCovStyle = '"display:none"';
       }
   }
   else
   {
       $assigCovStyle = '"display"';
   }

   if (scalar(keys %{$ENGINE->{'errors'}}) > 0 && $closeErrorSW eq 'N')
   {
       $ENGINE->{'claimGeneral'}->{'PROP_OR_LIAB'} = $ENGINE->{'CGI'}->param('propLiabRadio') || '';
       $storm_type = $ENGINE->{'CGI'}->param('stormType') || '';
       $severityOfDamage = $ENGINE->{'CGI'}->param('severityOfDamage') || '';
       my $assignmentIDHold = $assignmentResultsCL->[0]->{'ASSIGNMENT_ID'} ||'';
       my $schedItemHold = $ENGINE->{'CGI'}->param('schedItemRadio') || '';
       my $suitFiledHold = $ENGINE->{'CGI'}->param('suitFiledRadio') || '';

       if ($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /010|011|012|030|031|051|110|111|112|113|120/) {$showCauseOfLoss = 1};
       if ($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} !~ /300|301|302|303|330|331|332|350|360|550|810|814|816/)
       {
           my $storm_Type = createSelect($ENGINE, { 'attributes' => { 'name' => 'stormType', 'id' => 'stormType' }, 'value' => $storm_type, 'options' => \%stormType });
           $stormHTML = Claims_Misc::createLabelHelp({
               'label' => $agentRedAst . ' Is this a Storm Claim? ',
               'help'  => 'Select if this is a Hail, Wind, Water Backup, or Not a Storm Claim.  This is a required field.',
               'data'  => $storm_Type,
               'id'    => 'storm_help',
           });
       }
       $stormHTML .= '<input type="hidden" name="stormTypeID" id="stormTypeID" value="' . $assignmentIDHold . '" />';

       if ($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /112|113|120|100|575|105/)
       {
           my $sevDamage = createSelect($ENGINE, { 'attributes' => { 'name' => 'severityOfDamage', 'id' => 'severityOfDamage' }, 'value' => $severityOfDamage, 'options' => \%severityOfDamages });
           $sevDamageHTML = Claims_Misc::createLabelHelp({
               'label' => $agentRedAst . ' Severity of Damages ',
               'help'  => 'Minor - Cosmetic damage to property only. Moderate - Functional damage to property. Severe - Substantial physical damage to property. Uninhabitable - Damage to property is not safe or suitable to be lived in or used.',
               'data'  => $sevDamage,
               'id'    => 'severity_help',
           });
       }

       if($ENGINE->{'READONLY'})
       {
           if(defined($schedItemHold) && $schedItemHold eq 'N')
           {
               $schedItem = 'No';
           }
           elsif(defined($schedItemHold) && $schedItemHold eq 'Y')
           {
               $schedItem = 'Yes';
           }

           if(defined($suitFiledHold) && $suitFiledHold eq 'N')
           {
               $suitFiled = 'No';
           }
           elsif(defined($suitFiledHold) && $suitFiledHold eq 'Y')
           {
               $suitFiled = 'Yes';
           }
           elsif(defined($suitFiledHold) && $suitFiledHold eq 'U')
           {
               $suitFiled = 'Unknown';
           }
       }
       else
       {
           if(defined($schedItemHold) && $schedItemHold eq 'N')
           {
               $schedItem = '<input type="radio" name="schedItemRadio" id="schedItemRadio" value="Y" /> Yes <input type="radio" name="schedItemRadio" id="schedItemRadio" value="N" checked="checked" /> No';
           }
           elsif(defined($schedItemHold) && $schedItemHold eq 'Y')
           {
               $schedItem = '<input type="radio" name="schedItemRadio" id="schedItemRadio" value="Y" checked="checked" /> Yes <input type="radio" name="schedItemRadio" id="schedItemRadio" value="N" /> No';
           }
           else
           {
               $schedItem = '<input type="radio" name="schedItemRadio" id="schedItemRadio" value="Y" /> Yes <input type="radio" name="schedItemRadio" id="schedItemRadio" value="N" /> No';
           }

           if(defined($suitFiledHold) && $suitFiledHold eq 'U')
           {
               $suitFiled = '<input type="radio" name="suitFiledRadio" id="suitFiledRadio" value="Y" /> Yes <input type="radio" name="suitFiledRadio" id="suitFiledRadio" value="N" /> No <input type="radio" name="suitFiledRadio" id="suitFiledRadio" value="U" checked="checked" /> Unknown';
           }
           elsif(defined($suitFiledHold) && $suitFiledHold eq 'Y')
           {
               $suitFiled = '<input type="radio" name="suitFiledRadio" id="suitFiledRadio" value="Y" checked="checked" /> Yes <input type="radio" name="suitFiledRadio" id="suitFiledRadio" value="N" /> No <input type="radio" name="suitFiledRadio" id="suitFiledRadio" value="U"  /> Unknown';
           }
           elsif(defined($suitFiledHold) && $suitFiledHold eq 'N')
           {
               $suitFiled = '<input type="radio" name="suitFiledRadio" id="suitFiledRadio" value="Y" /> Yes <input type="radio" name="suitFiledRadio" id="suitFiledRadio" value="N" checked="checked" /> No <input type="radio" name="suitFiledRadio" id="suitFiledRadio" value="U" /> Unknown';
           }
           else
           {
               $suitFiled = '<input type="radio" name="suitFiledRadio" id="suitFiledRadio" value="Y" /> Yes <input type="radio" name="suitFiledRadio" id="suitFiledRadio" value="N" /> No <input type="radio" name="suitFiledRadio" id="suitFiledRadio" value="U" /> Unknown';
           }
       }
       if ($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /112|113|120|100/)
       {
           $scheduledItemHTML = Claims_Misc::createLabelHelp({
               'label' => $agentRedAst . ' Does this Involve Scheduled Items? ',
               'help'  => 'Does this loss involve scheduled items?',
               'data'  => $schedItem,
               'id'    => 'schedItem_help',
           });
       }

       if ($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /112|113|120|100|200|205|330|331|332|300|301|302|575|105|810|814|816/)
       {
           $suitFiledHTML = Claims_Misc::createLabelHelp({
               'label' => $agentRedAst . ' Suit Filed? ',
               'help'  => 'Was there a suit filed on this claim?  This is a required field.',
               'data'  => $suitFiled,
               'id'    => 'suitFiled_help',
           });
       }
   }
   else
   {
       $storm_type = $assignmentResultsCL->[0]->{'STORM_TYPE'} || '';
       $severityOfDamage = $assignmentResultsCL->[0]->{'SEVERITY_OF_DAMAGE'} || '';
       my $assignmentIDHold = $assignmentResultsCL->[0]->{'ASSIGNMENT_ID'} || '';
       my $schedItemHold = $assignmentResultsCL->[0]->{'SCHEDULED_ITEM'} || '';
       my $suitFiledHold = $assignmentResultsCL->[0]->{'SUIT_FILED'} || '';

       if ($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} !~ /300|301|302|303|330|331|332|350|360|550|810|814|816/)
       {
           my $storm_Type = createSelect($ENGINE, { 'attributes' => { 'name' => 'stormType', 'id' => 'stormType' }, 'value' => $storm_type, 'options' => \%stormType });
           $stormHTML = Claims_Misc::createLabelHelp({
               'label' => $agentRedAst . ' Is this a Storm Claim? ',
               'help'  => 'Select if this is a Hail, Wind, Water Backup, or Not a Storm Claim.  This is a required field.',
               'data'  => $storm_Type,
               'id'    => 'storm_help',
           });
       }
       $stormHTML .= '<input type="hidden" name="stormTypeID" id="stormTypeID" value="' . $assignmentIDHold . '" />';

       if ($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /112|113|120|100|575|105/)
       {
           my $sevDamage = createSelect($ENGINE, { 'attributes' => { 'name' => 'severityOfDamage', 'id' => 'severityOfDamage' }, 'value' => $severityOfDamage, 'options' => \%severityOfDamages });
           $sevDamageHTML = Claims_Misc::createLabelHelp({
               'label' => $agentRedAst . ' Severity of Damages ',
               'help'  => 'Minor - Cosmetic damage to property only. Moderate - Functional damage to property. Severe - Substantial physical damage to property. Uninhabitable - Damage to property is not safe or suitable to be lived in or used.',
               'data'  => $sevDamage,
               'id'    => 'severity_help',
           });
       }

       if ($ENGINE->{'READONLY'}) {
           if (defined($schedItemHold) && $schedItemHold eq 'N') {
               $schedItem = 'No';
           }
           elsif (defined($schedItemHold) && $schedItemHold eq 'Y') {
               $schedItem = 'Yes';
           }

           if (defined($suitFiledHold) && $suitFiledHold eq 'N') {
               $suitFiled = 'No';
           }
           elsif (defined($suitFiledHold) && $suitFiledHold eq 'Y') {
               $suitFiled = 'Yes';
           }
           elsif (defined($suitFiledHold) && $suitFiledHold eq 'U') {
               $suitFiled = 'Unknown';
           }
       }
       else {
           if (defined($schedItemHold) && $schedItemHold eq 'N') {
               $schedItem = '<input type="radio" name="schedItemRadio" id="schedItemRadio" value="Y" /> Yes <input type="radio" name="schedItemRadio" id="schedItemRadio" value="N" checked="checked" /> No';
           }
           elsif (defined($schedItemHold) && $schedItemHold eq 'Y') {
               $schedItem = '<input type="radio" name="schedItemRadio" id="schedItemRadio" value="Y" checked="checked" /> Yes <input type="radio" name="schedItemRadio" id="schedItemRadio" value="N" /> No';
           }
           else {
               $schedItem = '<input type="radio" name="schedItemRadio" id="schedItemRadio" value="Y" /> Yes <input type="radio" name="schedItemRadio" id="schedItemRadio" value="N" /> No';
           }

           if (defined($suitFiledHold) && $suitFiledHold eq 'U') {
               $suitFiled = '<input type="radio" name="suitFiledRadio" id="suitFiledRadio" value="Y" /> Yes <input type="radio" name="suitFiledRadio" id="suitFiledRadio" value="N" /> No <input type="radio" name="suitFiledRadio" id="suitFiledRadio" value="U" checked="checked" /> Unknown';
           }
           elsif (defined($suitFiledHold) && $suitFiledHold eq 'Y') {
               $suitFiled = '<input type="radio" name="suitFiledRadio" id="suitFiledRadio" value="Y" checked="checked" /> Yes <input type="radio" name="suitFiledRadio" id="suitFiledRadio" value="N" /> No <input type="radio" name="suitFiledRadio" id="suitFiledRadio" value="U"  /> Unknown';
           }
           elsif (defined($suitFiledHold) && $suitFiledHold eq 'N') {
               $suitFiled = '<input type="radio" name="suitFiledRadio" id="suitFiledRadio" value="Y" /> Yes <input type="radio" name="suitFiledRadio" id="suitFiledRadio" value="N" checked="checked" /> No <input type="radio" name="suitFiledRadio" id="suitFiledRadio" value="U" /> Unknown';
           }
           else {
               $suitFiled = '<input type="radio" name="suitFiledRadio" id="suitFiledRadio" value="Y" /> Yes <input type="radio" name="suitFiledRadio" id="suitFiledRadio" value="N" /> No <input type="radio" name="suitFiledRadio" id="suitFiledRadio" value="U" /> Unknown';
           }
       }
       if ($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /112|113|120|100/)
       {
           $scheduledItemHTML = Claims_Misc::createLabelHelp({
               'label' => $agentRedAst . ' Does this Involve Scheduled Items? ',
               'help'  => 'Does this loss involve scheduled items?',
               'data'  => $schedItem,
               'id'    => 'schedItem_help',
           });
       }

       if ($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /112|113|120|100|200|205|330|331|332|300|301|302|575|105|810|814|816/)
       {
           $suitFiledHTML = Claims_Misc::createLabelHelp({
               'label' => $agentRedAst . ' Suit Filed? ',
               'help'  => 'Was there a suit filed on this claim?  This is a required field.',
               'data'  => $suitFiled,
               'id'    => 'suitFiled_help',
           });
       }
   }


   my $passLOB = $ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'};
   my $liability = 0;
   if ($LineCode =~ /300|301|302|330|331|332|810|811|812|814|815|816/)
   {
      # Limit Claim Type to 'Liability only' for Farm Liability & General Liability
      $claimType = 'Claim Type';
      $propLiab = '<input type="radio" name="propLiabRadio" id="propLiabRadio2" value="L" checked="checked" /> Liability';
      $liability = 1;
   }
   elsif ($LineCode =~ /105|200|205/)
   {
      # Limit Claim Type to 'Property only' for Inland Marine & Commercial Property
      $claimType = 'Claim Type';
      $propLiab = '<input type="radio" name="propLiabRadio" id="propLiabRadio1" value="P" checked="checked" /> Property';
   }
   elsif ($ENGINE->{'claimGeneral'}->{'PROP_OR_LIAB'} eq 'P')
   {
      if (!$ENGINE->{'READONLY'})
      {
         $claimType = 'Select Claim Type';
         $propLiab = '<input type="radio" name="propLiabRadio" id="propLiabRadio1" value="P" checked="checked" onclick="prodInfo(this,'.$passLOB.')"/> Property <input type="radio" name="propLiabRadio" id="propLiabRadio2" value="L" onclick="prodInfo(this,'.$passLOB.')"/> Liability <input type="radio" name="propLiabRadio" id="propLiabRadio3" value="B" onclick="prodInfo(this,'.$passLOB.')"/> Property &amp; Liability';
      }
      else
      {
         $claimType = 'Claim Type';
         $propLiab = 'Property';
      }
   }
   elsif ($ENGINE->{'claimGeneral'}->{'PROP_OR_LIAB'} eq 'L')
   {
      if (!$ENGINE->{'READONLY'})
      {
         $claimType = 'Select Claim Type';
         $propLiab = '<input type="radio" name="propLiabRadio" id="propLiabRadio1" value="P" onclick="prodInfo(this,'.$passLOB.')"/> Property <input type="radio" name="propLiabRadio" id="propLiabRadio2" value="L" checked="checked" onclick="prodInfo(this,'.$passLOB.')"/> Liability <input type="radio" name="propLiabRadio" id="propLiabRadio3" value="B" onclick="prodInfo(this,'.$passLOB.')"/> Property &amp; Liability';
         $liability = 1;
      }
      else
      {
         $claimType = 'Claim Type';
         $propLiab = 'Liability';
         $liability = 1;
      }
   }
   elsif($ENGINE->{'claimGeneral'}->{'PROP_OR_LIAB'} eq 'B')
   {
      if (!$ENGINE->{'READONLY'})
      {
         $claimType = 'Select Claim Type';
         $propLiab = '<input type="radio" name="propLiabRadio" id="propLiabRadio1" value="P" onclick="prodInfo(this,'.$passLOB.')"/> Property <input type="radio" name="propLiabRadio" id="propLiabRadio2" value="L" onclick="prodInfo(this,'.$passLOB.')"/> Liability <input type="radio" name="propLiabRadio" id="propLiabRadio3" value="B" checked="checked" onclick="prodInfo(this,'.$passLOB.')"/> Property &amp; Liability';
      }
      else
      {
         $claimType = 'Claim Type';
         $propLiab = 'Property &amp; Liability';
      }
   }
   else
   {
      if (!$ENGINE->{'READONLY'})
      {
         $claimType = 'Select Claim Type';
         $propLiab = '<input type="radio" name="propLiabRadio" id="propLiabRadio1" value="P" onclick="prodInfo(this,'.$passLOB.')"/> Property <input type="radio" name="propLiabRadio" id="propLiabRadio2" value="L" onclick="prodInfo(this,'.$passLOB.')"/> Liability <input type="radio" name="propLiabRadio" id="propLiabRadio3" value="B" onclick="prodInfo(this,'.$passLOB.')"/> Property &amp; Liability';
      }
      else
      {
         $claimType = 'Claim Type';
         $propLiab = 'Unknown';
      }
   }

   my $locations = '';
   my $autoLight = '';
   my $buildingItem = '';
   my $typeOfLoss = '';
   my ($causeOfLoss,$lossCodes) = getCauseOfLoss($ENGINE);

   if ($causeOfLoss eq '' && $lossCodes eq '')
   {
      $typeOfLoss = '';
   }
   else
   {
      $typeOfLoss = 'Type of Loss';
   }


#   my $lossLocationHTML = '';
   my $propertyLocationHTML = '';
   my $ac = "'AC',";

#   if ($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'Internal')
#   {
#      $ac = "'AC',";
#   }

   my $havePolicyLoc = 'N';
   my $propertyResults = [];
#   my @sortedPropertyResults = ();

   if ($homePolicy eq 'Y' || $businPolicy eq 'Y' || $cfPolicy eq 'Y' || $arPolicy eq 'Y' || $imPolicy eq 'Y' || $dpPolicy eq 'Y')
   {
      my $propertyQuery = $ENGINE->{'DBH'}->prepare(
         "SELECT L.CLM_COMMON_STAT_ID AS MY_COMMON_STAT_ID, C.*, L.*, P.*
            FROM CLAIMDB.CLM_COMMON_STAT AS C
           INNER JOIN CLAIMDB.CLM_LOCATION AS L
              ON C.CLM_COMMON_STAT_ID = L.CLM_COMMON_STAT_ID AND L.LOC_TYPE IN (".$ac."'LL') AND L.DATE_DELETED = '9999-01-01 01:00:00.000000'
            LEFT OUTER JOIN CLAIMDB.CLM_PROPERTY_STAT AS P
              ON C.CLM_COMMON_STAT_ID = P.CLM_COMMON_STAT_ID AND P.DATE_DELETED = '9999-01-01 01:00:00.000000'
           WHERE C.CLAIM_ID = ?
             AND C.DATE_DELETED = '9999-01-01 01:00:00.000000' ORDER BY C.CLM_COMMON_STAT_ID") || error($ENGINE,'Property query prepare failed: '.$ENGINE->{'DBH'}->errstr);

      $propertyQuery->execute($claimid) || error($ENGINE,'Property query execute failed: '.$ENGINE->{'DBH'}->errstr);

      $propertyResults = $propertyQuery->fetchall_arrayref({});
   }
   elsif ($liabPolicy eq 'Y' || $glPolicy eq 'Y' || $ucPolicy eq 'Y' || $upPolicy eq 'Y')
   {
      my $liabilityQuery = $ENGINE->{'DBH'}->prepare(
         "SELECT L.CLM_COMMON_STAT_ID AS MY_COMMON_STAT_ID, C.*, L.*, P.*
            FROM CLAIMDB.CLM_COMMON_STAT AS C
           INNER JOIN CLAIMDB.CLM_LOCATION AS L
              ON C.CLM_COMMON_STAT_ID = L.CLM_COMMON_STAT_ID AND L.LOC_TYPE IN (".$ac."'LL') AND L.DATE_DELETED = '9999-01-01 01:00:00.000000'
            LEFT OUTER JOIN CLAIMDB.CLM_LIAB_STAT AS P ON C.CLM_COMMON_STAT_ID = P.CLM_COMMON_STAT_ID AND P.DATE_DELETED = '9999-01-01 01:00:00.000000'
           WHERE C.CLAIM_ID = ?
             AND C.DATE_DELETED = '9999-01-01 01:00:00.000000' ORDER BY C.CLM_COMMON_STAT_ID") || error($ENGINE,'Property query prepare failed: '.$ENGINE->{'DBH'}->errstr);

      $liabilityQuery->execute($claimid) || error($ENGINE,'Property query execute failed: '.$ENGINE->{'DBH'}->errstr);

      $propertyResults = $liabilityQuery->fetchall_arrayref({});
   }

   my $totalLocations = scalar(@$propertyResults);

#   my $cashResvQuery = $ENGINE->{'DBH'}->prepare(
#      "SELECT R.CLAIM_ID
#         FROM CLAIMDB.CLM_CASH AS C
#        INNER JOIN CLAIMDB.CLM_RESERVES AS R ON C.CLAIM_ID = R.CLAIM_ID AND R.DATE_DELETED = '9999-01-01 01:00:00.000000'
#        WHERE C.CLAIM_ID = ?
#          AND C.DATE_DELETED = '9999-01-01 01:00:00.000000'") || error($ENGINE,'Cash Reserve query prepare failed: '.$ENGINE->{'DBH'}->errstr);

#   $cashResvQuery->execute($claimid) || error($ENGINE,'Cash Reserve query execute failed: '.$ENGINE->{'DBH'}->errstr);

#   my $cashResvResults = $cashResvQuery->fetchall_arrayref({});

#   my $haveCashResv = 'N';
#   for my $cr (@$cashResvResults)
#   {
#      if (defined($cr->{'CLAIM_ID'}) && $cr->{'CLAIM_ID'} gt '')
#      {
#         $haveCashResv = 'Y';
#      }
#   }

  #read to check for paids or reserves for properties on the claim
  #these vehicles cannot have the delete button as an option
  #108484 Added CO.LOCATION_NO to query.  Liab lines might have 0 in unit.  Needed to look at location too.
     my $moneyhResults = [];
     if($LineCode =~ /300|301|302|330|331|332|810|811|812|813|814|815|816|817|818/)
     {
             my $moneyQuery = $ENGINE->{'DBH'}->prepare(
                "SELECT
                    P.CLM_COMMON_STAT_ID,
                    CO.UNIT_NO,
                    CO.LOCATION_NO,
                    R.COVERAGE_ID AS RESERVES,
                    CS.COVERAGE_ID AS CASH
                FROM
                   CLAIMDB.CLM_LIAB_STAT P
                INNER JOIN
                   CLAIMDB.CLM_COMMON_STAT CO
                ON
                  P.CLM_COMMON_STAT_ID = CO.CLM_COMMON_STAT_ID
                INNER JOIN
                   CLAIMDB.CLM_COVS_ENDORSES C
                ON
                  CO.CLAIM_ID = C.CLAIM_ID
                  AND CO.UNIT_NO = C.UNIT_NO
                  AND CO.LOCATION_NO = C.LOCATION_NO
                LEFT OUTER JOIN
                    CLAIMDB.CLM_RESERVES R
                ON
                    C.COVERAGE_ID = R.COVERAGE_ID
                LEFT OUTER JOIN
                    CLAIMDB.CLM_CASH CS
                ON
                    C.COVERAGE_ID = CS.COVERAGE_ID
                WHERE
                        P.CLAIM_ID = ?
                        AND (CS.COVERAGE_ID IS NOT NULL
                            OR  R.COVERAGE_ID IS NOT NULL)
                        AND P.DATE_DELETED = \'9999-01-01 01:00:00.000000\'") || $error->($ENGINE,'CLM_VEHICLE query prepare failed: '.$ENGINE->{'DBH'}->errstr);
             $moneyQuery->execute($claimid) || $error->($ENGINE,'Vehicle query execute failed: '.$ENGINE->{'DBH'}->errstr);
             $moneyhResults = $moneyQuery->fetchall_arrayref({});
     }
     else
     {
             my $moneyQuery = $ENGINE->{'DBH'}->prepare(
                "SELECT
                    P.CLM_COMMON_STAT_ID,
                    CO.UNIT_NO,
                    R.COVERAGE_ID AS RESERVES,
                    CS.COVERAGE_ID AS CASH
                FROM
                   CLAIMDB.CLM_PROPERTY_STAT P
                INNER JOIN
                   CLAIMDB.CLM_COMMON_STAT CO
                ON
                  P.CLM_COMMON_STAT_ID = CO.CLM_COMMON_STAT_ID
                INNER JOIN
                   CLAIMDB.CLM_COVS_ENDORSES C
                ON
                  CO.CLAIM_ID = C.CLAIM_ID
                  AND CO.UNIT_NO = C.UNIT_NO
                LEFT OUTER JOIN
                    CLAIMDB.CLM_RESERVES R
                ON
                    C.COVERAGE_ID = R.COVERAGE_ID
                LEFT OUTER JOIN
                    CLAIMDB.CLM_CASH CS
                ON
                    C.COVERAGE_ID = CS.COVERAGE_ID
                WHERE
                        P.CLAIM_ID = ?
                        AND (CS.COVERAGE_ID IS NOT NULL
                            OR  R.COVERAGE_ID IS NOT NULL)
                        AND P.DATE_DELETED = \'9999-01-01 01:00:00.000000\'") || $error->($ENGINE,'CLM_VEHICLE query prepare failed: '.$ENGINE->{'DBH'}->errstr);
             $moneyQuery->execute($claimid) || $error->($ENGINE,'Vehicle query execute failed: '.$ENGINE->{'DBH'}->errstr);
             $moneyhResults = $moneyQuery->fetchall_arrayref({});
     }


   my $liabSpace = '';
   my $propertyLocationStat = '';
   my $assignHide3IDs = '';

   for my $p (@$propertyResults)
   {
      my $badOutdoorLighting = '';
      my $badInteriorUpkeep = '';
      my $badExteriorUpkeep = '';
      my $badYardUpkeep = '';
      my $badConstType = '';
      my $badResidenceType = '';
      my $badOccupancyType = '';
      my $badPrimaryHeatType = '';
      my $badBurglarAlarmType = '';
      my $badSmokeDetectorType = '';
      my $badFireDetectorType = '';
      my $badEQStories = '';
      my $badManualRateType = '';
      my $badVacantType = '';
      my $badCrimeOffPrem = '';
      my $badECSymbolType = '';
      my $badPCTMonthlyLmtType = '';
      my $badEQSprinklerLeakType = '';
      my $badProtectDeviceType = '';
      my $badFireLegalType = '';
      my $badNumUnitsType = '';
      my $badPelletHeatType = '';
      my $badLossSettleType = '';
      my $badMhTieDownType = '';
      my $badMhWidth = '';
      my $badMhLength = '';
      my $badProtectCodeType = '';
      my $badYearBuilt = '';
      my $badNumOfFamilies = '';
      my $badRatingIDType = '';
      my $badPackageCodeType = '';
      my $badUmbrellaFHType = '';
      my $foundError = 'N';
      $assignCovHTML = '';


      #loop thru the property rows from the policy, if any exist, and
      #look for matches to what is already on the claim.  Mark these as
      #matched.
      for my $Loc (@{$ENGINE->{'locRef'}})
          {
            #We have to check if this is a gl policy because we are sending the record type and location in the location field IE 2001.
            if($LineCode =~ /810|814|816/)
            {
                    if ($p->{'LOC_TYPE'} eq 'AC'
                        && $p->{'LOCATION_NO'} == substr($Loc->{'location_no'},2,2)
                        && $p->{'UNIT_NO'} == $Loc->{'building_no'})
                    {
                         $Loc->{'ONCLAIM'} = 'Y';
                         last;
                    }
            }
            else
            {
                    if ($p->{'LOC_TYPE'} eq 'AC'
                        && $p->{'LOCATION_NO'} == $Loc->{'location_no'}
                        && $p->{'UNIT_NO'} == $Loc->{'building_no'})
                    {
        #                if ($glPolicy eq 'Y')
        #                {
        #                print '<br>location: ' . $p->{'LOCATION_NO'};
        #                print '<br>unit: ' .  $p->{'UNIT_NO'};
        #                print '<br>subline: ' . $p->{'SUB_LINE'};
        #                print '<br>sublineB: ' . $Loc->{'subline_code'};
        #                    #for general liability, sublines in the select box
        #                    #aren't the same as the subline that is put on the
        #                    #clm_common_stat, so special if statement here to handle
        #                    #that situation
        #                    #Policy Code (ISO)  IMT Stat
        #                    #334    810 - Premises Operations Liability
        #                    #335    814 - Owners and Contractors Protective Liability
        #                    #336    816 - Products and Completed Operations Liability
        #                    if ($Loc->{'subline_code'} eq '810'
        #                        &&  $p->{'SUB_LINE'} eq '334')
        #                    {
        #                        #found a match!!
        #                        $Loc->{'ONCLAIM'} = 'Y';
        #                        last;
        #                    }
        #                    elsif ($Loc->{'subline_code'} eq '814'
        #                        &&  $p->{'SUB_LINE'} eq '335')
        #                    {
        #                        #found a match!!
        #                        $Loc->{'ONCLAIM'} = 'Y';
        #                        last;
        #                    }
        #                    elsif ($Loc->{'subline_code'} eq '816'
        #                        &&  $p->{'SUB_LINE'} eq '336')
        #                    {
        #                        #found a match!!
        #                        $Loc->{'ONCLAIM'} = 'Y';
        #                        last;
        #                    }
        #                    elsif ($p->{'SUB_LINE'} eq $Loc->{'subline_code'})
        #                    {
        #                        $Loc->{'ONCLAIM'} = 'Y';
        #                        last;
        #                    }
        #                }
        #                else
        #                {
                            #we found a match, put the match indicator on it!

                         $Loc->{'ONCLAIM'} = 'Y';
                         last;
        #               }
                    }
                }
          }

      # Put red arrow by the fields that have an error for property stat records.
      for my $pc (@{$ENGINE->{'PropLiab'}->{'CLM_PROPERTY_STAT'}})
      {
          if(defined($pc->{'CLM_COMMON_STAT_ID'}) && $pc->{'CLM_COMMON_STAT_ID'} eq $p->{'CLM_COMMON_STAT_ID'})
          {
              if(exists $pc->{'errInternalUpkeep'.$pc->{'CLM_COMMON_STAT_ID'}})
              { $badInteriorUpkeep = $pc->{'errInternalUpkeep'.$pc->{'CLM_COMMON_STAT_ID'}}; $foundError = 'Y';}
              if(exists $pc->{'errExteriorUpkeep'.$pc->{'CLM_COMMON_STAT_ID'}})
              { $badExteriorUpkeep = $pc->{'errExteriorUpkeep'.$pc->{'CLM_COMMON_STAT_ID'}}; $foundError = 'Y';}
              if(exists $pc->{'errYardUpkeep'.$pc->{'CLM_COMMON_STAT_ID'}})
              { $badYardUpkeep = $pc->{'errYardUpkeep'.$pc->{'CLM_COMMON_STAT_ID'}}; $foundError = 'Y';}
              if(exists $pc->{'errConstCode'.$pc->{'CLM_COMMON_STAT_ID'}})
              { $badConstType = $pc->{'errConstCode'.$pc->{'CLM_COMMON_STAT_ID'}}; $foundError = 'Y';}
              if(exists $pc->{'errDFResidenceType'.$pc->{'CLM_COMMON_STAT_ID'}})
              { $badResidenceType = $pc->{'errDFResidenceType'.$pc->{'CLM_COMMON_STAT_ID'}}; $foundError = 'Y';}
              if(exists $pc->{'errCFOccupancyCode'.$pc->{'CLM_COMMON_STAT_ID'}})
              { $badOccupancyType = $pc->{'errCFOccupancyCode'.$pc->{'CLM_COMMON_STAT_ID'}}; $foundError = 'Y';}
              if(exists $pc->{'errDFPrimaryHeat'.$pc->{'CLM_COMMON_STAT_ID'}})
              { $badPrimaryHeatType = $pc->{'errDFPrimaryHeat'.$pc->{'CLM_COMMON_STAT_ID'}}; $foundError = 'Y';}
              if(exists $pc->{'errDFBruglarAlarm'.$pc->{'CLM_COMMON_STAT_ID'}})
              { $badBurglarAlarmType = $pc->{'errDFBruglarAlarm'.$pc->{'CLM_COMMON_STAT_ID'}}; $foundError = 'Y';}
              if(exists $pc->{'errDFSmokeDetect'.$pc->{'CLM_COMMON_STAT_ID'}})
              { $badSmokeDetectorType = $pc->{'errDFSmokeDetect'.$pc->{'CLM_COMMON_STAT_ID'}}; $foundError = 'Y';}
              if(exists $pc->{'errDFFireDetect'.$pc->{'CLM_COMMON_STAT_ID'}})
              { $badFireDetectorType = $pc->{'errDFFireDetect'.$pc->{'CLM_COMMON_STAT_ID'}}; $foundError = 'Y';}
              if(exists $pc->{'errDFEQStories'.$pc->{'CLM_COMMON_STAT_ID'}})
              { $badEQStories = $pc->{'errDFEQStories'.$pc->{'CLM_COMMON_STAT_ID'}}; $foundError = 'Y';}
              if(exists $pc->{'errOccupSizeLoc'.$pc->{'CLM_COMMON_STAT_ID'}})
              { $badVacantType = $pc->{'errOccupSizeLoc'.$pc->{'CLM_COMMON_STAT_ID'}}; $foundError = 'Y';}
              if(exists $pc->{'errCFCrimeOffp'.$pc->{'CLM_COMMON_STAT_ID'}})
              { $badCrimeOffPrem = $pc->{'errCFCrimeOffp'.$pc->{'CLM_COMMON_STAT_ID'}}; $foundError = 'Y';}
              if(exists $pc->{'errCFECSymbol'.$pc->{'CLM_COMMON_STAT_ID'}})
              { $badECSymbolType = $pc->{'errCFECSymbol'.$pc->{'CLM_COMMON_STAT_ID'}}; $foundError = 'Y';}
              if(exists $pc->{'errCFExtraexpMthlim'.$pc->{'CLM_COMMON_STAT_ID'}})
              { $badPCTMonthlyLmtType = $pc->{'errCFExtraexpMthlim'.$pc->{'CLM_COMMON_STAT_ID'}}; $foundError = 'Y';}
              if(exists $pc->{'errCFManualRateInd'.$pc->{'CLM_COMMON_STAT_ID'}})
              { $badManualRateType = $pc->{'errCFManualRateInd'.$pc->{'CLM_COMMON_STAT_ID'}}; $foundError = 'Y';}
              if(exists $pc->{'errCFSprinkleakSus'.$pc->{'CLM_COMMON_STAT_ID'}})
              { $badEQSprinklerLeakType = $pc->{'errCFSprinkleakSus'.$pc->{'CLM_COMMON_STAT_ID'}}; $foundError = 'Y';}
              if(exists $pc->{'errLegaliabTypcod'.$pc->{'CLM_COMMON_STAT_ID'}})
              { $badFireLegalType = $pc->{'errLegaliabTypcod'.$pc->{'CLM_COMMON_STAT_ID'}}; $foundError = 'Y';}
              if(exists $pc->{'errProtectiveDev'.$pc->{'CLM_COMMON_STAT_ID'}})
              { $badProtectDeviceType = $pc->{'errProtectiveDev'.$pc->{'CLM_COMMON_STAT_ID'}}; $foundError = 'Y';}
              if(exists $pc->{'errHOPelletHeating'.$pc->{'CLM_COMMON_STAT_ID'}})
              { $badPelletHeatType = $pc->{'errHOPelletHeating'.$pc->{'CLM_COMMON_STAT_ID'}}; $foundError = 'Y';}
              if(exists $pc->{'errHONumUnits'.$pc->{'CLM_COMMON_STAT_ID'}})
              { $badNumUnitsType = $pc->{'errHONumUnits'.$pc->{'CLM_COMMON_STAT_ID'}}; $foundError = 'Y';}
              if(exists $pc->{'errLossSettlementType'.$pc->{'CLM_COMMON_STAT_ID'}})
              { $badLossSettleType = $pc->{'errLossSettlementType'.$pc->{'CLM_COMMON_STAT_ID'}}; $foundError = 'Y';}
              if(exists $pc->{'errProtectCode'.$pc->{'CLM_COMMON_STAT_ID'}})
              { $badProtectCodeType = $pc->{'errProtectCode'.$pc->{'CLM_COMMON_STAT_ID'}}; $foundError = 'Y';}
              if(exists $pc->{'errYearBuild'.$pc->{'CLM_COMMON_STAT_ID'}})
              { $badYearBuilt = $pc->{'errYearBuild'.$pc->{'CLM_COMMON_STAT_ID'}}; $foundError = 'Y';}
              if(exists $pc->{'errNumberOfFamiles'.$pc->{'CLM_COMMON_STAT_ID'}})
              { $badNumOfFamilies = $pc->{'errNumberOfFamiles'.$pc->{'CLM_COMMON_STAT_ID'}}; $foundError = 'Y';}
              if(exists $pc->{'errMHTieDown'.$pc->{'CLM_COMMON_STAT_ID'}})
              { $badMhTieDownType = $pc->{'errMHTieDown'.$pc->{'CLM_COMMON_STAT_ID'}}; $foundError = 'Y';}
              if(exists $pc->{'errMHWidth'.$pc->{'CLM_COMMON_STAT_ID'}})
              { $badMhWidth = $pc->{'errMHWidth'.$pc->{'CLM_COMMON_STAT_ID'}}; $foundError = 'Y';}
              if(exists $pc->{'errMHLength'.$pc->{'CLM_COMMON_STAT_ID'}})
              { $badMhLength = $pc->{'errMHLength'.$pc->{'CLM_COMMON_STAT_ID'}}; $foundError = 'Y';}
              if(exists $pc->{'errBoartRateIdent'.$pc->{'CLM_COMMON_STAT_ID'}})
              { $badRatingIDType = $pc->{'errBoartRateIdent'.$pc->{'CLM_COMMON_STAT_ID'}}; $foundError = 'Y';}
#              if(exists $pc->{'errUmbrellaFHType'.$pc->{'CLM_COMMON_STAT_ID'}})
#              { $badUmbrellaFHType = $pc->{'errUmbrellaFHType'.$pc->{'CLM_COMMON_STAT_ID'}}; $foundError = 'Y';}
          }
      }

      # Put red arrow by the fields that have an error for liability stat records.
      for my $pl (@{$ENGINE->{'PropLiab'}->{'CLM_LIAB_STAT'}})
      {
          if(defined($pl->{'CLM_COMMON_STAT_ID'}) && $pl->{'CLM_COMMON_STAT_ID'} eq $p->{'CLM_COMMON_STAT_ID'})
          {
              if(exists $pl->{'errPKProgCode'.$pl->{'CLM_COMMON_STAT_ID'}})
              { $badPackageCodeType = $pl->{'errPKProgCode'.$pl->{'CLM_COMMON_STAT_ID'}};
                $foundError = 'Y';}
              if(exists $pl->{'errUmbrellaFHType'.$pl->{'CLM_COMMON_STAT_ID'}})
              { $badUmbrellaFHType = $pl->{'errUmbrellaFHType'.$pl->{'CLM_COMMON_STAT_ID'}};
                $foundError = 'Y';}
          }
      }

#      if ($p->{'LOC_TYPE'} eq 'LL')
#      {
#         $lossLocationHTML .= '<tr id="lossLocRow'.$p->{'MY_COMMON_STAT_ID'}.'" style="display"><th>Loss Location</th><td><input type="hidden" name="lossLocID'.$p->{'MY_COMMON_STAT_ID'}.'" id="lossLocID'.$p->{'MY_COMMON_STAT_ID'}.'" value="'.$p->{'MY_COMMON_STAT_ID'}.'">';
#         if (defined($p->{'FL_ACRES'}) && $p->{'FL_ACRES'} =~ /\w/ && $p->{'FL_ACRES'} > 0)
#         {
#            $lossLocationHTML .= 'Location: '.$p->{'LOCATION_NO'}.' Acres: '.$p->{'FL_ACRES'}.' ';
#            if (defined($p->{'FL_SECTION'}) && $p->{'FL_SECTION'} =~ /\w/)
#            {
#               $lossLocationHTML .= ' Section: '.$p->{'FL_SECTION'}.' ';
#            }
#            if (defined($p->{'FL_RANGE'}) && $p->{'FL_RANGE'} =~ /\w/)
#            {
#               $lossLocationHTML .= ' Range: '.$p->{'FL_RANGE'}.' ';
#            }
#            if (defined($p->{'FL_TOWNSHIP_WORDAGE'}) && $p->{'FL_TOWNSHIP_WORDAGE'} =~ /\w/)
#            {
#               $lossLocationHTML .= ' Township: '.$p->{'FL_TOWNSHIP_WORDAGE'}.' ';
#            }
#            if (defined($p->{'FL_COUNTY_WORDS'}) && $p->{'FL_COUNTY_WORDS'} =~ /\w/)
#            {
#               $lossLocationHTML .= ' County: '.$p->{'FL_COUNTY_WORDS'}.' ';
#            }
#         }
#         else
#         {
#            if (defined($p->{'ADDRESS1'}) && $p->{'ADDRESS1'} =~ /\w/)
#            {
#               # This if is checking to see if it is a GL policy.  Address 1 and Address 2 is holding the description of the location.
#               if($glPolicy eq 'N')
#               { $lossLocationHTML .= 'Location: '.$p->{'LOCATION_NO'}.' Address: '.$p->{'ADDRESS1'}.' '; }
#               else
#               { $lossLocationHTML .= 'Location: '.$p->{'LOCATION_NO'}.' Address: '.$p->{'ADDRESS1'}; }
#            }
#            if (defined($p->{'ADDRESS2'}) && $p->{'ADDRESS2'} =~ /\w/)
#            {
#               $lossLocationHTML .= $p->{'ADDRESS2'}.' ';
#            }
#            if (defined($p->{'CITY'}) && $p->{'CITY'} =~ /\w/)
#            {
#               $lossLocationHTML .= ' City: '.$p->{'CITY'}.' ';
#            }
#         }

#         if (defined($p->{'STATE'}) && $p->{'STATE'} =~ /\w/)
#         {
#            $lossLocationHTML .= ' State: '.$p->{'STATE'}.' ';
#         }
#         if (defined($p->{'ZIP1_5'}) && $p->{'ZIP1_5'} =~ /\w/)
#         {
#            $lossLocationHTML .= $p->{'ZIP1_5'}.'-';
#         }
#         if (defined($p->{'ZIP6_12'}) && $p->{'ZIP6_12'} =~ /\w/ && $p->{'ZIP6_12'} ne '0000')
#         {
#            $lossLocationHTML .= $p->{'ZIP6_12'}.' ';
#         }

#         chop($lossLocationHTML);
#         $lossLocationHTML .= '</td><td><input type="button" value="Remove" onclick="removeLossLoc(document.getElementById(\'lossLocID'.$p->{'MY_COMMON_STAT_ID'}.'\'))" /></td></tr>';

#         if ($LineCode =~ /105|575|580/)
#         {
#            $lossLocationHTML .= '<tr><th style="padding-left:2em">Building</th><td>Building: '.$p->{'UNIT_NO'}.'</td></tr>';
#         }
#         elsif ($LineCode =~ /200|205/)
#         {
#            $lossLocationHTML .= '<tr><th style="padding-left:2em">Item</th><td>Unit: '.$p->{'UNIT_NO'}.'</td></tr>';
#         }
#      }

       $assignmentQuery = $ENGINE->{'DBH'}->prepare(
           'SELECT STORM_TYPE, IMT_CAUSE_OF_LOSS, COVERAGE_ID, TYPE_OF_LOSS, LOSS_CODE_GROUP, COMMON_STAT_ID, ASSIGNMENT_ID
          FROM CLAIMDB.CLM_ASSIGNMENT_FIELDS
          WHERE CLAIM_ID = ?
          AND PARTY_ID IS NULL
          AND COMMON_STAT_ID = ?
          ORDER BY COMMON_STAT_ID, ASSIGNMENT_ID') || $error->($ENGINE, 'Assignment fields query prepare failed: ' . $ENGINE->{'DBH'}->errstr);
       $assignmentQuery->execute($claimid,$p->{'CLM_COMMON_STAT_ID'}) || $error->($ENGINE, 'Assignment fields query execute failed: ' . $ENGINE->{'DBH'}->errstr);
       my $assignmentResults = $assignmentQuery->fetchall_arrayref({});

       my $covCntr = 0;
       if (scalar(keys %{$ENGINE->{'errors'}}) > 0 && $closeErrorSW eq 'N')
       {
            if(!($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /350|360|303|550/)) {
                my %params = $ENGINE->{'CGI'}->Vars();
                my $cnt = 0;
                my $holdCNT = '';
                my $key2ID = '';
                for my $key2 (sort keys %params)
                {
                    $badassignCov = '';
                    $badassignTypeOfLoss = '';
                    $badassignCauseOfLoss = '';
                    if ($key2 =~ /^coveragesList(\d+)$/ || $key2 =~ /^coveragesList(\d*_\d+)$/)
                    {
                        $key2ID = $1;
                        if ($cnt gt 0) {
                            $holdCNT = '_' . $cnt
                        }
                        else
                        { $holdCNT = '';}
                        if (substr($key2, 0, 13) eq 'coveragesList' && $key2ID eq $p->{'CLM_COMMON_STAT_ID'}. $holdCNT) {
                            my $assignmentID = $ENGINE->{'CGI'}->param('assignmentID' . $key2ID) || $assignmentResults->[$cnt]->{'ASSIGNMENT_ID'} || '';
                            my $coverageSelect = $ENGINE->{'CGI'}->param('coveragesList' . $key2ID) || '';
                            my @coverage = split('_', $coverageSelect);
                            my $coverageID = $coverage[0];
                            my $lossGroups = $coverage[1];
                            if ($coverageID eq 'XX') {$coverageID = '0';}
                            my $type_of_loss = substr($ENGINE->{'CGI'}->param('lossCodesList' . $key2ID), 0, 2);
                            my $cause_of_loss = $ENGINE->{'CGI'}->param('COLList' . $key2ID);
                            foreach my $key3 (%{$ENGINE->{'errors'}}) {
                                #die($ENGINE->{'errors'}{$key3}->[0]);
                                if ($key3 eq 'coveragesList' . $key2ID)
                                {$badassignCov = $ENGINE->{'errors'}{$key3}->[0];}
                                if ($key3 eq 'lossCodesList' . $key2ID)
                                {$badassignTypeOfLoss = $ENGINE->{'errors'}{$key3}->[0];}
                                if ($key3 eq 'COLList' . $key2ID)
                                {$badassignCauseOfLoss = $ENGINE->{'errors'}{$key3}->[0];}
                            }

                            ($assignCovHTML) = buildCoverage($ENGINE, $p, $claimid, $showCauseOfLoss, $coverageID || '', $lossGroups || '', $type_of_loss || '', $cause_of_loss || '', $assignCovHTML, $holdCNT, $assignmentID, $badassignCov, $badassignTypeOfLoss, $badassignCauseOfLoss, $storm_type);
                            $cnt++;
                        }
                    }
                }
          }
       }
       else
       {
           if ($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /010|011|012|030|031|051|110|111|112|113|120/) {$showCauseOfLoss = 1;}
            if(!($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /350|360|303|550/)) {

               my $assignmentLength = @$assignmentResults;
               if($assignmentLength == 0)
               {
                   if($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /100|105|575/)
                   {
                       if($storm_type =~ /H|I|W/)
                       {
                           my $subCov = '';
                           if($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} eq '100')
                           {
                               my $subCovQuery = $ENGINE->{'DBH'}->prepare(
                                   'SELECT SUB_COV
                                  FROM CLAIMDB.CLM_COVS_ENDORSES
                                  WHERE CLAIM_ID = ?
                                  AND COV_OR_ENDORSE = ?
                                  ORDER BY COVERAGE_ID') || $error->($ENGINE, 'Sub Covereage query prepare failed: ' . $ENGINE->{'DBH'}->errstr);
                               $subCovQuery->execute($claimid,'C') || $error->($ENGINE, 'Sub Coverage query execute failed: ' . $ENGINE->{'DBH'}->errstr);
                               my $subCovResults = $subCovQuery->fetchall_arrayref({});

                               for my $sc (@$subCovResults)
                               {
                                   if(defined($sc->{'SUB_COV'}) && $sc->{'SUB_COV'} eq '428')
                                   {
                                       $subCov = '428';
                                   }
                                   elsif(defined($sc->{'SUB_COV'}) && $sc->{'SUB_COV'} eq '421')
                                   {
                                       $subCov = '421';
                                   }
                                   elsif(defined($sc->{'SUB_COV'}) && $sc->{'SUB_COV'} eq '422')
                                   {
                                       $subCov = '422';
                                   }
                               }
                           }
                           elsif($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} eq '105')
                           {
                               $subCov = '025';
                           }
                            my $covQuery = $ENGINE->{'DBH'}->prepare("SELECT DISTINCT
                                    C.COVERAGE_ID,
                                    C.COVERAGE,
                                        C.IMT_COVERAGE,
                                        GC.IMT_DESCRIPT,
                                        GC.VALID_LOSSCODE_GROUP,
                                        C.SUB_COV,
                                        C.CLASS,
                                        C.LIMIT1,
                                        C.LIMIT2,
                                        D.DEDUCTIBLE,
                                        D.TYPE_OF_DEDUCTIBLE
                                        FROM
                                            CLAIMDB.CLM_GENERAL G
                                    INNER JOIN
                                        CLAIMDB.CLM_COVS_ENDORSES C
                                    ON
                                        G.CLAIM_ID = C.CLAIM_ID
                                    INNER JOIN
                                        GENSUPDB.COVERAGES GC
                                    ON
                                        GC.COMPANY_CODE = G.COMPANY_NO
                                        AND GC.IMT_LINE_CODE = G.IMT_LINE_CODE
                                        AND GC.IMT_COV_CODE = C.IMT_COVERAGE
                                        AND GC.IMT_COVERAGE = C.COVERAGE
                                        AND GC.EFFECTIVE_DATE <= G.POL_EFF_DATE
                                        AND GC.OBSOLETE_DATE > G.POL_EFF_DATE
                                    LEFT OUTER JOIN
                                            CLAIMDB.CLM_DEDUCTIBLES D
                                        ON
                                            C.COVERAGE_ID = D.COVERAGE_ID
                                    WHERE
                                        G.CLAIM_ID = ?
                                        AND C.COV_OR_ENDORSE = 'C'
                                        AND C.COVERAGE IN ('BLDG','BPP')
                                        AND C.SUB_COV = ?
                                        AND C.DATE_DELETED = '9999-01-01 01:00:00.000000'
                                        and
                                        ((c.unit_no = ?
                                        and c.location_no = ?)
                                        or (c.unit_no = 0
                                        and c.location_no = 0))
                                    ORDER BY
                                            C.IMT_COVERAGE asc,
                                            C.COVERAGE_ID asc,
                                            GC.IMT_DESCRIPT asc,
                                            C.SUB_COV") ||
                                $error->($ENGINE);
                            $covQuery->execute($p->{'CLAIM_ID'},$subCov,$p->{'UNIT_NO'},$p->{'LOCATION_NO'}) || $error->($ENGINE);
                            my $covResults = $covQuery->fetchall_arrayref({});
                            my $coverageID = $covResults->[0]->{'COVERAGE_ID'};
                            my $lossCodeGroup = $covResults->[0]->{'VALID_LOSSCODE_GROUP'};

                            ($assignCovHTML) = buildCoverage($ENGINE,$p,$claimid,$showCauseOfLoss,$coverageID,$lossCodeGroup,'','','','','','','','',$storm_type);
                       }
                       else
                       {
                           ($assignCovHTML) = buildCoverage($ENGINE,$p,$claimid,$showCauseOfLoss,'','','','','','','','','','',$storm_type);
                       }
                   }
                   else
                   {
                       ($assignCovHTML) = buildCoverage($ENGINE, $p, $claimid, $showCauseOfLoss, '', '', '', '', '', '','','','','',$storm_type);
                   }
               }

               my $cnt = 0;
               my $holdCNT = '';
               for my $ad (@$assignmentResults)
               {
                   if($ad->{'COMMON_STAT_ID'} eq $p->{'CLM_COMMON_STAT_ID'})
                   {
                       if($cnt gt 0)
                       {
                           $holdCNT = '_'.$cnt
                       }
                       else
                       { $holdCNT = ''; }

                       my $assignmentID = $ad->{'ASSIGNMENT_ID'};
                       ($assignCovHTML) = buildCoverage($ENGINE,$p,$claimid,$showCauseOfLoss,$ad->{'COVERAGE_ID'} || '',$ad->{'LOSS_CODE_GROUP'} || '',$ad->{'TYPE_OF_LOSS'} || '',$ad->{'IMT_CAUSE_OF_LOSS'} || '',$assignCovHTML,$holdCNT,$assignmentID,$badassignCov,$badassignTypeOfLoss,$badassignCauseOfLoss, $storm_type);
                       $cnt++;
                       $covCntr++;
                   }
                }
           }
       }

       my $covCntrID = 'covCntr' . $p->{'CLM_COMMON_STAT_ID'};
       my $covCounter = '<input type="hidden" name="'.$covCntrID.'" id="'.$covCntrID.'" value="'.$covCntr.'" />';
       $assignCovHTML .= $covCounter;
       my $addCov = 'addCov' . $p->{'CLM_COMMON_STAT_ID'};
       ##############################Add Coverage Button#######################
       if ($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /300|301|302|330|331|332|810|814|816/ && $ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} eq 'P') {
           $addCovBut = '<li><input type="button" class="add" name="' . $addCov . '" id="' . $addCov . '" value="Add Coverage" onclick="addNewDetCov(this);"/></li>';
           $assignCovHTML .= $addCovBut;
       }
       elsif($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /100|105|575/ && $ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} eq 'P' && $storm_type eq 'C')
       {
           $addCovBut = '<li><input type="button" class="add" name="' . $addCov . '" id="' . $addCov . '" value="Add Coverage" onclick="addNewDetCov(this);"/></li>';
           $assignCovHTML .= $addCovBut;
       }

      if (($homePolicy eq 'Y' && $ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} ne 'P' )
          || ($homePolicy eq 'Y' && $ENGINE->{'claimGeneral'}->{'MANUAL_OR_WHAT'} eq 'M' && $ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'Internal')
          || $homePolicy eq '')
      {
#        if (($homePolicy eq 'Y' && $ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} ne 'P')
#          || ($homePolicy eq 'Y' && $ENGINE->{'claimGeneral'}->{'MANUAL_OR_WHAT'} eq 'M')
#          || $homePolicy eq '')
#      {
#              if ($p->{'LOC_TYPE'} eq 'AC' && $ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'Internal')
              if ($p->{'LOC_TYPE'} eq 'AC')
              {
                 if ($liabPolicy eq 'Y' || $glPolicy eq 'Y')
                 {
                    $liabSpace = '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;';
                 }

#                 $propertyLocationHTML .= '<div name="policyLocHeading'.$p->{'CLM_COMMON_STAT_ID'}.'" id="policyLocHeading'.$p->{'CLM_COMMON_STAT_ID'}.'" style="margin-bottom:1em">';
#                 $propertyLocationHTML .= '<div id="policyLocRow'.$p->{'CLM_COMMON_STAT_ID'}.'" style="display" class="nowrap">';
                 $propertyLocationHTML .= '<input type="hidden" name="policyLocSection" id="policyLocSection" value=""/>';
                 $propertyLocationHTML .= '<li id="policyLocHeading'.$p->{'CLM_COMMON_STAT_ID'}.'" style="display"><label>Policy Location:</label><div><input type="hidden" name="policyLocIDCS'.$p->{'CLM_COMMON_STAT_ID'}.'" id="policyLocIDCS'.$p->{'CLM_COMMON_STAT_ID'}.'" value="'.$p->{'CLM_COMMON_STAT_ID'}.'"/><input type="hidden" name="policyLocIDL'.$p->{'CLM_COMMON_STAT_ID'}.'" id="policyLocIDL'.$p->{'CLM_COMMON_STAT_ID'}.'" value="'.$p->{'LOCATION_ID'}.'"/>';
                 if($ENGINE->{'CGI'}->param('focusSet') eq 'set')
                 {
                     $focusSet = $p->{'CLM_COMMON_STAT_ID'};
                 }
                 else
                 {
                     $focusSet = '';
                 }

                 if (defined($p->{'FL_ACRES'}) && $p->{'FL_ACRES'} =~ /\w/ && $p->{'FL_ACRES'} > 0)
                 {
                    if($LineCode =~ /810|814|816/)
                    { $propertyLocationHTML .= 'Location: '.$p->{'LOCATION_NO'}.' '; }
                    else
                    { $propertyLocationHTML .= 'Location: '.$p->{'LOCATION_NO'}.' Acres: '.$p->{'FL_ACRES'}.' '; }
                    if (defined($p->{'FL_SECTION'}) && $p->{'FL_SECTION'} =~ /\w/)
                    {
                       $propertyLocationHTML .= ' Section: '.$p->{'FL_SECTION'}.' ';
                    }
                    if (defined($p->{'FL_RANGE'}) && $p->{'FL_RANGE'} =~ /\w/)
                    {
                       $propertyLocationHTML .= ' Range: '.$p->{'FL_RANGE'}.' ';
                    }
                    if (defined($p->{'FL_TOWNSHIP_WORDAGE'}) && $p->{'FL_TOWNSHIP_WORDAGE'} =~ /\w/)
                    {
                       $propertyLocationHTML .= ' Township: '.$p->{'FL_TOWNSHIP_WORDAGE'}.' ';
                    }
                    if (defined($p->{'FL_COUNTY_WORDS'}) && $p->{'FL_COUNTY_WORDS'} =~ /\w/)
                    {
                       $propertyLocationHTML .= ' County: '.$p->{'FL_COUNTY_WORDS'}.' ';
                    }
                 }
                 else
                 {
                    if (defined($p->{'ADDRESS1'}) && $p->{'ADDRESS1'} =~ /\w/)
                    {
                       # This if is checking to see if it is a GL policy.  Address 1 and Address 2 is holding the description of the location.
                       if($glPolicy eq 'Y')
                       {
                       if(defined($p->{'UNIT_NO'}) && $p->{'UNIT_NO'} =~ /98|99/)
                       { $propertyLocationHTML .= 'Class Code: '.$p->{'ADDRESS1'}.' '; }
                       else
#                           { $propertyLocationHTML .= 'Location: '.$p->{'LOCATION_NO'}.' Unit: '.$p->{'UNIT_NO'}.' Address: '.$p->{'ADDRESS1'}.' '; }
                           {
                               if($ENGINE->{'claimGeneral'}->{'SYSTEM_IND'} eq ' C')
                               { $propertyLocationHTML .= 'Location: '.$p->{'LOCATION_NO'}.' Unit: '.$p->{'UNIT_NO'}.' '.$p->{'ADDRESS1'}.' '; }
                               else
                               { $propertyLocationHTML .= $p->{'ADDRESS1'}.' '; }
                           }
                       }
                       elsif (!defined $p->{'UNIT_NO'}
                               || $p->{'UNIT_NO'} == 0)
                               {
#                                       $propertyLocationHTML .= 'Location: '.$p->{'LOCATION_NO'}.' Unit: '.$p->{'UNIT_NO'}.' '.$p->{'ADDRESS1'}.' ';
                                       $propertyLocationHTML .= $p->{'ADDRESS1'}.' ';
                               }
                               elsif(defined($p->{'UNIT_NO'}) && $p->{'UNIT_NO'} eq '99' && $arPolicy eq 'Y')
                               {
                        $propertyLocationHTML .= 'Class Code: '.$p->{'ADDRESS1'}.' ';
                               }
                               else
#                       { $propertyLocationHTML .= 'Location: '.$p->{'LOCATION_NO'}.' Address: '.$p->{'ADDRESS1'}.' '; }
                   { $propertyLocationHTML .= 'Location: '.$p->{'LOCATION_NO'}.' Building: '.$p->{'UNIT_NO'}.' '.$p->{'ADDRESS1'}.' '; }
                    }
                if (defined($p->{'ADDRESS2'}) && $p->{'ADDRESS2'} =~ /\w/ && defined($p->{'UNIT_NO'}) && $p->{'UNIT_NO'} =~ /98|99/)
                {
                    $propertyLocationHTML .= $p->{'ADDRESS2'};
                }
                else
                {
                    $propertyLocationHTML .= $p->{'ADDRESS2'}.' ';
                }

                if (defined($p->{'CITY'}) && $p->{'CITY'} =~ /\w/)
                    {
                    if ( ($glPolicy eq 'Y')
                            && (!defined $p->{'UNIT_NO'}
                        || $p->{'UNIT_NO'} == 0) )
                    {
                             #do nothing
                    }
#                    elsif(defined($p->{'UNIT_NO'}) && $p->{'UNIT_NO'} =~ /98|99/ && $arPolicy eq 'Y' || $glPolicy eq 'Y')
#                    {
#                        $propertyLocationHTML .= $p->{'CITY'}.' ';
#                    }
                    else
                    {
#                        $propertyLocationHTML .= ' City: '.$p->{'CITY'}.' ';
                        $propertyLocationHTML .= $p->{'CITY'}.' ';
                    }
                    }
                 }

                 if (defined($p->{'STATE'}) && $p->{'STATE'} =~ /\w/)
                 {
#                    $propertyLocationHTML .= ' State: '.$p->{'STATE'}.' ';
                    $propertyLocationHTML .= $p->{'STATE'}.' ';
                 }
                 if (defined($p->{'ZIP1_5'}) && $p->{'ZIP1_5'} =~ /\w/)
                 {
                    $propertyLocationHTML .= $p->{'ZIP1_5'}.'-';
                 }
                 if (defined($p->{'ZIP6_12'}) && $p->{'ZIP6_12'} =~ /\w/ && $p->{'ZIP6_12'} ne '0000')
                 {
                    $propertyLocationHTML .= $p->{'ZIP6_12'}.' ';
                 }

             if(defined($p->{'UNIT_NO'}) && $p->{'UNIT_NO'} !~ /98|99/)
                 { chop($propertyLocationHTML); }

              my $removeOrNot = 'Y';

              for my $cr (@$moneyhResults)
              {
                   if($p->{'CLM_COMMON_STAT_ID'} =~ /^new/)
                   {
                       $removeOrNot = 'Y';
                   }
                   elsif ($cr->{'CLM_COMMON_STAT_ID'} == $p->{'CLM_COMMON_STAT_ID'})
                   {
                       $removeOrNot = 'N';
                   }
              }

              if($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~/112|113|120/)
              {
                  $removeOrNot = 'N';
              }

             if (!$ENGINE->{'READONLY'})
             {
                  if ($removeOrNot eq 'Y')
                  {
                     $propertyLocationHTML .= '<input type="button" class="delete" value="Remove" onclick="removePolicyLoc(document.getElementById(\'policyLocIDCS'.$p->{'CLM_COMMON_STAT_ID'}.'\'))" /></div></li>';
                  }
                  else
                  {
                     $propertyLocationHTML .= '</div></li><li></li>';
                  }
              }
              else
              {
                 $propertyLocationHTML .= '</div></li><li></li>';
              }

              $propertyLocationHTML .= '<ul class="leftlabel_twocol" id="assignHide3'.$p->{'CLM_COMMON_STAT_ID'}.'" style='.$assigCovStyle.'>'.$assignCovHTML.'</ul>';
              $assignHide3IDs .= $p->{'CLM_COMMON_STAT_ID'}.'|';
#              die Data::Dumper::Dumper($propertyLocationHTML);
#             if ($LineCode =~ /105|575|580/)
#             {
#                $propertyLocationHTML .= '<tr><th style="padding-left:2em">Building</th><td>Building: '.$p->{'UNIT_NO'}.'</td></tr>';
#             }
#             elsif ($LineCode =~ /200|205/)
#             {
#                $propertyLocationHTML .= '<tr><th style="padding-left:2em">Item</th><td>Unit: '.$p->{'UNIT_NO'}.'</td></tr>';
#             }
                 if ($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'Internal')
                 {
                    if ($LineCode =~ /100|110|111|112|113|120/)
                    {
                       if (defined($p->{'INTERIOR_UPKEEP'}) && $ENGINE->{'READONLY'})
                       {
                          my $data = $iUpkeep{$p->{'INTERIOR_UPKEEP'}} || 'Unknown';
                          $propertyLocationHTML .= '<li>';
                          $propertyLocationHTML .= Claims_Misc::createLabelHelp({
                               'label' =>  'Home Interior Upkeep: ',
                               'help'  =>  'Select the condition of the home interior. Unknown can be selected if you don\'t have the information.  This is an optional field.',
                               'data'  =>  $data,
                               'id'    =>  'home_interior_upkeep_help',
                           });
                          $propertyLocationHTML .= '</li>';
                          #$propertyLocationHTML .= '<tr><th style="padding-left:2em"><span class="help" onclick="help(this.title);" title="Select the condition of the home interior. Unknown can be selected if you don\'t have the information.  This is an optional field.">Home Interior Upkeep</span></th><td>'.($iUpkeep{$p->{'INTERIOR_UPKEEP'}} || 'Unknown').'</td></tr>';
                       }
                       elsif (defined($p->{'INTERIOR_UPKEEP'}) && !$ENGINE->{'READONLY'})
                       {
                          my $iUpkeepOption = '<select name="iupkeep'.$p->{'CLM_COMMON_STAT_ID'}.'" id="iupkeep'.$p->{'CLM_COMMON_STAT_ID'}.'">';
                          for my $key (sort keys %iUpkeep)
                          {
#                         if ($p->{'INTERIOR_UPKEEP'} eq '')
#                         {
#                            $p->{'INTERIOR_UPKEEP'} = ' ';
#                         }

                             my $selected = '';
                             if (scalar(keys %{$ENGINE->{'errors'}}) > 0 && $closeErrorSW eq 'N')
                             {
                             if(defined($ENGINE->{'CGI'}->param('iupkeep'.$p->{'CLM_COMMON_STAT_ID'})) && $ENGINE->{'CGI'}->param('iupkeep'.$p->{'CLM_COMMON_STAT_ID'}) eq $key)
                             {
                                 $selected = ' selected="selected"';
                             }
                             }
                             else
                             {
                                 if ($p->{'INTERIOR_UPKEEP'} eq $key)
                                 {
                                    $selected = ' selected="selected"';
                                 }
                         }

                             $iUpkeepOption .= '<option value="'.$key.'"'.$selected.'>'.$iUpkeep{$key}.'</option>';
                          }

                          if(defined($ENGINE->{'errors'}->{'iupkeep'.$p->{'CLM_COMMON_STAT_ID'}}))
                          {
                            $badInteriorUpkeep = '<li><div class="errorInfo">'.$ENGINE->{'errors'}->{'iupkeep'.$p->{'CLM_COMMON_STAT_ID'}}[0].'</div></li>';
                            $badInteriorUpkeep =~ s/ on the Details screen//g ;
                          }
                          $propertyLocationHTML .= '<li>';
                          $propertyLocationHTML .= Claims_Misc::createLabelHelp({
                               'label' =>  'Home Interior Upkeep: ',
                               'help'  =>  'Select the condition of the home interior. Unknown can be selected if you don\'t have the information.  This is an optional field.',
                               'data'  =>  $iUpkeepOption.'</select>',
                               'id'    =>  'home_interior_upkeep_help',
                           });
                          $propertyLocationHTML .= '</li>'.$badInteriorUpkeep;
                          #$propertyLocationHTML .= '<tr><th style="padding-left:2em"><span class="help" onclick="help(this.title);" title="Select the condition of the home interior. Unknown can be selected if you don\'t have the information.  This is an optional field.">Home Interior Upkeep</span></th><td>'.$badInteriorUpkeep.$iUpkeepOption.'</select></td></tr>';
                       }

                       if (defined($p->{'EXTERIOR_UPKEEP'}) && $ENGINE->{'READONLY'})
                       {
                          my $data = $eUpkeep{$p->{'EXTERIOR_UPKEEP'}} || 'Unknown';
                          $propertyLocationHTML .= '<li>';
                          $propertyLocationHTML .= Claims_Misc::createLabelHelp({
                               'label' =>  'Home Exterior Upkeep: ',
                               'help'  =>  'Select the condition of the home exterior.  Unknown can be selected if the information isn\'t available.  This is an optional field.',
                               'data'  =>  $data,
                               'id'    =>  'home_exterior_upkeep_help',
                           });
                          $propertyLocationHTML .= '</li>';
                          #$propertyLocationHTML .= '<tr><th style="padding-left:2em"><span class="help" onclick="help(this.title);" title="Select the condition of the home exterior.  Unknown can be selected if the information isn\'t available.  This is an optional field. ">Home Exterior Upkeep</span></th><td>'.($eUpkeep{$p->{'EXTERIOR_UPKEEP'}} || 'Unknown').'</td></tr>';
                       }
                       elsif (defined($p->{'EXTERIOR_UPKEEP'}) && !$ENGINE->{'READONLY'})
                       {
                          my $eUpkeepOption = '<select name="eupkeep'.$p->{'CLM_COMMON_STAT_ID'}.'" id="eupkeep'.$p->{'CLM_COMMON_STAT_ID'}.'">';
                          for my $key (sort keys %eUpkeep)
                          {
#                         if ($p->{'EXTERIOR_UPKEEP'} eq '')
#                         {
#                            $p->{'EXTERIOR_UPKEEP'} = ' ';
#                         }

                             my $selected = '';
                             if (scalar(keys %{$ENGINE->{'errors'}}) > 0 && $closeErrorSW eq 'N')
                             {
                             if(defined($ENGINE->{'CGI'}->param('eupkeep'.$p->{'CLM_COMMON_STAT_ID'})) && $ENGINE->{'CGI'}->param('eupkeep'.$p->{'CLM_COMMON_STAT_ID'}) eq $key)
                             {
                                 $selected = ' selected="selected"';
                             }
                             }
                             else
                             {
                                 if ($p->{'EXTERIOR_UPKEEP'} eq $key)
                                 {
                                    $selected = ' selected="selected"';
                                 }
                             }

                             $eUpkeepOption .= '<option value="'.$key.'"'.$selected.'>'.$eUpkeep{$key}.'</option>';
                          }

                          if(defined($ENGINE->{'errors'}->{'eupkeep'.$p->{'CLM_COMMON_STAT_ID'}}))
                          {
                              $badExteriorUpkeep = '<li><div class="errorInfo">'.$ENGINE->{'errors'}->{'eupkeep'.$p->{'CLM_COMMON_STAT_ID'}}[0].'</div></li>';
                              $badExteriorUpkeep =~ s/ on the Details screen//g ;
                          }
                          $propertyLocationHTML .= '<li>';
                          $propertyLocationHTML .= Claims_Misc::createLabelHelp({
                               'label' =>  'Home Exterior Upkeep: ',
                               'help'  =>  'Select the condition of the home exterior.  Unknown can be selected if the information isn\'t available.  This is an optional field.',
                               'data'  =>  $eUpkeepOption.'</select>',
                               'id'    =>  'home_exterior_upkeep_help',
                           });
                          $propertyLocationHTML .= '</li>'.$badExteriorUpkeep;
                          #$propertyLocationHTML .= '<tr><th style="padding-left:2em"><span class="help" onclick="help(this.title);" title="Select the condition of the home exterior.  Unknown can be selected if the information isn\'t available.  This is an optional field. ">Home Exterior Upkeep</span></th><td>'.$badExteriorUpkeep.$eUpkeepOption.'</select></td></tr>';
                       }

                       if (defined($p->{'YARD_UPKEEP'}) && $ENGINE->{'READONLY'})
                       {
                          my $data = $yUpkeep{$p->{'YARD_UPKEEP'}} || 'Unknown';
                          $propertyLocationHTML .= '<li>';
                          $propertyLocationHTML .= Claims_Misc::createLabelHelp({
                               'label' =>  'Yard Upkeep: ',
                               'help'  =>  'Select the condition of the yard.  Unknown can be selected if the information isn\'t available.  This is an optional field.',
                               'data'  =>  $data,
                               'id'    =>  'yard_upkeep_help',
                           });
                          $propertyLocationHTML .= '</li>';
                          #$propertyLocationHTML .= '<tr><th style="padding-left:2em"><span class="help" onclick="help(this.title);" title="Select the condition of the yard. Unknown can be selected if the information isn\'t available.  This is an optional field.">Yard Upkeep</span></th><td>'.($yUpkeep{$p->{'YARD_UPKEEP'}} || 'Unknown').'</td></tr>';
                       }
                       elsif (defined($p->{'YARD_UPKEEP'}) && !$ENGINE->{'READONLY'})
                       {
                          my $yUpkeepOption = '<select name="yupkeep'.$p->{'CLM_COMMON_STAT_ID'}.'" id="yupkeep'.$p->{'CLM_COMMON_STAT_ID'}.'">';
                          for my $key (sort keys %yUpkeep)
                          {
#                         if ($p->{'YARD_UPKEEP'} eq '')
#                         {
#                            $p->{'YARD_UPKEEP'} = ' ';
#                         }

                             my $selected = '';
                             if (scalar(keys %{$ENGINE->{'errors'}}) > 0 && $closeErrorSW eq 'N')
                             {
                             if(defined($ENGINE->{'CGI'}->param('yupkeep'.$p->{'CLM_COMMON_STAT_ID'})) && $ENGINE->{'CGI'}->param('yupkeep'.$p->{'CLM_COMMON_STAT_ID'}) eq $key)
                             {
                                 $selected = ' selected="selected"';
                             }
                             }
                             else
                             {
                                 if ($p->{'YARD_UPKEEP'} eq $key)
                                 {
                                    $selected = ' selected="selected"';
                                 }
                             }

                             $yUpkeepOption .= '<option value="'.$key.'"'.$selected.'>'.$yUpkeep{$key}.'</option>';
                          }


                          if(defined($ENGINE->{'errors'}->{'yupkeep'.$p->{'CLM_COMMON_STAT_ID'}}))
                          {
                              $badYardUpkeep = '<li><div class="errorInfo">'.$ENGINE->{'errors'}->{'yupkeep'.$p->{'CLM_COMMON_STAT_ID'}}[0].'</div></li>';
                              $badYardUpkeep =~ s/ on the Details screen//g ;
                          }

                          $propertyLocationHTML .= '<li>';
                          $propertyLocationHTML .= Claims_Misc::createLabelHelp({
                               'label' =>  'Yard Upkeep: ',
                               'help'  =>  'Select the condition of the yard.  Unknown can be selected if the information isn\'t available.  This is an optional field.',
                               'data'  =>  $yUpkeepOption.'</select>',
                               'id'    =>  'yard_upkeep_help',
                           });
                          $propertyLocationHTML .= '</li>'.$badYardUpkeep;
                          #$propertyLocationHTML .= '<tr><th style="padding-left:2em"><span class="help" onclick="help(this.title);" title="Select the condition of the yard. Unknown can be selected if the information isn\'t available.  This is an optional field.">Yard Upkeep</span></th><td>'.$badYardUpkeep.$yUpkeepOption.'</select></td></tr>';
                       }
                    }
                  if($LineCode =~ /100|105|112|113|575/ && $ENGINE->{'claimGeneral'}->{'PROP_OR_LIAB'} =~ /P|B/)
                  {
                     my $recovReserveQuery = $ENGINE->{'DBH'}->prepare
                        ("select distinct r.recoverable
                           from claimdb.clm_reserves r
                           join claimdb.clm_covs_endorses c
                           on r.coverage_id = c.coverage_id
                           where
                           c.claim_id = ?
                           and c.location_no = ?
                           and r.recoverable = 'X'
                           for fetch only;")
                        || error($ENGINE,'Recovery reserve prepare failed: '.$ENGINE->{'DBH'}->errstr);
                     $recovReserveQuery->execute($p->{'CLAIM_ID'},$p->{'LOCATION_NO'})
                        || error($ENGINE,'Recovery reserve query execute failed: '.$ENGINE->{'DBH'}->errstr);
                     my $recovReserveResults = $recovReserveQuery->fetchall_arrayref({})
                        || error($ENGINE,'Recovery reserve query fetch failed: '.$ENGINE->{'DBH'}->errstr);

                     if (defined($recovReserveResults->[0]) && $recovReserveResults->[0]->{'RECOVERABLE'} eq 'X')
                     {
                        #roof replacement
                        my $hasRoofBeenReplaced = '';
                        my $roofReplacedFieldId = 'roofReplaced'.$p->{'CLM_COMMON_STAT_ID'};

                        if(defined $p->{'ROOF_REPLACED'} && $p->{'ROOF_REPLACED'} ne '')
                        {$hasRoofBeenReplaced = $p->{'ROOF_REPLACED'};}
                        if(scalar(keys %{$ENGINE->{'errors'}}) > 0 
                           && $closeErrorSW eq 'N'
                           && defined($ENGINE->{'CGI'}->param($roofReplacedFieldId)) 
                           && $ENGINE->{'CGI'}->param($roofReplacedFieldId) ne '')
                        {$hasRoofBeenReplaced = $ENGINE->{'CGI'}->param($roofReplacedFieldId);}

                        my $roofReplacedError = '';
                        my $roofReplacedRadio = '';

                        if($ENGINE->{'READONLY'})
                        {
                           if($hasRoofBeenReplaced eq 'Y')
                           {$roofReplacedRadio = 'Yes';}
                           elsif($hasRoofBeenReplaced eq 'N')
                           {$roofReplacedRadio = 'No';}
                           elsif($hasRoofBeenReplaced eq 'U')
                           {$roofReplacedRadio = 'Unknown';}
                        }
                        else
                        {
                           my $roofReplacedYes = '';
                           my $roofReplacedNo = '';
                           my $roofReplacedUnknown = '';
                           if($hasRoofBeenReplaced eq 'Y')
                           {$roofReplacedYes = 'checked';}
                           elsif($hasRoofBeenReplaced eq 'N')
                           {$roofReplacedNo = 'checked';}
                           elsif($hasRoofBeenReplaced eq 'U')
                           {$roofReplacedUnknown = 'checked';}

                           my $onChangeFunction = 'onclick="roofReplacedChange(this,\''.$p->{'CLM_COMMON_STAT_ID'}.'\')"';

                           $roofReplacedRadio = "<input type=\"radio\" name=\"$roofReplacedFieldId\" id=\"$roofReplacedFieldId\" value=\"Y\" $roofReplacedYes $onChangeFunction/> Yes <input type=\"radio\" name=\"$roofReplacedFieldId\" id=\"$roofReplacedFieldId\" value=\"N\" $roofReplacedNo $onChangeFunction /> No <input type=\"radio\" name=\"$roofReplacedFieldId\" id=\"$roofReplacedFieldId\" value=\"U\" $roofReplacedUnknown $onChangeFunction/> Unknown";
                        
                           if(defined($ENGINE->{'errors'}->{$roofReplacedFieldId}))
                           {
                              $roofReplacedError = '<li><div class="errorInfo">'.$ENGINE->{'errors'}->{$roofReplacedFieldId}[0].'</div></li>';
                              $roofReplacedError =~ s/ on the Details screen//g ;
                           }
                        
                        }

                        $propertyLocationHTML .= '<li>';
                        $propertyLocationHTML .= Claims_Misc::createLabelHelp({
                              'label' =>  'Has the Roof Been Replaced?: ',
                              'help'  =>  'This field is required.',
                              'data'  =>  $roofReplacedRadio,
                              'id'    =>  $roofReplacedFieldId.'_help',
                        });
                        $propertyLocationHTML .= '</li>'.$roofReplacedError;

                        my $showYearReplaced = 'style="display:none"';
                        if($hasRoofBeenReplaced eq 'Y')
                        {$showYearReplaced = '';}

                        my $yearReplacedError = '';
                        my $yearReplaced = '';
                        my $yearReplacedFieldId = 'yearRoofReplaced'.$p->{'CLM_COMMON_STAT_ID'};

                        if(defined $p->{'YEAR_ROOF_REPLACED'} && $p->{'YEAR_ROOF_REPLACED'} ne '')
                        {$yearReplaced = $p->{'YEAR_ROOF_REPLACED'};}
                        if(scalar(keys %{$ENGINE->{'errors'}}) > 0 
                           && $closeErrorSW eq 'N'
                           && defined($ENGINE->{'CGI'}->param($yearReplacedFieldId)) 
                           && $ENGINE->{'CGI'}->param($yearReplacedFieldId) ne '')
                        {$yearReplaced = $ENGINE->{'CGI'}->param($yearReplacedFieldId);}

                        my $yearReplacedField = "";
                        my $yearReplacedLiId = 'yearRoofReplacedLi'.$p->{'CLM_COMMON_STAT_ID'};

                        if($ENGINE->{'READONLY'})
                        {$yearReplacedField = $yearReplaced;}
                        else
                        {
                           my ($day,$mon,$currentYear) = (localtime())[3..5];
                           $currentYear+=1900;
                           $yearReplacedField = "<input size=\"6\" maxlength=\"4\" value=\"$yearReplaced\" name=\"$yearReplacedFieldId\" id=\"$yearReplacedFieldId\" >";
                           $yearReplacedField .= " Enter an UW memo notifying them the roof has been replaced and the year it was replaced.";

                           if(defined($ENGINE->{'errors'}->{$yearReplacedFieldId}))
                           {
                              $yearReplacedError = '<li><div class="errorInfo">'.$ENGINE->{'errors'}->{$yearReplacedFieldId}[0].'</div></li>';
                              $yearReplacedError =~ s/ on the Details screen//g ;
                           }
                        
                        }

                        $propertyLocationHTML .= "<li id=\"$yearReplacedLiId\" $showYearReplaced>";
                        $propertyLocationHTML .= Claims_Misc::createLabelHelp({
                              'label' =>  'Year Roof Replaced: ',
                              'help'  =>  'This field is required.',
                              'data'  =>  $yearReplacedField,
                              'id'    =>  $yearReplacedFieldId.'_help',
                        });
                        $propertyLocationHTML .= '</li>'.$yearReplacedError;

                        my $depreciationAmtError = '';
                        my $depreciationAmt = '';
                        my $depreciationAmtFieldId = 'depreciationAmt'.$p->{'CLM_COMMON_STAT_ID'};

                        if(defined $p->{'DEPRECIATE_AMOUNT'} && $p->{'DEPRECIATE_AMOUNT'} ne '')
                        {$depreciationAmt = $p->{'DEPRECIATE_AMOUNT'};}
                        if(scalar(keys %{$ENGINE->{'errors'}}) > 0 
                           && $closeErrorSW eq 'N'
                           && defined($ENGINE->{'CGI'}->param($depreciationAmtFieldId)) 
                           && $ENGINE->{'CGI'}->param($depreciationAmtFieldId) ne '')
                        {$depreciationAmt = $ENGINE->{'CGI'}->param($depreciationAmtFieldId);}

                        my $depreciationAmtField = "";
                        my $depreciationAmtLiId = 'depreciationAmtLi'.$p->{'CLM_COMMON_STAT_ID'};

                        if($ENGINE->{'READONLY'})
                        {$depreciationAmtField = $depreciationAmt;}
                        else
                        {
                           $depreciationAmtField = "<input value=\"$depreciationAmt\" name=\"$depreciationAmtFieldId\" id=\"$depreciationAmtFieldId\" >";
                        
                           if(defined($ENGINE->{'errors'}->{$depreciationAmtFieldId}))
                           {
                              $depreciationAmtError = '<li><div class="errorInfo">'.$ENGINE->{'errors'}->{$depreciationAmtFieldId}[0].'</div></li>';
                              $depreciationAmtError =~ s/ on the Details screen//g ;
                           }
                        
                        }

                        $propertyLocationHTML .= "<li id=\"$depreciationAmtLiId\" $showYearReplaced>";
                        $propertyLocationHTML .= Claims_Misc::createLabelHelp({
                              'label' =>  'Nonrecoverable Depreciation Amount: ',
                              'help'  =>  'This field is required.',
                              'data'  =>  $depreciationAmtField,
                              'id'    =>  $depreciationAmtFieldId.'_help',
                        });
                        $propertyLocationHTML .= '</li>'.$depreciationAmtError;
                     }
                  }

                #initialize stat fields.
                    my $constType = '';
                    my $lossSettleType = '';
                    my $occupancyType = '';
                    my $primaryHeatType = '';
                    my $burglarAlarmType = '';
                    my $crimeOffPremType = '';
                    my $ecSymbolType = '';
                    my $pctMonthlyLmtType = '';
                    my $manualRateType = '';
                    my $eqSprinklerLeakType = '';
                    my $pelletHeatType = '';
                    my $vacantType = '';
                    my $smokeDetectorType = '';
                    my $fireDetectorType = '';
                    my $mhTieDownType = '';
                    my $fireLegalType = '';
                    my $protectDeviceType = '';
                    my $protectCodeType = '';
                    my $residenceType = '';
#                    my $ratingIDType = createSelect($ENGINE,{'attributes'=>{'name'=>'ratingIDType'.$p->{'CLM_COMMON_STAT_ID'}},'value'=>$p->{'BOART_RATE_IDENT'},'options'=>\%ratingIDCodes});
                    my $packageCodeType = '';
#                    my $entityCodeType = createSelect($ENGINE,{'attributes'=>{'name'=>'entityCodeType'.$p->{'CLM_COMMON_STAT_ID'}},'value'=>$p->{'ENTITY_CODE'},'options'=>\%entityCodes});
                    my $numUnitsType = '';
                    my $AAIS_FormsType = '';
                    my $CPPackageType = '';
                    my $GLPackageType = '';
                    my $theftRiskType = '';
                    my $liabRateType = '';
                    my $unbrellaFHType = '';

#                    my $fireDetectorInd = createCheckBox($ENGINE,{'attributes'=>{'name'=>'fireDetectorInd'.$p->{'CLM_COMMON_STAT_ID'},'value'=>'Y'},'value'=>$p->{'DF_FIRE_DETECT'},'onvalue'=>'Y'});
#                    my $burglarAlarmInd = createCheckBox($ENGINE,{'attributes'=>{'name'=>'burglarAlarmInd'.$p->{'CLM_COMMON_STAT_ID'},'value'=>'Y'},'value'=>$p->{'DF_BURGLR_ALARM'},'onvalue'=>'Y'});
                    my $sprinklerInd = '';
                    my $townRowInd = '';
                    my $ordLawInd = '';
                    my $buildRiskInd = '';
                    my $theftCovInd = '';
                    my $theftContInd = '';
                    my $specialRateInd = '';
                    my $sprinklerLeakInd = '';
                    my $contentsInd = '';
                    my $reinsurInd = '';
                    my $ownerLessorInd = '';
                    my $woodStoveInd = '';
                    my $swimPoolInd = '';
                    my $firLegAdtlChgInd = '';
                    my $seasonalInd = '';
                    my $sprinklerCreditInd = '';
                    my $autoIncreaseInd = '';
                    my $liabOnlyInd = '';
                    my $restrictInd = '';
                    my $gemInd = '';
                    my $fairPlanInd = '';
                    my $livestockExcInd = '';

                    my $yearBuilt = '';
                    my $numOfFamilies = '';
                    my $mhWidth = '';
                    my $mhLength = '';
                    my $eqStories = '';
                    my $totalAcres = '';

                    my $holdReadOnly = $ENGINE->{'READONLY'};
                if($ENGINE->{'claimGeneral'}->{'MANUAL_OR_WHAT'} ne 'M')
                {
                        $ENGINE->{'READONLY'} = 1;
                }
                if($screenErrors eq 'Y')
                {
                    if ($homePolicy eq 'Y' || $businPolicy eq 'Y' || $cfPolicy eq 'Y' || $arPolicy eq 'Y' || $imPolicy eq 'Y' || $dpPolicy eq 'Y')
                    {
                        $propertyResults = $ENGINE->{'PropLiab'}->{'CLM_PROPERTY_STAT'};
                    }
                    elsif($liabPolicy eq 'Y' || $glPolicy eq 'Y' || $ucPolicy eq 'Y' || $upPolicy eq 'Y')
                    {
                        $propertyResults = $ENGINE->{'PropLiab'}->{'CLM_LIAB_STAT'};
                    }

                    for my $perr (@$propertyResults)
                    {
                            if($perr->{'CLM_COMMON_STAT_ID'} eq $p->{'CLM_COMMON_STAT_ID'})
                            {
                                    $constType = createSelect($ENGINE,{'attributes'=>{'name'=>'constType'.$perr->{'CLM_COMMON_STAT_ID'},'id'=>'constType'.$perr->{'CLM_COMMON_STAT_ID'}},'value'=>$perr->{'CONST_CODE'},'options'=>\%constCodes});
                                    $lossSettleType = createSelect($ENGINE,{'attributes'=>{'name'=>'lossSettleType'.$perr->{'CLM_COMMON_STAT_ID'},'id'=>'lossSettleType'.$perr->{'CLM_COMMON_STAT_ID'}},'value'=>$perr->{'LOSS_SETTLEMENT_TYPE'},'options'=>\%lossSettleCodes});
                                    $occupancyType = createSelect($ENGINE,{'attributes'=>{'name'=>'occupancyType'.$perr->{'CLM_COMMON_STAT_ID'},'id'=>'occupancyType'.$perr->{'CLM_COMMON_STAT_ID'}},'value'=>$perr->{'CF_OCCUPANCY_CODE'},'options'=>\%occupancyCodes});
                                    $primaryHeatType = createSelect($ENGINE,{'attributes'=>{'name'=>'primaryHeatType'.$perr->{'CLM_COMMON_STAT_ID'},'id'=>'primaryHeatType'.$perr->{'CLM_COMMON_STAT_ID'}},'value'=>$perr->{'DF_PRIMARY_HEAT'},'options'=>\%primaryHeatCodes});
                                    $burglarAlarmType = createSelect($ENGINE,{'attributes'=>{'name'=>'burglarAlarmType'.$perr->{'CLM_COMMON_STAT_ID'},'id'=>'burglarAlarmType'.$perr->{'CLM_COMMON_STAT_ID'}},'value'=>$perr->{'DF_BURGLR_ALARM'},'options'=>\%burglarAlarmCodes});
                                    $crimeOffPremType = createSelect($ENGINE,{'attributes'=>{'name'=>'crimeOffPremType'.$perr->{'CLM_COMMON_STAT_ID'},'id'=>'crimeOffPremType'.$perr->{'CLM_COMMON_STAT_ID'}},'value'=>$perr->{'CF_CRIME_OFFP'},'options'=>\%crimeOffPremCodes});
                                    $ecSymbolType = createSelect($ENGINE,{'attributes'=>{'name'=>'ecSymbolType'.$perr->{'CLM_COMMON_STAT_ID'},'id'=>'ecSymbolType'.$perr->{'CLM_COMMON_STAT_ID'}},'value'=>$perr->{'CF_EC_SYMBOL'},'options'=>\%ecSymbolCodes});
                                    $pctMonthlyLmtType = createSelect($ENGINE,{'attributes'=>{'name'=>'pctMonthlyLmtType'.$perr->{'CLM_COMMON_STAT_ID'},'id'=>'pctMonthlyLmtType'.$perr->{'CLM_COMMON_STAT_ID'}},'value'=>$perr->{'CF_EXTRAEXP_MTHLIM'},'options'=>\%pctMonthlyLmtCodes});
                                    $manualRateType = createSelect($ENGINE,{'attributes'=>{'name'=>'manualRateType'.$perr->{'CLM_COMMON_STAT_ID'},'id'=>'manualRateType'.$perr->{'CLM_COMMON_STAT_ID'}},'value'=>$perr->{'CF_MANUAL_RATE_IND'},'options'=>\%manualRateCodes});
                                    $eqSprinklerLeakType = createSelect($ENGINE,{'attributes'=>{'name'=>'eqSprinklerLeakType'.$perr->{'CLM_COMMON_STAT_ID'},'id'=>'eqSprinklerLeakType'.$perr->{'CLM_COMMON_STAT_ID'}},'value'=>$perr->{'CF_SPRINKLEAK_SUS'},'options'=>\%eqSprinklerLeakCodes});
                                    $pelletHeatType = createSelect($ENGINE,{'attributes'=>{'name'=>'pelletHeatType'.$perr->{'CLM_COMMON_STAT_ID'},'id'=>'pelletHeatType'.$perr->{'CLM_COMMON_STAT_ID'}},'value'=>$perr->{'HO_PELLET_HEATING'},'options'=>\%pelletHeatCodes});
                                    $vacantType = createSelect($ENGINE,{'attributes'=>{'name'=>'vacantType'.$perr->{'CLM_COMMON_STAT_ID'},'id'=>'vacantType'.$perr->{'CLM_COMMON_STAT_ID'}},'value'=>$perr->{'OCCUP_SIZE_LOC'},'options'=>\%vacantCodes});
                                    $smokeDetectorType = createSelect($ENGINE,{'attributes'=>{'name'=>'smokeDetectorType'.$perr->{'CLM_COMMON_STAT_ID'},'id'=>'smokeDetectorType'.$perr->{'CLM_COMMON_STAT_ID'}},'value'=>$perr->{'DF_SMOKE_DETECT'},'options'=>\%smokeDetectorCodes});
                                    $fireDetectorType = createSelect($ENGINE,{'attributes'=>{'name'=>'fireDetectorType'.$perr->{'CLM_COMMON_STAT_ID'},'id'=>'fireDetectorType'.$perr->{'CLM_COMMON_STAT_ID'}},'value'=>$perr->{'DF_FIRE_DETECT'},'options'=>\%fireDetectorCodes});
                                    $mhTieDownType = createSelect($ENGINE,{'attributes'=>{'name'=>'mhTieDownType'.$perr->{'CLM_COMMON_STAT_ID'},'id'=>'mhTieDownType'.$perr->{'CLM_COMMON_STAT_ID'}},'value'=>$perr->{'MH_TIE_DOWN'},'options'=>\%mhTieDownCodes});
                                    $fireLegalType = createSelect($ENGINE,{'attributes'=>{'name'=>'fireLegalType'.$perr->{'CLM_COMMON_STAT_ID'},'id'=>'fireLegalType'.$perr->{'CLM_COMMON_STAT_ID'}},'value'=>$perr->{'CF_LEGALIAB_TYPCOD'},'options'=>\%fireLegalCodes});
                                    $protectDeviceType = createSelect($ENGINE,{'attributes'=>{'name'=>'protectDeviceType'.$perr->{'CLM_COMMON_STAT_ID'},'id'=>'protectDeviceType'.$perr->{'CLM_COMMON_STAT_ID'}},'value'=>$perr->{'PROTECTIVE_DEV'},'options'=>\%protectDeviceCodes});
                                    $protectCodeType = createSelect($ENGINE,{'attributes'=>{'name'=>'protectCodeType'.$perr->{'CLM_COMMON_STAT_ID'},'id'=>'protectCodeType'.$perr->{'CLM_COMMON_STAT_ID'}},'value'=>$perr->{'PROTECT_CODE'},'options'=>\%protectCodes});
                                    $residenceType = createSelect($ENGINE,{'attributes'=>{'name'=>'residenceType'.$perr->{'CLM_COMMON_STAT_ID'},'id'=>'residenceType'.$perr->{'CLM_COMMON_STAT_ID'}},'value'=>$perr->{'DF_RESIDENCE_TYPE'},'options'=>\%residenceTypeCodes});
                    #               my $ratingIDType = createSelect($ENGINE,{'attributes'=>{'name'=>'ratingIDType'.$perr->{'CLM_COMMON_STAT_ID'}},'value'=>$perr->{'BOART_RATE_IDENT'},'options'=>\%ratingIDCodes});
                                    $packageCodeType = createSelect($ENGINE,{'attributes'=>{'name'=>'packageCodeType'.$perr->{'CLM_COMMON_STAT_ID'},'id'=>'packageCodeType'.$perr->{'CLM_COMMON_STAT_ID'}},'value'=>$perr->{'BOART_TYPE_OF_PAK'},'options'=>\%packageCodes});
                    #               my $entityCodeType = createSelect($ENGINE,{'attributes'=>{'name'=>'entityCodeType'.$perr->{'CLM_COMMON_STAT_ID'}},'value'=>$perr->{'ENTITY_CODE'},'options'=>\%entityCodes});
                                    $numUnitsType = createSelect($ENGINE,{'attributes'=>{'name'=>'numOfUnits'.$perr->{'CLM_COMMON_STAT_ID'},'id'=>'numOfUnits'.$perr->{'CLM_COMMON_STAT_ID'}},'value'=>$perr->{'HO_NUM_UNITS'},'options'=>\%numUnitsCodes});
                                    $AAIS_FormsType = createSelect($ENGINE,{'attributes'=>{'name'=>'aaisForm'.$perr->{'CLM_COMMON_STAT_ID'},'id'=>'aaisForm'.$perr->{'CLM_COMMON_STAT_ID'}},'value'=>$perr->{'BOART_AAIS_FORM'},'options'=>\%AAISForm});
                                    $CPPackageType = createSelect($ENGINE,{'attributes'=>{'name'=>'CPPackageCode'.$perr->{'CLM_COMMON_STAT_ID'},'id'=>'CPPackageCode'.$perr->{'CLM_COMMON_STAT_ID'}},'value'=>$perr->{'PACKAGE_CODE'},'options'=>\%CPPackageCodes});
                                    $GLPackageType = createSelect($ENGINE,{'attributes'=>{'name'=>'GLPackageCode'.$perr->{'CLM_COMMON_STAT_ID'},'id'=>'GLPackageCode'.$perr->{'CLM_COMMON_STAT_ID'}},'value'=>$perr->{'PK_PROG_CODE'},'options'=>\%GLPackageCodes});
                                    $theftRiskType = createSelect($ENGINE,{'attributes'=>{'name'=>'theftRiskCode'.$perr->{'CLM_COMMON_STAT_ID'},'id'=>'theftRiskCode'.$perr->{'CLM_COMMON_STAT_ID'}},'value'=>$perr->{'CF_THEFT_RISK_CODE'},'options'=>\%CPTheftRiskCodes});
                                    $liabRateType = createSelect($ENGINE,{'attributes'=>{'name'=>'liabRateCode'.$perr->{'CLM_COMMON_STAT_ID'},'id'=>'liabRateCode'.$perr->{'CLM_COMMON_STAT_ID'}},'value'=>$perr->{'FL_RATE_CODE'},'options'=>\%liabRateCodes});
                                    $unbrellaFHType = createSelect($ENGINE,{'attributes'=>{'name'=>'umbrellaFHCode'.$perr->{'CLM_COMMON_STAT_ID'},'id'=>'umbrellaFHCode'.$perr->{'CLM_COMMON_STAT_ID'}},'value'=>$perr->{'UMBRELLA_FH'},'options'=>\%umbrella_FHCodes});

                    #               my $fireDetectorInd = createCheckBox($ENGINE,{'attributes'=>{'name'=>'fireDetectorInd'.$perr->{'CLM_COMMON_STAT_ID'},'value'=>'Y'},'value'=>$perr->{'DF_FIRE_DETECT'},'onvalue'=>'Y'});
                    #               my $burglarAlarmInd = createCheckBox($ENGINE,{'attributes'=>{'name'=>'burglarAlarmInd'.$perr->{'CLM_COMMON_STAT_ID'},'value'=>'Y'},'value'=>$perr->{'DF_BURGLR_ALARM'},'onvalue'=>'Y'});
                                    $sprinklerInd = createCheckBox($ENGINE,{'attributes'=>{'name'=>'sprinklerInd'.$perr->{'CLM_COMMON_STAT_ID'},'value'=>'Y'},'value'=>$perr->{'SPRINKLER_IND'},'onvalue'=>'Y'});
                                    $townRowInd = createCheckBox($ENGINE,{'attributes'=>{'name'=>'townRowInd'.$perr->{'CLM_COMMON_STAT_ID'},'value'=>'Y'},'value'=>$perr->{'HO_TOWNROW_IND'},'onvalue'=>'Y'});
                                    $ordLawInd = createCheckBox($ENGINE,{'attributes'=>{'name'=>'ordLawInd'.$perr->{'CLM_COMMON_STAT_ID'},'value'=>'Y'},'value'=>$perr->{'ORDINANCE_OR_LAW'},'onvalue'=>'Y'});
                                    $buildRiskInd = createCheckBox($ENGINE,{'attributes'=>{'name'=>'buildRiskInd'.$perr->{'CLM_COMMON_STAT_ID'},'value'=>'Y'},'value'=>$perr->{'DF_BUILDERS_RISK'},'onvalue'=>'Y'});
                                    $theftCovInd = createCheckBox($ENGINE,{'attributes'=>{'name'=>'theftCovInd'.$perr->{'CLM_COMMON_STAT_ID'},'value'=>'Y'},'value'=>$perr->{'CF_THEFT_COV_IND'},'onvalue'=>'Y'});
                                    $theftContInd = createCheckBox($ENGINE,{'attributes'=>{'name'=>'theftContInd'.$perr->{'CLM_COMMON_STAT_ID'},'value'=>'Y'},'value'=>$perr->{'CF_THFT_CONTCV_IND'},'onvalue'=>'Y'});
                                    $specialRateInd = createCheckBox($ENGINE,{'attributes'=>{'name'=>'specialRateInd'.$perr->{'CLM_COMMON_STAT_ID'},'value'=>'Y'},'value'=>$perr->{'CF_SPEC_RATE_IND'},'onvalue'=>'Y'});
                                    $sprinklerLeakInd = createCheckBox($ENGINE,{'attributes'=>{'name'=>'sprinklerLeakInd'.$perr->{'CLM_COMMON_STAT_ID'},'value'=>'Y'},'value'=>$perr->{'CF_SPRINK_LEAK_IND'},'onvalue'=>'Y'});
                                    $contentsInd = createCheckBox($ENGINE,{'attributes'=>{'name'=>'contentsInd'.$perr->{'CLM_COMMON_STAT_ID'},'value'=>'Y'},'value'=>$perr->{'CF_CONTENTS_IND'},'onvalue'=>'Y'});
                                    $reinsurInd = createCheckBox($ENGINE,{'attributes'=>{'name'=>'reinsurInd'.$perr->{'CLM_COMMON_STAT_ID'},'value'=>'Y'},'value'=>$perr->{'CF_REINS_IND'},'onvalue'=>'Y'});
                                    $ownerLessorInd = createCheckBox($ENGINE,{'attributes'=>{'name'=>'ownerLessorInd'.$perr->{'CLM_COMMON_STAT_ID'},'value'=>'Y'},'value'=>$perr->{'BOART_OWN_LESS_IND'},'onvalue'=>'Y'});
                                    $woodStoveInd = createCheckBox($ENGINE,{'attributes'=>{'name'=>'woodStoveInd'.$perr->{'CLM_COMMON_STAT_ID'},'value'=>'Y'},'value'=>$perr->{'WOOD_STOVE'},'onvalue'=>'Y'});
                                    $swimPoolInd = createCheckBox($ENGINE,{'attributes'=>{'name'=>'swimPoolInd'.$perr->{'CLM_COMMON_STAT_ID'},'value'=>'Y'},'value'=>$perr->{'SWIM_POOL'},'onvalue'=>'Y'});
                                    $firLegAdtlChgInd = createCheckBox($ENGINE,{'attributes'=>{'name'=>'firLegAdtlChgInd'.$perr->{'CLM_COMMON_STAT_ID'},'value'=>'Y'},'value'=>$perr->{'CF_FIRLEG_ADTLCHG'},'onvalue'=>'Y'});
                                    $seasonalInd = createCheckBox($ENGINE,{'attributes'=>{'name'=>'seasonalInd'.$perr->{'CLM_COMMON_STAT_ID'},'value'=>'Y'},'value'=>$perr->{'HO_PRIM_SEC_SEAS'},'onvalue'=>'Y'});
                                    $sprinklerCreditInd = createCheckBox($ENGINE,{'attributes'=>{'name'=>'sprinklerCreditInd'.$perr->{'CLM_COMMON_STAT_ID'},'value'=>'Y'},'value'=>$perr->{'CF_SPRINK_CRED_IND'},'onvalue'=>'Y'});
                                    $autoIncreaseInd = createCheckBox($ENGINE,{'attributes'=>{'name'=>'autoIncreaseInd'.$perr->{'CLM_COMMON_STAT_ID'},'value'=>'Y'},'value'=>$perr->{'ART_AUTO_INCREASE'},'onvalue'=>'Y'});
                                    $liabOnlyInd = createCheckBox($ENGINE,{'attributes'=>{'name'=>'liabOnlyInd'.$perr->{'CLM_COMMON_STAT_ID'},'value'=>'Y'},'value'=>$perr->{'DF_LIAB_ONLY_IND'},'onvalue'=>'Y'});
                                    $restrictInd = createCheckBox($ENGINE,{'attributes'=>{'name'=>'restrictInd'.$perr->{'CLM_COMMON_STAT_ID'},'value'=>'1'},'value'=>$perr->{'IM_RESCTRICT_OR_NOT'},'onvalue'=>'1'});
                                    $gemInd = createCheckBox($ENGINE,{'attributes'=>{'name'=>'gemInd'.$perr->{'CLM_COMMON_STAT_ID'},'value'=>'Y'},'value'=>$perr->{'HO_GEM_IND'},'onvalue'=>'Y'});
                                    $fairPlanInd = createCheckBox($ENGINE,{'attributes'=>{'name'=>'fairPlanInd'.$perr->{'CLM_COMMON_STAT_ID'},'value'=>'Y'},'value'=>$perr->{'FAIR_PLAN_IND'},'onvalue'=>'Y'});
                                    $livestockExcInd = createCheckBox($ENGINE,{'attributes'=>{'name'=>'livestockExcInd'.$perr->{'CLM_COMMON_STAT_ID'},'value'=>'Y'},'value'=>$perr->{'FL_LIVESTOCK_EXCL'},'onvalue'=>'Y'});

                                    $yearBuilt = createTextInput($ENGINE,{'attributes'=>{'name'=>'yearBuilt'.$perr->{'CLM_COMMON_STAT_ID'},'id'=>'yearBuilt'.$perr->{'CLM_COMMON_STAT_ID'},'size'=>'4','maxlength'=>'4'},'value'=>$perr->{'YEAR_BUILT'}});
                                    $numOfFamilies = createTextInput($ENGINE,{'attributes'=>{'name'=>'numOfFamilies'.$perr->{'CLM_COMMON_STAT_ID'},'id'=>'numOfFamilies'.$perr->{'CLM_COMMON_STAT_ID'},'size'=>'5'},'value'=>$perr->{'NUMBER_OF_FAMILIES'}});
                                    $mhWidth = createTextInput($ENGINE,{'attributes'=>{'name'=>'mhWidth'.$perr->{'CLM_COMMON_STAT_ID'},'id'=>'mhWidth'.$perr->{'CLM_COMMON_STAT_ID'},'size'=>'3'},'value'=>$perr->{'MH_WIDTH'}});
                                    $mhLength = createTextInput($ENGINE,{'attributes'=>{'name'=>'mhLength'.$perr->{'CLM_COMMON_STAT_ID'},'id'=>'mhLength'.$perr->{'CLM_COMMON_STAT_ID'},'size'=>'3'},'value'=>$perr->{'MH_LENGTH'}});
                                    $eqStories = createTextInput($ENGINE,{'attributes'=>{'name'=>'eqStories'.$perr->{'CLM_COMMON_STAT_ID'},'id'=>'eqStories'.$perr->{'CLM_COMMON_STAT_ID'},'size'=>'3'},'value'=>$perr->{'DF_EQ_STORIES'}});
                                    $totalAcres = createTextInput($ENGINE,{'attributes'=>{'name'=>'totalAcres'.$perr->{'CLM_COMMON_STAT_ID'},'id'=>'totalAcres'.$perr->{'CLM_COMMON_STAT_ID'},'size'=>'5'},'value'=>$perr->{'FL_EXPOSURE_TOTAL_ACRES'}});
                            }
                    }
                }
                else
                {

                        $constType = createSelect($ENGINE,{'attributes'=>{'name'=>'constType'.$p->{'CLM_COMMON_STAT_ID'},'id'=>'constType'.$p->{'CLM_COMMON_STAT_ID'}},'value'=>$p->{'CONST_CODE'},'options'=>\%constCodes});
                        $lossSettleType = createSelect($ENGINE,{'attributes'=>{'name'=>'lossSettleType'.$p->{'CLM_COMMON_STAT_ID'},'id'=>'lossSettleType'.$p->{'CLM_COMMON_STAT_ID'}},'value'=>$p->{'LOSS_SETTLEMENT_TYPE'},'options'=>\%lossSettleCodes});
                        $occupancyType = createSelect($ENGINE,{'attributes'=>{'name'=>'occupancyType'.$p->{'CLM_COMMON_STAT_ID'},'id'=>'occupancyType'.$p->{'CLM_COMMON_STAT_ID'}},'value'=>$p->{'CF_OCCUPANCY_CODE'},'options'=>\%occupancyCodes});
                        $primaryHeatType = createSelect($ENGINE,{'attributes'=>{'name'=>'primaryHeatType'.$p->{'CLM_COMMON_STAT_ID'},'id'=>'primaryHeatType'.$p->{'CLM_COMMON_STAT_ID'}},'value'=>$p->{'DF_PRIMARY_HEAT'},'options'=>\%primaryHeatCodes});
                        $burglarAlarmType = createSelect($ENGINE,{'attributes'=>{'name'=>'burglarAlarmType'.$p->{'CLM_COMMON_STAT_ID'},'id'=>'burglarAlarmType'.$p->{'CLM_COMMON_STAT_ID'}},'value'=>$p->{'DF_BURGLR_ALARM'},'options'=>\%burglarAlarmCodes});
                        $crimeOffPremType = createSelect($ENGINE,{'attributes'=>{'name'=>'crimeOffPremType'.$p->{'CLM_COMMON_STAT_ID'},'id'=>'crimeOffPremType'.$p->{'CLM_COMMON_STAT_ID'}},'value'=>$p->{'CF_CRIME_OFFP'},'options'=>\%crimeOffPremCodes});
                        $ecSymbolType = createSelect($ENGINE,{'attributes'=>{'name'=>'ecSymbolType'.$p->{'CLM_COMMON_STAT_ID'},'id'=>'ecSymbolType'.$p->{'CLM_COMMON_STAT_ID'}},'value'=>$p->{'CF_EC_SYMBOL'},'options'=>\%ecSymbolCodes});
                        $pctMonthlyLmtType = createSelect($ENGINE,{'attributes'=>{'name'=>'pctMonthlyLmtType'.$p->{'CLM_COMMON_STAT_ID'},'id'=>'pctMonthlyLmtType'.$p->{'CLM_COMMON_STAT_ID'}},'value'=>$p->{'CF_EXTRAEXP_MTHLIM'},'options'=>\%pctMonthlyLmtCodes});
                        $manualRateType = createSelect($ENGINE,{'attributes'=>{'name'=>'manualRateType'.$p->{'CLM_COMMON_STAT_ID'},'id'=>'manualRateType'.$p->{'CLM_COMMON_STAT_ID'}},'value'=>$p->{'CF_MANUAL_RATE_IND'},'options'=>\%manualRateCodes});
                        $eqSprinklerLeakType = createSelect($ENGINE,{'attributes'=>{'name'=>'eqSprinklerLeakType'.$p->{'CLM_COMMON_STAT_ID'},'id'=>'eqSprinklerLeakType'.$p->{'CLM_COMMON_STAT_ID'}},'value'=>$p->{'CF_SPRINKLEAK_SUS'},'options'=>\%eqSprinklerLeakCodes});
                        $pelletHeatType = createSelect($ENGINE,{'attributes'=>{'name'=>'pelletHeatType'.$p->{'CLM_COMMON_STAT_ID'},'id'=>'pelletHeatType'.$p->{'CLM_COMMON_STAT_ID'}},'value'=>$p->{'HO_PELLET_HEATING'},'options'=>\%pelletHeatCodes});
                        $vacantType = createSelect($ENGINE,{'attributes'=>{'name'=>'vacantType'.$p->{'CLM_COMMON_STAT_ID'},'id'=>'vacantType'.$p->{'CLM_COMMON_STAT_ID'}},'value'=>$p->{'OCCUP_SIZE_LOC'},'options'=>\%vacantCodes});
                        $smokeDetectorType = createSelect($ENGINE,{'attributes'=>{'name'=>'smokeDetectorType'.$p->{'CLM_COMMON_STAT_ID'},'id'=>'smokeDetectorType'.$p->{'CLM_COMMON_STAT_ID'}},'value'=>$p->{'DF_SMOKE_DETECT'},'options'=>\%smokeDetectorCodes});
                        $fireDetectorType = createSelect($ENGINE,{'attributes'=>{'name'=>'fireDetectorType'.$p->{'CLM_COMMON_STAT_ID'},'id'=>'fireDetectorType'.$p->{'CLM_COMMON_STAT_ID'}},'value'=>$p->{'DF_FIRE_DETECT'},'options'=>\%fireDetectorCodes});
                        $mhTieDownType = createSelect($ENGINE,{'attributes'=>{'name'=>'mhTieDownType'.$p->{'CLM_COMMON_STAT_ID'},'id'=>'mhTieDownType'.$p->{'CLM_COMMON_STAT_ID'}},'value'=>$p->{'MH_TIE_DOWN'},'options'=>\%mhTieDownCodes});
                        $fireLegalType = createSelect($ENGINE,{'attributes'=>{'name'=>'fireLegalType'.$p->{'CLM_COMMON_STAT_ID'},'id'=>'fireLegalType'.$p->{'CLM_COMMON_STAT_ID'}},'value'=>$p->{'CF_LEGALIAB_TYPCOD'},'options'=>\%fireLegalCodes});
                        $protectDeviceType = createSelect($ENGINE,{'attributes'=>{'name'=>'protectDeviceType'.$p->{'CLM_COMMON_STAT_ID'},'id'=>'protectDeviceType'.$p->{'CLM_COMMON_STAT_ID'}},'value'=>$p->{'PROTECTIVE_DEV'},'options'=>\%protectDeviceCodes});
                        $protectCodeType = createSelect($ENGINE,{'attributes'=>{'name'=>'protectCodeType'.$p->{'CLM_COMMON_STAT_ID'},'id'=>'protectCodeType'.$p->{'CLM_COMMON_STAT_ID'}},'value'=>$p->{'PROTECT_CODE'},'options'=>\%protectCodes});
                        $residenceType = createSelect($ENGINE,{'attributes'=>{'name'=>'residenceType'.$p->{'CLM_COMMON_STAT_ID'},'id'=>'residenceType'.$p->{'CLM_COMMON_STAT_ID'}},'value'=>$p->{'DF_RESIDENCE_TYPE'},'options'=>\%residenceTypeCodes});
        #               my $ratingIDType = createSelect($ENGINE,{'attributes'=>{'name'=>'ratingIDType'.$p->{'CLM_COMMON_STAT_ID'}},'value'=>$p->{'BOART_RATE_IDENT'},'options'=>\%ratingIDCodes});
                        $packageCodeType = createSelect($ENGINE,{'attributes'=>{'name'=>'packageCodeType'.$p->{'CLM_COMMON_STAT_ID'},'id'=>'packageCodeType'.$p->{'CLM_COMMON_STAT_ID'}},'value'=>$p->{'BOART_TYPE_OF_PAK'},'options'=>\%packageCodes});
        #               my $entityCodeType = createSelect($ENGINE,{'attributes'=>{'name'=>'entityCodeType'.$p->{'CLM_COMMON_STAT_ID'}},'value'=>$p->{'ENTITY_CODE'},'options'=>\%entityCodes});
                        $numUnitsType = createSelect($ENGINE,{'attributes'=>{'name'=>'numOfUnits'.$p->{'CLM_COMMON_STAT_ID'},'id'=>'numOfUnits'.$p->{'CLM_COMMON_STAT_ID'}},'value'=>$p->{'HO_NUM_UNITS'},'options'=>\%numUnitsCodes});
                        $AAIS_FormsType = createSelect($ENGINE,{'attributes'=>{'name'=>'aaisForm'.$p->{'CLM_COMMON_STAT_ID'},'id'=>'aaisForm'.$p->{'CLM_COMMON_STAT_ID'}},'value'=>$p->{'BOART_AAIS_FORM'},'options'=>\%AAISForm});
                        $CPPackageType = createSelect($ENGINE,{'attributes'=>{'name'=>'CPPackageCode'.$p->{'CLM_COMMON_STAT_ID'},'id'=>'CPPackageCode'.$p->{'CLM_COMMON_STAT_ID'}},'value'=>$p->{'PACKAGE_CODE'},'options'=>\%CPPackageCodes});
                        $GLPackageType = createSelect($ENGINE,{'attributes'=>{'name'=>'GLPackageCode'.$p->{'CLM_COMMON_STAT_ID'},'id'=>'GLPackageCode'.$p->{'CLM_COMMON_STAT_ID'}},'value'=>$p->{'PK_PROG_CODE'},'options'=>\%GLPackageCodes});
                        $theftRiskType = createSelect($ENGINE,{'attributes'=>{'name'=>'theftRiskCode'.$p->{'CLM_COMMON_STAT_ID'},'id'=>'theftRiskCode'.$p->{'CLM_COMMON_STAT_ID'}},'value'=>$p->{'CF_THEFT_RISK_CODE'},'options'=>\%CPTheftRiskCodes});
                        $liabRateType = createSelect($ENGINE,{'attributes'=>{'name'=>'liabRateCode'.$p->{'CLM_COMMON_STAT_ID'},'id'=>'liabRateCode'.$p->{'CLM_COMMON_STAT_ID'}},'value'=>$p->{'FL_RATE_CODE'},'options'=>\%liabRateCodes});
                        $unbrellaFHType = createSelect($ENGINE,{'attributes'=>{'name'=>'umbrellaFHCode'.$p->{'CLM_COMMON_STAT_ID'},'id'=>'umbrellaFHCode'.$p->{'CLM_COMMON_STAT_ID'}},'value'=>$p->{'UMBRELLA_FH'},'options'=>\%umbrella_FHCodes});

        #               my $fireDetectorInd = createCheckBox($ENGINE,{'attributes'=>{'name'=>'fireDetectorInd'.$p->{'CLM_COMMON_STAT_ID'},'value'=>'Y'},'value'=>$p->{'DF_FIRE_DETECT'},'onvalue'=>'Y'});
        #               my $burglarAlarmInd = createCheckBox($ENGINE,{'attributes'=>{'name'=>'burglarAlarmInd'.$p->{'CLM_COMMON_STAT_ID'},'value'=>'Y'},'value'=>$p->{'DF_BURGLR_ALARM'},'onvalue'=>'Y'});
                        $sprinklerInd = createCheckBox($ENGINE,{'attributes'=>{'name'=>'sprinklerInd'.$p->{'CLM_COMMON_STAT_ID'},'value'=>'Y'},'value'=>$p->{'SPRINKLER_IND'},'onvalue'=>'Y'});
                        $townRowInd = createCheckBox($ENGINE,{'attributes'=>{'name'=>'townRowInd'.$p->{'CLM_COMMON_STAT_ID'},'value'=>'Y'},'value'=>$p->{'HO_TOWNROW_IND'},'onvalue'=>'Y'});
                        $ordLawInd = createCheckBox($ENGINE,{'attributes'=>{'name'=>'ordLawInd'.$p->{'CLM_COMMON_STAT_ID'},'value'=>'Y'},'value'=>$p->{'ORDINANCE_OR_LAW'},'onvalue'=>'Y'});
                        $buildRiskInd = createCheckBox($ENGINE,{'attributes'=>{'name'=>'buildRiskInd'.$p->{'CLM_COMMON_STAT_ID'},'value'=>'Y'},'value'=>$p->{'DF_BUILDERS_RISK'},'onvalue'=>'Y'});
                        $theftCovInd = createCheckBox($ENGINE,{'attributes'=>{'name'=>'theftCovInd'.$p->{'CLM_COMMON_STAT_ID'},'value'=>'Y'},'value'=>$p->{'CF_THEFT_COV_IND'},'onvalue'=>'Y'});
                        $theftContInd = createCheckBox($ENGINE,{'attributes'=>{'name'=>'theftContInd'.$p->{'CLM_COMMON_STAT_ID'},'value'=>'Y'},'value'=>$p->{'CF_THFT_CONTCV_IND'},'onvalue'=>'Y'});
                        $specialRateInd = createCheckBox($ENGINE,{'attributes'=>{'name'=>'specialRateInd'.$p->{'CLM_COMMON_STAT_ID'},'value'=>'Y'},'value'=>$p->{'CF_SPEC_RATE_IND'},'onvalue'=>'Y'});
                        $sprinklerLeakInd = createCheckBox($ENGINE,{'attributes'=>{'name'=>'sprinklerLeakInd'.$p->{'CLM_COMMON_STAT_ID'},'value'=>'Y'},'value'=>$p->{'CF_SPRINK_LEAK_IND'},'onvalue'=>'Y'});
                        $contentsInd = createCheckBox($ENGINE,{'attributes'=>{'name'=>'contentsInd'.$p->{'CLM_COMMON_STAT_ID'},'value'=>'Y'},'value'=>$p->{'CF_CONTENTS_IND'},'onvalue'=>'Y'});
                        $reinsurInd = createCheckBox($ENGINE,{'attributes'=>{'name'=>'reinsurInd'.$p->{'CLM_COMMON_STAT_ID'},'value'=>'Y'},'value'=>$p->{'CF_REINS_IND'},'onvalue'=>'Y'});
                        $ownerLessorInd = createCheckBox($ENGINE,{'attributes'=>{'name'=>'ownerLessorInd'.$p->{'CLM_COMMON_STAT_ID'},'value'=>'Y'},'value'=>$p->{'BOART_OWN_LESS_IND'},'onvalue'=>'Y'});
                        $woodStoveInd = createCheckBox($ENGINE,{'attributes'=>{'name'=>'woodStoveInd'.$p->{'CLM_COMMON_STAT_ID'},'value'=>'Y'},'value'=>$p->{'WOOD_STOVE'},'onvalue'=>'Y'});
                        $swimPoolInd = createCheckBox($ENGINE,{'attributes'=>{'name'=>'swimPoolInd'.$p->{'CLM_COMMON_STAT_ID'},'value'=>'Y'},'value'=>$p->{'SWIM_POOL'},'onvalue'=>'Y'});
                        $firLegAdtlChgInd = createCheckBox($ENGINE,{'attributes'=>{'name'=>'firLegAdtlChgInd'.$p->{'CLM_COMMON_STAT_ID'},'value'=>'Y'},'value'=>$p->{'CF_FIRLEG_ADTLCHG'},'onvalue'=>'Y'});
                        $seasonalInd = createCheckBox($ENGINE,{'attributes'=>{'name'=>'seasonalInd'.$p->{'CLM_COMMON_STAT_ID'},'value'=>'Y'},'value'=>$p->{'HO_PRIM_SEC_SEAS'},'onvalue'=>'Y'});
                        $sprinklerCreditInd = createCheckBox($ENGINE,{'attributes'=>{'name'=>'sprinklerCreditInd'.$p->{'CLM_COMMON_STAT_ID'},'value'=>'Y'},'value'=>$p->{'CF_SPRINK_CRED_IND'},'onvalue'=>'Y'});
                        $autoIncreaseInd = createCheckBox($ENGINE,{'attributes'=>{'name'=>'autoIncreaseInd'.$p->{'CLM_COMMON_STAT_ID'},'value'=>'Y'},'value'=>$p->{'ART_AUTO_INCREASE'},'onvalue'=>'Y'});
                        $liabOnlyInd = createCheckBox($ENGINE,{'attributes'=>{'name'=>'liabOnlyInd'.$p->{'CLM_COMMON_STAT_ID'},'value'=>'Y'},'value'=>$p->{'DF_LIAB_ONLY_IND'},'onvalue'=>'Y'});
                        $restrictInd = createCheckBox($ENGINE,{'attributes'=>{'name'=>'restrictInd'.$p->{'CLM_COMMON_STAT_ID'},'value'=>'1'},'value'=>$p->{'IM_RESCTRICT_OR_NOT'},'onvalue'=>'1'});
                        $gemInd = createCheckBox($ENGINE,{'attributes'=>{'name'=>'gemInd'.$p->{'CLM_COMMON_STAT_ID'},'value'=>'Y'},'value'=>$p->{'HO_GEM_IND'},'onvalue'=>'Y'});
                        $fairPlanInd = createCheckBox($ENGINE,{'attributes'=>{'name'=>'fairPlanInd'.$p->{'CLM_COMMON_STAT_ID'},'value'=>'Y'},'value'=>$p->{'FAIR_PLAN_IND'},'onvalue'=>'Y'});
                        $livestockExcInd = createCheckBox($ENGINE,{'attributes'=>{'name'=>'livestockExcInd'.$p->{'CLM_COMMON_STAT_ID'},'value'=>'Y'},'value'=>$p->{'FL_LIVESTOCK_EXCL'},'onvalue'=>'Y'});

                        $yearBuilt = createTextInput($ENGINE,{'attributes'=>{'name'=>'yearBuilt'.$p->{'CLM_COMMON_STAT_ID'},'id'=>'yearBuilt'.$p->{'CLM_COMMON_STAT_ID'},'size'=>'4','maxlength'=>'4'},'value'=>$p->{'YEAR_BUILT'}});
                        $numOfFamilies = createTextInput($ENGINE,{'attributes'=>{'name'=>'numOfFamilies'.$p->{'CLM_COMMON_STAT_ID'},'id'=>'numOfFamilies'.$p->{'CLM_COMMON_STAT_ID'},'size'=>'5'},'value'=>$p->{'NUMBER_OF_FAMILIES'}});
                        $mhWidth = createTextInput($ENGINE,{'attributes'=>{'name'=>'mhWidth'.$p->{'CLM_COMMON_STAT_ID'},'id'=>'mhWidth'.$p->{'CLM_COMMON_STAT_ID'},'size'=>'3'},'value'=>$p->{'MH_WIDTH'}});
                        $mhLength = createTextInput($ENGINE,{'attributes'=>{'name'=>'mhLength'.$p->{'CLM_COMMON_STAT_ID'},'id'=>'mhLength'.$p->{'CLM_COMMON_STAT_ID'},'size'=>'3'},'value'=>$p->{'MH_LENGTH'}});
                        $eqStories = createTextInput($ENGINE,{'attributes'=>{'name'=>'eqStories'.$p->{'CLM_COMMON_STAT_ID'},'id'=>'eqStories'.$p->{'CLM_COMMON_STAT_ID'},'size'=>'3'},'value'=>$p->{'DF_EQ_STORIES'}});
                        $totalAcres = createTextInput($ENGINE,{'attributes'=>{'name'=>'totalAcres'.$p->{'CLM_COMMON_STAT_ID'},'id'=>'totalAcres'.$p->{'CLM_COMMON_STAT_ID'},'size'=>'5'},'value'=>$p->{'FL_EXPOSURE_TOTAL_ACRES'}});
                    }

                    if($ENGINE->{'claimGeneral'}->{'MANUAL_OR_WHAT'} ne 'M')
                    { $ENGINE->{'READONLY'} = $holdReadOnly; }
                    my $i = $p->{'CLM_COMMON_STAT_ID'};

                    my $erroredDisplay = 'display:none';
                    if($foundError eq 'Y')
                    {
                        $erroredDisplay = '';
                    }
                    $propertyLocationHTML .= '<li id="propertyStatHeading'.$p->{'CLM_COMMON_STAT_ID'}.'">[<a class="showhide" onclick="toggle(\'propertyStat'.$i.'\');">Show/hide Stat</a>]</li><li style="'.$erroredDisplay.'" name="propertyStat'.$p->{'CLM_COMMON_STAT_ID'}.'" id="propertyStat'.$p->{'CLM_COMMON_STAT_ID'}.'"><ul class="leftlabel_twocol">';

                if ($LineCode =~ /100|105|112|113|200|205|575|580/)
                {
               $propertyLocationHTML .= <<EOF;
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Construction Type</label><div>$constType</div></li>
EOF
                }

                if ($LineCode =~ /100/)
                {
               $propertyLocationHTML .= <<EOF;
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Residence Type</label><div>$residenceType</div></li>$badResidenceType
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Occupancy Type</label><div>$occupancyType</div></li>$badOccupancyType
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Primary Heat</label><div>$primaryHeatType</div></li>$badPrimaryHeatType
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Burglar Alarm</label><div>$burglarAlarmType</div></li>$badBurglarAlarmType
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Smoke Detector</label><div>$smokeDetectorType</div></li>$badSmokeDetectorType
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Fire Detector</label><div>$fireDetectorType</div></li>$badFireDetectorType
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Builders Risk</label><div>$buildRiskInd</div></li>
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Liability Only</label><div>$liabOnlyInd</div></li>
EOF
                }

                if ($LineCode =~ /100|105/)
                {
               $propertyLocationHTML .= <<EOF;
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Number of Stories</label><div>$eqStories</div></li>$badEQStories
EOF
                }

                if ($LineCode =~ /105/)
                {
               $propertyLocationHTML .= <<EOF;
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Manual Rate</label><div>$manualRateType</div></li>$badManualRateType
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Vacancy Type</label><div>$vacantType</div></li>$badVacantType
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Crime Off Premises</label><div>$crimeOffPremType</div></li>$badCrimeOffPrem
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>EC Symbol</label><div>$ecSymbolType</div></li>$badECSymbolType
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Percent of Monthly limit</label><div>$pctMonthlyLmtType</div></li>$badPCTMonthlyLmtType
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Sprinkler Leakage Susceptibilty</label><div>$eqSprinklerLeakType</div></li>$badEQSprinklerLeakType
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Theft Coverage</label><div>$theftCovInd</div></li>
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Theft Contents Coverage</label><div>$theftContInd</div></li>
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Contents</label><div>$contentsInd</div></li>
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Special Rate</label><div>$specialRateInd</div></li>
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Sprinkler Credit</label><div>$sprinklerCreditInd</div></li>
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Sprinkler Leak</label><div>$sprinklerLeakInd</div></li>
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Protective Device</label><div>$protectDeviceType</div></li>$badProtectDeviceType
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Reinsurance</label><div>$reinsurInd</div></li>
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Fire Legal Type</label><div>$fireLegalType</div></li>$badFireLegalType
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Fire Legal Additional Charge</label><div>$firLegAdtlChgInd</div></li>
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Package Code</label><div>$CPPackageType</div></li>
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Theft Risk Code</label><div>$theftRiskType</div></li>
EOF
                }

                if ($LineCode =~ /112|113/)
                {
               $propertyLocationHTML .= <<EOF;
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Number of Units</label><div>$numUnitsType</div></li>$badNumUnitsType
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Pellet Heating</label><div>$pelletHeatType</div></li>$badPelletHeatType
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Townhouse/Rowhouse</label><div>$townRowInd</div></li>
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Gem</label><div>$gemInd</div></li>
EOF
                }

                if ($LineCode =~ /112|113|120/)
                {
               $propertyLocationHTML .= <<EOF;
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Loss Settlement Type</label><div>$lossSettleType</div></li>$badLossSettleType
EOF
                }

                if ($LineCode =~ /112|113|120|575|580/)
                {
               $propertyLocationHTML .= <<EOF;
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Fair Plan</label><div>$fairPlanInd</div></li>
EOF
                }

                if ($LineCode =~ /120/)
                {
               $propertyLocationHTML .= <<EOF;
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Tie Downs</label><div>$mhTieDownType</div></li>$badMhTieDownType
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Width</label><div>$mhWidth</div></li>$badMhWidth
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Length</label><div>$mhLength</div></li>$badMhLength
EOF
                }

                if ($LineCode =~ /100|105|112|113|120/)
                {
               $propertyLocationHTML .= <<EOF;
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Ordinance or Law</label><div>$ordLawInd</div></li>
EOF
                }

                if ($LineCode =~ /100|105|110|111|112|113|200|205|580/)
                {
               $propertyLocationHTML .= <<EOF;
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Protection Code</label><div>$protectCodeType</div></li>$badProtectCodeType
EOF
                }

                if ($LineCode =~ /100|112|113|120/)
                {
               $propertyLocationHTML .= <<EOF;
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Secondary Residence</label><div>$seasonalInd</div></li>
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Construction Year</label><div>$yearBuilt</div></li>$badYearBuilt
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Number of Families</label><div>$numOfFamilies</div></li>$badNumOfFamilies
EOF
                }

                if ($LineCode =~ /100|575|580/)
                {
               $propertyLocationHTML .= <<EOF;
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Sprinkler System</label><div>$sprinklerInd</div></li>
EOF
                }

                if ($LineCode =~ /200|205/)
                {
               $propertyLocationHTML .= <<EOF;
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Restrictions</label><div>$restrictInd</div></li>
EOF
                }

                if ($LineCode =~ /300|301|302|330|331|332/)
                {
               $propertyLocationHTML .= <<EOF;
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Total Policy Acres</label><div>$totalAcres</div></li>
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Pers Liab Rate Code</label><div>$liabRateType</div></li>
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Livestock Exclusion</label><div>$livestockExcInd</div></li>
EOF
                }

                if ($LineCode =~ /350/)
                {
               $propertyLocationHTML .= <<EOF;
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label><span class="help" onclick="help(this.title);" title="Refer to Risk Type on the Umbrella policiy's General screen to obtain the Umbrella Type.">Umbrella Type</span></label><div>$unbrellaFHType</div></li>$badUmbrellaFHType
EOF
                }

#                if ($LineCode =~ /550/)
#                {
#               $propertyLocationHTML .= <<EOF;
#<tr id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Bond Type</label><div>$bondTypeType</div></tr>
#EOF
#                }

                if ($LineCode =~ /575/)
                {
               $propertyLocationHTML .= <<EOF;
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Owner or Lessor</label><div>$ownerLessorInd</div></li>
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Type Of Pak</label><div>$packageCodeType</div></li>
EOF
                }

                if ($LineCode =~ /575|580/)
                {
               $propertyLocationHTML .= <<EOF;
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>AAIS Form</label><div>$AAIS_FormsType</div></li>
EOF
                }

                if ($LineCode =~ /580/)
                {
               $propertyLocationHTML .= <<EOF;
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Protective Device</label><div>$protectDeviceType</div></li>$badProtectDeviceType
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Construction Year</label><div>$yearBuilt</div></li>$badYearBuilt
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Automatic Increase</label><div>$autoIncreaseInd</div></li>
EOF
                }

                if ($LineCode =~ /810|814|816/)
                {
               $propertyLocationHTML .= <<EOF;
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Package Code</label><div>$GLPackageType</div></li>$badPackageCodeType
EOF
#<tr id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Rating ID</th><div>$badRatingIDType$ratingIDType</div></tr>
#<tr id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Entity Code</th><div>$entityCodeType</div></tr>
                }

            # All lines but Surety and Fidelity Bonds
                if($LineCode !~ /303|550/)
                {
            $propertyLocationHTML .= <<EOF;
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Wood Stove</label><div>$woodStoveInd</div></li>
<li id="p$p->{'CLM_COMMON_STAT_ID'}"><label>Swimming Pool</label><div>$swimPoolInd</div></li>
EOF
                }
                $propertyLocationHTML .= '</ul></li>';
             }
          }
      }
   }

#   die Data::Dumper::Dumper($propertyLocationHTML);
   $locations =~ s/<br \/>$//;

   my $lossLocSpace = '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;';
#   if (!$ENGINE->{'READONLY'})
#   {
#       print ' loss location1 ';
#       if ($lossLocationVardata ne '')
#       {
#          print ' loss location2 ';
#          for my $v (@sortedVardataResults)
#          {
#             if ($v->{'DATA_TYPE'} eq 'LOCATION')
#             {
#                $lossLocationVardata = $v->{'VARDATA'};
#                my $lossLocVardataID = $v->{'CLM_VARDATA_ID'};
#                $lossLocationHTML .= '<tr id="varDataLocRow'.$lossLocVardataID.'" style="display"><th>Loss Location</th><td><input type="hidden" name="varDataLocID'.$lossLocVardataID.'" id="varDataLocID'.$lossLocVardataID.'" value="'.$lossLocVardataID .'">'.$lossLocationVardata.'</td><td><input type="button" value="Remove" onclick="removeVarDataLoc(document.getElementById(\'varDataLocID'.$lossLocVardataID.'\'))" /></td></tr>';
#             }
#             else
#             {
#                 print ' loss location3 ';
#                 my $lossLocVardataID = 'new'.0;
#                 $lossLocationHTML .= '<tr id="varDataLocRow'.$lossLocVardataID.'" style="display"><th>Loss Location</th><td><input type="hidden" name="varDataLocID'.$lossLocVardataID.'" id="varDataLocID'.$lossLocVardataID.'" value="'.$lossLocVardataID .'">'.$lossLocationVardata.'</td><td><input type="button" value="Remove" onclick="removeVarDataLoc(document.getElementById(\'varDataLocID'.$lossLocVardataID.'\'))" /></td></tr>';
#             }
#          }
#       }
#       else
#       {
#           print ' loss location4 ';
#           my $lossLocVardataID = 'new'.0;
#           $lossLocationHTML .= '<tr id="varDataLocRow'.$lossLocVardataID.'" style="display"><th>Loss Location</th><td><input type="hidden" name="varDataLocID'.$lossLocVardataID.'" id="varDataLocID'.$lossLocVardataID.'" value="'.$lossLocVardataID .'">'.$lossLocationVardata.'</td><td><input type="button" value="Remove" onclick="removeVarDataLoc(document.getElementById(\'varDataLocID'.$lossLocVardataID.'\'))" /></td></tr>';
#       }
#   }
#   else
#   {
#       if ($lossLocationVardata ne '')
#       {
#          for my $v (@sortedVardataResults)
#          {
#             if ($v->{'DATA_TYPE'} eq 'LOCATION')
#             {
#                $lossLocationVardata = $v->{'VARDATA'};
#                my $lossLocVardataID = $v->{'CLM_VARDATA_ID'};
#                $lossLocationHTML .= '<tr id="varDataLocRow'.$lossLocVardataID.'" style="display"><th>Loss Location</th><td><input type="hidden" name="varDataLocID'.$lossLocVardataID.'" id="varDataLocID'.$lossLocVardataID.'" value="'.$lossLocVardataID .'">'.$lossLocationVardata.'</td></tr>';
#             }
#          }
#       }
#       else
#       { $lossLocationHTML = '<tr><th>Loss Location</th><td>Unknown</td></tr>'; }
#   }
#   if ($lossLocationHTML eq '')
#   {
#      $lossLocationHTML = '<tr><th>Loss Location</th><td>Unknown</td></tr>';
#   }

   chop($buildingItem);
   chop($buildingItem);

   my $authorityResults = getAuthorityData($ENGINE);
   my $authorities = $authorityResults->{'authority'};
   my $totalLossVehResults = getVehTotalLossData($ENGINE);
   my $totalLossVehicle = $totalLossVehResults->{'totalLossVehicle'};
   my $claimHeading = getClaimHeading($ENGINE);
   my $lossLocationButton = '';
   my $policyLocationButton = '';
   my $polLocErrorSelect = '';
   my $badLossDate = '';
   my $badReportDate = '';

   # Put red arrow by the fields that have an error.
   for my $pc (@{$ENGINE->{'PropLiab'}->{'CLM_GENERAL'}})
   {
       if(defined($pc->{'CLAIM_ID'}) && $pc->{'CLAIM_ID'} eq $claimid)
       {
           if(exists $pc->{'errLossDate'})
           { $badLossDate = $pc->{'errLossDate'}; }
           elsif(defined($ENGINE->{'errors'}->{'lossDate'}))
           {$badLossDate = $ENGINE->{'errors'}->{'lossDate'}[0];}
           if(exists $pc->{'errReportDate'})
           { $badReportDate = $pc->{'errReportDate'}; }
       }
   }
   my $loss_date_attn = '';
   if (!$ENGINE->{'READONLY'})
   {
#      if ($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'Internal')
#      {
#         $claimLossDate = '<input type="hidden" value="" name="changedLossDate" id="changedLossDate">'.$badLossDate.'<input  size="10" maxlength="10" id="lossDate" name="lossDate" value="'.$claimLossDate.'" onchange="changeLossDate(\'Claims_PropLiab\')"> <span style="position:relative;" id="lossDateIcon"></span><img style="border:0;cursor:pointer;vertical-align:middle;" src="calendar.gif" onclick="showCalendarLossDate(\'lossDate\',\'lossDateIcon\');" alt="to pop-up calendar"/>';
         $claimLossDate = '<input type="hidden" value="" name="changedLossDate" id="changedLossDate"/><input type="hidden" value="'.$claimLossDate.'" name="oldLossDate" id="oldLossDate"/><input class="date" maxlength="10" id="lossDate" name="lossDate" value="'.$claimLossDate.'" onchange="showLossDateMessage()"/>';
         if($ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} eq 'P')
         {
             $loss_date_attn = <<EOF;
<ul id="changeLossDateMessage" style="display:none;">
    <li class="no_indent">
        <div class="errorInfo">
             ATTENTION, by changing the date of loss all information pulled from the policy including the vehicle or location information will be lost.
             In addition, any contact information and parties involved will be lost.
             This information will need to be re-entered after the loss date is changed.
             <br/>Are you sure you would like to change the loss date?
             <br/>
             <input type="button" name="changeLossDateAnswer" value="Yes" onclick="changeLossDate(\'Claims_PropLiab\')"/>
             <input type="button" name="changeLossDateAnswer" value="No" onclick="hideLossDateMessage()"/>
        </div>
    </li>
</ul>
EOF
         }
         else
         {
             $loss_date_attn = <<EOF;
<ul id="changeLossDateMessage" style="display:none;">
    <li class="no_indent">
        <div class="errorInfo">
             ATTENTION, by changing the date of loss all information pulled from the policy including the vehicle or location information will be lost.
             In addition, any contact information and parties involved will be lost.
             This information will need to be re-entered after the loss date is changed.
             <br/>Are you sure you would like to change the loss date?
             <br/>
             <input type="button" name="changeLossDateAnswer" value="Yes" onclick="changeLossDate(\'Claims_PropLiab\')"/>
             <input type="button" name="changeLossDateAnswer" value="No" onclick="hideLossDateMessage()"/>
        </div>
    </li>
</ul>
EOF
         }
         $printLossDate = '<input class="date" maxlength="10" id="reportDate" name="reportDate" value="'.$printLossDate.'"/>';
#      }


      ##########################################
      # add ADD button for loss location  here #
      ##########################################

#      my $lossLocationSelect = '';
#      if ($homePolicy eq 'Y')
#      {
#         if (defined($ENGINE->{'locRef'}))
#         {
#            for my $p (@{$ENGINE->{'locRef'}})
#            {
#               $p->{'PropNum'} =~ s/\s*$//;
#               $p->{'PropAdd1'} =~ s/\s*$//;
#               $p->{'PropAdd2'} =~ s/\s*$//;
#               $p->{'City'} =~ s/\s*$//;
#               $p->{'State'} =~ s/\s*$//;
#               $p->{'Zip5'} =~ s/\s*$//;

#               my $value = $p->{'PropNum'};

#               $lossLocationSelect .= $value.'|Location:'.$value.' '.$p->{'PropAdd1'}.' '.$p->{'PropAdd2'}.' '.$p->{'City'}.' '.$p->{'State'}.' '.$p->{'Zip5'}.'|';
#            }
#         }
#      }
#      elsif($liabPolicy eq 'Y')
#      {
#         if (defined($ENGINE->{'locRef'}))
#         {
#            for my $p (@{$ENGINE->{'locRef'}})
#            {
#               $p->{'PropNum'} =~ s/\s*$//;
#               $p->{'PropAdd1'} =~ s/\s*$//;
#               $p->{'PropAdd2'} =~ s/\s*$//;
#               $p->{'City'} =~ s/\s*$//;
#               $p->{'State'} =~ s/\s*$//;
#               $p->{'Zip5'} =~ s/\s*$//;
#               $p->{'Acres'} =~ s/\s*$//;
#               $p->{'Section'} =~ s/\s*$//;
#               $p->{'StreetTwp'} =~ s/\s*$//;
#               $p->{'Range'} =~ s/\s*$//;
#               $p->{'County'} =~ s/\s*$//;
#               $p->{'County'} =~ s/&/&amp;/g;
#               $p->{'County'} =~ s/</&lt;/g;
#               $p->{'County'} =~ s/\n/<br \/>/g;
#               $p->{'County'} =~ s/'/&rsquo;/g;

#               my $value = $p->{'PropNum'};

#               if(defined($p->{'Acres'}) && $p->{'Acres'} gt 0)
#               {
#                  $lossLocationSelect .= $value.'|Location:'.$value.' Acres:'.$p->{'Acres'}.' Section:'.$p->{'Section'}.' Range:'.$p->{'Range'}.' Township:'.$p->{'StreetTwp'}.' County:'.$p->{'County'};
#               }
#               else
#               {
#                  $lossLocationSelect .= $value.'|Location:'.$value.' '.$p->{'PropAdd1'}.' '.$p->{'PropAdd2'}.' '.$p->{'City'};
#               }
#               $lossLocationSelect .= ' '.$p->{'State'}.' '.$p->{'Zip5'}.'|';
#            }
#         }
#      }
#      elsif($businPolicy eq 'Y')
#      {
#         if (defined($ENGINE->{'locRef'}))
#         {
#            for my $p (@{$ENGINE->{'locRef'}})
#            {
#               $p->{'LocNum'} =~ s/\s*$//;
#               $p->{'BldNum'} =~ s/\s*$//;
#               $p->{'PropAdd'} =~ s/\s*$//;
#               $p->{'propDesc'} =~ s/\s*$//;

#               my $value = $p->{'LocNum'}.$p->{'BldNum'};

#               $lossLocationSelect .= $value.'|Location:'.$p->{'LocNum'}.' Building:'.$p->{'BldNum'}.' '.$p->{'PropAdd'}.' '.$p->{'propDesc'}.'|';
#            }
#         }
#      }
#      elsif($glPolicy eq 'Y')
#      {
#         if (defined($ENGINE->{'locRef'}))
#         {
#            for my $p (@{$ENGINE->{'locRef'}})
#            {
#               $p->{'LocNum'} =~ s/\s*$//;
#               $p->{'Subline'} =~ s/\s*$//;
#               if(defined($p->{'PropAdd'}) && $p->{'PropAdd'} gt '')
#               { $p->{'PropAdd'} =~ s/\s*$//; }
#               else
#               { $p->{'PropAdd'} = ' ' }
#               $p->{'Descr'} =~ s/\s*$//;

#               my $value = $p->{'LocNum'}.$p->{'Subline'};

#               $lossLocationSelect .= $value.'|Location:'.$p->{'LocNum'}.' Subline:'.$p->{'Subline'}.' '.$p->{'PropAdd'}.' '.$p->{'Descr'}.'|';
#            }
#         }
#      }
#      elsif($cfPolicy eq 'Y')
#      {
#         if (defined($ENGINE->{'locRef'}))
#         {
#            for my $p (@{$ENGINE->{'locRef'}})
#            {
#               $p->{'locNo'} =~ s/\s*$//;
#               $p->{'bldNum'} =~ s/\s*$//;
#               $p->{'PropAdd'} =~ s/\s*$//;
#               $p->{'City'} =~ s/\s*$//;
#               $p->{'propDesc'} =~ s/\s*$//;

#               my $value = $p->{'locNo'}.$p->{'bldNum'};

#               $lossLocationSelect .= $value.'|Location:'.$p->{'locNo'}.' Building:'.$p->{'bldNum'}.' '.$p->{'PropAdd'}.' '.$p->{'City'}.' '.$p->{'propDesc'}.'|';
#            }
#         }
#      }


#      $lossLocationSelect .= 'OT|Other';
#      $lossLocationButton = '<div id="addLossLocDiv" ><input type="button" value="Add Loss Location" onclick="addLossLoc(\''.$lossLocationSelect.'\')" /></div>';

      ###########################################
      # add ADD button for Policy location here #
      ###########################################




     #now check the claim to see if any of these location/buildings
     #are on it.  If so, put an indicator on the interface hash
     #to show that the location exists so it can be bypassed when
     #building the select box.

      my $displayGLSelect = 'N';

#      if ($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'Internal')
      if (($homePolicy eq '' && $ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} eq 'P') ||
         ($homePolicy eq '' && $ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} ne 'P') ||
         ($homePolicy gt '' && $ENGINE->{'claimGeneral'}->{'MANUAL_OR_WHAT'} eq 'M')
          && $ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'Internal')
      {
         $policyLocationSelect = '';
         my @sortedCommStatErrorResults = ();
         if (scalar(keys %{$ENGINE->{'errors'}}) > 0 && $screenErrors eq 'Y')
         {
            if (defined($ENGINE->{'PropLiab'}->{'CLM_COMMON_STAT'}))
            {
               @sortedCommStatErrorResults = sort({$a->{'CLM_COMMON_STAT_ID'} cmp $b->{'CLM_COMMON_STAT_ID'}} @{$ENGINE->{'PropLiab'}->{'CLM_COMMON_STAT'}});
               for my $cs (@sortedCommStatErrorResults)
               {
                   my $badPolicyLoc = '';
                   my $badOthPolLocLocN = '';
                   my $badOthPolLocUnitN = '';
                   my $badOthPolLocAdd1 = '';
                   my $badOthPolLocCity = '';
                   my $badOthPolLocState = '';
                   my $badOthPolLocZip = '';
                  # Put red arrow by the fields that have an error.
                  for my $pc (@{$ENGINE->{'PropLiab'}->{'CLM_COMMON_STAT'}})
                  {
                      if(defined($pc->{'CLM_COMMON_STAT_ID'}) && $pc->{'CLM_COMMON_STAT_ID'} eq $cs->{'CLM_COMMON_STAT_ID'})
                      {    #die Data::Dumper::Dumper($pc,$ENGINE->{'errors'});
                          if(defined $ENGINE->{'errors'} && defined $ENGINE->{'errors'}->{'polLocList'})
                          { $badPolicyLoc = $ENGINE->{'errors'}->{'polLocList'}[0]; }
                          if(exists $pc->{'errPolLocN'})
                          { $badOthPolLocLocN = $pc->{'errPolLocN'}; }
                          if(exists $pc->{'errPolLocUnitN'})
                          { $badOthPolLocUnitN = $pc->{'errPolLocUnitN'}; }
                          if(defined $ENGINE->{'errors'} && defined $ENGINE->{'errors'}->{'othPolLocZip'})
                          { $badOthPolLocZip = $ENGINE->{'errors'}->{'othPolLocZip'}[0]; }
                          if(defined $ENGINE->{'errors'} && defined $ENGINE->{'errors'}->{'othPolLocAdd1'})
                          { $badOthPolLocAdd1 = $ENGINE->{'errors'}->{'othPolLocAdd1'}[0]; }
                          if(defined $ENGINE->{'errors'} && defined $ENGINE->{'errors'}->{'othPolLocCity'})
                          { $badOthPolLocCity = $ENGINE->{'errors'}->{'othPolLocCity'}[0]; }
                          if(defined $ENGINE->{'errors'} && defined $ENGINE->{'errors'}->{'othPolLocState'})
                          { $badOthPolLocState = $ENGINE->{'errors'}->{'othPolLocState'}[0]; }
                      }
                  }

                   if($cs->{'CLM_COMMON_STAT_ID'} =~ /new/ && $liabPolicy eq 'Y')
                   {
                       my $options = '<select id="polLocList'.$cs->{'CLM_COMMON_STAT_ID'}.'" name="polLocList'.$cs->{'CLM_COMMON_STAT_ID'}.'" onchange="othPolicyLoc(this,\''.$cs->{'CLM_COMMON_STAT_ID'}.'\')"><option value=""></option>';
                       for my $p (@{$ENGINE->{'locRef'}})
                       {

                                       if (defined $p->{'ONCLAIM'}
                                                 &&  $p->{'ONCLAIM'} eq 'Y')
                                      {
                                        #skip this one, its already on the claim.
                                        next;
                                      }

                          $p->{'PropNum'} =~ s/\s*$//;
                          $p->{'PropAdd1'} =~ s/\s*$//;
                          $p->{'PropAdd1'} =~ s/'/&#39;;/g;
                          $p->{'PropAdd1'} =~ s/&/&amp;/g;
                          $p->{'PropAdd1'} =~ s/"//g;
                          $p->{'PropAdd2'} =~ s/\s*$//;
                          $p->{'PropAdd2'} =~ s/'/&#39;;/g;
                          $p->{'PropAdd2'} =~ s/&/&amp;/g;
                          $p->{'PropAdd2'} =~ s/"//g;
                          $p->{'City'} =~ s/\s*$//;
                          $p->{'State'} =~ s/\s*$//;
                          $p->{'Zip5'} =~ s/\s*$//;
                          $p->{'Acres'} =~ s/\s*$//;
                          $p->{'Section'} =~ s/\s*$//;
                          $p->{'StreetTwp'} =~ s/\s*$//;
                          $p->{'Range'} =~ s/\s*$//;
                          $p->{'County'} =~ s/\s*$//;
                          $p->{'County'} =~ s/&/&amp;/g;
                          $p->{'County'} =~ s/</&lt;/g;
                          $p->{'County'} =~ s/\n/<br \/>/g;
                          $p->{'County'} =~ s/'/&rsquo;/g;
                          $p->{'Occupancy'} =~ s/\s*$//;

                          my $value = $p->{'PropNum'};
                          my $selected = '';
                          my $occupancy = '';
                          my $color = '';
                          if(defined($p->{'Occupancy'}) && $p->{'Occupancy'} eq 'P')
                          { $occupancy = 'Residence'; $color = 'style="color:blue"';}
                          if($value eq $cs->{'LOCATION_NO'})
                          { $selected = ' selected="selected"'; }
                          if (defined($p->{'Acres'}) && $p->{'Acres'} gt 0)
                          {
                             $policyLocationSelect = 'Location:'.$value.' Acres:'.$p->{'Acres'}.' Section:'.$p->{'Section'}.' Range:'.$p->{'Range'}.' Township:'.$p->{'StreetTwp'}.' County:'.$p->{'County'};
                          }
                          else
                          {
                             $policyLocationSelect .= 'Location:'.$value.' '.$p->{'PropAdd1'}.' '.$p->{'PropAdd2'}.' '.$p->{'City'};
                          }

                          $policyLocationSelect .= ' '.$p->{'State'}.' '.$p->{'Zip5'}.' '.$occupancy;
                          $options .= '<option '.$color.'value="'.$value.'"'.$selected.'>'.$policyLocationSelect.'</option>';
                       }
                       if($ManualOrWhat ne 'M')
                       {
                           $polLocErrorSelect .= '<li id="polLoc'.$cs->{'CLM_COMMON_STAT_ID'}.'"> <div id="addPolLocDiv'.$cs->{'CLM_COMMON_STAT_ID'}.'" class="nowrap"><label>Policy Location:</label><div><input type="hidden" name="ipolLocID'.$cs->{'CLM_COMMON_STAT_ID'}.'" value="" />';
                           $polLocErrorSelect .= $options.'</select><input type="button" class="delete" value="Remove" onclick="deleteOthPolLoc(\''.$cs->{'CLM_COMMON_STAT_ID'}.'\')" /></div></div></li>'.$badPolicyLoc;
                       }
                       if ($ManualOrWhat eq 'M')
                       {
                           $options .= '<option value="OT" selected="selected">Other</option>';

                           for my $loc (@{$ENGINE->{'PropLiab'}->{'CLM_LOCATION'}})
                           {
                               if (defined($loc->{'CLM_COMMON_STAT_ID'}) && $loc->{'CLM_COMMON_STAT_ID'} eq $cs->{'CLM_COMMON_STAT_ID'})
                               {
                                   $polLocErrorSelect .= '<li id="polLoc'.$cs->{'CLM_COMMON_STAT_ID'}.'"> <div id="addPolLocDiv'.$cs->{'CLM_COMMON_STAT_ID'}.'" class="nowrap"><b>Add Policy Location:</b><div><input type="hidden" name="ipolLocID'.$cs->{'CLM_COMMON_STAT_ID'}.'" value="" />';
                                   $polLocErrorSelect .= $badPolicyLoc.$options.'</select><input type="button" class="delete" value="Remove" onclick="deleteOthPolLoc(\''.$cs->{'CLM_COMMON_STAT_ID'}.'\')" /></div></div></li><br />';
#<div id="othPolicyLoc$cs->{'CLM_COMMON_STAT_ID'}"><b>Other Policy Location Number</b>&nbsp;&nbsp;&nbsp;$badOthPolLocLocN<input  size="5" name="othPolLocLocN$cs->{'CLM_COMMON_STAT_ID'}" id="othPolLocLocN$cs->{'CLM_COMMON_STAT_ID'}" value="$cs->{'LOCATION_NO'}" />
#<b>Other Policy Unit Number</b>&nbsp;&nbsp;&nbsp;$badOthPolLocUnitN<input  size="5" name="othPolLocUnitN$cs->{'CLM_COMMON_STAT_ID'}" id="othPolLocUnitN$cs->{'CLM_COMMON_STAT_ID'}" value="$cs->{'UNIT_NO'}" /><br />
                       $polLocErrorSelect .= <<EOF;
<div id="othPolicyLoc$cs->{'CLM_COMMON_STAT_ID'}"><b>Address1</b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$badOthPolLocAdd1<input  size="30" name="othPolLocAdd1$cs->{'CLM_COMMON_STAT_ID'}" id="othPolLocAdd1$cs->{'CLM_COMMON_STAT_ID'}" value="$loc->{'ADDRESS1'}" /><br />
<b>Address2</b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input  size="30" name="othPolLocAdd2$cs->{'CLM_COMMON_STAT_ID'}" id="othPolLocAdd2$cs->{'CLM_COMMON_STAT_ID'}" value="$loc->{'ADDRESS2'}" /><br />
<b>City</b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$badOthPolLocCity<input  size="30" name="othPolLocCity$cs->{'CLM_COMMON_STAT_ID'}" id="othPolLocCity$cs->{'CLM_COMMON_STAT_ID'}" value="$loc->{'CITY'}" />
&nbsp;&nbsp;<b>State</b>&nbsp;&nbsp;$badOthPolLocState<input  size="2" name="othPolLocState$cs->{'CLM_COMMON_STAT_ID'}" id="othPolLocState$cs->{'CLM_COMMON_STAT_ID'}" value="$loc->{'STATE'}" />
&nbsp;&nbsp;<b>Zip</b>&nbsp;&nbsp;$badOthPolLocZip<input  size="5" name="othPolLocZip$cs->{'CLM_COMMON_STAT_ID'}" id="othPolLocZip$cs->{'CLM_COMMON_STAT_ID'}" value="$cs->{'ZIP_CODE'}" /></div></div>
EOF
                               }
                           }
                       }
                   }
                   elsif($cs->{'CLM_COMMON_STAT_ID'} =~ /new/ && ($businPolicy eq 'Y' || $arPolicy eq 'Y'))
                   {
                       my $options = '<select id="polLocList'.$cs->{'CLM_COMMON_STAT_ID'}.'" name="polLocList'.$cs->{'CLM_COMMON_STAT_ID'}.'" onchange="othPolicyLoc(this,\''.$cs->{'CLM_COMMON_STAT_ID'}.'\')"><option value=""></option>';
                       for my $p (@{$ENGINE->{'locRef'}})
                       {
                                       if (defined $p->{'ONCLAIM'}
                                                 &&  $p->{'ONCLAIM'} eq 'Y')
                                                 {
                                                 #skip this one, its already on the claim.
                                                         next;
                                                 }


                           $p->{'LocNum'} =~ s/\s*$//;
                           $p->{'BldNum'} =~ s/\s*$//;
                           $p->{'PropAdd'} =~ s/\s*$//;
                           $p->{'PropAdd'} =~ s/'/&#39;;/g;
                           $p->{'PropAdd'} =~ s/&/&amp;/g;
                           $p->{'PropAdd'} =~ s/"//g;
                           $p->{'propDesc'} =~ s/\s*$//;
                           $p->{'propDesc'} =~ s/'/&#39;;/g;
                           $p->{'propDesc'} =~ s/&/&amp;/g;

                           # put location and building number in $value so both are sent to the interfac program.
                          my $value = $p->{'LocNum'}.'_'.$p->{'BldNum'};
                          my $screenValue = $cs->{'LOCATION_NO'}.'_'.$cs->{'UNIT_NO'};
                          my $selected = '';
                          if($value eq $screenValue)
                          { $selected = ' selected="selected"'; }
                          if($arPolicy eq 'Y' && $p->{'BldNum'} eq '99')
                          { $policyLocationSelect = 'Class Code:'.' '.$p->{'PropAdd'}.' '.$p->{'propDesc'}; }
                          elsif($arPolicy eq 'Y')
                          { $policyLocationSelect = 'Location:'.$p->{'LocNum'}.$p->{'PropAdd'}.' '.$p->{'propDesc'}; }
                          else
                          { $policyLocationSelect = 'Location:'.$p->{'LocNum'}.' Building:'.$p->{'BldNum'}.' '.$p->{'PropAdd'}.' '.$p->{'propDesc'}; }
                          $options .= '<option value="'.$value.'"'.$selected.'>'.$policyLocationSelect.'</option>';
                       }
                       if($ManualOrWhat ne 'M')
                       {
                           $polLocErrorSelect .= '<li id="polLoc'.$cs->{'CLM_COMMON_STAT_ID'}.'"> <div id="addPolLocDiv'.$cs->{'CLM_COMMON_STAT_ID'}.'" class="nowrap"><label>Add Policy Location:</label><div><input type="hidden" name="ipolLocID'.$cs->{'CLM_COMMON_STAT_ID'}.'" value="" />';
                           $polLocErrorSelect .= $options.'</select><input type="button" class="delete" value="Remove" onclick="deleteOthPolLoc(\''.$cs->{'CLM_COMMON_STAT_ID'}.'\')" /></div></div></li>'.$badPolicyLoc;
                       }
                       if ($ManualOrWhat eq 'M')
                       {
                           $options .= '<option value="OT" selected="selected">Other</option>';
                           for my $loc (@{$ENGINE->{'PropLiab'}->{'CLM_LOCATION'}})
                           {
                               if (defined($loc->{'CLM_COMMON_STAT_ID'}) && $loc->{'CLM_COMMON_STAT_ID'} eq $cs->{'CLM_COMMON_STAT_ID'})
                               {
                                   $polLocErrorSelect .= '<li id="polLoc'.$cs->{'CLM_COMMON_STAT_ID'}.'"> <div id="addPolLocDiv'.$cs->{'CLM_COMMON_STAT_ID'}.'" class="nowrap"><b>Add Policy Location:</b><div><input type="hidden" name="ipolLocID'.$cs->{'CLM_COMMON_STAT_ID'}.'" value="" />';
                                   $polLocErrorSelect .= $badPolicyLoc.$options.'</select><input type="button" class="delete" value="Remove" onclick="deleteOthPolLoc(\''.$cs->{'CLM_COMMON_STAT_ID'}.'\')" /></div></div></li><br />';
#<div id="othPolicyLoc$cs->{'CLM_COMMON_STAT_ID'}"><b>Other Policy Location Number</b>&nbsp;&nbsp;&nbsp;$badOthPolLocLocN<input  size="5" name="othPolLocLocN$cs->{'CLM_COMMON_STAT_ID'}" id="othPolLocLocN$cs->{'CLM_COMMON_STAT_ID'}" value="$cs->{'LOCATION_NO'}" />
#<b>Other Policy Unit Number</b>&nbsp;&nbsp;&nbsp;$badOthPolLocUnitN<input  size="5" name="othPolLocUnitN$cs->{'CLM_COMMON_STAT_ID'}" id="othPolLocUnitN$cs->{'CLM_COMMON_STAT_ID'}" value="$cs->{'UNIT_NO'}" /><br />
                       $polLocErrorSelect .= <<EOF;
<div id="othPolicyLoc$cs->{'CLM_COMMON_STAT_ID'}"><b>Address1</b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$badOthPolLocAdd1<input  size="30" name="othPolLocAdd1$cs->{'CLM_COMMON_STAT_ID'}" id="othPolLocAdd1$cs->{'CLM_COMMON_STAT_ID'}" value="$loc->{'ADDRESS1'}" /><br />
<b>Address2</b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input  size="30" name="othPolLocAdd2$cs->{'CLM_COMMON_STAT_ID'}" id="othPolLocAdd2$cs->{'CLM_COMMON_STAT_ID'}" value="$loc->{'ADDRESS2'}" /><br />
<b>City</b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$badOthPolLocCity<input  size="30" name="othPolLocCity$cs->{'CLM_COMMON_STAT_ID'}" id="othPolLocCity$cs->{'CLM_COMMON_STAT_ID'}" value="$loc->{'CITY'}" />
&nbsp;&nbsp;<b>State</b>&nbsp;&nbsp;$badOthPolLocState<input  size="2" name="othPolLocState$cs->{'CLM_COMMON_STAT_ID'}" id="othPolLocState$cs->{'CLM_COMMON_STAT_ID'}" value="$loc->{'STATE'}" />
&nbsp;&nbsp;<b>Zip</b>&nbsp;&nbsp;$badOthPolLocZip<input  size="5" name="othPolLocZip$cs->{'CLM_COMMON_STAT_ID'}" id="othPolLocZip$cs->{'CLM_COMMON_STAT_ID'}" value="$cs->{'ZIP_CODE'}" /></div></div>
EOF
                               }
                           }
                       }
                   }
                   elsif($cs->{'CLM_COMMON_STAT_ID'} =~ /new/
                           && ($glPolicy eq 'Y' || $imPolicy eq 'Y' || $ucPolicy eq 'Y' || $upPolicy eq 'Y'))
                   {
                       my $options = '<select id="polLocList'.$cs->{'CLM_COMMON_STAT_ID'}.'" name="polLocList'.$cs->{'CLM_COMMON_STAT_ID'}.'" onchange="othPolicyLoc(this,\''.$cs->{'CLM_COMMON_STAT_ID'}.'\')"><option value=""></option>';
                       for my $p (@{$ENGINE->{'locRef'}})
                       {

                                                        if (defined $p->{'ONCLAIM'}
                                                         &&  $p->{'ONCLAIM'} eq 'Y')
                                  {
                                    #skip this one, its already on the claim.
                                    next;
                                  }

                          $p->{'LocNum'} =~ s/\s*$//;
                          $p->{'BldNum'} =~ s/\s*$//;
                          #$p->{'Subline'} =~ s/\s*$//;
                          if(defined($p->{'PropAdd'}) && $p->{'PropAdd'} gt '')
                          {
                             $p->{'PropAdd'} =~ s/\s*$//;
                             $p->{'PropAdd'} =~ s/'/&#39;;/g;
                             $p->{'PropAdd'} =~ s/&/&amp;/g;
                             $p->{'PropAdd'} =~ s/"//g;
                          }
                          else
                          { $p->{'PropAdd'} = ' ' }
                          if (defined $p->{'Descr'}
                                  && $p->{'Descr'} gt '')
                          {
                                  $p->{'Descr'} =~ s/\s*$//;
                                  $p->{'Descr'} =~ s/'/&#39;;/g;
                            $p->{'Descr'} =~ s/&/&amp;/g;
                          }
                          else
                          {
                                   $p->{'Descr'} = '';
                          }

                          my $value = $p->{'LocNum'}.'_'.$p->{'BldNum'};
                          my $screenValue = $cs->{'LOCATION_NO'}.'_'.$cs->{'UNIT_NO'};
                          my $selected = '';
                          if($value eq $screenValue)
                          { $selected = ' selected="selected"'; }
                          if(defined($p->{'BldNum'}) && $p->{'BldNum'} eq '0000' && $glPolicy eq 'Y')
                          { $policyLocationSelect = $p->{'PropAdd'}.' '.$p->{'Descr'}; }
                          elsif($glPolicy eq 'Y' && $p->{'BldNum'} =~ /0098|0099/)
                          { $policyLocationSelect = 'Class Code:'.' '.$p->{'PropAdd'}.' '.$p->{'Descr'}; }
                          else
                          {
#                              $policyLocationSelect = 'Location:'.$p->{'LocNum'}.' Subline:'.$p->{'Subline'}.' '.$p->{'PropAdd'}.' '.$p->{'Descr'};
                              $policyLocationSelect = 'Location:'.substr($p->{'LocNum'},2,2).' Unit:'.$p->{'BldNum'}.' '.$p->{'PropAdd'}.' '.$p->{'Descr'};
                          }

                          $options .= '<option value="'.$value.'"'.$selected.'>'.$policyLocationSelect.'</option>';
                       }
                       if($ManualOrWhat ne 'M')
                       {
                           $polLocErrorSelect .= '<li id="polLoc'.$cs->{'CLM_COMMON_STAT_ID'}.'"> <div id="addPolLocDiv'.$cs->{'CLM_COMMON_STAT_ID'}.'" class="nowrap"><label>Policy Location:</label><div><input type="hidden" name="ipolLocID'.$cs->{'CLM_COMMON_STAT_ID'}.'" value="" />';
                           $polLocErrorSelect .= $options.'</select><input type="button" class="delete" value="Remove" onclick="deleteOthPolLoc(\''.$cs->{'CLM_COMMON_STAT_ID'}.'\')" /></div></div></li>'.$badPolicyLoc;
                       }
                       if ($ManualOrWhat eq 'M')
                       {
                           $options .= '<option value="OT" selected="selected">Other</option>';

                           for my $loc (@{$ENGINE->{'PropLiab'}->{'CLM_LOCATION'}})
                           {
                               if (defined($loc->{'CLM_COMMON_STAT_ID'}) && $loc->{'CLM_COMMON_STAT_ID'} eq $cs->{'CLM_COMMON_STAT_ID'})
                               {
                                    my $city_disabled = 'disabled="disabled"';
                                    if($loc->{'CITY'})
                                    {$city_disabled = '';}
#                                   $polLocErrorSelect .= '<li id="polLoc'.$cs->{'CLM_COMMON_STAT_ID'}.'"><label>Add Policy Location10</label><div><input type="hidden" name="ipolLocID'.$cs->{'CLM_COMMON_STAT_ID'}.'" value="" />';
#                                   $polLocErrorSelect .= $badPolicyLoc.$options.'</select><input type="button" class="delete" value="Remove" onclick="deleteOthPolLoc(\''.$cs->{'CLM_COMMON_STAT_ID'}.'\')" /></div><br />';
#<div id="othPolicyLoc$cs->{'CLM_COMMON_STAT_ID'}"><b>Other Policy Location Number</b>&nbsp;&nbsp;&nbsp;$badOthPolLocLocN<input  size="5" name="othPolLocLocN$cs->{'CLM_COMMON_STAT_ID'}" id="othPolLocLocN$cs->{'CLM_COMMON_STAT_ID'}" value="$cs->{'LOCATION_NO'}" />
#<b>Other Policy Unit Number</b>&nbsp;&nbsp;&nbsp;$badOthPolLocUnitN<input  size="5" name="othPolLocUnitN$cs->{'CLM_COMMON_STAT_ID'}" id="othPolLocUnitN$cs->{'CLM_COMMON_STAT_ID'}" value="$cs->{'UNIT_NO'}" /><br />
                       $polLocErrorSelect .= <<EOF;
<li id="polLoc$cs->{'CLM_COMMON_STAT_ID'}">
    <label>Add Policy Location</label>
    <div>
        <input type="hidden" name="ipolLocID$cs->{'CLM_COMMON_STAT_ID'}" value="" />
        $options
        </select>
        $badPolicyLoc
        <input type="button" class="delete" value="Remove" onclick="deleteOthPolLoc(\'$cs->{'CLM_COMMON_STAT_ID'}')" />
        <ul class="toplabel_onecol" id="othPolicyLoc$cs->{'CLM_COMMON_STAT_ID'}">
            <li>
                <label>Address1:</label>
                <div><input size="30" name="othPolLocAdd1$cs->{'CLM_COMMON_STAT_ID'}" id="othPolLocAdd1$cs->{'CLM_COMMON_STAT_ID'}" value="$loc->{'ADDRESS1'}" /></div>
            </li>
            $badOthPolLocAdd1
            <li>
                <label>Address2:</label>
                <div><input  size="30" name="othPolLocAdd2$cs->{'CLM_COMMON_STAT_ID'}" id="othPolLocAdd2$cs->{'CLM_COMMON_STAT_ID'}" value="$loc->{'ADDRESS2'}" /></div>
            </li>
            <li>
                <label>City:</label>
                <div>
                    <input $city_disabled type="text" size="30" name="othPolLocCity$cs->{'CLM_COMMON_STAT_ID'}displayed" id="othPolLocCity$cs->{'CLM_COMMON_STAT_ID'}displayed" value="$loc->{'CITY'}" />
                    <input type="hidden" name="othPolLocCity$cs->{'CLM_COMMON_STAT_ID'}" id="othPolLocCity$cs->{'CLM_COMMON_STAT_ID'}" value="$loc->{'CITY'}" />
                </div>
            </li>
            $badOthPolLocCity
            <li>
                <label>State:</label>
                <div>
                    <input disabled="disabled" type="text" size="2" name="othPolLocState$cs->{'CLM_COMMON_STAT_ID'}displayed" id="othPolLocState$cs->{'CLM_COMMON_STAT_ID'}displayed" value="$loc->{'STATE'}" />
                    <input type="hidden" name="othPolLocState$cs->{'CLM_COMMON_STAT_ID'}" id="othPolLocState$cs->{'CLM_COMMON_STAT_ID'}" value="$loc->{'STATE'}" />
                </div>
            </li>
            $badOthPolLocState
            <li>
                <label>Zip:</label>
                <div>
                    <input size="5" name="othPolLocZip$cs->{'CLM_COMMON_STAT_ID'}" id="othPolLocZip$cs->{'CLM_COMMON_STAT_ID'}" value="$cs->{'ZIP_CODE'}" onchange="ajaxZipRequest('othPolLocZip$cs->{'CLM_COMMON_STAT_ID'}','othPolLocCity$cs->{'CLM_COMMON_STAT_ID'}','othPolLocState$cs->{'CLM_COMMON_STAT_ID'}','$cs->{'CLM_COMMON_STAT_ID'}')" />
                </div>
            </li>
            $badOthPolLocZip
        </ul>
    </div>
</li>
EOF
                               }
                           }
                       }
                   }
                   elsif($cs->{'CLM_COMMON_STAT_ID'} =~ /new/ && ($cfPolicy eq 'Y' || $homePolicy eq 'Y'))
                   {
                       my $options = '<select id="polLocList'.$cs->{'CLM_COMMON_STAT_ID'}.'" name="polLocList'.$cs->{'CLM_COMMON_STAT_ID'}.'" onchange="othPolicyLoc(this,\''.$cs->{'CLM_COMMON_STAT_ID'}.'\')"><option value=""></option>';
                       for my $p (@{$ENGINE->{'locRef'}})
                       {
                                                   if (defined $p->{'ONCLAIM'}
                                                 &&  $p->{'ONCLAIM'} eq 'Y')
                                         {
                                                 #skip this one, its already on the claim.
                                                 next;
                                         }

                           $p->{'locNo'} =~ s/\s*$//;
                           $p->{'bldNum'} =~ s/\s*$//;
                           $p->{'PropAdd'} =~ s/\s*$//;
                           $p->{'PropAdd'} =~ s/'/&#39;;/g;
                           $p->{'PropAdd'} =~ s/&/&amp;/g;
                           $p->{'PropAdd'} =~ s/"//g;
                           $p->{'City'} =~ s/\s*$//;
                           $p->{'propDesc'} =~ s/\s*$//;
                           $p->{'propDesc'} =~ s/'/&#39;;/g;
                           $p->{'propDesc'} =~ s/&/&amp;/g;

                          my $value = $p->{'locNo'}.'_'.$p->{'bldNum'};
                          my $screenValue = $cs->{'LOCATION_NO'}.'_'.$cs->{'UNIT_NO'};
                          my $selected = '';
                          if($value eq $screenValue)
                          { $selected = ' selected="selected"'; }
                          $policyLocationSelect = 'Location:'.$p->{'locNo'}.' Building:'.$p->{'bldNum'}.' '.$p->{'PropAdd'}.' '.$p->{'City'}.' '.$p->{'propDesc'};
                          $options .= '<option value="'.$value.'"'.$selected.'>'.$policyLocationSelect.'</option>';
                       }
                       if($ManualOrWhat ne 'M')
                       {
                           $polLocErrorSelect .= '<li id="polLoc'.$cs->{'CLM_COMMON_STAT_ID'}.'"> <div id="addPolLocDiv'.$cs->{'CLM_COMMON_STAT_ID'}.'" class="nowrap"><label>Policy Location:</label><div><input type="hidden" name="ipolLocID'.$cs->{'CLM_COMMON_STAT_ID'}.'" value="" />';
                           $polLocErrorSelect .= $options.'</select><input type="button" class="delete" value="Remove" onclick="deleteOthPolLoc(\''.$cs->{'CLM_COMMON_STAT_ID'}.'\')" /></div></div></li>'.$badPolicyLoc;
                       }
                       if ($ManualOrWhat eq 'M')
                       {
                           $options .= '<option value="OT" selected="selected">Other</option>';
                           for my $loc (@{$ENGINE->{'PropLiab'}->{'CLM_LOCATION'}})
                           {
                               if (defined($loc->{'CLM_COMMON_STAT_ID'}) && $loc->{'CLM_COMMON_STAT_ID'} eq $cs->{'CLM_COMMON_STAT_ID'})
                               {
                                   $polLocErrorSelect .= '<li id="polLoc'.$cs->{'CLM_COMMON_STAT_ID'}.'"> <div id="addPolLocDiv'.$cs->{'CLM_COMMON_STAT_ID'}.'" class="nowrap"><b>Add Policy Location:</b><div><input type="hidden" name="ipolLocID'.$cs->{'CLM_COMMON_STAT_ID'}.'" value="" />';
                                   $polLocErrorSelect .= $badPolicyLoc.$options.'</select><input type="button" class="delete" value="Remove" onclick="deleteOthPolLoc(\''.$cs->{'CLM_COMMON_STAT_ID'}.'\')" /></div></div></li><br />';
#<div id="othPolicyLoc$cs->{'CLM_COMMON_STAT_ID'}"><b>Other Policy Location Number</b>&nbsp;&nbsp;&nbsp;$badOthPolLocLocN<input  size="5" name="othPolLocLocN$cs->{'CLM_COMMON_STAT_ID'}" id="othPolLocLocN$cs->{'CLM_COMMON_STAT_ID'}" value="$cs->{'LOCATION_NO'}" />
#<b>Other Policy Unit Number</b>&nbsp;&nbsp;&nbsp;$badOthPolLocUnitN<input  size="5" name="othPolLocUnitN$cs->{'CLM_COMMON_STAT_ID'}" id="othPolLocUnitN$cs->{'CLM_COMMON_STAT_ID'}" value="$cs->{'UNIT_NO'}" /><br />
                       $polLocErrorSelect .= <<EOF;
<div id="othPolicyLoc$cs->{'CLM_COMMON_STAT_ID'}"><b>Address1</b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$badOthPolLocAdd1<input  size="30" name="othPolLocAdd1$cs->{'CLM_COMMON_STAT_ID'}" id="othPolLocAdd1$cs->{'CLM_COMMON_STAT_ID'}" value="$loc->{'ADDRESS1'}" /><br />
<b>Address2</b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input  size="30" name="othPolLocAdd2$cs->{'CLM_COMMON_STAT_ID'}" id="othPolLocAdd2$cs->{'CLM_COMMON_STAT_ID'}" value="$loc->{'ADDRESS2'}" /><br />
<b>City</b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$badOthPolLocCity<input  size="30" name="othPolLocCity$cs->{'CLM_COMMON_STAT_ID'}" id="othPolLocCity$cs->{'CLM_COMMON_STAT_ID'}" value="$loc->{'CITY'}" />
&nbsp;&nbsp;<b>State</b>&nbsp;&nbsp;$badOthPolLocState<input  size="2" name="othPolLocState$cs->{'CLM_COMMON_STAT_ID'}" id="othPolLocState$cs->{'CLM_COMMON_STAT_ID'}" value="$loc->{'STATE'}" />
&nbsp;&nbsp;<b>Zip</b>&nbsp;&nbsp;$badOthPolLocZip<input  size="5" name="othPolLocZip$cs->{'CLM_COMMON_STAT_ID'}" id="othPolLocZip$cs->{'CLM_COMMON_STAT_ID'}" value="$cs->{'ZIP_CODE'}" /></div></div>
EOF
                               }
                           }
                       }
                   }
                   elsif($cs->{'CLM_COMMON_STAT_ID'} =~ /new/ && ($dpPolicy eq 'Y'))
                   {
                       my $options = '<select id="polLocList'.$cs->{'CLM_COMMON_STAT_ID'}.'" name="polLocList'.$cs->{'CLM_COMMON_STAT_ID'}.'" onchange="othPolicyLoc(this,\''.$cs->{'CLM_COMMON_STAT_ID'}.'\')"><option value=""></option>';
                       for my $p (@{$ENGINE->{'locRef'}})
                       {
                                                   if (defined $p->{'ONCLAIM'}
                                                 &&  $p->{'ONCLAIM'} eq 'Y')
                                         {
                                                 #skip this one, its already on the claim.
                                                 next;
                                         }
                           $p->{'PropNum'} =~ s/\s*$//;
                           $p->{'PropAdd1'} =~ s/\s*$//;
                               $p->{'PropAdd1'} =~ s/'/&#39;;/g;
                               $p->{'PropAdd1'} =~ s/&/&amp;/g;
                               $p->{'PropAdd1'} =~ s/"//g;
                           $p->{'PropAdd2'} =~ s/\s*$//;
                               $p->{'PropAdd2'} =~ s/'/&#39;;/g;
                               $p->{'PropAdd2'} =~ s/&/&amp;/g;
                               $p->{'PropAdd2'} =~ s/"//g;
                           $p->{'City'} =~ s/\s*$//;
                           $p->{'State'} =~ s/\s*$//;
                           $p->{'Zip5'} =~ s/\s*$//;

                          #my $value = $p->{'locNo'}.$p->{'bldNum'};
                          my $value = $p->{'PropNum'};
                          #my $screenValue = $cs->{'LOCATION_NO'}.$cs->{'UNIT_NO'};
                          my $screenValue = $cs->{'LOCATION_NO'};
                          my $selected = '';
                          if($value eq $screenValue)
                          { $selected = ' selected="selected"'; }
                          $policyLocationSelect = 'Location:'.$p->{'PropNum'}.' '.$p->{'PropAdd1'}.' '.$p->{'PropAdd2'}.' '.$p->{'City'}.' '.$p->{'State'}.' '.$p->{'Zip5'};
                          $options .= '<option value="'.$value.'"'.$selected.'>'.$policyLocationSelect.'</option>';
                       }
                       if($ManualOrWhat ne 'M')
                       {
                           $polLocErrorSelect .= '<li id="polLoc'.$cs->{'CLM_COMMON_STAT_ID'}.'"> <div id="addPolLocDiv'.$cs->{'CLM_COMMON_STAT_ID'}.'" class="nowrap"><label>Policy Location:</label><div><input type="hidden" name="ipolLocID'.$cs->{'CLM_COMMON_STAT_ID'}.'" value="" />';
                           $polLocErrorSelect .= $options.'</select><input type="button" class="delete" value="Remove" onclick="deleteOthPolLoc(\''.$cs->{'CLM_COMMON_STAT_ID'}.'\')" /></div></div></li>'.$badPolicyLoc;
                       }
                       if ($ManualOrWhat eq 'M')
                       {
                           $options .= '<option value="OT" selected="selected">Other</option>';
                           for my $loc (@{$ENGINE->{'PropLiab'}->{'CLM_LOCATION'}})
                           {
                               if (defined($loc->{'CLM_COMMON_STAT_ID'}) && $loc->{'CLM_COMMON_STAT_ID'} eq $cs->{'CLM_COMMON_STAT_ID'})
                               {
                                   $polLocErrorSelect .= '<li id="polLoc'.$cs->{'CLM_COMMON_STAT_ID'}.'"> <div id="addPolLocDiv'.$cs->{'CLM_COMMON_STAT_ID'}.'" class="nowrap"><b>Add Policy Location:</b><div><input type="hidden" name="ipolLocID'.$cs->{'CLM_COMMON_STAT_ID'}.'" value="" />';
                                   $polLocErrorSelect .= $badPolicyLoc.$options.'</select><input type="button" class="delete" value="Remove" onclick="deleteOthPolLoc(\''.$cs->{'CLM_COMMON_STAT_ID'}.'\')" /></div></div></li><br />';
                       $polLocErrorSelect .= <<EOF;
<div id="othPolicyLoc$cs->{'CLM_COMMON_STAT_ID'}"><b>Address1</b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$badOthPolLocAdd1<input  size="30" name="othPolLocAdd1$cs->{'CLM_COMMON_STAT_ID'}" id="othPolLocAdd1$cs->{'CLM_COMMON_STAT_ID'}" value="$loc->{'ADDRESS1'}" /><br />
<b>Address2</b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input  size="30" name="othPolLocAdd2$cs->{'CLM_COMMON_STAT_ID'}" id="othPolLocAdd2$cs->{'CLM_COMMON_STAT_ID'}" value="$loc->{'ADDRESS2'}" /><br />
<b>City</b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$badOthPolLocCity<input  size="30" name="othPolLocCity$cs->{'CLM_COMMON_STAT_ID'}" id="othPolLocCity$cs->{'CLM_COMMON_STAT_ID'}" value="$loc->{'CITY'}" />
&nbsp;&nbsp;<b>State</b>&nbsp;&nbsp;$badOthPolLocState<input  size="2" name="othPolLocState$cs->{'CLM_COMMON_STAT_ID'}" id="othPolLocState$cs->{'CLM_COMMON_STAT_ID'}" value="$loc->{'STATE'}" />
&nbsp;&nbsp;<b>Zip</b>&nbsp;&nbsp;$badOthPolLocZip<input  size="5" name="othPolLocZip$cs->{'CLM_COMMON_STAT_ID'}" id="othPolLocZip$cs->{'CLM_COMMON_STAT_ID'}" value="$cs->{'ZIP_CODE'}" /></div></div>
EOF
                               }
                           }
                       }
                   }

               }
            }
         }
#         if ($homePolicy eq 'Y')
#         {
#            if (defined($ENGINE->{'locRef'}))
#            {
#               for my $p (@{$ENGINE->{'locRef'}})
#               {
#                  $p->{'PropNum'} =~ s/\s*$//;
#                  $p->{'PropAdd1'} =~ s/\s*$//;
#                  $p->{'PropAdd2'} =~ s/\s*$//;
#                  $p->{'City'} =~ s/\s*$//;
#                  $p->{'State'} =~ s/\s*$//;
#                  $p->{'Zip5'} =~ s/\s*$//;

#                  my $value = $p->{'PropNum'};

#                  $policyLocationSelect .= $value.'|Location:'.$value.' '.$p->{'PropAdd1'}.' '.$p->{'PropAdd2'}.' '.$p->{'City'}.' '.$p->{'State'}.' '.$p->{'Zip5'}.'|';
#               }
#            }
#         }
         if($liabPolicy eq 'Y')
         {
            if (defined($ENGINE->{'locRef'}))
            {
               for my $p (@{$ENGINE->{'locRef'}})
               {
                         if (defined $p->{'ONCLAIM'}
                                 &&  $p->{'ONCLAIM'} eq 'Y')
                         {
                                 #skip this one, its already on the claim.
                                 next;
                         }

                  $p->{'PropNum'} =~ s/\s*$//;
                  $p->{'PropAdd1'} =~ s/\s*$//;
                  $p->{'PropAdd1'} =~ s/'/&#39;;/g;
                  $p->{'PropAdd1'} =~ s/&/&amp;/g;
                  $p->{'PropAdd1'} =~ s/"//g;
                  $p->{'PropAdd2'} =~ s/\s*$//;
                  $p->{'PropAdd2'} =~ s/'/&#39;;/g;
                  $p->{'PropAdd2'} =~ s/&/&amp;/g;
                  $p->{'PropAdd2'} =~ s/"//g;
                  $p->{'City'} =~ s/\s*$//;
                  $p->{'State'} =~ s/\s*$//;
                  $p->{'Zip5'} =~ s/\s*$//;
                  $p->{'Acres'} =~ s/\s*$//;
                  $p->{'Section'} =~ s/\s*$//;
                  $p->{'StreetTwp'} =~ s/\s*$//;
                  $p->{'StreetTwp'} =~ s/'/&#39;;/g;
                  $p->{'StreetTwp'} =~ s/&/&amp;/g;
                  $p->{'Range'} =~ s/\s*$//;
                  $p->{'County'} =~ s/\s*$//;
                  $p->{'County'} =~ s/&/&amp;/g;
                  $p->{'County'} =~ s/</&lt;/g;
                  $p->{'County'} =~ s/\n/<br \/>/g;
                  $p->{'County'} =~ s/'/&rsquo;/g;
                  $p->{'Occupancy'} =~ s/\s*$//;

                  my $value = $p->{'PropNum'};

                  my $occupancy = '';
                  if(defined($p->{'Occupancy'}) && $p->{'Occupancy'} eq 'P')
                  { $occupancy = 'Residence'; }

                  if (defined($p->{'Acres'}) && $p->{'Acres'} gt 0)
                  {
                     $policyLocationSelect .= $value.'|Location:'.$value.' Acres:'.$p->{'Acres'}.' Section:'.$p->{'Section'}.' Range:'.$p->{'Range'}.' Township:'.$p->{'StreetTwp'}.' County:'.$p->{'County'};
                  }
                  else
                  {
                     $policyLocationSelect .= $value.'|Location:'.$value.' '.$p->{'PropAdd1'}.' '.$p->{'PropAdd2'}.' '.$p->{'City'};
                  }

                  $policyLocationSelect .= ' '.$p->{'State'}.' '.$p->{'Zip5'}.' '.$occupancy.'|';
               }
            }
         }
         elsif ($businPolicy eq 'Y' || $arPolicy eq 'Y' || $imPolicy eq 'Y')
         {
            if (defined($ENGINE->{'locRef'}))
            {
               for my $p (@{$ENGINE->{'locRef'}})
               {

                         if (defined $p->{'ONCLAIM'}
                                 &&  $p->{'ONCLAIM'} eq 'Y')
                         {
                                 #skip this one, its already on the claim.
                                 next;
                         }


                  $p->{'LocNum'} =~ s/\s*$//;
                  $p->{'BldNum'} =~ s/\s*$//;
                  $p->{'PropAdd'} =~ s/\s*$//;
                  $p->{'PropAdd'} =~ s/'/&#39;;/g;
                  $p->{'PropAdd'} =~ s/&/&amp;/g;
                  $p->{'PropAdd'} =~ s/"//g;
                  $p->{'propDesc'} =~ s/\s*$//;
                  $p->{'propDesc'} =~ s/'/&#39;;/g;
                  $p->{'propDesc'} =~ s/&/&amp;/g;

                  # put location and building number in $value so both are sent to the interfac program.
                  my $value = $p->{'LocNum'}.'_'.$p->{'BldNum'};

                  if($arPolicy eq 'Y' && $p->{'BldNum'} eq '99')
                  { $policyLocationSelect .= $value.'|Class Code:'.' '.$p->{'PropAdd'}.' '.$p->{'propDesc'}.'|'; }
                  elsif($arPolicy eq 'Y' && $value gt '')
                  { $policyLocationSelect .= $value.'|Location:'.$p->{'LocNum'}.$p->{'PropAdd'}.' '.$p->{'propDesc'}.'|'; }
                  elsif($businPolicy eq 'Y' || $imPolicy eq 'Y')
                  { $policyLocationSelect .= $value.'|Location:'.$p->{'LocNum'}.' Building:'.$p->{'BldNum'}.' '.$p->{'PropAdd'}.' '.$p->{'propDesc'}.'|'; }
               }
            }
         }
         elsif($glPolicy eq 'Y')
         {
            if (defined($ENGINE->{'locRef'}))
            {
               for my $p (@{$ENGINE->{'locRef'}})
               {
                  #print '<br/>buildling select box with: ' . $p->{'LocNum'};
                  #print '<br/> and building#: ' . $p->{'BldNum'};
                         if (defined $p->{'ONCLAIM'}
                                 &&  $p->{'ONCLAIM'} eq 'Y')
                         {  #print '<br/>should be skipping one!';
                                 #skip this one, its already on the claim.
                                 next;
                         }

                  $p->{'LocNum'} =~ s/\s*$//;
                  #$p->{'Subline'} =~ s/\s*$//;
                  $p->{'BldNum'} =~ s/\s*$//;
                  if(defined($p->{'PropAdd'}) && $p->{'PropAdd'} gt '')
                  {
                      $p->{'PropAdd'} =~ s/\s*$//;
                      $p->{'PropAdd'} =~ s/'/&#39;;/g;
                      $p->{'PropAdd'} =~ s/&/&amp;/g;
                      $p->{'PropAdd'} =~ s/"//g;
                  }
                  else
                  { $p->{'PropAdd'} = ' ' }
                  $p->{'Descr'} =~ s/\s*$//;
                  $p->{'Descr'} =~ s/'/&#39;;/g;
                  $p->{'Descr'} =~ s/&/&amp;/g;

                  if(defined($p->{'Acres'}) && $p->{'Acres'} gt '')
                  {
                          $p->{'State'} =~ s/\s*$//;
                          $p->{'Zip5'} =~ s/\s*$//;
                          $p->{'Acres'} =~ s/\s*$//;
                          $p->{'Section'} =~ s/\s*$//;
                          $p->{'StreetTwp'} =~ s/\s*$//;
                          $p->{'StreetTwp'} =~ s/'/&#39;;/g;
                          $p->{'StreetTwp'} =~ s/&/&amp;/g;
                          $p->{'Range'} =~ s/\s*$//;
                      $p->{'County'} =~ s/\s*$//;
                      $p->{'County'} =~ s/&/&amp;/g;
                      $p->{'County'} =~ s/</&lt;/g;
                      $p->{'County'} =~ s/\n/<br \/>/g;
                      $p->{'County'} =~ s/'/&rsquo;/g;
                  }

                  #my $value = $p->{'LocNum'}.$p->{'Subline'};
                  my $value = $p->{'LocNum'}.'_'.$p->{'BldNum'};

                  if(defined($p->{'BldNum'}) && $p->{'BldNum'} eq '0000')
                  { $policyLocationSelect .= $value.'|'.$p->{'PropAdd'}.' '.$p->{'Descr'}.'|'; }
                  elsif (defined($p->{'Acres'}) && $p->{'Acres'} gt '')
                  {
                     $policyLocationSelect .= $value.'|Section:'.$p->{'Section'}.' Range:'.$p->{'Range'}.' Township:'.$p->{'StreetTwp'}.' County:'.$p->{'County'}.' State:'.$p->{'State'}.'|';
                  }
                  elsif($p->{'BldNum'} =~ /0098|0099/)
                  { $policyLocationSelect .= $value.'|Class Code:'.' '.$p->{'PropAdd'}.' '.$p->{'Descr'}.'|'; }
                  else
                  #$policyLocationSelect .= $value.'|Location:'.$p->{'LocNum'}.' Subline:'.$p->{'Subline'}.' '.$p->{'PropAdd'}.' '.$p->{'Descr'}.'|';
                  { $policyLocationSelect .= $value.'|Location:'.substr($p->{'LocNum'},2,2).' Unit:'.$p->{'BldNum'}.' '.$p->{'PropAdd'}.' '.$p->{'Descr'}.'|'; }

                  if($displayGLSelect eq 'N' && $p->{'LocNum'} ne '0000' && $p->{'BldNum'} ne '0000')
                  { $displayGLSelect = 'Y'; }
#                  else
#                  { $displayGLSelect = 'N'; }
               }
            }
         }
         elsif($cfPolicy eq 'Y')
         {
            if (defined($ENGINE->{'locRef'}))
            {
               for my $p (@{$ENGINE->{'locRef'}})
               {

                         if (defined $p->{'ONCLAIM'}
                                 &&  $p->{'ONCLAIM'} eq 'Y')
                         {
                                 #skip this one, its already on the claim.
                                 next;
                         }

                  $p->{'locNo'} =~ s/\s*$//;
                  $p->{'bldNum'} =~ s/\s*$//;
                  $p->{'PropAdd'} =~ s/\s*$//;
                  $p->{'PropAdd'} =~ s/'/&#39;;/g;
                  $p->{'PropAdd'} =~ s/&/&amp;/g;
                  $p->{'PropAdd'} =~ s/"//g;
                  $p->{'City'} =~ s/\s*$//;
                  $p->{'propDesc'} =~ s/\s*$//;
                  $p->{'propDesc'} =~ s/'/&#39;;/g;
                  $p->{'propDesc'} =~ s/&/&amp;/g;

                  my $value = $p->{'locNo'}.'_'.$p->{'bldNum'};

                  $policyLocationSelect .= $value.'|Location:'.$p->{'locNo'}.' Building:'.$p->{'bldNum'}.' '.$p->{'PropAdd'}.' '.$p->{'City'}.' '.$p->{'propDesc'}.'|';
               }
            }
         }
         elsif($dpPolicy eq 'Y')
         {
            if (defined($ENGINE->{'locRef'}))
            {
               for my $p (@{$ENGINE->{'locRef'}})
               {

                         if (defined $p->{'ONCLAIM'}
                                 &&  $p->{'ONCLAIM'} eq 'Y')
                         {
                                 #skip this one, its already on the claim.
                                 next;
                         }

                        $p->{'PropNum'} =~ s/\s*$//;
                        $p->{'PropAdd1'} =~ s/\s*$//;
                        $p->{'PropAdd1'} =~ s/'/&#39;;/g;
                        $p->{'PropAdd1'} =~ s/&/&amp;/g;
                        $p->{'PropAdd1'} =~ s/"//g;
                        $p->{'PropAdd2'} =~ s/\s*$//;
                        $p->{'PropAdd2'} =~ s/'/&#39;;/g;
                        $p->{'PropAdd2'} =~ s/&/&amp;/g;
                        $p->{'PropAdd2'} =~ s/"//g;
                        $p->{'City'} =~ s/\s*$//;
                        $p->{'State'} =~ s/\s*$//;
                        $p->{'Zip5'} =~ s/\s*$//;

                  #my $value = $p->{'locNo'}.$p->{'bldNum'};
                  my $value = $p->{'PropNum'};

                  $policyLocationSelect .= $value.'|Location:'.$p->{'PropNum'}.' '.$p->{'PropAdd1'}.' '.$p->{'PropAdd2'}.' '.$p->{'City'}.' '.$p->{'State'}.' '.$p->{'Zip5'}.'|';

#                          #my $value = $p->{'locNo'}.$p->{'bldNum'};
#                          my $value = $p->{'locNo'};
#                          #my $screenValue = $cs->{'LOCATION_NO'}.$cs->{'UNIT_NO'};
#                          #my $screenValue = $cs->{'LOCATION_NO'};
#                          #my $selected = '';
#                          #if($value eq $screenValue)
#                          #{ $selected = ' selected="selected"'; }
#                          $policyLocationSelect = 'Location:'.$p->{'locNo'}.' '.$p->{'PropAdd1'}.' '.$p->{'PropAdd2'}.' '.$p->{'City'}.' '.$p->{'State'}.' '.$p->{'Zip5'};
#                          $options .= '<option value="'.$value.'"'.$selected.'>'.$policyLocationSelect.'</option>';





               }
            }
         }
         if ($ManualOrWhat eq 'M')
         {
            $policyLocationSelect .= 'OT|Other';
         }
         else
         {
            chop($policyLocationSelect);
         }

         if($LineCode !~ /303|550/
                 && $policyLocationSelect gt '')
         {
             my $loc_err = '';
             if(defined($ENGINE->{'errors'}->{'policyLocation'}))
             {$loc_err = $ENGINE->{'errors'}->{'policyLocation'}[0];}
             $policyLocationButton = '<li id="addPolLocDiv"><input type="button" class="add" value="Add Policy Location" onclick="addPolLoc(\''.$policyLocationSelect.'\')" /></li>'.$loc_err;
         }
#         if($LineCode =~ /550/ || ($glPolicy eq 'Y' && $displayGLSelect eq 'N'))
#         {

#         }
#         else
#         {
#             $policyLocationButton = '<div id="addPolLocDiv"> <input type="button" value="Add Policy Location" onclick="addPolLoc(\''.$policyLocationSelect.'\')" /></div>';
#         }

      }
   }

   my $detailsHeading = 'Details';
   if($ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} eq 'P')
   { $detailsHeading = 'Claim Submission'; }


           #code to automatically click the Add Policy Location button on load of screen
        my $clickloc = '';
        if ($ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'}
                && $ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} =~ /A|P|M/
                && !$ENGINE->{'READONLY'}
                && $totalLocations == 0
                && $screenErrors eq 'N'
                && $homePolicy eq ''
                && $LineCode !~ /303|550/)
        {
        $policyLocationSelect =~ s/&amp;/&/g;
             $clickloc = '<script type="text/javascript">
                                addPolLoc(\''.$policyLocationSelect.'\');
                                        </script>';
        }

   my $datehelp = '';
   if ( $ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'Internal')
        {
      $datehelp = '"The loss date may be changed. If you change the loss date, the policy and driver information will be lost along with any entries not previously saved.  Policy information will be recalculated for the new date chosen.  Once this claim is reported to the bureaus at the end of the month you will not be allowed to change the loss date."';
        }
   else
        {
      $datehelp = '"You may change the loss date prior to submitting the claim. If you change the loss date, the policy and driver information will be lost along with any entries not previously saved. Policy information will be recalculated for the new date chosen."';
        }

   my $loss_date = Claims_Misc::createLabelHelp({
                            'label' =>  $agentRedAst.' Loss Date: ',
                            'help'  =>  $datehelp,
                            'data'  =>  $claimLossDate.$loss_date_attn,
                            'id'    =>  'loss_date_help',
                        });
   my $type_of_loss = Claims_Misc::createLabelHelp({
                            'label' =>  $agentRedAst.' '.$claimType.':',
                            'help'  =>  'Select the type of loss sustained.  This is a required field.',
                            'data'  =>  $propLiab,
                            'id'    =>  'loss_type_help',
                            'error' =>  $badClaimType,
                        });
   my $loss_reported_date = Claims_Misc::createLabelHelp({
                            'label' =>  $agentRedAst.' Date Reported to Agent: ',
                            'help'  =>  'Enter the date the loss was reported to the agent. The system will enter the current date by default.  This is a required field.',
                            'data'  =>  $printLossDate,
                            'id'    =>  'report_date_help',
                        });

   my $loss_details_error = '';
   if(defined($ENGINE->{'errors'}->{'lossDescription'}))
   {$loss_details_error = $ENGINE->{'errors'}->{'lossDescription'}[0];}

    my $loss_desc_help_words = 'Enter the details of the loss.  The first entry should be a brief description with complete details entered using the -View Details- button.  This is a required field.';
    my $loss_details_agent = 0;
    my $loss_desc_details_help_words = '';
    if(defined $ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'}
                   && $ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} eq 'P'
                   && !$ENGINE->{'READONLY'} && $ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'Agent')
    {
        $loss_desc_help_words = 'Enter a brief description of the loss';
        $loss_details_agent = 1;
        $showLossDetail = '';
        $moreDetailsButton = <<EOF;
<a class="showhide" onclick="toggle('detailText');$switchFunction" name="showDetButton" id="showDetButton">Hide Details</a>
EOF

        $loss_desc_details_help_words = <<EOF;
<div class="helptext">
    Enter more loss details if available
</div>
EOF
    }

   my $loss_details = Claims_Misc::createLabelHelp({
                            'label' =>  $agentRedAst.' Loss Description: ',
                            'help'  =>  $loss_desc_help_words,
                            'data'  =>  $lossDesc.' '.$moreDetailsButton,
                            'id'    =>  'loss_desc_help',
                            'help_expand' => $loss_details_agent,
                        });

   my $loss_city_err = $ENGINE->{'errors'}->{'lossCity'}[0] || '';
   my $loss_state_err = $ENGINE->{'errors'}->{'lossState'}[0] || '';
   my $loss_county_err = $ENGINE->{'errors'}->{'lossCounty'}[0] || '';
   my $loss_zip_err = $ENGINE->{'errors'}->{'lossZip'}[0] || '';
   my $loss_city = '';
   my $loss_county = '';
   my $loss_zip = '';
   my $loss_state_loc = '';
   if($loss_city_err eq '')
   {
       $loss_city = $assignmentResultsCL->[0]->{'ACCIDENT_CITY'} || $ENGINE->{'CGI'}->param('lossCity') || '';
   }
   else
   {
       $loss_city = $ENGINE->{'CGI'}->param('lossCity') || '';
   }
   if($loss_zip_err eq '')
   {
       $loss_zip = $assignmentResultsCL->[0]->{'ACCIDENT_ZIP'} || $ENGINE->{'CGI'}->param('lossZip') || '';
       $loss_state_loc = $assignmentResultsCL->[0]->{'ACCIDENT_STATE'} || $ENGINE->{'CGI'}->param('lossState') || '';
       $loss_county = $assignmentResultsCL->[0]->{'ACCIDENT_COUNTY'} || $ENGINE->{'CGI'}->param('lossCounty')|| '';
   }
   else
   {
       $loss_zip = $ENGINE->{'CGI'}->param('lossZip') || '';
       $loss_state_loc = $ENGINE->{'CGI'}->param('lossState') || '';
       $loss_county = $ENGINE->{'CGI'}->param('lossCounty') || '';
   }
   my $disabled = 'disabled="disabled"';
   if($loss_city ne '')
   {$disabled = '';}

   my $loss_state_data = '';
   if($ENGINE->{'READONLY'})
   {
   $loss_state_data = <<EOF;
<ul class="leftlabel_twocol">
        <li>
           <label>City:</label>
           <div>
               $loss_city
           </div>
        </li>
$loss_city_err
        <li>
            <label>State:</label>
            <div>
                $loss_state_loc
            </div>
        </li>
$loss_state_err
         <li>
            <label>County:</label>
            <div>
                $loss_county
            </div>
        </li>
$loss_county_err
        <li>
            <label>ZIP:</label>
            <div>
                $loss_zip
           </div>
        </li>
$loss_zip_err
    </ul>
EOF
   }
   else
   {
       $loss_state_data = <<EOF;
<ul class="leftlabel_twocol">
        <li>
           <label>City:</label>
           <div>
               <input $disabled size="30" maxlength="30" value="$loss_city" name="lossCitydisplayed" id="lossCitydisplayed" onchange="updateCity(\'lossCity\')"/>
               <input type="hidden" value="$loss_city" name="lossCity" id="lossCity"/>
           </div>
        </li>
$loss_city_err
        <li>
            <label>State:</label>
            <div>
                <input disabled="disabled" size="5" maxlength="2" value="$loss_state_loc" name="lossStatedisplayed" id="lossStatedisplayed"/>
                <input type="hidden" value="$loss_state_loc" name="lossState" id="lossState"/>
            </div>
        </li>
$loss_state_err
         <li>
            <label>County:</label>
            <div>
                <input disabled="disabled" size="30" maxlength="30" value="$loss_county" name="lossCountydisplayed" id="lossCountydisplayed"/>
                <input type="hidden" value="$loss_county" name="lossCounty" id="lossCounty"/>
            </div>
        </li>
$loss_county_err
        <li>
            <label>ZIP:</label>
            <div>
                <input size="7" maxlength="5" value="$loss_zip" name="lossZip" id="lossZip" onchange="ajaxZipRequest('lossZip','lossCity','lossState','','lossCounty')"/>
           </div>
        </li>
$loss_zip_err
    </ul>
EOF
   }

   my $loss_city_state_zip = '<li>';
   $loss_city_state_zip .= Claims_Misc::createLabelHelp({
       'label'    => $agentRedAst . ' City, State, County and ZIP Where Loss Occurred: ',
       'help'     => 'Enter City, State, County and ZIP where accident/loss occurred. City, State, and County will prefill when ZIP is entered. This is a required field.',
       'data'     => $loss_state_data,
       'id'       => 'loss_state_help',
       'div_info' => 'class="pad_div"',
                        });
   $loss_city_state_zip .= '</li>';
   my $loss_loc_error = '';
   if(defined($ENGINE->{'errors'}->{'locationOfLoss'}))
   {$loss_loc_error = $ENGINE->{'errors'}->{'locationOfLoss'}[0];}
   elsif(defined($ENGINE->{'errors'}->{'lossLocation'}))
   {$loss_loc_error = $ENGINE->{'errors'}->{'lossLocation'}[0];}
   elsif(defined($ENGINE->{'errors'}->{''}) &&
   ($ENGINE->{'errors'}->{''}[0] =~ /Loss Location/))
   {$loss_loc_error = $ENGINE->{'errors'}->{''}[0]}
    my $loss_location = Claims_Misc::createLabelHelp({
                            'label' =>  $lossLocationHeading.':',
                            'help'  =>  $lochelp,
                            'data'  =>  $lossLocationHTML,
                            'id'    =>  'loss_loc_help',
                        });

    my $witness = Claims_Misc::createLabelHelp({
                            'label' =>  'Witnesses:',
                            'help'  =>  'If any witnesses are available for the claim, enter the contact information you have obtained.  You may list as many witnesses as needed by clicking the Add Witness button.',
                            'data'  =>  $witnesses,
                            'id'    =>  'witness_help',
                        });
#    die Data::Dumper::Dumper($loss_location,$loss_loc_error,$ENGINE->{'errors'}->{''});
    my $loss_loc_style = '';
    if(!$lossLocationButton){
        $loss_loc_style = 'style="display:none"';
    }
    my $prop_loc_style = '';
    if(!$propertyLocationHTML){
        $prop_loc_style = 'style="display:none"';
    }
    my $prop_stat_style = '';
    if(!$propertyLocationStat){
        $prop_stat_style = 'style="display:none"';
    }
    my $polLocError_style = '';
    if(!$polLocErrorSelect && !$policyLocationButton){
        $polLocError_style = 'style="display:none"';
    }

    my $xactAnalysis_obj = create_XactObj({ Engine => $ENGINE });
    my $show_button = show_XactAnalysis({ Engine => $ENGINE });
    my $dateXactSent = check_button_pressed({ Engine => $ENGINE });

    my $json = encode_json($xactAnalysis_obj);
    use HTML::Entities qw( encode_entities );
    my $encode_entity = encode_entities($json);
    my $party_id = $xactAnalysis_obj->[0]->{'Party_id'};

my $dump = <<EOF;
<div class="subsection">
                 <h4>Loss Details</h4>
                    <ul class="leftlabel_twocol">
EOF
                if($show_button)
                {
                    if (scalar $dateXactSent)
                    {
                        $dump .= <<EOF;
                    <li><input type="button" id="sendXactAnalysis$party_id" value="Sent to XactAnalysis on $dateXactSent" disabled/></li>
EOF
                    }
                    else
                    {
                        $dump .= <<EOF;
                    <li><input type="button" id="sendXactAnalysis$party_id" class="Submit" value="Send to XactAnalysis" onclick="sendXactAnalysis('$encode_entity','$party_id')"/></li>
EOF
                    }
                }
                    $dump .= <<EOF;
                       <li>
                            $loss_date
                            <input type="hidden" value="" name="lossDateChange" id="lossDateChange"/>
                       </li>
                       $badLossDate
                       <li>
                            $type_of_loss
                       </li>
                       $badClaimType
                       <li>
                            $loss_reported_date
                       </li>
                       $badReportDate
                       <li>
                            $loss_details
                       </li>
                       $loss_details_error
                       <li id="detailText" style="$showLossDetail">
                            <label>Loss Description Details:</label>
                            $loss_desc_details_help_words
                            <div>$lossDetail</div>
                        </li>
                        $loss_desc_err

                        $lossLocationButton
                        $propertyLocationHTML
                        <li $prop_stat_style>
                          $propertyLocationStat
                        </li>
                        $polLocErrorSelect
                        $policyLocationButton
                        <li>
                            $loss_city_state_zip
                        </li>
                        <li>
                            $loss_location
                        </li>
                        $loss_loc_error
                        <li id="assingHide1" style=$assignmentStyle>
                            $sevDamageHTML
                        </li>
                        $badSeverityDamage
                        <li id="assingHide2" style=$assignmentStyle>
                            $scheduledItemHTML
                        </li>
                        $badScheduledItem
                        <li>
                            $suitFiledHTML
                        </li>
                        $badSuitFiled
                    </ul>
           </div><!--/subsection--><!--/loss details-->
EOF

#die Data::Dumper::Dumper($dump);

    if(defined($ENGINE->{'errors'}->{''}))
    {
        my $found_loss_loc_err = 0;
        my $other_screen_errs = '<fieldset><h2 class="flags">Other Screen Errors</h2><div class="subsection"><ul class="leftlabel_twocol">';
        for my $err (@{$ENGINE->{'errors'}->{''}})
        {
            if(!($ENGINE->{'errors'}->{''}[0] =~ /Loss Location/))
            {
                $other_screen_errs .= $err;
            }
            else
            {$found_loss_loc_err = 1;}
        }
        $other_screen_errs .= '</ul></div></fieldset>';

        my $size = @{$ENGINE->{'errors'}->{''}};
        if($size >= 2 )
        {
            $ENGINE->{'output'} .= $other_screen_errs;
        }

#        die Data::Dumper::Dumper($size,$ENGINE->{'errors'}->{''});
    }

    my $last_save = $ENGINE->{'claimGeneral'}->{'LAST_SAVE'};

    my $totalLossSection = '';
    if(defined $ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'}
                   && $ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} ne 'P' && $LineCode =~ /300|301|302/)
    {
        $totalLossSection = <<HTML;
    <div class="subsection"><!--Total Loss Vehicle-->
        <h4>Total Loss Vehicle Reporting</h4>
        <ul class="leftlabel_twocol">
        $totalLossVehicle
        </ul>
    </div><!--/subsection--><!--/Total Loss Vehicle-->
HTML
    }

    my $homeCoverages = '';
    if($ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} eq 'P' && $LineCode =~ /112|113|120/)
    {
        $homeCoverages .= '<ul class="leftlabel_twocol" id="assignHide3" style='.$assigCovStyle.'>';
        $homeCoverages .= '<li>';
        $homeCoverages .= $assignCovHTML;
        $homeCoverages .= '</li></ul>';
    }
    else
    {
        $assignCovHTML = '';
    }

   $ENGINE->{'output'} .=  <<HTML;
<div class="row">
    <fieldset>
        <script type="text/javascript">
           \$(function() {
               \$("#lossDate").datepicker(DATE_PICKER_OPTIONS);
               \$("#reportDate").datepicker(DATE_PICKER_OPTIONS);
           });
        </script>
        <h2>$detailsHeading</h2>
        <form name="mainform" id="mainform" action="$action" method="post" accept-charset="UTF-8">
            <!--loss details-->
            <div class="subsection">
                 <h4>Loss Details</h4>
                    <ul class="leftlabel_twocol">
HTML
                if($show_button)
                {
                    if (scalar $dateXactSent)
                    {
                        $ENGINE->{'output'} .= <<HTML;
                    <li><input type="button" id="sendXactAnalysis$party_id" value="Sent to XactAnalysis on $dateXactSent" disabled/></li>
HTML
                    }
                    else
                    {
                        $ENGINE->{'output'} .= <<HTML;
                    <li><input type="button" id="sendXactAnalysis$party_id" class="Submit" value="Send to XactAnalysis" onclick="sendXactAnalysis('$encode_entity','$party_id')"/></li>
HTML
                    }
                }
                        $ENGINE->{'output'} .=  <<HTML;
                       <li>
                            $loss_date
                            <input type="hidden" value="" name="lossDateChange" id="lossDateChange"/>
                       </li>
                       $badLossDate
                       <li>
                            $type_of_loss
                       </li>
                       $badClaimType
                       <li>
                            $stormHTML
                       </li>
                       $badClaimStorm
                        $homeCoverages
                       <li>
                            $loss_reported_date
                       </li>
                       $badReportDate
                       <li>
                            $loss_details
                       </li>
                       $loss_details_error
                       <li id="detailText" style="$showLossDetail">
                            <label>Loss Description Details:</label>
                            $loss_desc_details_help_words
                            <div>$lossDetail</div>
                        </li>
                        $loss_desc_err

                        $lossLocationButton
                        $propertyLocationHTML
                        <li $prop_stat_style>
                          $propertyLocationStat
                        </li>
                        $polLocErrorSelect
                        $policyLocationButton
                        <li>
                            $loss_city_state_zip
                        </li>
                        <li>
                            $loss_location
                        </li>
                        $loss_loc_error
                        <li id="assignHide1" style=$assignmentStyle>
                            $sevDamageHTML
                        </li>
                        $badSeverityDamage
                        <li id="assignHide2" style=$assignmentStyle>
                            $scheduledItemHTML
                        </li>
                        $badScheduledItem
                        <li>
                            $suitFiledHTML
                        </li>
                        $badSuitFiled
                    </ul>
           </div><!--/subsection--><!--/loss details-->
           $totalLossSection
           <!--insureds and claimants-->
           <div class="subsection" id="insureds_claimants">
              <!--<h4><span class="help" onclick="help(this.title);" title="List all individuals or businesses involved in the claim.  At least one of the names must be a named insured.  Auto losses:  Even if the driver is not a named insured, a named insured must be added as a Party Involved.  Please add all other parties involved in the loss by selecting Add Party Involved.">Insureds and Claimants</span></h4>-->
              <h4>
              <span>Insureds and Claimants</span>
              <a class="help" onclick="toggleHelp(\'insured_claimants_help\')">&nbsp;&nbsp;</a>
              </h4>
              <div class="helptext hidden" id="insured_claimants_help" style="margin-left:1em">
                     List all individuals or businesses involved in the claim.  At least one of the names must be a named insured.  Auto losses:  Even if the driver is not a named insured, a named insured must be added as a Party Involved.  Please add all other parties involved in the loss by selecting Add Party Involved.
              </div>
              <ul>
              $missPropImj
              </ul>
              $partiesData
           </div> <!--/insureds and claimants--><!--/subsection-->
           <div class="subsection"><!--additional information-->
               <h4>Additional Information</h4>
               <!--witnesses-->
               <ul class="leftlabel_twocol">
                      <li>$witness</li>
                   </ul>
                   $witnessData
               <!--authorities-->
               <ul class="leftlabel_twocol">
                   $authorities
           </div><!--/additional information--><!--/subsection-->

           <!--product information-->
           $manufacturerData
           <!--/product information-->

           <!--other information-->
           <div class="subsection">
               <h4>Other Information</h4>
                  <ul class="leftlabel_twocol">
                      $otherInfoData1
                      $otherInfoDataA
                      $otherInfoData2
                  </ul>
           </div><!--/other information--><!--/subsection-->
           <div style="display:none">
               <input type="hidden" value="$sessID" name="sessionID"/>
               <input type="hidden" value="Claims_PropLiab" name="save" id="save"/>
               <input type="hidden" value="Claims_PropLiab" name="load" id="load"/>
               <input type="hidden" value="" name="transactionCode" id="transactionCode"/>
               <input type="hidden" value="$claimid" name="claimid"/>
               <input type="hidden" value="$last_save" name="last_save" id="last_save"/>
               <input type="hidden" value="$ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'}" name ="screenStatus" id="screenStatus" />
               <input type="hidden" name="claimExists" id="claimExists" value="N" />
               <input type="hidden" name="isManual" id="isManual" value="$ENGINE->{'claimGeneral'}->{'MANUAL_OR_WHAT'}" />
               <input type="hidden" value="$LineCode" name="lineCode" id="lineCode"/>
               <input type="hidden" value="$focusSet" name="focusSet" id="focusSet"/>
               <input type="hidden" value="0" name="whatToDo" id="whatToDo"/>
               <input type="hidden" value="$ENGINE->{'claimGeneral'}->{'SYSTEM_IND'}" name="systemInd" id="systemInd"/>
               <input type="hidden" value="$assignHide3IDs" name="assignLocIDs" id="assignLocIDs" />
           </div>
        </form>
    </fieldset>
</div><!-- /.row -->
HTML

    my $propfooter = '<div class="row">' . getFooter($ENGINE) . '</div><!-- /.row -->';

   $ENGINE->{'output'} .=  <<EOF;
$propfooter
</div>
</body>
$clickloc
</html>
EOF
#die Data::Dumper::Dumper($ENGINE->{'output'});
}

sub buildCoverage
{
   my $ENGINE = shift;
   my $p = shift;
   my $claimid = shift;
   my $showCauseOfLoss = shift;
   my $coverageIn = shift;
   my $lossGroup = shift;
   my $lossCode = shift;
   my $causeOfLoss = shift;
   my $assignCovHTML = shift;
   my $holdCNT = shift;
   my $assignmentID = shift;
   my $badassignCov = shift;
   my $badassignTypeOfLoss = shift;
   my $badassignCauseOfLoss = shift;
   my $stormType = shift;


   my $error = $ENGINE->{'error'};

   my $addCovBut = '';
   my $agentRedAst = '';
   if (defined $ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'}
                   && $ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} eq 'P'
                   && !$ENGINE->{'READONLY'})
   {
            $agentRedAst = '<span style="color:red">*</span>';
   }

   my $LossCodeSelect = 'LossCodeSelect' . $p->{'CLM_COMMON_STAT_ID'}.$holdCNT;
   my $covsList = '';
   my $losscodeList = '';
   my $errArrow = '';
   my $COLSelect = 'COLSelect' . $p->{'CLM_COMMON_STAT_ID'}.$holdCNT;
   my $COLcodeList = '';

   if($coverageIn eq '')
   { $coverageIn = 0; }

   my $imtCoverage = '';
   my $coverageQuery = $ENGINE->{'DBH'}->prepare(
       "SELECT COVERAGE
FROM CLAIMDB.CLM_COVS_ENDORSES
WHERE COVERAGE_ID = ?") || $error->($ENGINE, 'CLM_COVS_ENDORSE query prepare failed: ' . $ENGINE->{'DBH'}->errstr);

   $coverageQuery->execute($coverageIn) || $error->($ENGINE, 'CLM_COVS_ENDORSE query execute failed: ' . $ENGINE->{'DBH'}->errstr);

   my $coverageResults = $coverageQuery->fetchall_arrayref({});
   my $selectedCoverage = $coverageResults->[0]->{'COVERAGE'};

   $covsList = GetCoveragesList($ENGINE,
       $claimid,
       $p->{'CLM_COMMON_STAT_ID'}.$holdCNT,
       $p->{'LOCATION_NO'},
       $p->{'UNIT_NO'},
       $lossGroup,
       $coverageIn,
       $ENGINE->{'CGI'}->param('whatToDo') || '',
       $stormType);

   my $validLossGroupQuery = $ENGINE->{'DBH'}->prepare(
       "SELECT IMT_COV_CODE, IMT_COVERAGE, IMT_DESCRIPT, VALID_LOSSCODE_GROUP
FROM GENSUPDB.COVERAGES
WHERE COMPANY_CODE = ?
AND IMT_LINE_CODE = ?
AND STATE = ?
AND IMT_COVERAGE = ?
AND OBSOLETE_DATE = '9999-01-01'") || $error->($ENGINE, 'Coverages query prepare failed: ' . $ENGINE->{'DBH'}->errstr);

   $validLossGroupQuery->execute($ENGINE->{'claimGeneral'}->{'COMPANY_NO'}, $ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'}, $ENGINE->{'claimGeneral'}->{'POLICY_STATE'}, $selectedCoverage) || $error->($ENGINE, 'Coverage query execute failed: ' . $ENGINE->{'DBH'}->errstr);

   my $validLossGroupResults = $validLossGroupQuery->fetchall_arrayref({});

   my %hmCovlist;
   $hmCovlist{'XX_XX'} = 'SELECT COVERAGE';
   my $workKey = '';
   my $validLossGrp = '0';
   my $cov_code = '';
   for my $v (@$validLossGroupResults) {
       $workKey = '0_' . $v->{VALID_LOSSCODE_GROUP} . '_' . $v->{IMT_COV_CODE};
       $hmCovlist{$workKey} = $v->{IMT_DESCRIPT};
       $validLossGrp = $v->{VALID_LOSSCODE_GROUP};
       $cov_code = $v->{IMT_COV_CODE};
   }

   my $coverage = '0_' . $validLossGrp . '_' . $coverageIn;

   my $validCauseLossGroupQuery = $ENGINE->{'DBH'}->prepare(
       "SELECT CAUSE_OF_LOSS_GROUP
FROM GENSUPDB.VALID_LOSSCODES
WHERE VALID_LOSSCODE_GROUP = ?
AND LOSS_CODE = ?") || $error->($ENGINE, 'Assignment fields query prepare failed: ' . $ENGINE->{'DBH'}->errstr);

   $validCauseLossGroupQuery->execute($validLossGrp, $lossCode) || $error->($ENGINE, 'Assignment fields query execute failed: ' . $ENGINE->{'DBH'}->errstr);

   my $validCauseLossGroupResults = $validCauseLossGroupQuery->fetchall_arrayref({});
   my $valid_cause_of_loss_group = $validCauseLossGroupResults->[0]->{'CAUSE_OF_LOSS_GROUP'} || '';

   my $covGroup = 'CoverageGroup' . $p->{'CLM_COMMON_STAT_ID'}.$holdCNT;
   my $ResCovTab = 'ResCovTab' . $p->{'CLM_COMMON_STAT_ID'}.$holdCNT;
   my $delCov = 'delCov' . $p->{'CLM_COMMON_STAT_ID'}.$holdCNT;
   my $addCov = 'addCov' . $p->{'CLM_COMMON_STAT_ID'}.$holdCNT;
   my $resSelected = 'resSelected' . $p->{'CLM_COMMON_STAT_ID'}.$holdCNT;
   my $assignment = 'assignmentID' . $p->{'CLM_COMMON_STAT_ID'}.$holdCNT;
   if (defined($coverageIn) && $coverageIn gt '') {
       my $workResType = 'subro';
       my $covId = '0';
       $losscodeList = GetLosscodeList($ENGINE,
           $p->{'CLM_COMMON_STAT_ID'}.$holdCNT,
           $ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'},
           $lossCode,
           $validLossGrp,
           $covId,
           $valid_cause_of_loss_group,
           $ENGINE->{'claimGeneral'}->{'SYSTEM_IND'},
           $cov_code,
           $workResType,
           $stormType);

       if ($showCauseOfLoss) {
           $COLcodeList = GetCauseOfLossList($ENGINE,
               $p->{'CLM_COMMON_STAT_ID'}.$holdCNT,
               $ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'},
               $valid_cause_of_loss_group,
               $causeOfLoss,
               $stormType);
       }
   }

   my $assignCoverage = '';
   $assignCoverage = $covsList;
   $assignCovHTML .= '<li name="' . $covGroup . '" id="' . $covGroup . '" >'
       . '<ul name="' . $ResCovTab . '"  id="' . $ResCovTab . '"  class="leftlabel_twocol">   ';
   $assignCovHTML .= '<li>';
   $assignCovHTML .= Claims_Misc::createLabelHelp({
       'label' => $agentRedAst . ' Select Coverage ',
       'help'  => 'What type of coverage for the policy?  This is a required field.',
       'data'  => $assignCoverage,
       'id'    => 'coverage_help',
   });
   $assignCovHTML .= $badassignCov;
   $assignCovHTML .= '<li></li>';
   $assignCovHTML .= '<input type="hidden" value="subro" name="' . $resSelected . '" id="' . $resSelected . '"/>';
   $assignCovHTML .= '<input type="hidden" value="'.$assignmentID.'" name="' . $assignment . '" id="' . $assignment . '"/>';
   $assignCovHTML .= '<input type="button" class="delete" name="' . $delCov . '" id="' . $delCov . '" style="display:none" class="delete" value="Delete Coverage" onclick="delNewCov(this);"/>';
   if ($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /112|113|120/) {
       $assignCovHTML .= '<input type="hidden" name="policyLocIDCS' . $p->{'CLM_COMMON_STAT_ID'} . '" id="policyLocIDCS' . $p->{'CLM_COMMON_STAT_ID'} . '" value="' . $p->{'CLM_COMMON_STAT_ID'} . '"/>';
   }
   $assignCovHTML .= '</li>';

   $assignCovHTML .=
       '<li><label><span> '.$agentRedAst.'Type of Loss </span> <a class="help" onclick="toggleHelp(\'loss_code_help\')">&nbsp;&nbsp;</a></label>'
           . '<div class="helptext hidden" id="loss_code_help"> What is the type of loss for this policy?  This is a required field. </div>'
           . '<div name="' . $LossCodeSelect . '" id="' . $LossCodeSelect . '" >' . $losscodeList . '</div>'
           . '</li>';
   $assignCovHTML .= $badassignTypeOfLoss;
   $assignCovHTML .= '<li></li>';
   #we only show cause of loss for certain lines of business.
   if ($showCauseOfLoss) {
       $assignCovHTML .=
           '<li><label><span> '.$agentRedAst.'Cause of Loss </span> <a class="help" onclick="toggleHelp(\'cause_loss_help\')">&nbsp;&nbsp;</a></label>'
               . '<div class="helptext hidden" id="cause_loss_help"> What is the primary cause of loss for this claim?  This is a required field. </div>'
               . '<div name="' . $COLSelect . '" id="' . $COLSelect . '" >' . $COLcodeList . '</div>'
               . '</li>';
       $assignCovHTML .= $badassignCauseOfLoss;
       $assignCovHTML .= '<li></li>';
   }
   $assignCovHTML .= '</ul></li>';

    return($assignCovHTML);
}

sub saveScreen
{
   my $ENGINE = shift;
    if($ENGINE->{'READONLY'})
      { return; }

   my $error = $ENGINE->{'error'};

   my $sessID = $ENGINE->{'SESSION'}->{'sessionID'};
   my $action = $ENGINE->{'ACTION'};

   my $name = $ENGINE->{'AUTH'}->{'name'};
   my $claimid = $ENGINE->{'claimGeneral'}->{'CLAIM_ID'};
   my $LineCode = $ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'};
   my $UserType = $ENGINE->{'AUTH'}->{'IMTOnline_UserType'};
   my $ManualOrWhat = $ENGINE->{'claimGeneral'}->{'MANUAL_OR_WHAT'};
   my $userID = $ENGINE->{'AUTH'}->{'user_key'};

   my $errInd = '';
   my $progErr = '';

   if (editScreen($ENGINE))
   {
      # SET DELETE DATE ON CLM_VARDATA LOSSSTOTTH RECORD IF ACCIDENT STATE IS NO LONGER 'OTHER'
      for my $v (@{$ENGINE->{'PropLiab'}->{'CLM_VARDATA'}})
      {
         if ($ENGINE->{'PropLiab'}->{'CLM_GENERAL'}->[0]->{'ACCIDENT_STATE'} ne 'OT')
         {
            if (defined($v->{'DATA_TYPE'}) && $v->{'DATA_TYPE'} eq 'LOSSSTOTH')
            {
               $v->{'DELETE'} = 1;
            }
         }
      }

      VARDATAsave($ENGINE,$ENGINE->{'PropLiab'}->{'CLM_VARDATA'},$ENGINE->{'PropLiab'}->{'OLD_CLM_VARDATA'});


      #################
      #  CLM_GENERAL  #
      #################
      #If this is a submitted claim we need to see if this is a storm or not.  So we can move the unassigned claim
      #to the correct unassigned tab in Tracker.  110525
      my $unassignedStormClaim = '';
      my $unassignedClaim = '';
      if ($ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} =~ /P|M/)
      {
              my $notificationQuery = $ENGINE->{'DBH'}->prepare(
                 "SELECT NOTIFICATION_ID
                     FROM CLAIMDB.CLM_NOTIFICATION
                   WHERE CLAIM_ID = ? AND TYPE IN ('C','M') AND MESSAGE_ID = '1' AND COMPLETION_DATE IS NULL") || $error->($ENGINE);

              $notificationQuery->execute($claimid) || $error->($ENGINE);

              my $notificationResults = $notificationQuery->fetchall_arrayref({});

              my $NTFHasBasketUpdate = $ENGINE->{'DBH'}->prepare(
                 'UPDATE CLAIMDB.CLM_NTF_HAS_BASKET
                     SET BASKET_ID = ?
                   WHERE NOTIFICATION_ID = ?') || $error->($ENGINE);

          if(defined($notificationResults->[0]->{'NOTIFICATION_ID'}))
          {
                  if((defined $ENGINE->{'PropLiab'}->{'CLM_GENERAL'}->[0]->{'CLM_STORM_ID'})
                     && $ENGINE->{'PropLiab'}->{'CLM_GENERAL'}->[0]->{'CLM_STORM_ID'} ne 'NULL')
                  {
                      if($ENGINE->{'claimGeneral'}->{'BRANCH'} eq '20')
                      { $unassignedStormClaim = UNASSIGNED_DM_STORM_BASKET_ID; }
                      elsif($ENGINE->{'claimGeneral'}->{'BRANCH'} eq '50')
                      { $unassignedStormClaim = UNASSIGNED_SF_STORM_BASKET_ID; }
                      elsif($ENGINE->{'claimGeneral'}->{'BRANCH'} eq '60')
                      { $unassignedStormClaim = UNASSIGNED_SC_STORM_BASKET_ID; }
                      $NTFHasBasketUpdate->execute($unassignedStormClaim,$notificationResults->[0]->{'NOTIFICATION_ID'}) || $error->($ENGINE);
                  }
                  elsif((defined $ENGINE->{'PropLiab'}->{'CLM_GENERAL'}->[0]->{'CLM_STORM_ID'})
                     && $ENGINE->{'PropLiab'}->{'CLM_GENERAL'}->[0]->{'CLM_STORM_ID'} eq 'NULL')
                  {
                      if($ENGINE->{'claimGeneral'}->{'BRANCH'} eq '20')
                      { $unassignedClaim = UNASSIGNED_DM_BASKET_ID; }
                      elsif($ENGINE->{'claimGeneral'}->{'BRANCH'} eq '50')
                      { $unassignedClaim = UNASSIGNED_SF_BASKET_ID; }
                      elsif($ENGINE->{'claimGeneral'}->{'BRANCH'} eq '60')
                      { $unassignedClaim = UNASSIGNED_SC_BASKET_ID; }
                      $NTFHasBasketUpdate->execute($unassignedClaim,$notificationResults->[0]->{'NOTIFICATION_ID'}) || $error->($ENGINE);
                  }
          }
      }
      my $workStormID = 'NULL';
      my $generalUpdate = $ENGINE->{'DBH'}->prepare(
         'UPDATE CLAIMDB.CLM_GENERAL
             SET REPORTED_DATE = ?,
                 ACCIDENT_STATE = ?,
                 UMBRELLA = ?,
                 UMBRELLA_POLICY_NO = ?,
                 NOPAY_CLOSE_REASON = ?,
                 PROP_OR_LIAB = ?,
                 INITIATION_POINT = ?,
                 CLM_STORM_ID = '.$workStormID.'
           WHERE CLAIM_ID = ?') || $error->($ENGINE);


      my $umbrellaSW = '';
      my $umbrellaPolNo = '';
      my $initiationPoint = $ENGINE->{'claimGeneral'}->{'INITIATION_POINT'};
      if($ManualOrWhat eq 'M')
      {
          $umbrellaSW = 'N';
          $umbrellaPolNo = '';
          #initiation point was defaulting to AG - Agent, for manual
          #claims.  These are entered only by internal users, so we are
          #over-riding that here for non-converted claims.  12/22/2011.  akc.
          if ($initiationPoint ne 'CV')
          {
                  $initiationPoint = 'IU';
          }
      }
      else
      {
          $umbrellaSW = $ENGINE->{'claimGeneral'}->{'UMBRELLA'};
          $umbrellaPolNo = $ENGINE->{'claimGeneral'}->{'UMBRELLA_POLICY_NO'};
      }
      if($LineCode =~ /350|360/)
      {
          $umbrellaSW = 'N';
          $umbrellaPolNo = '';
      }
      for my $g (@{$ENGINE->{'PropLiab'}->{'CLM_GENERAL'}})
      {
         $generalUpdate->execute($g->{'REPORTED_DATE'},
                                 $g->{'ACCIDENT_STATE'},
                                 $umbrellaSW,
                                 $umbrellaPolNo,
                                 '',
                                 $g->{'PROP_OR_LIAB'},
                                 $initiationPoint,
                                 $claimid) || $error->($ENGINE);
      }
      validateClaimID($ENGINE);


      #################
      # Common Stat   #
      #################
      my $commStatInsert = $ENGINE->{'DBH'}->prepare(
         "SELECT CLM_COMMON_STAT_ID
            FROM FINAL TABLE
                   (INSERT INTO CLAIMDB.CLM_COMMON_STAT
                      (CLM_COMMON_STAT_ID,
                       CLAIM_ID,
                       LOCATION_NO,
                       UNIT_NO,
                       COUNTY_CODE,
                       CITY_SUBCNTY,
                       TERRITORY,
                       SUB_LINE,
                       ZIP_CODE,
                       ZIP_CODE2,
                       MANUAL_AUTOMATIC,
                       PERS_OR_COMM,
                       RATE_STATE)
          VALUES (NEXT VALUE FOR CLAIMDB.CLM_COMMON_STAT_ID_SEQ,?,?,?,?,?,?,?,?,?,?,?,?))")  || $error->($ENGINE);

      my $commStatDelete = '';
      my $covs_EndorsesDelete = '';

      if ($ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} eq 'P')
      {
         $commStatDelete = $ENGINE->{'DBH'}->prepare(
            "DELETE FROM CLAIMDB.CLM_COMMON_STAT
              WHERE CLM_COMMON_STAT_ID = ?
                AND CLAIM_ID = ?")  || $error->($ENGINE);
                         #delete the row from clm_covs_endorses.
         $covs_EndorsesDelete = $ENGINE->{'DBH'}->prepare(
            "DELETE FROM CLAIMDB.CLM_COVS_ENDORSES
              WHERE CLAIM_ID = ?
                AND LOCATION_NO = ?
                AND UNIT_NO = ?")  || $error->($ENGINE);
      }
      else
      {
         $commStatDelete = $ENGINE->{'DBH'}->prepare(
            "UPDATE CLAIMDB.CLM_COMMON_STAT
                SET DATE_DELETED = CURRENT TIMESTAMP
              WHERE CLM_COMMON_STAT_ID = ?
                AND CLAIM_ID = ?")  || $error->($ENGINE);
         $covs_EndorsesDelete = $ENGINE->{'DBH'}->prepare(
            "UPDATE CLAIMDB.CLM_COVS_ENDORSES
                SET DATE_DELETED = CURRENT TIMESTAMP
              WHERE CLAIM_ID = ?
                AND LOCATION_NO = ?
                AND UNIT_NO = ?")  || $error->($ENGINE);
      }

      my @insertCommonStatID = ();

      my $umbrellaSubLine = '';
      if($LineCode =~ /350|360/)
      { $umbrellaSubLine = '019'; }

      for my $n (@{$ENGINE->{'PropLiab'}->{'CLM_COMMON_STAT'}})
      {
         if ($n->{'NOUPDATE'} && !$n->{'DELETE'})
         {
            next;
         }

         if ($n->{'DELETE'})
         {
            $commStatDelete->execute($n->{'CLM_COMMON_STAT_ID'},$claimid) || $error->($ENGINE);
            $covs_EndorsesDelete->execute($claimid,$n->{'LOCATION_NO'},$n->{'UNIT_NO'}) || $error->($ENGINE);
            deleteLienholderMortgagee($ENGINE,$claimid,$n->{'CLM_COMMON_STAT_ID'});
         }
         elsif (defined($n->{'CLM_COMMON_STAT_ID'}) && $n->{'CLM_COMMON_STAT_ID'} =~ /^new/ && $ManualOrWhat eq 'M')
         {
            if (defined($n->{'LOC_TYPE'}) && $n->{'LOC_TYPE'} eq 'AC')
            {
               $commStatInsert->execute($claimid,
                                        $n->{'LOCATION_NO'},
                                        $n->{'UNIT_NO'},
                                        $n->{'COUNTY_CODE'},
                                        $n->{'CITY_SUBCNTY'},
                                        $n->{'TERRITORY'},
                                        $umbrellaSubLine,
                                        $n->{'ZIP1_5'},
                                        '0000000',
                                        'M',
                                        'P',
                                        $ENGINE->{'claimGeneral'}->{'POLICY_STATE'}) || $error->($ENGINE);

               my $result = $commStatInsert->fetchall_arrayref({});

               if (scalar(@$result) < 1)
               {
                  $error->($ENGINE);
               }

               $n->{'CLM_COMMON_STAT_ID'} = $result->[0]->{'CLM_COMMON_STAT_ID'};

#               $insertCommonStatID = $result->[0]->{'CLM_COMMON_STAT_ID'};
               push(@insertCommonStatID,$result->[0]->{'CLM_COMMON_STAT_ID'});

               # Add NEWROW flag for transhist
               $n->{'NEWROW'} = 1;



            }
         }
         else
         {
             push(@insertCommonStatID,$n->{'CLM_COMMON_STAT_ID'});
         }
      }

      #################
      # Location      #
      #################
      my $locationInsert = $ENGINE->{'DBH'}->prepare(
         "SELECT LOCATION_ID
            FROM FINAL TABLE
                    (INSERT INTO CLAIMDB.CLM_LOCATION
                       (LOCATION_ID,
                        CLM_COMMON_STAT_ID,
                        LOC_TYPE,
                        ADDRESS1,
                        ADDRESS2,
                        CITY,
                        STATE,
                        ZIP1_5,
                        ZIP6_12,
                        CLAIM_ID)
          VALUES (NEXT VALUE FOR CLAIMDB.LOCATION_ID_SEQ,?,?,?,?,?,?,?,?,?))")  || $error->($ENGINE);

      my $locationUpdate = $ENGINE->{'DBH'}->prepare(
         'UPDATE CLAIMDB.CLM_LOCATION
             SET ROOF_REPLACED = ?,
                 YEAR_ROOF_REPLACED = ?,
                 DEPRECIATE_AMOUNT = ?
           WHERE LOCATION_ID = ?
             AND CLAIM_ID = ?') || $error->($ENGINE);

      my $locationDelete = '';

      if ($ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} eq 'P')
      {
         $locationDelete = $ENGINE->{'DBH'}->prepare(
            "DELETE FROM CLAIMDB.CLM_LOCATION
              WHERE CLM_COMMON_STAT_ID = ?
                AND CLAIM_ID = ?")  || $error->($ENGINE);
      }
      else
      {
         $locationDelete = $ENGINE->{'DBH'}->prepare(
            "UPDATE CLAIMDB.CLM_LOCATION
                SET DATE_DELETED = CURRENT TIMESTAMP
              WHERE CLM_COMMON_STAT_ID = ?
                AND CLAIM_ID = ?")  || $error->($ENGINE);
      }

      my $i = 0;
      for my $n (@{$ENGINE->{'PropLiab'}->{'CLM_LOCATION'}})
      {
         if ($n->{'NOUPDATE'} && !$n->{'DELETE'})
         {
            next;
         }

         if ($n->{'DELETE'})
         {
            $locationDelete->execute($n->{'CLM_COMMON_STAT_ID'},$claimid) || $error->($ENGINE);
         }
         elsif (defined($n->{'LOCATION_ID'}) && $n->{'LOCATION_ID'} =~ /^new/ && $ManualOrWhat eq 'M')
         {
            if (defined($n->{'LOC_TYPE'}) && $n->{'LOC_TYPE'} eq 'AC')
            {
               $locationInsert->execute($insertCommonStatID[$i],
                                        $n->{'LOC_TYPE'},
                                        $n->{'ADDRESS1'},
                                        $n->{'ADDRESS2'},
                                        $n->{'CITY'},
                                        $n->{'STATE'},
                                        $n->{'ZIP1_5'},
                                        ' ',
                                        $claimid) || $error->($ENGINE);

               my $result = $locationInsert->fetchall_arrayref({});

               if (scalar(@$result) < 1)
               {
                  $error->($ENGINE);
               }

               $n->{'LOCATION_ID'} = $result->[0]->{'LOCATION_ID'};

               # Add NEWROW flag for transhist
               $n->{'NEWROW'} = 1;
               $i++;
            }
         }
         else
         {
            $locationUpdate->execute($n->{'ROOF_REPLACED'}||undef,
                                     $n->{'YEAR_ROOF_REPLACED'}||undef,
                                     $n->{'DEPRECIATE_AMOUNT'},
                                     $n->{'LOCATION_ID'},
                                     $claimid) || $error->($ENGINE);

         }
      }

      #################
      # Property Stat #
      #################

      my $propertyStatInsert = $ENGINE->{'DBH'}->prepare(
         'SELECT CLM_COMMON_STAT_ID FROM FINAL TABLE
                (INSERT INTO CLAIMDB.CLM_PROPERTY_STAT
                   (CLM_COMMON_STAT_ID,
                    CLAIM_ID,
                    INTERIOR_UPKEEP,
                    EXTERIOR_UPKEEP,
                    YARD_UPKEEP,
                    AUTO_OUTSIDE_LIGHT,
                    CONST_CODE,
                    SPRINKLER_IND,
                    ORDINANCE_OR_LAW,
                    YEAR_BUILT,
                    LOSS_SETTLEMENT_TYPE,
                    OCCUP_SIZE_LOC,
                    PROTECTIVE_DEV,
                    PROTECT_CODE,
                    ZONE,
                    PROP_CLASS,
                    IM176_PROT_CREDIT)
          VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?))') || $error->($ENGINE);
#      my $propertyStatInsert = $ENGINE->{'DBH'}->prepare(
#         'SELECT CLM_COMMON_STAT_ID FROM FINAL TABLE
#                (INSERT INTO CLAIMDB.CLM_PROPERTY_STAT
#                   (CLM_COMMON_STAT_ID,
#                    CLAIM_ID,
#                    INTERIOR_UPKEEP,
#                    EXTERIOR_UPKEEP,
#                    YARD_UPKEEP,
#                    AUTO_OUTSIDE_LIGHT,
#                    CONST_CODE,
#                    CF_OCCUPANCY_CODE,
#                    DF_FIRE_DETECT,
#                    DF_BURGLR_ALARM,
#                    DF_PRIMARY_HEAT,
#                    CF_CRIME_OFFP,
#                    CF_EC_SYMBOL,
#                    CF_EXTRAEXP_MTHLIM,
#                    CF_MANUAL_RATE_IND,
#                    CF_SPRINKLEAK_SUS,
#                    HO_PELLET_HEATING,
#                    SPRINKLER_IND,
#                    HO_TOWNROW_IND,
#                    ORDINANCE_OR_LAW,
#                    DF_BUILDERS_RISK,
#                    CF_THEFT_COV_IND,
#                    CF_THFT_CONTCV_IND,
#                    CF_SPEC_RATE_IND,
#                    CF_SPRINK_LEAK_IND,
#                    CF_CONTENTS_IND,
#                    CF_REINS_IND,
#                    BOART_OWN_LESS_IND,
#                    WOOD_STOVE,
#                    SWIM_POOL,
#                    YEAR_BUILT,
#                    NUMBER_OF_FAMILIES,
#                    HO_NUM_UNITS,
#                    MH_WIDTH,
#                    MH_LENGTH,
#                    CF_FIRLEG_ADTLCHG,
#                    DF_EQ_STORIES,
#                    HO_PRIM_SEC_SEAS,
#                    CF_SPRINK_CRED_IND,
#                    LOSS_SETTLEMENT_TYPE,
#                    OCCUP_SIZE_LOC,
#                    DF_SMOKE_DETECT,
#                    MH_TIE_DOWN,
#                    CF_LEGALIAB_TYPCOD,
#                    PROTECTIVE_DEV,
#                    ART_AUTO_INCREASE,
#                    PROTECT_CODE,
#                    DF_RESIDENCE_TYPE,
#                    BOART_RATE_IDENT,
#                    BOART_TYPE_OF_PAK,
#                    DF_LIAB_ONLY_IND,
#                    IM_RESCTRICT_OR_NOT)
#          VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?))') || $error->($ENGINE);


      my $propertyStatUpdate = $ENGINE->{'DBH'}->prepare(
         'UPDATE CLAIMDB.CLM_PROPERTY_STAT
             SET INTERIOR_UPKEEP = ?,
                 EXTERIOR_UPKEEP = ?,
                 YARD_UPKEEP = ?,
                 AUTO_OUTSIDE_LIGHT = ?,
                 CONST_CODE = ?,
                 CF_OCCUPANCY_CODE = ?,
                 DF_FIRE_DETECT = ?,
                 DF_BURGLR_ALARM = ?,
                 DF_PRIMARY_HEAT = ?,
                 CF_CRIME_OFFP = ?,
                 CF_EC_SYMBOL = ?,
                 CF_EXTRAEXP_MTHLIM = ?,
                 CF_MANUAL_RATE_IND = ?,
                 CF_SPRINKLEAK_SUS = ?,
                 HO_PELLET_HEATING = ?,
                 SPRINKLER_IND = ?,
                 HO_TOWNROW_IND = ?,
                 ORDINANCE_OR_LAW = ?,
                 FAIR_PLAN_IND = ?,
                 DF_BUILDERS_RISK = ?,
                 CF_THEFT_COV_IND = ?,
                 CF_THFT_CONTCV_IND = ?,
                 CF_SPEC_RATE_IND = ?,
                 CF_SPRINK_LEAK_IND = ?,
                 CF_CONTENTS_IND = ?,
                 CF_REINS_IND = ?,
                 BOART_OWN_LESS_IND = ?,
                 WOOD_STOVE = ?,
                 SWIM_POOL = ?,
                 YEAR_BUILT = ?,
                 NUMBER_OF_FAMILIES = ?,
                 HO_NUM_UNITS = ?,
                 MH_WIDTH = ?,
                 MH_LENGTH = ?,
                 CF_FIRLEG_ADTLCHG = ?,
                 DF_EQ_STORIES = ?,
                 HO_PRIM_SEC_SEAS = ?,
                 CF_SPRINK_CRED_IND = ?,
                 LOSS_SETTLEMENT_TYPE = ?,
                 OCCUP_SIZE_LOC = ?,
                 DF_SMOKE_DETECT = ?,
                 MH_TIE_DOWN = ?,
                 CF_LEGALIAB_TYPCOD = ?,
                 PROTECTIVE_DEV = ?,
                 ART_AUTO_INCREASE = ?,
                 PROTECT_CODE = ?,
                 DF_RESIDENCE_TYPE = ?,
                 BOART_RATE_IDENT = ?,
                 BOART_AAIS_FORM = ?,
                 BOART_TYPE_OF_PAK = ?,
                 DF_LIAB_ONLY_IND = ?,
                 IM_RESCTRICT_OR_NOT = ?,
                 HO_GEM_IND = ?
           WHERE CLM_COMMON_STAT_ID = ?
             AND CLAIM_ID = ?') || $error->($ENGINE);

      my $propertyStatDelete = '';

      if ($ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} eq 'P')
      {
         $propertyStatDelete = $ENGINE->{'DBH'}->prepare(
            'DELETE FROM CLAIMDB.CLM_PROPERTY_STAT
              WHERE CLM_COMMON_STAT_ID = ?
                AND CLAIM_ID = ?')  || $error->($ENGINE);
      }
      else
      {
         $propertyStatDelete = $ENGINE->{'DBH'}->prepare(
            'UPDATE CLAIMDB.CLM_PROPERTY_STAT
                SET DATE_DELETED = CURRENT TIMESTAMP
              WHERE CLM_COMMON_STAT_ID = ?
                AND CLAIM_ID = ?')  || $error->($ENGINE);
      }

      $i = 0;
      for my $n (@{$ENGINE->{'PropLiab'}->{'CLM_PROPERTY_STAT'}})
      {
         if ($n->{'NOUPDATE'} && !$n->{'DELETE'})
         {
            next;
         }

         if ($n->{'DELETE'})
         {
            $propertyStatDelete->execute($n->{'CLM_COMMON_STAT_ID'},$claimid) || $error->($ENGINE);;
         }
         elsif (!defined($n->{'CLM_COMMON_STAT_ID'}))
         {
            $propertyStatInsert->execute($insertCommonStatID[$i],
                                         $claimid,
                                         '',
                                         '',
                                         '',
                                         '',
                                         '',
                                         '',
                                         '',
                                         '',
                                         '',
                                         '',
                                         '',
                                         '',
                                         '',
                                         '',
                                         '') || $error->($ENGINE);
#            $propertyStatInsert->execute($insertCommonStatID,
#                                         $claimid,
#                                         $n->{'INTERIOR_UPKEEP'},
#                                         $n->{'EXTERIOR_UPKEEP'},
#                                         $n->{'YARD_UPKEEP'},
#                                         $n->{'AUTO_OUTSIDE_LIGHT'},
#                                         $n->{'CONST_CODE'},
#                                         $n->{'CF_OCCUPANCY_CODE'},
#                                         $n->{'DF_FIRE_DETECT'},
#                                         $n->{'DF_BURGLR_ALARM'},
#                                         $n->{'DF_PRIMARY_HEAT'},
#                                         $n->{'CF_CRIME_OFFP'},
#                                         $n->{'CF_EC_SYMBOL'},
#                                         $n->{'CF_EXTRAEXP_MTHLIM'},
#                                         $n->{'CF_MANUAL_RATE_IND'},
#                                         $n->{'CF_SPRINKLEAK_SUS'},
#                                         $n->{'HO_PELLET_HEATING'},
#                                         $n->{'SPRINKLER_IND'},
#                                         $n->{'HO_TOWNROW_IND'},
#                                         $n->{'ORDINANCE_OR_LAW'},
#                                         $n->{'DF_BUILDERS_RISK'},
#                                         $n->{'CF_THEFT_COV_IND'},
#                                         $n->{'CF_THFT_CONTCV_IND'},
#                                         $n->{'CF_SPEC_RATE_IND'},
#                                         $n->{'CF_SPRINK_LEAK_IND'},
#                                         $n->{'CF_CONTENTS_IND'},
#                                         $n->{'CF_REINS_IND'},
#                                         $n->{'BOART_OWN_LESS_IND'},
#                                         $n->{'WOOD_STOVE'},
#                                         $n->{'SWIM_POOL'},
#                                         $n->{'YEAR_BUILT '},
#                                         $n->{'NUMBER_OF_FAMILIES'},
#                                         $n->{'HO_NUM_UNITS'},
#                                         $n->{'MH_WIDTH'},
#                                         $n->{'MH_LENGTH'},
#                                         $n->{'CF_FIRLEG_ADTLCHG'},
#                                         $n->{'DF_EQ_STORIES'},
#                                         $n->{'HO_PRIM_SEC_SEAS'},
#                                         $n->{'CF_SPRINK_CRED_IND'},
#                                         $n->{'LOSS_SETTLEMENT_TYPE'},
#                                         $n->{'OCCUP_SIZE_LOC'},
#                                         $n->{'DF_SMOKE_DETECT'},
#                                         $n->{'MH_TIE_DOWN'},
#                                         $n->{'CF_LEGALIAB_TYPCOD'},
#                                         $n->{'PROTECTIVE_DEV'},
#                                         $n->{'ART_AUTO_INCREASE'},
#                                         $n->{'PROTECT_CODE'},
#                                         $n->{'DF_RESIDENCE_TYPE'},
#                                         $n->{'BOART_RATE_IDENT'},
#                                         $n->{'BOART_TYPE_OF_PAK'},
#                                         $n->{'DF_LIAB_ONLY_IND'},
#                                         $n->{'IM_RESCTRICT_OR_NOT'}) || $error->($ENGINE);

            my $result = $propertyStatInsert->fetchall_arrayref({});

            if (scalar(@$result) < 1)
            {
               $error->($ENGINE);
            }

            $n->{'CLM_COMMON_STAT_ID'} = $result->[0]->{'CLM_COMMON_STAT_ID'};


            # Add NEWROW flag for transhist
            $n->{'NEWROW'} = 1;
            $i++;
         }
         else
         {
#                  print "<br/>trying to update: ";
#                  print "<br/>clmCommonStatId: " . $n->{'CLM_COMMON_STAT_ID'};
#                  print "<br/>ClaimID: " . $claimid;
#                  print "<br/>n->{'INTERIOR_UPKEEP'}: " . $n->{'INTERIOR_UPKEEP'};
#                  print "<br/>n->{'EXTERIOR_UPKEEP'}: " . $n->{'EXTERIOR_UPKEEP'};
#                  print "<br/>n->{'YARD_UPKEEP'}: " . $n->{'YARD_UPKEEP'};
#                  print "<br/>n->{'AUTO_OUTSIDE_LIGHT'}: " .  $n->{'AUTO_OUTSIDE_LIGHT'};
#                  print "<br/>n->{'CONST_CODE'}: " . $n->{'CONST_CODE'};
#                  print "<br/>n->{'CF_OCCUPANCY_CODE'}: " . $n->{'CF_OCCUPANCY_CODE'};
#                  print "<br/>n->{'DF_FIRE_DETECT'}: " . $n->{'DF_FIRE_DETECT'};
#                  print "<br/>n->{'DF_BURGLR_ALARM'}: " .  $n->{'DF_BURGLR_ALARM'};
#                  print "<br/>n->{'DF_PRIMARY_HEAT'}: " . $n->{'DF_PRIMARY_HEAT'};
#                  print "<br/>n->{'CF_CRIME_OFFP'}: " .  $n->{'CF_CRIME_OFFP'};
#                  print "<br/>n->{'CF_EC_SYMBOL'}: " . $n->{'CF_EC_SYMBOL'};
#                  print "<br/>n->{'CF_EXTRAEXP_MTHLIM'} " . $n->{'CF_EXTRAEXP_MTHLIM'};
#                  print "<br/>n->{'CF_MANUAL_RATE_IND'} " . $n->{'CF_MANUAL_RATE_IND'};
#                  print "<br/>n->{'CF_SPRINKLEAK_SUS'} " . $n->{'CF_SPRINKLEAK_SUS'};
#                  print "<br/>n->{'HO_PELLET_HEATING'} " . $n->{'HO_PELLET_HEATING'};
#                  print "<br/>n->{'SPRINKLER_IND'} " . $n->{'SPRINKLER_IND'};
#                  print "<br/>n->{'HO_TOWNROW_IND'} " . $n->{'HO_TOWNROW_IND'};
#                  print "<br/>n->{'ORDINANCE_OR_LAW'} " . $n->{'ORDINANCE_OR_LAW'};
#                  print "<br/>n->{'FAIR_PLAN_IND'} " . $n->{'FAIR_PLAN_IND'};
#                  print "<br/>n->{'DF_BUILDERS_RISK'} " . $n->{'DF_BUILDERS_RISK'};
#                  print "<br/>n->{'CF_THEFT_COV_IND'} " . $n->{'CF_THEFT_COV_IND'};
#                  print "<br/>n->{'CF_THFT_CONTCV_IND'} " . $n->{'CF_THFT_CONTCV_IND'};
#                  print "<br/>n->{'CF_SPEC_RATE_IND'} " . $n->{'CF_SPEC_RATE_IND'};
#                  print "<br/>n->{'CF_SPRINK_LEAK_IND'} " . $n->{'CF_SPRINK_LEAK_IND'};
#                  print "<br/>n->{'CF_CONTENTS_IND'} " . $n->{'CF_CONTENTS_IND'};
#                  print "<br/>n->{'CF_REINS_IND'} " . $n->{'CF_REINS_IND'};
#                  print "<br/>n->{'BOART_OWN_LESS_IND'} " . $n->{'BOART_OWN_LESS_IND'};
#                  print "<br/>n->{'WOOD_STOVE'} " . $n->{'WOOD_STOVE'};
#                  print "<br/>n->{'SWIM_POOL'} " . $n->{'SWIM_POOL'};
#                  print "<br/>n->{'YEAR_BUILT'} " . $n->{'YEAR_BUILT'};
#                  print "<br/>n->{'NUMBER_OF_FAMILIES'} " . $n->{'NUMBER_OF_FAMILIES'};
#                  print "<br/>n->{'HO_NUM_UNITS'} " . $n->{'HO_NUM_UNITS'};
#                  print "<br/>n->{'MH_WIDTH'} " . $n->{'MH_WIDTH'};
#                  print "<br/>n->{'MH_LENGTH'} " . $n->{'MH_LENGTH'};
#                  print "<br/>n->{'CF_FIRLEG_ADTLCHG'} " . $n->{'CF_FIRLEG_ADTLCHG'};
#                  print "<br/>n->{'DF_EQ_STORIES'} " . $n->{'DF_EQ_STORIES'};
#                  print "<br/>n->{'HO_PRIM_SEC_SEAS'} " . $n->{'HO_PRIM_SEC_SEAS'};
#                  print "<br/>n->{'CF_SPRINK_CRED_IND'} " . $n->{'CF_SPRINK_CRED_IND'};
#                  print "<br/>n->{'LOSS_SETTLEMENT_TYPE'} " . $n->{'LOSS_SETTLEMENT_TYPE'};
#                  print "<br/>n->{'OCCUP_SIZE_LOC'} " . $n->{'OCCUP_SIZE_LOC'};
#                  print "<br/>n->{'DF_SMOKE_DETECT'} " . $n->{'DF_SMOKE_DETECT'};
#                  print "<br/>n->{'MH_TIE_DOWN'} " . $n->{'MH_TIE_DOWN'};
#                  print "<br/>n->{'CF_LEGALIAB_TYPCOD'} " . $n->{'CF_LEGALIAB_TYPCOD'};
#                  print "<br/>n->{'PROTECTIVE_DEV'} " . $n->{'PROTECTIVE_DEV'};
#                  print "<br/>n->{'ART_AUTO_INCREASE'} " . $n->{'ART_AUTO_INCREASE'};
#                  print "<br/>n->{'PROTECT_CODE'} " . $n->{'PROTECT_CODE'};
#                  print "<br/>n->{'DF_RESIDENCE_TYPE'} " . $n->{'DF_RESIDENCE_TYPE'};
#                  print "<br/>n->{'BOART_RATE_IDENT'} " . $n->{'BOART_RATE_IDENT'};
#                  print "<br/>n->{'BOART_AAIS_FORM'} " . $n->{'BOART_AAIS_FORM'};
#                  print "<br/>n->{'BOART_TYPE_OF_PAK'} " . $n->{'BOART_TYPE_OF_PAK'};
#                  print "<br/>n->{'DF_LIAB_ONLY_IND'} " . $n->{'DF_LIAB_ONLY_IND'};
#                  print "<br/>n->{'IM_RESCTRICT_OR_NOT'} " . $n->{'IM_RESCTRICT_OR_NOT'};
#                  print "<br/>n->{'HO_GEM_IND'} " . $n->{'HO_GEM_IND'};
            $propertyStatUpdate->execute($n->{'INTERIOR_UPKEEP'},
                                         $n->{'EXTERIOR_UPKEEP'},
                                         $n->{'YARD_UPKEEP'},
                                         $n->{'AUTO_OUTSIDE_LIGHT'},
                                         $n->{'CONST_CODE'},
                                         $n->{'CF_OCCUPANCY_CODE'},
                                         $n->{'DF_FIRE_DETECT'},
                                         $n->{'DF_BURGLR_ALARM'},
                                         $n->{'DF_PRIMARY_HEAT'},
                                         $n->{'CF_CRIME_OFFP'},
                                         $n->{'CF_EC_SYMBOL'},
                                         $n->{'CF_EXTRAEXP_MTHLIM'},
                                         $n->{'CF_MANUAL_RATE_IND'},
                                         $n->{'CF_SPRINKLEAK_SUS'},
                                         $n->{'HO_PELLET_HEATING'},
                                         $n->{'SPRINKLER_IND'},
                                         $n->{'HO_TOWNROW_IND'},
                                         $n->{'ORDINANCE_OR_LAW'},
                                         $n->{'FAIR_PLAN_IND'},
                                         $n->{'DF_BUILDERS_RISK'},
                                         $n->{'CF_THEFT_COV_IND'},
                                         $n->{'CF_THFT_CONTCV_IND'},
                                         $n->{'CF_SPEC_RATE_IND'},
                                         $n->{'CF_SPRINK_LEAK_IND'},
                                         $n->{'CF_CONTENTS_IND'},
                                         $n->{'CF_REINS_IND'},
                                         $n->{'BOART_OWN_LESS_IND'},
                                         $n->{'WOOD_STOVE'},
                                         $n->{'SWIM_POOL'},
                                         $n->{'YEAR_BUILT'},
                                         $n->{'NUMBER_OF_FAMILIES'},
                                         $n->{'HO_NUM_UNITS'},
                                         $n->{'MH_WIDTH'},
                                         $n->{'MH_LENGTH'},
                                         $n->{'CF_FIRLEG_ADTLCHG'},
                                         $n->{'DF_EQ_STORIES'},
                                         $n->{'HO_PRIM_SEC_SEAS'},
                                         $n->{'CF_SPRINK_CRED_IND'},
                                         $n->{'LOSS_SETTLEMENT_TYPE'},
                                         $n->{'OCCUP_SIZE_LOC'},
                                         $n->{'DF_SMOKE_DETECT'},
                                         $n->{'MH_TIE_DOWN'},
                                         $n->{'CF_LEGALIAB_TYPCOD'},
                                         $n->{'PROTECTIVE_DEV'},
                                         $n->{'ART_AUTO_INCREASE'},
                                         $n->{'PROTECT_CODE'},
                                         $n->{'DF_RESIDENCE_TYPE'},
                                         $n->{'BOART_RATE_IDENT'},
                                         $n->{'BOART_AAIS_FORM'},
                                         $n->{'BOART_TYPE_OF_PAK'},
                                         $n->{'DF_LIAB_ONLY_IND'},
                                         $n->{'IM_RESCTRICT_OR_NOT'},
                                         $n->{'HO_GEM_IND'},
                                         $n->{'CLM_COMMON_STAT_ID'},
                                         $claimid) || $error->($ENGINE);
         }
      }

      if ($ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} eq 'P')
      {
         $commStatDelete = $ENGINE->{'DBH'}->prepare(
            "DELETE FROM CLAIMDB.CLM_COMMON_STAT
              WHERE CLM_COMMON_STAT_ID = ?
                AND CLAIM_ID = ?")  || $error->($ENGINE);
      }
      else
      {
         $commStatDelete = $ENGINE->{'DBH'}->prepare(
            "UPDATE CLAIMDB.CLM_COMMON_STAT
                SET DATE_DELETED = CURRENT TIMESTAMP
              WHERE CLM_COMMON_STAT_ID = ?
                AND CLAIM_ID = ?")  || $error->($ENGINE);
      }

      my @sorted_stat_list = sort({$a->{'CLM_COMMON_STAT_ID'} cmp $b->{'CLM_COMMON_STAT_ID'}} @{$ENGINE->{'PropLiab'}->{'CLM_COMMON_STAT'}});

      for my $n (@sorted_stat_list)
      {
         if ($n->{'NOUPDATE'} && !$n->{'DELETE'})
         {
            next;
         }

         if ($n->{'DELETE'})
         {
            $commStatDelete->execute($n->{'CLM_COMMON_STAT_ID'},$claimid) || $error->($ENGINE);
         }
         elsif (defined($n->{'CLM_COMMON_STAT_ID'}) && $n->{'CLM_COMMON_STAT_ID'} =~ /^new/)
         {
#            if(defined($n->{'LOC_TYPE'}) && $n->{'LOC_TYPE'} eq 'LL')
#            {
#               my $lossDate = $ENGINE->{'claimGeneral'}->{'LOSS_DATE_TIME'};

#               if (length($lossDate) >= 10)
#               {
#                  $lossDate = substr($lossDate,5,2).'/'.substr($lossDate,8,2).'/'.substr($lossDate,0,4);
#               }

#               print ' locationNum '.$n->{'LOCATION_NO'};
#               print ' unitno '.$n->{'UNIT_NO'};
#               my $PIReturn = PolicyInterface($ENGINE,{'polNo'=>$ENGINE->{'claimGeneral'}->{'POLICY_NUMBER'},'lossDate'=>$lossDate,'claimid'=>$claimid,'trans'=>'PROPADDL','locNo'=>$n->{'LOCATION_NO'},'UnitNo'=>$n->{'UNIT_NO'}});
##               my $PIReturn = PolicyInterface($ENGINE,{'polNo'=>$ENGINE->{'claimGeneral'}->{'POLICY_NUMBER'},'lossDate'=>$lossDate,'claimid'=>$claimid,'trans'=>'PROPADDL','locNo'=>$n->{'LOCATION_NO'}});

#               $errInd = $PIReturn->{'errInd'};
#               $progErr = $PIReturn->{'progErr'};
#            }
            if (defined($n->{'LOC_TYPE'}) && $n->{'LOC_TYPE'} eq 'AC')
            {
               my $lossDate = $ENGINE->{'claimGeneral'}->{'LOSS_DATE_TIME'};
               if (length($lossDate) >= 10)
               {
                  $lossDate = substr($lossDate,5,2).'/'.substr($lossDate,8,2).'/'.substr($lossDate,0,4);
               }

               my $PIReturn = PolicyInterface($ENGINE,{'polNo'=>$ENGINE->{'claimGeneral'}->{'POLICY_NUMBER'},'lossDate'=>$lossDate,'claimid'=>$claimid,'trans'=>'PROPADD','locNo'=>$n->{'LOCATION_NO'},'UnitNo'=>$n->{'UNIT_NO'}});
#               my $PIReturn = PolicyInterface($ENGINE,{'polNo'=>$ENGINE->{'claimGeneral'}->{'POLICY_NUMBER'},'lossDate'=>$lossDate,'claimid'=>$claimid,'trans'=>'PROPADD','locNo'=>$n->{'LOCATION_NO'}});

               $errInd = $PIReturn->{'errInd'};

               $progErr = $PIReturn->{'progErr'};
            }
         }
      }

      #################
      # Liability Stat #
      #################

      my $liabStatInsert = $ENGINE->{'DBH'}->prepare(
         'SELECT CLM_COMMON_STAT_ID FROM FINAL TABLE
                (INSERT INTO CLAIMDB.CLM_LIAB_STAT
                   (CLM_COMMON_STAT_ID,
                    CLAIM_ID,
                    PK_PROG_CODE,
                    UMBRELLA_FH,
                    FL_RATE_CODE,
                         FL_LIVESTOCK_EXCL,
                         FL_EXPOSURE_TOTAL_ACRES,
                         FL_NUM_OF_ANMLS,
                    ISO_POLICY_CODE)
          VALUES (?,?,?,?,?,?,?,?,?))') || $error->($ENGINE);


      my $liabStatUpdate = $ENGINE->{'DBH'}->prepare(
         'UPDATE CLAIMDB.CLM_LIAB_STAT
             SET PK_PROG_CODE = ?,
                 UMBRELLA_FH = ?,
                 ISO_POLICY_CODE = ?,
                 AMOUNT_OF_RISK = ?,
                 FL_RATE_CODE = ?,
                 FL_LIVESTOCK_EXCL = ?,
                 FL_EXPOSURE_TOTAL_ACRES = ?,
                 FL_NUM_OF_ANMLS = ?
           WHERE CLM_COMMON_STAT_ID = ?
             AND CLAIM_ID = ?') || $error->($ENGINE);

      my $liablityStatDelete = '';

      if ($ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} eq 'P')
      {
         $liablityStatDelete = $ENGINE->{'DBH'}->prepare(
            'DELETE FROM CLAIMDB.CLM_LIAB_STAT
              WHERE CLM_COMMON_STAT_ID = ?
                AND CLAIM_ID = ?')  || $error->($ENGINE);
      }
      else
      {
         $liablityStatDelete = $ENGINE->{'DBH'}->prepare(
            'UPDATE CLAIMDB.CLM_LIAB_STAT
                SET DATE_DELETED = CURRENT TIMESTAMP
              WHERE CLM_COMMON_STAT_ID = ?
                AND CLAIM_ID = ?')  || $error->($ENGINE);
      }

      $i = 0;
      for my $n (@{$ENGINE->{'PropLiab'}->{'CLM_LIAB_STAT'}})
      {
         if ($n->{'NOUPDATE'} && !$n->{'DELETE'})
         {
            next;
         }

         if ($n->{'DELETE'})
         {
            $liablityStatDelete->execute($n->{'CLM_COMMON_STAT_ID'},$claimid) || $error->($ENGINE);;
         }
         elsif (!defined($n->{'CLM_COMMON_STAT_ID'}))
         {
            $liabStatInsert->execute($insertCommonStatID[$i],
                                         $claimid,
                                         '',
                                         $n->{'UMBRELLA_FH'} || '',
                                         undef,
                                         undef,
                                         undef,
                                         undef,
                                         '') || $error->($ENGINE);


            my $result = $liabStatInsert->fetchall_arrayref({});

            if (scalar(@$result) < 1)
            {
               $error->($ENGINE);
            }

            $n->{'CLM_COMMON_STAT_ID'} = $result->[0]->{'CLM_COMMON_STAT_ID'};

            # Add NEWROW flag for transhist
            $n->{'NEWROW'} = 1;
            $i++;
         }
         else
         {
            $liabStatUpdate->execute($n->{'PK_PROG_CODE'},
                                         $n->{'UMBRELLA_FH'},
                                         $n->{'ISO_POLICY_CODE'},
                                         $n->{'AMOUNT_OF_RISK'},
                                         $n->{'FL_RATE_CODE'} || undef,
                                         $n->{'FL_LIVESTOCK_EXCL'} || undef,
                                         $n->{'FL_EXPOSURE_TOTAL_ACRES'} || undef,
                                         $n->{'FL_NUM_OF_ANMLS'} || undef,
                                         $n->{'CLM_COMMON_STAT_ID'},
                                         $claimid) || $error->($ENGINE);
         }
      }

      #################
      # Assignment Fields #
      #################


      my $assignmentInsert = $ENGINE->{'DBH'}->prepare(
          'SELECT ASSIGNMENT_ID
            FROM FINAL TABLE
              (INSERT INTO CLAIMDB.CLM_ASSIGNMENT_FIELDS
                   (CLAIM_ID,
                    PARTY_ID,
                    COMMON_STAT_ID,
                    STORM_TYPE,
                    IMT_CAUSE_OF_LOSS,
                    ACCIDENT_CITY,
                    ACCIDENT_ZIP,
                    TYPE_OF_LOSS,
                    SEVERITY_OF_DAMAGE,
                    SCHEDULED_ITEM,
                    ACCIDENT_STATE,
                    SUIT_FILED,
                    PROP_DAMAGE_tYPE,
                    COVERAGE_ID,
                    LOSS_CODE_GROUP,
                    ACCIDENT_COUNTY,
                    ASSIGNMENT_ID)
          VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,NEXT VALUE FOR CLAIMDB.ASSIGNMENT_ID_SEQ))') || $error->($ENGINE);


      my $assignmentUpdate = $ENGINE->{'DBH'}->prepare(
         'UPDATE CLAIMDB.CLM_ASSIGNMENT_FIELDS
             SET PARTY_ID = ?,
                 COMMON_STAT_ID = ?,
                 STORM_TYPE = ?,
                 IMT_CAUSE_OF_LOSS = ?,
                 ACCIDENT_CITY = ?,
                 ACCIDENT_ZIP = ?,
                 TYPE_OF_LOSS = ?,
                 SEVERITY_OF_DAMAGE = ?,
                 SCHEDULED_ITEM = ?,
                 ACCIDENT_STATE = ?,
                 SUIT_FILED = ?,
                 PROP_DAMAGE_TYPE = ?,
                 COVERAGE_ID = ?,
                 LOSS_CODE_GROUP = ?,
                 ACCIDENT_COUNTY = ?
           WHERE CLAIM_ID = ? AND ASSIGNMENT_ID = ?') || $error->($ENGINE);

      my $assignmentDelete = '';

      if ($ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} eq 'P')
      {
         $assignmentDelete = $ENGINE->{'DBH'}->prepare(
            'DELETE FROM CLAIMDB.CLM_ASSIGNMENT_FIELDS
              WHERE COMMON_STAT_ID = ?
                AND CLAIM_ID = ?')  || $error->($ENGINE);
      }
      else
      {
         $assignmentDelete = $ENGINE->{'DBH'}->prepare(
            'UPDATE CLAIMDB.CLM_ASSIGNMENT_FIELDS
                SET DATE_DELETED = CURRENT TIMESTAMP
              WHERE COMMON_STAT_ID = ?
                AND CLAIM_ID = ?')  || $error->($ENGINE);
      }


      $i = 0;

       for my $n (@{$ENGINE->{'PropLiab'}->{'CLM_ASSIGNMENT_FIELDS'}})
      {
         if ($n->{'NOUPDATE'} && !$n->{'DELETE'})
         {
            next;
         }

         if ($n->{'DELETE'})
         {
            $assignmentDelete->execute($n->{'CLM_COMMON_STAT_ID'},$claimid) || $error->($ENGINE);;
         }

         elsif ((!defined($n->{'ASSIGNMENT_ID'})) || $n->{'ASSIGNMENT_ID'} eq '')
         {
             $assignmentInsert->execute(
                                         $n->{'CLAIM_ID'},
                                         $n->{'PARTY_ID'} || undef,
                                         $n->{'COMMON_STAT_ID'} || undef,
                                         $n->{'STORM_TYPE'} || '',
                                         $n->{'IMT_CAUSE_OF_LOSS'} || '',
                                         $n->{'ACCIDENT_CITY'} || '',
                                         $n->{'ACCIDENT_ZIP'} || '',
                                         $n->{'TYPE_OF_LOSS'} || '',
                                         $n->{'SEVERITY_OF_DAMAGE'} || '',
                                         $n->{'SCHEDULED_ITEM'} || '',
                                         $n->{'ACCIDENT_STATE'} || '',
                                         $n->{'SUIT_FILED'} || '',
                                         $n->{'PROP_DAMAGE_TYPE'} || '',
                                         $n->{'COVERAGE_ID'} || undef,
                                         $n->{'LOSS_CODE_GROUP'} || '',
                                         $n->{'ACCIDENT_COUNTY'} || '') || $error->($ENGINE);


            my $result = $assignmentInsert->fetchall_arrayref({});


            $n->{'ASSIGNMENT_ID'} = $result->[0]->{'ASSIGNMENT_ID'};

            # Add NEWROW flag for transhist
            $n->{'NEWROW'} = 1;
            $i++;
         }
         else
         {
            $assignmentUpdate->execute($n->{'PARTY_ID'} || undef,
                                         $n->{'COMMON_STAT_ID'} || undef,
                                         $n->{'STORM_TYPE'} || '',
                                         $n->{'IMT_CAUSE_OF_LOSS'} || '',
                                         $n->{'ACCIDENT_CITY'} || '',
                                         $n->{'ACCIDENT_ZIP'} || '',
                                         $n->{'TYPE_OF_LOSS'} || '',
                                         $n->{'SEVERITY_OF_DAMAGE'} || '',
                                         $n->{'SCHEDULED_ITEM'} || '',
                                         $n->{'ACCIDENT_STATE'} || '',
                                         $n->{'SUIT_FILED'} || '',
                                         $n->{'PROP_DAMAGE_TYPE'} || '',
                                         $n->{'COVERAGE_ID'} || undef,
                                         $n->{'LOSS_CODE_GROUP'} || '',
                                         $n->{'ACCIDENT_COUNTY'} || '',
                                         $claimid,$n->{'ASSIGNMENT_ID'}) || $error->($ENGINE);
         }
      }

#      #################
#      # Bond Info     #
#      #################

#      my $bondInfoInsert = $ENGINE->{'DBH'}->prepare(
#         'SELECT CLM_BOND_INFO_ID FROM FINAL TABLE
#                (INSERT INTO CLAIMDB.CLM_BOND_INFO
#                   (CLM_BOND_INFO_ID,
#                    CLAIM_ID,
#                    BOND_TYPE)
#          VALUES (NEXT VALUE FOR CLAIMDB.CLM_BOND_INFO_ID_SEQ,?,?))') || $error->($ENGINE);


#      my $bondInfoUpdate = $ENGINE->{'DBH'}->prepare(
#         'UPDATE CLAIMDB.CLM_BOND_INFO
#             SET BOND_TYPE = ?
#           WHERE CLAIM_ID = ?') || $error->($ENGINE);

#      my $bondInfoDelete = '';

#      if ($ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} eq 'P')
#      {
#         $bondInfoDelete = $ENGINE->{'DBH'}->prepare(
#            'DELETE FROM CLAIMDB.CLM_BOND_INFO
#              WHERE CLM_BOND_INFO_ID = ?
#                AND CLAIM_ID = ?')  || $error->($ENGINE);
#      }
#      else
#      {
#         $bondInfoDelete = $ENGINE->{'DBH'}->prepare(
#            'UPDATE CLAIMDB.CLM_BOND_INFO
#                SET DATE_DELETED = CURRENT TIMESTAMP
#              WHERE CLM_BOND_INFO_ID = ?
#                AND CLAIM_ID = ?')  || $error->($ENGINE);
#      }

#      for my $n (@{$ENGINE->{'PropLiab'}->{'CLM_BOND_INFO'}})
#      {
#         if ($n->{'NOUPDATE'} && !$n->{'DELETE'})
#         {
#            next;
#         }

#         if ($n->{'DELETE'})
#         {
#            $bondInfoDelete->execute($n->{'CLM_BOND_INFO_ID'},$claimid) || $error->($ENGINE);;
#         }
#         elsif (!defined($n->{'CLM_BOND_INFO_ID'}))
#         {
#            $bondInfoInsert->execute($claimid,
#                                         $n->{'BOND_TYPE'}) || $error->($ENGINE);


#            my $result = $bondInfoInsert->fetchall_arrayref({});

#            if (scalar(@$result) < 1)
#            {
#               $error->($ENGINE);
#            }

#            $n->{'CLM_BOND_INFO_ID'} = $result->[0]->{'CLM_BOND_INFO_ID'};

#            # Add NEWROW flag for transhist
#            $n->{'NEWROW'} = 1;
#         }
#         else
#         {

#            $bondInfoUpdate->execute($n->{'BOND_TYPE'},
#                                         $claimid) || $error->($ENGINE);
#         }
#      }

      Claims_Misc::updateLastSave($ENGINE);
      # Only run the transHistory if the claim is not in pending status.
      if ($ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} ne 'P')
      {
         transHistory($ENGINE,'CLM_GENERAL',$ENGINE->{'PropLiab'}->{'OLD_CLM_GENERAL'},$ENGINE->{'PropLiab'}->{'CLM_GENERAL'});
         transHistory($ENGINE,'CLM_PROPERTY_STAT',$ENGINE->{'PropLiab'}->{'OLD_CLM_PROPERTY_STAT'},$ENGINE->{'PropLiab'}->{'CLM_PROPERTY_STAT'});
         transHistory($ENGINE,'CLM_COMMON_STAT',$ENGINE->{'PropLiab'}->{'OLD_CLM_COMMON_STAT'},$ENGINE->{'PropLiab'}->{'CLM_COMMON_STAT'});
         transHistory($ENGINE,'CLM_LOCATION',$ENGINE->{'PropLiab'}->{'OLD_CLM_LOCATION'},$ENGINE->{'PropLiab'}->{'CLM_LOCATION'});
         transHistory($ENGINE,'CLM_LIAB_STAT',$ENGINE->{'PropLiab'}->{'OLD_CLM_LIAB_STAT'},$ENGINE->{'PropLiab'}->{'CLM_LIAB_STAT'});
         transHistory($ENGINE,'CLM_ASSIGNMENT_FLD',$ENGINE->{'PropLiab'}->{'OLD_CLM_ASSIGNMENT_FIELDS'},$ENGINE->{'PropLiab'}->{'CLM_ASSIGNMENT_FIELDS'});
#         transHistory($ENGINE,'CLM_BOND_INFO',$ENGINE->{'PropLiab'}->{'OLD_CLM_BOND_INFO'},$ENGINE->{'PropLiab'}->{'CLM_BOND_INFO'});
      }

      storePartiesData($ENGINE);

      storeAuthorityData($ENGINE);

      storeVehTotalLossData($ENGINE);

      storeOtherInfoData($ENGINE);

      storeSubmitClaimData($ENGINE);

      $ENGINE->{'DBH'}->commit();

      #Reload $ENGINE->{'claimGeneral'}
      validateClaimID($ENGINE);


   }
   else
   {
      $ENGINE->{'load'} = 'Claims_PropLiab';
   }
}

sub editScreen
{
   my $ENGINE = shift;
   my %errors = ();
   my $return = 1;
   my $claimid = $ENGINE->{'claimGeneral'}->{'CLAIM_ID'};
   my $LineCode = $ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'};
   my $UserType = $ENGINE->{'AUTH'}->{'IMTOnline_UserType'};
   my $ManualOrWhat = $ENGINE->{'claimGeneral'}->{'MANUAL_OR_WHAT'};
   my $errInd = '';
   my $progErr = '';
   my $policyLocationID = '';
   my $lossDate10 = substr($ENGINE->{'claimGeneral'}->{'LOSS_DATE_TIME'},0,10);
   my $errorDiv = '<div class="error"></div>';

#   print "<br/>"."lossDate10 = ". $lossDate10;

   my $error = $ENGINE->{'error'};
   my $holdLocNo = 0;
   my $holdCommonStatID = 0;
   if($ManualOrWhat eq 'M')
   {
       my $commStatResults = [];
       if($LineCode =~ /350|360/)
       {
           my $commStatQuery = $ENGINE->{'DBH'}->prepare(
           "SELECT C.*, L.LOC_TYPE
   FROM CLAIMDB.CLM_COMMON_STAT AS C
LEFT OUTER JOIN CLAIMDB.CLM_LOCATION AS L
  ON C.CLM_COMMON_STAT_ID = L.CLM_COMMON_STAT_ID
  AND L.LOC_TYPE = 'AC'
  AND L.DATE_DELETED = '9999-01-01 01:00:00.000000'
WHERE C.CLAIM_ID = ?") || $error->($ENGINE,'Common Stat query prepare failed: '.$ENGINE->{'DBH'}->errstr);

               $commStatQuery->execute($claimid) || $error->($ENGINE,'Common Stat query execute failed: '.$ENGINE->{'DBH'}->errstr);
               $commStatResults = $commStatQuery->fetchall_arrayref({});

           my $y = 0;
               for my $cs (@$commStatResults)
               {
               $y++;
               if(defined($cs->{'LOC_TYPE'}) && $cs->{'LOC_TYPE'} eq 'AC')
               {
                       if($cs->{'LOCATION_NO'} gt $holdLocNo)
                       { $holdLocNo = $cs->{'LOCATION_NO'}; }
                   }
                   else
                   {
                   $holdLocNo = $cs->{'LOCATION_NO'} - 1;
                   if($y ==1)
                   { $holdCommonStatID = $cs->{'CLM_COMMON_STAT_ID'}; }
                   }
               }

       }
       else
       {
               my $commStatQuery = $ENGINE->{'DBH'}->prepare(
                  'SELECT *
                     FROM CLAIMDB.CLM_COMMON_STAT
                    WHERE CLAIM_ID = ?
                      AND DATE_DELETED = \'9999-01-01 01:00:00.000000\'') || $error->($ENGINE,'Common Stat query prepare failed: '.$ENGINE->{'DBH'}->errstr);

               $commStatQuery->execute($claimid) || $error->($ENGINE,'Common Stat query execute failed: '.$ENGINE->{'DBH'}->errstr);
               $commStatResults = $commStatQuery->fetchall_arrayref({});

               for my $cs (@$commStatResults)
               {
                   if($cs->{'LOCATION_NO'} gt $holdLocNo)
                   { $holdLocNo = $cs->{'LOCATION_NO'}; }
               }
           }
   }

   my ($curDay, $curMonth, $curYear) = (localtime)[3,4,5];
   $curYear = $curYear+1900;
   $curDay = length($curDay)<2 ? '0'.$curDay : $curDay;
   $curMonth = $curMonth+1;
   $curMonth = length($curMonth)<2 ? '0'.$curMonth : $curMonth;
   my $checkDate = $curYear.$curMonth.$curDay;

   my $lossDateCK = $ENGINE->{'CGI'}->param('lossDate')||'';
   my $checkLossDate = '';

   if ($lossDateCK =~ /^(\d\d)(\d\d)(\d\d|\d\d\d\d)$/ || $lossDateCK =~ /^(\d\d?)[^0-9](\d\d?)[^0-9](\d\d|\d\d\d\d)$/)
   {
      my $day = length($2)<2 ? '0'.$2 : $2;
      my $month = length($1)<2 ? '0'.$1 : $1;
      my $year = length($3)<3 ? 2000 + $3 : $3;

      if (length($year)>4)
      {
         $year = substr($year,0,4);
      }
      $checkLossDate = $year.$month.$day;
   }

   my $lossDateChange = $ENGINE->{'CGI'}->param('lossDateChange')||'';

   my $generalData = {};
   $generalData->{'CLAIM_ID'} = $claimid;
   if (defined($lossDateChange) && $lossDateChange eq 'Y')
   {
      my ($day, $month, $year) = parseDateString($lossDateCK);
      if(defined($day))
      {
        if ($checkLossDate le $checkDate)
        {
           my $lossDate = $ENGINE->{'CGI'}->param('lossDate')||'';

           if ($lossDate =~ /^(\d\d)(\d\d)(\d\d|\d\d\d\d)$/ || $lossDate =~ /^(\d\d?)[^0-9](\d\d?)[^0-9](\d\d|\d\d\d\d)$/)
           {
              my $day = length($2)<2 ? '0'.$2 : $2;
              my $month = length($1)<2 ? '0'.$1 : $1;
              my $year = length($3)<3 ? 2000 + $3 : $3;

              if (length($year)>4)
              {
                 $year = substr($year,0,4);
              }

              $lossDate = $month.'/'.$day.'/'.$year;
           }

           my $PIReturn = PolicyInterface($ENGINE,{'polNo'=>$ENGINE->{'claimGeneral'}->{'POLICY_NUMBER'},'lossDate'=>$lossDate,'claimid'=>$claimid,'trans'=>'LOSSDATE','locNo'=>1});

           $errInd = $PIReturn->{'errInd'};
           $progErr = $PIReturn->{'progErr'};

           if ($errInd eq 'Y')
           {
  #            push(@{$errors{'lossDate'}},'Date change error from interface.'); $return=0;
              push(@{$errors{'lossDate'}},'Claim has already been reported, or a payment has been made.  The Loss Date cannot be changed.  Please hit the Save button to revert back to original loss date.'); $return=0;
              $generalData->{'errLossDate'} = '<li><div class="errorInfo">Claim has already been reported, or a payment has been made.  The Loss Date cannot be changed.  Please hit the Save button to revert back to original loss date.</div></li>';
           }

           # if the loss date is changed, do not edit the rest of the screen.
           # This is because everything is removed and added to the claim.
           # There is no need to edit it.
           $return=0;
        }
        else
        {
           push(@{$errors{'lossDate'}},'Loss Date cannot be greater than the current date.'); $return=0;
           $generalData->{'errLossDate'} = '<li><div class="errorInfo">Loss Date cannot be greater than the current date.</div></li>';
        }
      }
      else
      { push(@{$errors{'lossDate'}},'<li><div class="errorInfo">Invalid loss date.</div></li>'); $return=0; }
   }

   my $lossDate = $ENGINE->{'CGI'}->param('lossDate')||'';

   if ($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'Internal' && $ManualOrWhat eq 'M')
   {
       if ($lossDate =~ /^(\d\d)(\d\d)(\d\d|\d\d\d\d)$/ || $lossDate =~ /^(\d\d?)[^0-9](\d\d?)[^0-9](\d\d|\d\d\d\d)$/)
       {
          my $day = length($2)<2 ? '0'.$2 : $2;
          my $month = length($1)<2 ? '0'.$1 : $1;
          my $year = length($3)<3 ? 2000 + $3 : $3;

          if (length($year)>4)
          {
             $year = substr($year,0,4);
          }
          $generalData->{'LOSS_DATE_TIME'} = $year.'-'.$month.'-'.$day.' 01:00:00.000000';
       }
       else
       {
          push(@{$errors{'lossDate'}},'Invalid loss date.'); $return=0;
          $generalData->{'errLossDate'} = '<li><div class="errorInfo">Invalid loss date</div></li>';
       }
   }

   my $claimType = $ENGINE->{'CGI'}->param('propLiabRadio')||'';

   if ($claimType eq 'P')
   {
      $generalData->{'PROP_OR_LIAB'} = 'P';
   }
   elsif ($claimType eq 'L')
   {
      $generalData->{'PROP_OR_LIAB'} = 'L';
   }
   elsif ($claimType eq 'B')
   {
      $generalData->{'PROP_OR_LIAB'} = 'B';
   }
   else
   {
      $generalData->{'PROP_OR_LIAB'} = '';
      push(@{$errors{'propLiabRadio'}},'<li><div class="errorInfo">Please select a Claim Type.</div></li>'); $return=0;
   }


   my $assignmentData = {};
   my $stormTypeID = $ENGINE->{'CGI'}->param('stormTypeID')||'';
   my $stormType = $ENGINE->{'CGI'}->param('stormType')||'';
   my $severityOfDamage = $ENGINE->{'CGI'}->param('severityOfDamage')||'';
   my $schedItem = $ENGINE->{'CGI'}->param('schedItemRadio')||'';
   my $suitFiled = $ENGINE->{'CGI'}->param('suitFiledRadio')||'';
   my $liability = $ENGINE->{'claimGeneral'}->{'PROP_OR_LIAB'};
   my $stormTypeSave = '';
   my $severityOfDamageSave = '';
   $assignmentData->{'STORM_TYPE'} = $stormType;
   $assignmentData->{'SEVERITY_OF_DAMAGE'} = $severityOfDamage;
   $assignmentData->{'CLAIM_ID'} = $claimid;
   if(defined($ENGINE->{'CGI'}->param('propLiabRadio')))
   {$liability = $ENGINE->{'CGI'}->param('propLiabRadio');}

   $assignmentData->{'ASSIGNMENT_ID'} = $stormTypeID;
   $assignmentData->{'SCHEDULED_ITEM'} = $schedItem;
   $assignmentData->{'SUIT_FILED'} = $suitFiled;

   my $compareReportDate = '';
   my $reportDate = $ENGINE->{'CGI'}->param('reportDate')||'';

   if((defined($reportDate) && $reportDate ne '') && (defined($reportDate) && $reportDate ne 'Unknown'))
   {
       if ($reportDate =~ /^(\d\d)(\d\d)(\d\d|\d\d\d\d)$/ || $reportDate =~ /^(\d\d?)[^0-9](\d\d?)[^0-9](\d\d|\d\d\d\d)$/)
       {
          my $day = length($2)<2 ? '0'.$2 : $2;
          my $month = length($1)<2 ? '0'.$1 : $1;
          my $year = length($3)<3 ? 2000 + $3 : $3;

          if (length($year)>4)
          {
             $year = substr($year,0,4);
          }

          $generalData->{'REPORTED_DATE'} = $year.'-'.$month.'-'.$day.' 01:00:00.000000';
          $compareReportDate = $year.$month.$day;
       }
       else
       {
          push(@{$errors{'reportDate'}},'Invalid report date.'); $return=0;
          $generalData->{'errReportDate'} = $errorDiv;
       }
   }
   else
   { $generalData->{'REPORTED_DATE'} = '9999-01-01 01:00:00.000000' }

   #Verify that the date reported is not before the loss date
   #checkLossDate is the loss date
   #compareReportDate is the date reported
   if ((defined($reportDate) && $reportDate ne '') && (defined($reportDate) && $reportDate ne 'Unknown'))
   {
       if ($compareReportDate eq ''
                   || $compareReportDate < $checkLossDate)
       {
                push(@{$errors{'reportDate'}},'<li><div class="errorInfo">Reported date must be on or after the loss date.</div></li>');
              $return = 0;
              $generalData->{'errReportDate'} = '<li><div class="errorInfo">Reported date must be on or after the loss date.</div></li>';
       }
      if ($compareReportDate eq '' || $compareReportDate > $checkDate)
      {
           push(@{$errors{'reportDate'}},'<li><div class="errorInfo">Date Reported to Agent cannot be greater than the current date</div></li>');
           $return = 0;
           $generalData->{'errReportDate'} = '<li><div class="errorInfo">Date Reported to Agent cannot be greater than the current date</div></li>';
      }
   }


   my $descRows = VARDATAreadInput($ENGINE,{'name'=>'lossDescription','keys'=>{'LINE_TYPE'=>'D','DATA_TYPE'=>'DESCRIPT'}});

   push(@{$ENGINE->{'PropLiab'}->{'CLM_VARDATA'}},@$descRows);

   my $lossLocation = $ENGINE->{'CGI'}->param('lossLocation')||'';
   if(defined($lossLocation) && $lossLocation gt '')
   {
       my $lossLocRows = VARDATAreadInput($ENGINE,{'name'=>'lossLocation','keys'=>{'LINE_TYPE'=>'L','DATA_TYPE'=>'LOCATION'}});
       push(@{$ENGINE->{'PropLiab'}->{'CLM_VARDATA'}},@$lossLocRows);
   }

   my $detailsRows = VARDATAreadInput($ENGINE,{'name'=>'lossDetail','keys'=>{'LINE_TYPE'=>'D','DATA_TYPE'=>'DETAILS'}});
   my $cnt = length($ENGINE->{'CGI'}->param('lossDetail'));
   if($cnt > 1000)
   {
          push(@{$errors{'lossDetail'}},'<li><div class="errorInfo">Loss Description Details entry must be less than 1000 characters.</div></li>');
          $return = 0;
   }

   push(@{$ENGINE->{'PropLiab'}->{'CLM_VARDATA'}},@$detailsRows);

   my $lossStateRows = VARDATAreadInput($ENGINE,{'name'=>'lossStateOther','keys'=>{'LINE_TYPE'=>'L','DATA_TYPE'=>'LOSSSTOTH'}});

   push(@{$ENGINE->{'PropLiab'}->{'CLM_VARDATA'}},@$lossStateRows);


   # STATE WHERE LOSS OCCURRED
   my $lossState = $ENGINE->{'CGI'}->param('lossState');
   my $validState = 0;
   my %stateCountryCodes = stateCountryCodes();
   for my $key (sort keys %stateCountryCodes)
   {
      if ($lossState eq substr($key,2,2))
      {
         $validState = 1;
         last;
      }
   }
   if ($validState)
   {
#      $ENGINE->{'PropLiab'}->{'ACCIDENT_STATE'} = $lossState;
      $generalData->{'ACCIDENT_STATE'} = $lossState;
      $assignmentData->{'ACCIDENT_STATE'} = $lossState;
   }

   if($ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} =~ /P/)
   {
       $assignmentData->{'ACCIDENT_ZIP'} = $ENGINE->{'CGI'}->param('lossZip');
       $assignmentData->{'ACCIDENT_CITY'} = uc($ENGINE->{'CGI'}->param('lossCity'));
       $assignmentData->{'ACCIDENT_COUNTY'} = $ENGINE->{'CGI'}->param('lossCounty');
   }
   elsif($ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} =~ /M|A/)
   {
        my $zip_ok = 1;
        if($ENGINE->{'CGI'}->param('lossZip') eq '')
        {
            push(@{$errors{'lossZip'}},'<li><div class="errorInfo">Please enter the ZIP where the loss occurred.</div></li>');
            $return = 0;
            $zip_ok = 0;
        }
        elsif(length($ENGINE->{'CGI'}->param('lossZip')) != 5)
        {
            push(@{$errors{'lossZip'}},'<li><div class="errorInfo">Invalid ZIP entered.</div></li>');
            $return = 0;
            $zip_ok = 0;
        }

        if($ENGINE->{'CGI'}->param('lossCity') eq '' && $zip_ok)
        {
            push(@{$errors{'lossCity'}},'<li><div class="errorInfo">Please enter the city where the loss occurred.</div></li>');
            $return = 0;
        }

        if($zip_ok)
        {
            $assignmentData->{'ACCIDENT_ZIP'} = $ENGINE->{'CGI'}->param('lossZip');
            $assignmentData->{'ACCIDENT_CITY'} = $ENGINE->{'CGI'}->param('lossCity');
            $assignmentData->{'ACCIDENT_COUNTY'} = $ENGINE->{'CGI'}->param('lossCounty');
        }
   }

   # REQUIRE 'OTHER LOSS STATE/COUNTRY' TEXT IF LOSS STATE IS 'OTHER'
   if ($lossState eq 'OT')
   {
      if (!defined($ENGINE->{'CGI'}->param('lossStateOther')) || $ENGINE->{'CGI'}->param('lossStateOther') eq '')
      {
         push(@{$errors{'Other Loss State'}},'<li><div class="errorInfo">Please enter Other Loss State/Country</div></li>');
         $return = 0;
      }
   }

   push(@{$ENGINE->{'PropLiab'}->{'CLM_GENERAL'}},$generalData);
   push(@{$ENGINE->{'PropLiab'}->{'CLM_ASSIGNMENT_FIELDS'}}, $assignmentData);

#   ### LINE CODE 550 ##################################################
#   # Bond Type
#   my $bondInfoData = ();
#   my $bondInfoSW = 'N';
#   if ($LineCode =~ /550/)
#   {
#       my $bondType = $ENGINE->{'CGI'}->param('bondTypeCode');
#       my $bondInfoID = $ENGINE->{'CGI'}->param('bondInfoID');
#       my %bondTypeCodes = CLM_BOND_INFO__BOND_TYPE();
#       if (defined($bondTypeCodes{$bondType}) && $bondType ne '')
#       {
#           $bondInfoData->{'BOND_TYPE'} = $bondType;
#           if(defined($bondInfoID) && $bondInfoID gt '')
#           { $bondInfoData->{'CLM_BOND_INFO_ID'} = $bondInfoID; }
#           $bondInfoSW = 'Y';
#       }
#       else
#       {
#           if ($UserType eq 'Internal' && $ManualOrWhat eq 'M')
#           {
#               push(@{$errors{'BOND_TYPE'}},'Invalid Bond Type selected.');
#               $return=0;
#               $bondInfoData->{'errBondType'} = $errorDiv;
#           }
#       }
#       push(@{$ENGINE->{'PropLiab'}->{'CLM_BOND_INFO'}},$bondInfoData);
#   }
#   else
#   {
#       $bondInfoData->{'BOND_TYPE'} = ' ';
#   }
#   $bondInfoData->{'CLAIM_ID'} = $claimid;
#   push(@{$ENGINE->{'PropLiab'}->{'CLM_BOND_INFO'}},$bondInfoData);

   my $z = 0;
   my %params = $ENGINE->{'CGI'}->Vars();
#   my $error = $ENGINE->{'error'};

   for my $key (keys %params)
   {
#      if ($key =~ /^lossLocText(\d+)$/ || $key =~ /^lossLocText(new\d+)$/)
#      {
#         my $varOtherLossLocID = $1;
#         my $varLossLocData = {};
#         my $lossLocText = $ENGINE->{'CGI'}->param('lossLocText'.$varOtherLossLocID)||'';
##         my $lossLocSel = $ENGINE->{'CGI'}->param('lossLocList'.$varOtherLossLocID)||'';

##         if (defined($lossLocSel) && $lossLocSel eq 'OT')
##         {
#            if (defined($lossLocText) && $lossLocText ne '')
#            {
#               my $locRows = VARDATAreadInput($ENGINE,{'name'=>'lossLocText'.$varOtherLossLocID,'keys'=>{'LINE_TYPE'=>'L','DATA_TYPE'=>'LOCATION'}});
#               push(@{$ENGINE->{'PropLiab'}->{'CLM_VARDATA'}},@$locRows);
#            }
#            else
#            {
#               push(@{$errors{'lossLocation'}},'Invalid Loss Location entered.'); $return=0;
#            }
##         }
#      }
      if ($key =~ /^policyLocIDCS(\d+)$/ || $key =~ /^policyLocIDCS(new\d+)$/)
      {
         my $policyLocID = $1;
         my $propStatData = {};
         my $liabStatData = {};
         my $liabStatSW = 'N';
         $policyLocationID = $ENGINE->{'CGI'}->param('policyLocIDCS'.$policyLocID)||'';

         my $workKey = 'coveragesList' . $key . '_';
         my $workKey2 = 'coveragesList' . $key;
         my $s = 1;
         for my $key2 (sort keys %params)
         {

             if(($LineCode =~ /112|113|120|100|105/ && $ENGINE->{'CGI'}->param('propLiabRadio') ne 'L') ||
             ($LineCode =~ /200|205|330|331|332|300|301|302|575|810|814|816/))
             {
                 my $policyLocIDLen = length($policyLocID);
                 if (substr($key2, 0, 13) eq 'coveragesList' && $policyLocID eq substr($key2, 13, $policyLocIDLen)) {
                     my $policyLocID2 = substr($key2, 13, $policyLocIDLen);

                     my $coverageSelected = $ENGINE->{'CGI'}->param($key2) || '';
                     my @values = split('_', $coverageSelected);
                     my $coverage_ID = $values[0];
                     my $lossGroup = $values[1];

                     my $assignmentData = {};
                     ###The substring for lossCodesList and COLList need to be 13 because we are getting the coverageID from coveragesList parm above
                     my $lossCode = $ENGINE->{'CGI'}->param('lossCodesList' . substr($key2, 13)) || '';
                     my $COLCode = $ENGINE->{'CGI'}->param('COLList' . substr($key2, 13)) || '';

                     my $coverror = 'N';
                     if (!($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /350|360|303|550/)) {
                         if ($ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} =~ /P|M/ ) {
                             if (defined($coverageSelected) && $coverageSelected eq 'XX_XX') {
                                 $coverror = 'Y';
                             }
                             else {
                                 $assignmentData->{'CLAIM_ID'} = $claimid;
                                 $assignmentData->{'COMMON_STAT_ID'} = $policyLocID2;
                                 $assignmentData->{'COVERAGE_ID'} = $coverage_ID;
                                 $assignmentData->{'LOSS_CODE_GROUP'} = $lossGroup;
                                 if (defined($ENGINE->{'CGI'}->param('assignmentID' . substr($key2, 13))) && $ENGINE->{'CGI'}->param('assignmentID' . substr($key2, 13)) gt '')
                                 {$assignmentData->{'ASSIGNMENT_ID'} = $ENGINE->{'CGI'}->param('assignmentID' . substr($key2, 13)) || '';}
                             }

                             if (defined($lossCode) && $lossCode eq 'XX_XX') {
                                 $coverror = 'Y';
                             }
                             else {
                                 $assignmentData->{'CLAIM_ID'} = $claimid;
                                 $assignmentData->{'COMMON_STAT_ID'} = $policyLocID2;
                                 $assignmentData->{'TYPE_OF_LOSS'} = substr($lossCode, 0, 2);
                             }

                             if($ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'} =~ /112|113|120/)
                             {
                                 if (defined($COLCode) && $COLCode eq 'XX') {
                                     $coverror = 'Y';
                                 }
                                 else {
                                     $assignmentData->{'CLAIM_ID'} = $claimid;
                                     $assignmentData->{'COMMON_STAT_ID'} = $policyLocID2;
                                     $assignmentData->{'IMT_CAUSE_OF_LOSS'} = $COLCode;
                                 }
                             }
                             if($coverror eq 'N')
                             { push(@{$ENGINE->{'PropLiab'}->{'CLM_ASSIGNMENT_FIELDS'}}, $assignmentData); }
                         }
                     }
                 }
             }
         }

         if (defined($policyLocationID) && $policyLocationID ne '')
         {
            my $outdoorLighting = $ENGINE->{'CGI'}->param('outdoorLighting'.$policyLocID)||'';

            if (defined($outdoorLighting) && $outdoorLighting eq 'Y')
            {
               $propStatData->{'AUTO_OUTSIDE_LIGHT'} = 'Y';
            }
            elsif(defined($outdoorLighting) && $outdoorLighting eq 'N')
            {
               $propStatData->{'AUTO_OUTSIDE_LIGHT'} = 'N';
            }
            elsif(defined($outdoorLighting) && $outdoorLighting eq 'U')
            {
                $propStatData->{'AUTO_OUTSIDE_LIGHT'} = 'U';
            }
            else
            {
                $propStatData->{'AUTO_OUTSIDE_LIGHT'} = '';
            }

            $propStatData->{'CLM_COMMON_STAT_ID'} = $policyLocationID;


            ### LINE CODE 100,110,111,112,113,120 ##############################

            # Interior Upkeep
            if ($LineCode =~ /100|110|111|112|113|120/)
            {
               my $interiorUpkeep = $ENGINE->{'CGI'}->param('iupkeep'.$policyLocID)||'';
               if (defined($iUpkeep{$interiorUpkeep}) && $interiorUpkeep ne '')
               {
                  $propStatData->{'INTERIOR_UPKEEP'} = $interiorUpkeep;
               }
               else
               {
                  if ($UserType eq 'Internal' && $ManualOrWhat eq 'M')
                  {
                     push(@{$errors{'iupkeep'.$policyLocID}},'<li><div class="errorInfo">Invalid interior upkeep selection.</div></li>');
                     $return=0;
                     $propStatData->{'errInternalUpkeep'.$policyLocID} = '<li><div class="errorInfo">Invalid interior upkeep selection.</div></li>';
                  }
               }
            }
            else
            {
               $propStatData->{'INTERIOR_UPKEEP'} = '';
            }

            # Exterior Upkeep
            if ($LineCode =~ /100|110|111|112|113|120/)
            {
               my $exteriorUpkeep = $ENGINE->{'CGI'}->param('eupkeep'.$policyLocID)||'';
               if (defined($eUpkeep{$exteriorUpkeep}) && $exteriorUpkeep ne '')
               {
                  $propStatData->{'EXTERIOR_UPKEEP'} = $exteriorUpkeep;
               }
               else
               {
                  if ($UserType eq 'Internal' && $ManualOrWhat eq 'M')
                  {
                     push(@{$errors{'eupkeep'.$policyLocID}},'<li><div class="errorInfo">Invalid exterior upkeep selection.</div></li>');
                     $return=0;
                     $propStatData->{'errExteriorUpkeep'.$policyLocID} = '<li><div class="errorInfo">Invalid exterior upkeep selection.</div></li>';
                  }
               }
            }
            else
            {
               $propStatData->{'EXTERIOR_UPKEEP'} = '';
            }

            # Yard Upkeep
            if ($LineCode =~ /100|110|111|112|113|120/)
            {
               my $yardUpkeep = $ENGINE->{'CGI'}->param('yupkeep'.$policyLocID)||'';
               if(defined($yUpkeep{$yardUpkeep}) && $yardUpkeep ne '')
               {
                  $propStatData->{'YARD_UPKEEP'} = $yardUpkeep;
               }
               else
               {
                  if ($UserType eq 'Internal' && $ManualOrWhat eq 'M')
                  {
                     push(@{$errors{'yupkeep'.$policyLocID}},'<li><div class="errorInfo">Invalid yard upkeep selection.</div></li>');
                     $return=0;
                     $propStatData->{'errYardUpkeep'.$policyLocID} = '<li><div class="errorInfo">Invalid yard upkeep selection.</div></li>';
                  }
               }
            }
            else
            {
               $propStatData->{'YARD_UPKEEP'} = '';
            }


            ### LINE CODE 100,105,112,113,120 ##################################

            # Ordinance or Law
            if ($LineCode =~ /100|105|112|113|120/ && $ManualOrWhat eq 'M')
            {
               my $OrdLawInd = $ENGINE->{'CGI'}->param('ordLawInd'.$policyLocID);
               if ((defined($OrdLawInd)) && ($OrdLawInd ne ''))
               {
                  $propStatData->{'ORDINANCE_OR_LAW'} = 'Y';
               }
               else
               {
                  $propStatData->{'ORDINANCE_OR_LAW'} = 'N';
               }
            }
#            else
#            {
#               $propStatData->{'ORDINANCE_OR_LAW'} = 'N';
#            }



            # Construction Code
            if ($LineCode =~ /100|105|112|113|200|205|575|580/ && $ManualOrWhat eq 'M')
            {
               my $ConstCode = $ENGINE->{'CGI'}->param('constType'.$policyLocID);
               my %constCodes = CLM_PROPERTY_STAT__CONST_CODE($LineCode);
               if (defined($constCodes{$ConstCode}) && $ConstCode ne '')
               {
                  $propStatData->{'CONST_CODE'} = $ConstCode;
               }
               else
               {
                  if ($UserType eq 'Internal' && $ManualOrWhat eq 'M')
                  {
                     push(@{$errors{'constType'.$policyLocID}},'<li><div class="errorInfo">Invalid construction code selection.</div></li>');
                     $return=0;
                     $propStatData->{'errConstCode'.$policyLocID} = '<li><div class="errorInfo">Invalid construction code selection.</div></li>';
                  }
               }
            }
#            else
#            {
#               $propStatData->{'CONST_CODE'} = '';
#            }

            ### LINE CODE 100 ##################################################

            # Residence Type
            if ($LineCode =~ /100/ && $ManualOrWhat eq 'M')
            {
               my $ResidenceTypeCode = $ENGINE->{'CGI'}->param('residenceType'.$policyLocID);
               my %residenceTypeCodes = CLM_PROPERTY_STAT__DF_RESIDENCE_TYPE();
               if (defined($residenceTypeCodes{$ResidenceTypeCode}) && $ResidenceTypeCode ne '')
               {
                  $propStatData->{'DF_RESIDENCE_TYPE'} = $ResidenceTypeCode;
               }
               else
               {
                  if ($UserType eq 'Internal' && $ManualOrWhat eq 'M')
                  {
                     push(@{$errors{'residenceType'.$policyLocID}},'<li><div class="errorInfo">Invalid residence Type selection.</div></li>');
                     $return=0;
                     $propStatData->{'errDFResidenceType'.$policyLocID} = '<li><div class="errorInfo">Invalid residence Type selection.</div></li>';
                  }
               }
            }
#            else
#            {
#               $propStatData->{'DF_RESIDENCE_TYPE'} = '';
#            }

            # Occupancy Code
            if ($LineCode =~ /100/ && $ManualOrWhat eq 'M')
            {
               my $OccupancyCode = $ENGINE->{'CGI'}->param('occupancyType'.$policyLocID);
               my %occupancyCodes = CLM_PROPERTY_STAT__CF_OCCUPANCY_CODE();
               if (defined($occupancyCodes{$OccupancyCode}) && $OccupancyCode ne '')
               {
                  $propStatData->{'CF_OCCUPANCY_CODE'} = $OccupancyCode;
               }
               else
               {
                  if ($UserType eq 'Internal' && $ManualOrWhat eq 'M')
                  {
                     push(@{$errors{'occupancyType'.$policyLocID}},'<li><div class="errorInfo">Invalid occupancy code selection.</div></li>');
                     $return=0;
                     $propStatData->{'errCFOccupancyCode'.$policyLocID} = '<li><div class="errorInfo">Invalid occupancy code selection.</div></li>';
                  }
               }
            }
#            else
#            {
#               $propStatData->{'CF_OCCUPANCY_CODE'} = '';
#            }

            # Fire Detector
#            if ($LineCode =~ /100/)
#            {
#               my $FireDetectorInd = $ENGINE->{'CGI'}->param('fireDetectorInd'.$policyLocID);
#               if (defined($FireDetectorInd) && $FireDetectorInd ne '')
#               {
#                  $propStatData->{'DF_FIRE_DETECT'} = 'Y';
#               }
#               else
#               {
#                  $propStatData->{'DF_FIRE_DETECT'} = 'N';
#               }
#            }
#            else
#            {
#               $propStatData->{'DF_FIRE_DETECT'} = 'N';
#            }
            if ($LineCode =~ /100/ && $ManualOrWhat eq 'M')
            {
               my $FireDetectorCode = $ENGINE->{'CGI'}->param('fireDetectorType'.$policyLocID);
               my %fireDetectorCodes = CLM_PROPERTY_STAT__DF_FIRE_DETECT();
               if (defined($fireDetectorCodes{$FireDetectorCode}) && $FireDetectorCode ne '')
               {
                  $propStatData->{'DF_FIRE_DETECT'} = $FireDetectorCode;
               }
               else
               {
                  if ($UserType eq 'Internal' && $ManualOrWhat eq 'M')
                  {
                     push(@{$errors{'fireDetectorType'.$policyLocID}},'<li><div class="errorInfo">Invalid fire detector selection.</div></li>');
                     $return=0;
                     $propStatData->{'errDFFireDetect'.$policyLocID} = '<li><div class="errorInfo">Invalid fire detector selection.</div></li>';
                  }
               }
            }
#            else
#            {
#               $propStatData->{'DF_FIRE_DETECT'} = 'N';
#            }

            # Burglar Alarm
            if ($LineCode =~ /100/ && $ManualOrWhat eq 'M')
            {
               my $BurglarAlarmCode = $ENGINE->{'CGI'}->param('burglarAlarmType'.$policyLocID);
               my %burglarAlarmCodes = CLM_PROPERTY_STAT__DF_BURGLR_ALARM();
               if (defined($burglarAlarmCodes{$BurglarAlarmCode}) && $BurglarAlarmCode ne '')
               {
                  $propStatData->{'DF_BURGLR_ALARM'} = $BurglarAlarmCode;
               }
               else
               {
                  if ($UserType eq 'Internal' && $ManualOrWhat eq 'M')
                  {
                     push(@{$errors{'burglarAlarmType'.$policyLocID}},'<li><div class="errorInfo">Invalid burglar alarm selection.</div></li>');
                     $return=0;
                     $propStatData->{'errDFFireDetect'.$policyLocID} = '<li><div class="errorInfo">Invalid burglar alarm selection.</div></li>';
                  }
               }
            }
#            else
#            {
#               $propStatData->{'DF_BURGLR_ALARM'} = 'N';
#            }
#            if ($LineCode =~ /100/)
#            {
#               my $BurglarAlarmInd = $ENGINE->{'CGI'}->param('burglarAlarmInd'.$policyLocID);
#               if (defined($BurglarAlarmInd) && $BurglarAlarmInd ne '')
#               {
#                  $propStatData->{'DF_BURGLR_ALARM'} = 'Y';
#               }
#               else
#               {
#                  $propStatData->{'DF_BURGLR_ALARM'} = 'N';
#               }
#            }
#            else
#            {
#               $propStatData->{'DF_BURGLR_ALARM'} = 'N';
#            }

            # Primary Heat Source
            if ($LineCode =~ /100/ && $ManualOrWhat eq 'M')
            {
               my $PrimaryHeatCode = $ENGINE->{'CGI'}->param('primaryHeatType'.$policyLocID);
               my %primaryHeatCodes = CLM_PROPERTY_STAT__DF_PRIMARY_HEAT();
               if (defined($primaryHeatCodes{$PrimaryHeatCode}) && $PrimaryHeatCode ne '')
               {
                  $propStatData->{'DF_PRIMARY_HEAT'} = $PrimaryHeatCode;
               }
               else
               {
                  if ($UserType eq 'Internal' && $ManualOrWhat eq 'M')
                  {
                     push(@{$errors{'primaryHeatType'.$policyLocID}},'<li><div class="errorInfo">Invalid primary heat selection.</div></li>');
                     $return=0;
                     $propStatData->{'errDFPrimaryHeat'.$policyLocID} = '<li><div class="errorInfo">Invalid primary heat selection.</div></li>';
                  }
               }
            }
#            else
#            {
#               $propStatData->{'DF_PRIMARY_HEAT'} = '';
#            }

            # Builder's Risk
            if ($LineCode =~ /100/ && $ManualOrWhat eq 'M')
            {
               my $BuildRiskInd = $ENGINE->{'CGI'}->param('buildRiskInd'.$policyLocID);
               if ((defined($BuildRiskInd)) && ($BuildRiskInd ne ''))
               {
                  $propStatData->{'DF_BUILDERS_RISK'} = 'Y';
               }
               else
               {
                  $propStatData->{'DF_BUILDERS_RISK'} = 'N';
               }
            }
#            else
#            {
#               $propStatData->{'DF_BUILDERS_RISK'} = '';
#            }

            # Smoke Detector
            if ($LineCode =~ /100/ && $ManualOrWhat eq 'M')
            {
               my $SmokeDetectorCode = $ENGINE->{'CGI'}->param('smokeDetectorType'.$policyLocID);
               my %smokeDetectorCodes = CLM_PROPERTY_STAT__DF_SMOKE_DETECT();
               if (defined($smokeDetectorCodes{$SmokeDetectorCode}) && $SmokeDetectorCode ne '')
               {
                  $propStatData->{'DF_SMOKE_DETECT'} = $SmokeDetectorCode;
               }
               else
               {
                  if ($UserType eq 'Internal' && $ManualOrWhat eq 'M')
                  {
                     push(@{$errors{'smokeDetectorType'.$policyLocID}},'<li><div class="errorInfo">Invalid smoke detector selection.</div></li>');
                     $return=0;
                     $propStatData->{'errDFSmokeDetect'.$policyLocID} = '<li><div class="errorInfo">Invalid smoke detector selection.</div></li>';
                  }
               }
            }
#            else
#            {
#               $propStatData->{'DF_SMOKE_DETECT'} = 'N';
#            }

            # Liability Only indicator
            if ($LineCode =~ /100/ && $ManualOrWhat eq 'M')
            {
               my $LiabOnlyInd = $ENGINE->{'CGI'}->param('liabOnlyInd'.$policyLocID);
               if (defined($LiabOnlyInd) && $LiabOnlyInd ne '')
               {
                  $propStatData->{'DF_LIAB_ONLY_IND'} = 'Y';
               }
               else
               {
                   $propStatData->{'DF_LIAB_ONLY_IND'} = 'N';
               }
            }
#            else
#            {
#               $propStatData->{'DF_LIAB_ONLY_IND'} = 'N';
#            }


            ### LINE CODE 100,105 ##############################################

            # Number of Stories - Earthquake
            if ($LineCode =~ /100|105/ && $ManualOrWhat eq 'M')
            {
               my $EQstories = $ENGINE->{'CGI'}->param('eqStories'.$policyLocID);
               if (defined($EQstories) && $EQstories ne '')
               {
                  $propStatData->{'DF_EQ_STORIES'} = $EQstories;
               }
               else
               {
                  if ($UserType eq 'Internal' && $ManualOrWhat eq 'M')
                  {
                     push(@{$errors{'eqStories'.$policyLocID}},'<li><div class="errorInfo">Invalid number of stories (earthquake).</div></li>');
                     $return=0;
                     $propStatData->{'errDFEQStories'.$policyLocID} = '<li><div class="errorInfo">Invalid number of stories (earthquake).</div></li>';
                  }
               }
            }
#            else
#            {
#               $propStatData->{'DF_EQ_STORIES'} = '';
#            }



            ### LINE CODE 105 ##################################################

            # Vacant Code
            if ($LineCode =~ /105/ && $ManualOrWhat eq 'M')
            {
               my $VacantCode = $ENGINE->{'CGI'}->param('vacantType'.$policyLocID);
               my %vacantCodes = CLM_PROPERTY_STAT__CF_OCCUPANCY_CODE();
               if (defined($vacantCodes{$VacantCode}) && $VacantCode ne '')
               {
                  $propStatData->{'OCCUP_SIZE_LOC'} = $VacantCode;
               }
               else
               {
                  if ($UserType eq 'Internal' && $ManualOrWhat eq 'M')
                  {
                     push(@{$errors{'vacantType'.$policyLocID}},'<li><div class="errorInfo">Invalid vacancy selection.</div></li>');
                     $return=0;
                     $propStatData->{'errOccupSizeLoc'.$policyLocID} = '<li><div class="errorInfo">Invalid vacancy selection.</div></li>';
                  }
               }
            }
#            else
#            {
#               $propStatData->{'OCCUP_SIZE_LOC'} = '';
#            }

            # Crime Off Premises
            if ($LineCode =~ /105/ && $ManualOrWhat eq 'M')
            {
               my $CrimeOffPremCode = $ENGINE->{'CGI'}->param('crimeOffPremType'.$policyLocID);
               my %crimeOffPremCodes = CLM_PROPERTY_STAT__CF_CRIME_OFFP();
               if (defined($crimeOffPremCodes{$CrimeOffPremCode}) && $CrimeOffPremCode ne '')
               {
                  $propStatData->{'CF_CRIME_OFFP'} = $CrimeOffPremCode;
               }
               else
               {
                  if ($UserType eq 'Internal' && $ManualOrWhat eq 'M')
                  {
                     push(@{$errors{'crimeOffPremType'.$policyLocID}},'<li><div class="errorInfo">Invalid crime off premises selection.</div></li>');
                     $return=0;
                     $propStatData->{'errCFCrimeOffp'.$policyLocID} = '<li><div class="errorInfo">Invalid crime off premises selection.</div></li>';
                  }
               }
            }
#            else
#            {
#               $propStatData->{'CF_CRIME_OFFP'} = '000';
#            }


            # EC Symbol
            if ($LineCode =~ /105/ && $ManualOrWhat eq 'M')
            {
               my $ECsymbolCode = $ENGINE->{'CGI'}->param('ecSymbolType'.$policyLocID);
               my %ecSymbolCodes = CLM_PROPERTY_STAT__CF_EC_SYMBOL();
               if (defined($ecSymbolCodes{$ECsymbolCode}) && $ECsymbolCode ne '')
               {
                  $propStatData->{'CF_EC_SYMBOL'} = $ECsymbolCode;
               }
               else
               {
                  if ($UserType eq 'Internal' && $ManualOrWhat eq 'M')
                  {
                     push(@{$errors{'ecSymbolType'.$policyLocID}},'<li><div class="errorInfo">Invalid EC symbol selection.</div></li>');
                     $return=0;
                     $propStatData->{'errCFECSymbol'.$policyLocID} = '<li><div class="errorInfo">Invalid EC symbol selection.</div></li>';
                  }
               }
            }
#            else
#            {
#               $propStatData->{'CF_EC_SYMBOL'} = 'B';
#            }


            # Percent of Monthly Limit
            if ($LineCode =~ /105/ && $ManualOrWhat eq 'M')
            {
               my $PctMonthlyLmtCode = $ENGINE->{'CGI'}->param('pctMonthlyLmtType'.$policyLocID);
               my %pctMonthlyLmtCodes = CLM_PROPERTY_STAT__CF_EXTRAEXP_MTHLIM();
               if (defined($pctMonthlyLmtCodes{$PctMonthlyLmtCode}) && $PctMonthlyLmtCode ne '')
               {
                  $propStatData->{'CF_EXTRAEXP_MTHLIM'} = $PctMonthlyLmtCode;
               }
               else
               {
                  if ($UserType eq 'Internal' && $ManualOrWhat eq 'M')
                  {
                     push(@{$errors{'pctMonthlyLmtType'.$policyLocID}},'<li><div class="errorInfo">Invalid percent of monthly limit selection.</div></li>');
                     $return=0;
                     $propStatData->{'errCFExtraexpMthlim'.$policyLocID} = '<li><div class="errorInfo">Invalid percent of monthly limit selection.</div></li>';
                  }
               }
            }
#            else
#            {
#               $propStatData->{'CF_EXTRAEXP_MTHLIM'} = '';
#            }

            # Manual Rate Indicator
            if ($LineCode =~ /105/ && $ManualOrWhat eq 'M')
            {
               my $ManualRateCode = $ENGINE->{'CGI'}->param('manualRateType'.$policyLocID);
               my %manualRateCodes = CLM_PROPERTY_STAT__CF_MANUAL_RATE_IND();
               if (defined($manualRateCodes{$ManualRateCode}) && $ManualRateCode ne '')
               {
                  $propStatData->{'CF_MANUAL_RATE_IND'} = $ManualRateCode;
               }
               else
               {
                  push(@{$errors{'manualRateType'.$policyLocID}},'<li><div class="errorInfo">Invalid manual rate selection.</div></li>'); $return=0;
                  $propStatData->{'errCFManualRateInd'.$policyLocID} = '<li><div class="errorInfo">Invalid manual rate selection.</div></li>';
               }
            }
#            else
#            {
#               $propStatData->{'CF_MANUAL_RATE_IND'} = '0';
#            }


            # Earthquake Sprinkler Leakage Susceptibility
            if ($LineCode =~ /105/ && $ManualOrWhat eq 'M')
            {
               my $EQsprinklerLeakCode = $ENGINE->{'CGI'}->param('eqSprinklerLeakType'.$policyLocID);
               my %eqSprinklerLeakCodes = CLM_PROPERTY_STAT__CF_SPRINKLEAK_SUS();
               if (defined($eqSprinklerLeakCodes{$EQsprinklerLeakCode}) && $EQsprinklerLeakCode ne '')
               {
                  $propStatData->{'CF_SPRINKLEAK_SUS'} = $EQsprinklerLeakCode;
               }
               else
               {
                  if ($UserType eq 'Internal' && $ManualOrWhat eq 'M')
                  {
                     push(@{$errors{'eqSprinklerLeakType'.$policyLocID}},'<li><div class="errorInfo">Invalid sprinkler leakage selection.</div></li>');
                     $return=0;
                     $propStatData->{'errCFSprinkleakSus'.$policyLocID} = '<li><div class="errorInfo">Invalid sprinkler leakage selection.</div></li>';
                  }
               }
            }
#            else
#            {
#               $propStatData->{'CF_SPRINKLEAK_SUS'} = '';
#            }


            # Theft Coverage Indicator
            if ($LineCode =~ /105/ && $ManualOrWhat eq 'M')
            {
               my $TheftCovInd = $ENGINE->{'CGI'}->param('theftCovInd'.$policyLocID);
               if ((defined($TheftCovInd)) && ($TheftCovInd ne ''))
               {
                  $propStatData->{'CF_THEFT_COV_IND'} = 'Y';
               }
               else
               {
                  $propStatData->{'CF_THEFT_COV_IND'} = 'N';
               }
            }
#            else
#            {
#               $propStatData->{'CF_THEFT_COV_IND'} = 'N';
#            }

            # Theft Contents Coverage Indicator
            if ($LineCode =~ /105/ && $ManualOrWhat eq 'M')
            {
               my $TheftContInd = $ENGINE->{'CGI'}->param('theftContInd'.$policyLocID);
               if (defined($TheftContInd) && $TheftContInd ne '')
               {
                  $propStatData->{'CF_THFT_CONTCV_IND'} = 'Y';
               }
               else
               {
                  $propStatData->{'CF_THFT_CONTCV_IND'} = 'N';
               }
            }
#            else
#            {
#               $propStatData->{'CF_THFT_CONTCV_IND'} = 'N';
#            }

            # Special Rate Indicator
            if ($LineCode =~ /105/ && $ManualOrWhat eq 'M')
            {
               my $SpecialRateInd = $ENGINE->{'CGI'}->param('specialRateInd'.$policyLocID);
               if ((defined($SpecialRateInd)) && ($SpecialRateInd ne ''))
               {
                  $propStatData->{'CF_SPEC_RATE_IND'} = 'Y';
               }
               else
               {
                  $propStatData->{'CF_SPEC_RATE_IND'} = 'N';
               }
            }
#            else
#            {
#               $propStatData->{'CF_SPEC_RATE_IND'} = 'N';
#            }

            # Sprinkler Leakage Indicator
            if ($LineCode =~ /105/ && $ManualOrWhat eq 'M')
            {
               my $SprinklerLeakInd = $ENGINE->{'CGI'}->param('sprinklerLeakInd'.$policyLocID);
               if ((defined($SprinklerLeakInd)) && ($SprinklerLeakInd ne ''))
               {
                  $propStatData->{'CF_SPRINK_LEAK_IND'} = 'Y';
               }
               else
               {
                  $propStatData->{'CF_SPRINK_LEAK_IND'} = 'N';
               }
            }
#            else
#            {
#               $propStatData->{'CF_SPRINK_LEAK_IND'} = 'N';
#            }


            # Contents
            if ($LineCode =~ /105/ && $ManualOrWhat eq 'M')
            {
               my $ContentsInd = $ENGINE->{'CGI'}->param('contentsInd'.$policyLocID);
               if ((defined($ContentsInd)) && ($ContentsInd ne ''))
               {
                  $propStatData->{'CF_CONTENTS_IND'} = 'Y';
               }
               else
               {
                  $propStatData->{'CF_CONTENTS_IND'} = 'N';
               }
            }
#            else
#            {
#               $propStatData->{'CF_CONTENTS_IND'} = 'N';
#            }


            # Reinsurance
            if ($LineCode =~ /105/ && $ManualOrWhat eq 'M')
            {
               my $ReinsurInd = $ENGINE->{'CGI'}->param('reinsurInd'.$policyLocID);
               if ((defined($ReinsurInd)) && ($ReinsurInd ne ''))
               {
                  $propStatData->{'CF_REINS_IND'} = 'Y';
               }
               else
               {
                  $propStatData->{'CF_REINS_IND'} = 'N';
               }
            }
#            else
#            {
#               $propStatData->{'CF_REINS_IND'} = 'N';
#            }

            # Sprinkler Credit
            if ($LineCode =~ /105/ && $ManualOrWhat eq 'M')
            {
               my $SprinklerCreditInd = $ENGINE->{'CGI'}->param('sprinklerCreditInd'.$policyLocID);
               if (defined($SprinklerCreditInd) && $SprinklerCreditInd ne '')
               {
                  $propStatData->{'CF_SPRINK_CRED_IND'} = 'Y';
               }
               else
               {
                   $propStatData->{'CF_SPRINK_CRED_IND'} = 'N';
               }
            }
#            else
#            {
#               $propStatData->{'CF_SPRINK_CRED_IND'} = 'N';
#            }

            # Fire Legal Additional Charge
            if ($LineCode =~ /105/ && $ManualOrWhat eq 'M')
            {
               my $FirLegAdtlChgInd = $ENGINE->{'CGI'}->param('firLegAdtlChgInd'.$policyLocID);
               if ((defined($FirLegAdtlChgInd)) && ($FirLegAdtlChgInd ne ''))
               {
                  $propStatData->{'CF_FIRLEG_ADTLCHG'} = 'Y';
               }
               else
               {
                  $propStatData->{'CF_FIRLEG_ADTLCHG'} = 'N';
               }
            }
#            else
#            {
#               $propStatData->{'CF_FIRLEG_ADTLCHG'} = 'N';
#            }

            # Fire Legal Type
            if ($LineCode =~ /105/ && $ManualOrWhat eq 'M')
            {
               my $FireLegalCode = $ENGINE->{'CGI'}->param('fireLegalType'.$policyLocID);
               my %fireLegalCodes = CLM_PROPERTY_STAT__CF_LEGALIAB_TYPCOD();
               if (defined($fireLegalCodes{$FireLegalCode}) && $FireLegalCode ne '')
               {
                  $propStatData->{'CF_LEGALIAB_TYPCOD'} = $FireLegalCode;
               }
               else
               {
                  if ($UserType eq 'Internal' && $ManualOrWhat eq 'M')
                  {
                     push(@{$errors{'fireLegalType'.$policyLocID}},'Invalid fire legal type selection.');
                     $return=0;
                     $propStatData->{'errLegaliabTypcod'.$policyLocID} = $errorDiv;
                  }
               }
            }
#            else
#            {
#               $propStatData->{'CF_LEGALIAB_TYPCOD'} = '';
#            }

            # Protective Device
            if ($LineCode =~ /105|580/ && $ManualOrWhat eq 'M')
            {
               my $ProtectDeviceCode = $ENGINE->{'CGI'}->param('protectDeviceType'.$policyLocID);
               my %protectDeviceCodes = CLM_PROPERTY_STAT__PROTECTIVE_DEV($lossDate10);
               if (defined($protectDeviceCodes{$ProtectDeviceCode}) && $ProtectDeviceCode gt ' ')
               {
                  $propStatData->{'PROTECTIVE_DEV'} = $ProtectDeviceCode;
               }
               else
               {
                  if ($UserType eq 'Internal' && $ManualOrWhat eq 'M')
                  {
                     push(@{$errors{'protectDeviceType'.$policyLocID}},'<li><div class="errorInfo">Invalid protective device selection.</div></li>');
                     $return=0;
                     $propStatData->{'errProtectiveDev'.$policyLocID} = '<li><div class="errorInfo">Invalid protective device selection.</div></li>';
                  }
               }
            }
#            else
#            {
#               $propStatData->{'PROTECTIVE_DEV'} = '0';
#            }

            # Package Code
            if ($LineCode =~ /105/ && $ManualOrWhat eq 'M')
            {
               my $PackageCode = $ENGINE->{'CGI'}->param('CPPackageCode'.$policyLocID);
               my %packageCodes = getCPPackageCodes();
               if (defined($packageCodes{$PackageCode}) && $PackageCode ne '')
               {
                  $propStatData->{'PACKAGE_CODE'} = $PackageCode;
               }
               else
               {
                  if ($UserType eq 'Internal' && $ManualOrWhat eq 'M')
                  {
                     push(@{$errors{'CPPackageCode'.$policyLocID}},'<li><div class="errorInfo">Invalid package program code selection.</div></li>');
                     $return=0;
                     $propStatData->{'errCPPackageCode'.$policyLocID} = '<li><div class="errorInfo">Invalid package program code selection.</div></li>';
                  }
               }
            }
#            else
#            {
#               $propStatData->{'PACKAGE_CODE'} = '';
#            }

            # Theft Risk Code
            if ($LineCode =~ /105/ && $ManualOrWhat eq 'M')
            {
               my $theftRiskCode = $ENGINE->{'CGI'}->param('theftRiskCode'.$policyLocID);
               my %theftRiskCodes = getTheftRiskCodes();
               if (defined($theftRiskCodes{$theftRiskCode}) && $theftRiskCode ne '')
               {
                  $propStatData->{'CF_THEFT_RISK_CODE'} = $theftRiskCode;
               }
               else
               {
                  if ($UserType eq 'Internal' && $ManualOrWhat eq 'M')
                  {
                     push(@{$errors{'theftRiskCode'.$policyLocID}},'<li><div class="errorInfo">Invalid Theft Risk Code selection.</div></li>');
                     $return=0;
                     $propStatData->{'errTheftRiskCode'.$policyLocID} = '<li><div class="errorInfo">Invalid Theft Risk Code selection.</div></li>';
                  }
               }
            }
#            else
#            {
#               $propStatData->{'CF_THEFT_RISK_CODE'} = '';
#            }


            ### LINE CODE 112,113 ##############################################

            # Pellet Heating
            if ($LineCode =~ /112|113/ && $ManualOrWhat eq 'M')
            {
               my $PelletHeatCode = $ENGINE->{'CGI'}->param('pelletHeatType'.$policyLocID);
               my %pelletHeatCodes = CLM_PROPERTY_STAT__HO_PELLET_HEATING();
               if (defined($pelletHeatCodes{$PelletHeatCode}) && $PelletHeatCode ne '')
               {
                  $propStatData->{'HO_PELLET_HEATING'} = $PelletHeatCode;
               }
               else
               {
                  if ($UserType eq 'Internal' && $ManualOrWhat eq 'M')
                  {
                     push(@{$errors{'pelletHeatType'.$policyLocID}},'<li><div class="errorInfo">Invalid pellet heat selection.</div></li>');
                     $return=0;
                     $propStatData->{'errHOPelletHeating'.$policyLocID} = '<li><div class="errorInfo">Invalid pellet heat selection.</div></li>';
                  }
               }
            }
#            else
#            {
#               $propStatData->{'HO_PELLET_HEATING'} = '';
#            }


            # Townhouse/Rowhouse Indicator
            if ($LineCode =~ /112|113/ && $ManualOrWhat eq 'M')
            {
               my $TownRowInd = $ENGINE->{'CGI'}->param('townRowInd'.$policyLocID);
               if ((defined($TownRowInd)) && ($TownRowInd ne ''))
               {
                  $propStatData->{'HO_TOWNROW_IND'} = 'Y';
               }
               else
               {
                  $propStatData->{'HO_TOWNROW_IND'} = 'N';
               }
            }
#            else
#            {
#               $propStatData->{'HO_TOWNROW_IND'} = 'N';
#            }

            # Number of Units
            if ($LineCode =~ /112|113/ && $ManualOrWhat eq 'M')
            {
               my $NumOfUnits = $ENGINE->{'CGI'}->param('numOfUnits'.$policyLocID);
               if ((defined($NumOfUnits)) && ($NumOfUnits ne '' && $NumOfUnits ne 'N/A'))
               {
                  $propStatData->{'HO_NUM_UNITS'} = $NumOfUnits;
               }
               else
               {
                  if ($UserType eq 'Internal' && $ManualOrWhat eq 'M' && $ENGINE->{'claimGeneral'}->{'POLICY_FORM'} eq '4')
                  {
                     push(@{$errors{'numOfUnits'.$policyLocID}},'<li><div class="errorInfo">Invalid number of units.</div></li>');
                     $return=0;
                     $propStatData->{'errHONumUnits'.$policyLocID} = '<li><div class="errorInfo">Invalid number of units.</div></li>';
                  }
               }
            }
#            else
#            {
#               $propStatData->{'HO_NUM_UNITS'} = '';
#            }

            # Gem Indicator
            if ($LineCode =~ /112|113/ && $ManualOrWhat eq 'M')
            {
               my $gemInd = $ENGINE->{'CGI'}->param('gemInd'.$policyLocID);
               if ((defined($gemInd)) && ($gemInd ne ''))
               {
                  $propStatData->{'HO_GEM_IND'} = 'Y';
               }
               else
               {
                  $propStatData->{'HO_GEM_IND'} = 'N';
               }
            }
#            else
#            {
#               $propStatData->{'HO_GEM_IND'} = 'N';
#            }

            ### LINE CODE 112,113,120 ##########################################

            # Loss Settlement Type
            if ($LineCode =~ /112|113|120/ && $ManualOrWhat eq 'M')
            {
               my $LossSettleCode = $ENGINE->{'CGI'}->param('lossSettleType'.$policyLocID);
               my %lossSettleCodes = CLM_PROPERTY_STAT__LOSS_SETTLEMENT_TYPE();
               if (defined($lossSettleCodes{$LossSettleCode}) && $LossSettleCode ne '')
               {
                  $propStatData->{'LOSS_SETTLEMENT_TYPE'} = $LossSettleCode;
               }
               else
               {
                  if ($UserType eq 'Internal' && $ManualOrWhat eq 'M')
                  {
                     push(@{$errors{'lossSettleType'.$policyLocID}},'<li><div class="errorInfo">Invalid loss Settlement type selection.</div></li>');
                     $return=0;
                     $propStatData->{'errLossSettlementType'.$policyLocID} = '<li><div class="errorInfo">Invalid loss Settlement type selection.</div></li>';
                  }
               }
            }
#            else
#            {
#               $propStatData->{'LOSS_SETTLEMENT_TYPE'} = '';
#            }

            ### LINE CODE 112,113,120,200,205,575,580 ##########################################

            # Fair Plan Indicator
            if ($LineCode =~ /112|113|120|200|205|575|580/ && $ManualOrWhat eq 'M')
            {
               my $fairPlanInd = $ENGINE->{'CGI'}->param('fairPlanInd'.$policyLocID);
               if ((defined($fairPlanInd)) && ($fairPlanInd ne ''))
               {
                  $propStatData->{'FAIR_PLAN_IND'} = 'Y';
               }
               else
               {
                  $propStatData->{'FAIR_PLAN_IND'} = 'N';
               }
            }
#            else
#            {
#               $propStatData->{'FAIR_PLAN_IND'} = '';
#            }


            ### LINE CODE 100,110,111,112,113 ##################################

            # Protection Code
            if ($LineCode =~ /100|105|110|111|112|113|200|205|580/ && $ManualOrWhat eq 'M')
            {
               my $ProtectCode = $ENGINE->{'CGI'}->param('protectCodeType'.$policyLocID);
               my %protectCodes = CLM_PROPERTY_STAT__PROTECT_CODE($LineCode);
               if (defined($protectCodes{$ProtectCode}) && $ProtectCode ne '')
               {
                  $propStatData->{'PROTECT_CODE'} = $ProtectCode;
               }
               else
               {
                  push(@{$errors{'protectCodeType'.$policyLocID}},'<li><div class="errorInfo">Invalid protection code selection.</div></li>'); $return=0;
                  $propStatData->{'errProtectCode'.$policyLocID} = '<li><div class="errorInfo">Invalid protection code selection.</div></li>';
               }
            }
#            else
#            {
#               $propStatData->{'PROTECT_CODE'} = '00';
#            }

            ### LINE CODE 100,112,113,120 ######################################

            # Year Built
            if ($LineCode =~ /100|112|113|120|580/ && $ManualOrWhat eq 'M')
            {
               my $YearBuilt = $ENGINE->{'CGI'}->param('yearBuilt'.$policyLocID);
               if (defined($YearBuilt) && $YearBuilt ne '')
               {
                  $propStatData->{'YEAR_BUILT'} = $YearBuilt;
               }
               else
               {
                  if ($UserType eq 'Internal' && $ManualOrWhat eq 'M')
                  {
                     push(@{$errors{'yearBuilt'.$policyLocID}},'<li><div class="errorInfo">Invalid construction year.</div></li>');
                     $return=0;
                     $propStatData->{'errYearBuild'.$policyLocID} = '<li><div class="errorInfo">Invalid construction year.</div></li>';
                  }
               }
            }
#            else
#            {
#               $propStatData->{'YEAR_BUILT'} = '';
#            }

            # Seasonal Occupancy
            if ($LineCode =~ /100|112|113|120/ && $ManualOrWhat eq 'M')
            {
               my $SeasonalInd = $ENGINE->{'CGI'}->param('seasonalInd'.$policyLocID);
               if (defined($SeasonalInd) && $SeasonalInd ne '')
               {
                  $propStatData->{'HO_PRIM_SEC_SEAS'} = 'Y';
               }
               else
               {
                   $propStatData->{'HO_PRIM_SEC_SEAS'} = 'N';
               }
            }
#            else
#            {
#               $propStatData->{'HO_PRIM_SEC_SEAS'} = '';
#            }

            # Number of Families
            if ($LineCode =~ /100|112|113|120/ && $ManualOrWhat eq 'M')
            {
               my $NumOfFamilies = $ENGINE->{'CGI'}->param('numOfFamilies'.$policyLocID);
               if (defined($NumOfFamilies) && $NumOfFamilies ne '')
               {
                  $propStatData->{'NUMBER_OF_FAMILIES'} = int($NumOfFamilies);
               }
               else
               {
                  if ($UserType eq 'Internal' && $ManualOrWhat eq 'M')
                  {
                     push(@{$errors{'numOfFamilies'.$policyLocID}},'<li><div class="errorInfo">Invalid number of families.</div></li>');
                     $return=0;
                     $propStatData->{'errNumberOfFamiles'.$policyLocID} = '<li><div class="errorInfo">Invalid number of families.</div></li>';
                  }
               }
            }
#            else
#            {
#               $propStatData->{'NUMBER_OF_FAMILIES'} = '0';
#            }

            ### LINE CODE 120 ##################################################

            # Mobile Home Tie Downs
            if ($LineCode =~ /120/ && $ManualOrWhat eq 'M')
            {
               my $MHtieDownCode = $ENGINE->{'CGI'}->param('mhTieDownType'.$policyLocID);
               my %mhTieDownCodes = CLM_PROPERTY_STAT__MH_TIE_DOWN();
               if (defined($mhTieDownCodes{$MHtieDownCode}) && $MHtieDownCode ne '')
               {
                  $propStatData->{'MH_TIE_DOWN'} = $MHtieDownCode;
               }
               else
               {
                  if ($UserType eq 'Internal' && $ManualOrWhat eq 'M')
                  {
                     push(@{$errors{'mhTieDownType'.$policyLocID}},'<li><div class="errorInfo">Invalid mobile home tie down selection.</div></li>');
                     $return=0;
                     $propStatData->{'errMHTieDown'.$policyLocID} = '<li><div class="errorInfo">Invalid mobile home tie down selection.</div></li>';
                  }
               }
            }
#            else
#            {
#               $propStatData->{'MH_TIE_DOWN'} = '';
#            }

            # Mobile Home Width
            if ($LineCode =~ /120/ && $ManualOrWhat eq 'M')
            {
               my $MHwidth = $ENGINE->{'CGI'}->param('mhWidth'.$policyLocID);
               if (defined($MHwidth) && $MHwidth ne '')
               {
                  $propStatData->{'MH_WIDTH'} = $MHwidth;
               }
               else
               {
                  if ($UserType eq 'Internal' && $ManualOrWhat eq 'M')
                  {
                     push(@{$errors{'mhWidth'.$policyLocID}},'<li><div class="errorInfo">Invalid mobile home width.</div></li>');
                     $return=0;
                     $propStatData->{'errMHWidth'.$policyLocID} = '<li><div class="errorInfo">Invalid mobile home width.</div></li>';
                  }
               }
            }
#            else
#            {
#               $propStatData->{'MH_WIDTH'} = '';
#            }

            # Mobile Home Length
            if ($LineCode =~ /120/ && $ManualOrWhat eq 'M')
            {
               my $MHlength = $ENGINE->{'CGI'}->param('mhLength'.$policyLocID);
               if (defined($MHlength) && $MHlength ne '')
               {
                  $propStatData->{'MH_LENGTH'} = $MHlength;
               }
               else
               {
                  if ($UserType eq 'Internal' && $ManualOrWhat eq 'M')
                  {
                     push(@{$errors{'mhLength'.$policyLocID}},'<li><div class="errorInfo">Invalid mobile home length.</div></li>');
                     $return=0;
                     $propStatData->{'errMHLength'.$policyLocID} = '<li><div class="errorInfo">Invalid mobile home length.</div></li>';
                  }
               }
            }
#            else
#            {
#               $propStatData->{'MH_LENGTH'} = '';
#            }

            ### LINE CODE 100,575,580 ##########################################

            # Sprinkler System
            if ($LineCode =~ /100|575|580/ && $ManualOrWhat eq 'M')
            {
               my $SprinklerInd = $ENGINE->{'CGI'}->param('sprinklerInd'.$policyLocID);
               if ((defined($SprinklerInd)) && $SprinklerInd ne '')
               {
                  $propStatData->{'SPRINKLER_IND'} = 'Y';
               }
               else
               {
                  $propStatData->{'SPRINKLER_IND'} = 'N';
               }
            }
#            else
#            {
#               $propStatData->{'SPRINKLER_IND'} = 'N';
#            }

            ### LINE CODE 200,205 ##############################################

            # Restriction Indicator
            if ($LineCode =~ /200|205/ && $ManualOrWhat eq 'M')
            {
               my $RestrictInd = $ENGINE->{'CGI'}->param('restrictInd'.$policyLocID);
               if (defined($RestrictInd) && $RestrictInd ne '')
               {
                  $propStatData->{'IM_RESCTRICT_OR_NOT'} = '1';
               }
               else
               {
                   $propStatData->{'IM_RESCTRICT_OR_NOT'} = '2';
               }
            }
#            else
#            {
#               $propStatData->{'IM_RESCTRICT_OR_NOT'} = '';
#            }

            ### LINE CODE 300,301,302,330,331,332 ##############################

            # Total Acres
            if ($LineCode =~ /300|301|302|330|331|332/ && $ManualOrWhat eq 'M')
            {
               my $totalAcres = $ENGINE->{'CGI'}->param('totalAcres'.$policyLocID);
               if (defined($totalAcres) && $totalAcres ne '')
               {
                  $liabStatData->{'FL_EXPOSURE_TOTAL_ACRES'} = $totalAcres;
                  $liabStatSW = 'Y';
               }
               else
               {
                  if ($UserType eq 'Internal' && $ManualOrWhat eq 'M')
                  {
                     push(@{$errors{'totalAcres'.$policyLocID}},'<li><div class="errorInfo">Invalid Total Policy Acres.</div></li>');
                     $return=0;
                     $liabStatData->{'errTotalAcres'.$policyLocID} = '<li><div class="errorInfo">Invalid Total Policy Acres.</div></li>';
                  }
               }
            }
#            else
#            {
#               $liabStatData->{'FL_EXPOSURE_TOTAL_ACRES'} = '';
#            }

            # Liability Rate Code
            if ($LineCode =~ /300|301|302|330|331|332/ && $ManualOrWhat eq 'M')
            {
               my $liabRateCode = $ENGINE->{'CGI'}->param('liabRateCode'.$policyLocID);
               my %liabRateCodes = getLiabRateCodes();
               if (defined($liabRateCodes{$liabRateCode}) && $liabRateCode ne '')
               {
                  $liabStatData->{'FL_RATE_CODE'} = $liabRateCode;
                  $liabStatSW = 'Y';
               }
               else
               {
                  if ($UserType eq 'Internal' && $ManualOrWhat eq 'M')
                  {
                     push(@{$errors{'liabRateCode'.$policyLocID}},'<li><div class="errorInfo">Invalid Liability Rate Code selected.</div></li>');
                     $return=0;
                     $liabStatData->{'errLiabRateCode'.$policyLocID} = '<li><div class="errorInfo">Invalid Liability Rate Code selected.</div></li>';
                  }
               }
            }
#            else
#            {
#               $liabStatData->{'FL_RATE_CODE'} = '';
#            }

            # Livestock Exclusion
            if ($LineCode =~ /300|301|302|330|331|332/ && $ManualOrWhat eq 'M')
            {
               my $livestockExcInd = $ENGINE->{'CGI'}->param('livestockExcInd'.$policyLocID);
               if (defined($livestockExcInd) && $livestockExcInd ne '')
               {
                  $liabStatData->{'FL_LIVESTOCK_EXCL'} = 'Y';
                  $liabStatSW = 'Y';
               }
               else
               {
                   $liabStatData->{'FL_LIVESTOCK_EXCL'} = 'N';
                   $liabStatSW = 'Y';
               }
            }
#            else
#            {
#               $liabStatData->{'FL_LIVESTOCK_EXCL'} = '';
#            }

            # Umbrella FH type
            if ($LineCode =~ /350/ && $ManualOrWhat eq 'M')
            {
               my $umbrellaFHType = $ENGINE->{'CGI'}->param('umbrellaFHCode'.$policyLocID);
               my %umbrella_FHCodes = CLM_LIAB_STAT__UMBRELLA_FH();
               if (defined($umbrella_FHCodes{$umbrellaFHType}) && $umbrellaFHType ne ' ')
               {
                  $liabStatData->{'UMBRELLA_FH'} = $umbrellaFHType;
               }
               else
               {
                  if ($UserType eq 'Internal' && $ManualOrWhat eq 'M')
                  {
                     push(@{$errors{'umbrellaFHCode'.$policyLocID}},'<li><div class="errorInfo">Invalid Umbrella Type selected.</div></li>');
                     $return=0;
                     $liabStatData->{'errUmbrellaFHType'.$policyLocID} = '<li><div class="errorInfo">Invalid Umbrella Type selected.</div></li>';
                  }
               }
            }


            ### LINE CODE 575 ##################################################

            # Owner/Lessor Indicator
            if ($LineCode =~ /575/ && $ManualOrWhat eq 'M')
            {
               my $OwnerLessorInd = $ENGINE->{'CGI'}->param('ownerLessorInd'.$policyLocID);
               if ((defined($OwnerLessorInd)) && ($OwnerLessorInd ne ''))
               {
                  $propStatData->{'BOART_OWN_LESS_IND'} = 'Y';
               }
               else
               {
                  $propStatData->{'BOART_OWN_LESS_IND'} = 'N';
               }
            }
#            else
#            {
#               $propStatData->{'BOART_OWN_LESS_IND'} = '';
#            }

            # Type Of Pak
#            if ($LineCode =~ /575/ && $ManualOrWhat eq 'M')
#            {
               my $typeOfPak = $ENGINE->{'CGI'}->param('packageCodeType'.$policyLocID);
#               my %typeOfPakCodes = CLM_PROPERTY_STAT__BOART_TYPE_OF_PAK();
#               if (defined($typeOfPakCodes{$typeOfPak}) && $typeOfPak ne '')
#               {
                  $propStatData->{'BOART_TYPE_OF_PAK'} = $typeOfPak;
#               }
#               else
#               {
#                  if ($UserType eq 'Internal' && $ManualOrWhat eq 'M')
#                  {
#                     push(@{$errors{'packageCodeType'.$policyLocID}},'Invalid Type of Pak selected.');
#                     $return=0;
#                     $propStatData->{'errTypeOfPak'.$policyLocID} = $errorDiv;
#                  }
#               }
#            }
#            else
#            {
#               $propStatData->{'BOART_TYPE_OF_PAK'} = '';
#            }

            ### LINE CODE 575,580 ##############################################

            # Automatic Increase indicator
            if ($LineCode =~ /580/ && $ManualOrWhat eq 'M')
            {
               my $AutoIncreaseInd = $ENGINE->{'CGI'}->param('autoIncreaseInd'.$policyLocID);
               if (defined($AutoIncreaseInd) && $AutoIncreaseInd ne '')
               {
                  $propStatData->{'ART_AUTO_INCREASE'} = 'Y';
               }
               else
               {
                   $propStatData->{'ART_AUTO_INCREASE'} = 'N';
               }
            }
#            else
#            {
#               $propStatData->{'ART_AUTO_INCREASE'} = '';
#            }

            # AAIS Form
            if ($LineCode =~ /575|580/ && $ManualOrWhat eq 'M')
            {
               my $AAISForm = $ENGINE->{'CGI'}->param('aaisForm'.$policyLocID);
               my %AAISFormCodes = CLM_PROPERTY_STAT__BOART_AAIS_FORM();
               if (defined($AAISFormCodes{$AAISForm}) && $AAISForm ne '')
               {
                  $propStatData->{'BOART_AAIS_FORM'} = $AAISForm;
               }
               else
               {
                  if ($UserType eq 'Internal' && $ManualOrWhat eq 'M')
                  {
                     push(@{$errors{'aaisForm'.$policyLocID}},'<li><div class="errorInfo">Invalid AAIS Form selected.</div></li>');
                     $return=0;
                     $propStatData->{'errAAISForm'.$policyLocID} = '<li><div class="errorInfo">Invalid AAIS Form selected.</div></li>';
                  }
               }
            }
#            else
#            {
#               $propStatData->{'BOART_AAIS_FORM'} = '';
#            }


            ### LINE CODE 810,814,816 ##########################################

#            # Rating ID
#            if ($LineCode =~ /810|814|816/)
#            {
#               my $RatingIDCode = $ENGINE->{'CGI'}->param('ratingIDType'.$policyLocID);
#               my %ratingIDCodes = CLM_PROPERTY_STAT__BOART_RATE_IDENT();
#               if (defined($ratingIDCodes{$RatingIDCode}) && $RatingIDCode ne '')
#               {
#                  $propStatData->{'BOART_RATE_IDENT'} = $RatingIDCode;
#               }
#               else
#               {
#                  if ($UserType eq 'Internal' && $ManualOrWhat eq 'M')
#                  {
#                     push(@{$errors{'BOART_RATE_IDENT'}},'Invalid rating ID selection.');
#                     $return=0;
#                     $propStatData->{'errBoartRateIdent'} = $errorDiv;
#                  }
#               }
#            }
#            else
#            {
#               $propStatData->{'BOART_RATE_IDENT'} = '';
#            }

            # Package Code
            if ($LineCode =~ /810|814|816/ && $ManualOrWhat eq 'M')
            {
               my $PackageCode = $ENGINE->{'CGI'}->param('GLPackageCode'.$policyLocID);
               my %packageCodes = CLM_LIAB_STAT__PK_PROG_CODE();
               if (defined($packageCodes{$PackageCode}) && $PackageCode ne ' ')
               {
                  $liabStatData->{'PK_PROG_CODE'} = $PackageCode;
                  $liabStatSW = 'Y';
               }
               else
               {
                  if ($UserType eq 'Internal' && $ManualOrWhat eq 'M')
                  {
                     push(@{$errors{'GLPackageCode'.$policyLocID}},'<li><div class="errorInfo">Invalid package program code selection.</div></li>');
                     $return=0;
                     $liabStatData->{'errPKProgCode'.$policyLocID} = '<li><div class="errorInfo">Invalid package program code selection.</div></li>';
                  }
               }
            }
#            else
#            {
#               $liabStatData->{'PK_PROG_CODE'} = '';
#            }


            # Entity Code (ENTITY_CODE is not a field on CLM_PROPERTY_STAT table)
#            if ($LineCode =~ /810|814|816/)
#            {
#               my $EntityCode = $ENGINE->{'CGI'}->param('entityCodeType'.$policyLocID);
#               my %entityCodes = CLM_PARTIES__ENTITY_CODE();
#               if (defined($entityCodes{$EntityCode}) && $EntityCode ne '')
#               {
#                  $propStatData->{'ENTITY_CODE'} = $EntityCode;
#               }
#               else
#               {
#                  $propStatData->{'ENTITY_CODE'} = '';
#               }
#            }
#            else
#            {
#               $propStatData->{'ENTITY_CODE'} = '';
#            }

            ### ALL LINES ######################################################

            # Wood Stove
            if($ManualOrWhat eq 'M')
            {
                    my $WoodStoveInd = $ENGINE->{'CGI'}->param('woodStoveInd'.$policyLocID);
                    if ((defined($WoodStoveInd)) && ($WoodStoveInd ne ''))
                    {
                       $propStatData->{'WOOD_STOVE'} = 'Y';
                    }
                    else
                    {
                       $propStatData->{'WOOD_STOVE'} = 'N';
                    }
            }

            # Swimming Pool
            if($ManualOrWhat eq 'M')
            {
                    my $SwimPoolInd = $ENGINE->{'CGI'}->param('swimPoolInd'.$policyLocID);
                    if ((defined($SwimPoolInd)) && ($SwimPoolInd ne ''))
                    {
                       $propStatData->{'SWIM_POOL'} = 'Y';
                    }
                    else
                    {
                       $propStatData->{'SWIM_POOL'} = 'N';
                    }
            }

            #roof replacement
            if($LineCode =~ /100|105|112|113|575/ && $ENGINE->{'claimGeneral'}->{'PROP_OR_LIAB'} =~ /P|B/)
            {
               if(defined($ENGINE->{'CGI'}->param('roofReplaced'.$policyLocID)))
               {
                  my $locationData = {};
                  $locationData->{'CLM_COMMON_STAT_ID'} = $policyLocationID;
                  $locationData->{'LOCATION_ID'} = $ENGINE->{'CGI'}->param('policyLocIDL'.$policyLocID);
                  $locationData->{'ROOF_REPLACED'} = $ENGINE->{'CGI'}->param('roofReplaced'.$policyLocID) || '';
                  if($ENGINE->{'CGI'}->param('roofReplaced'.$policyLocID) eq 'Y')
                  {
                     my ($day,$mon,$currentYear) = (localtime())[3..5];
                     $currentYear+=1900;
                     my $yearRoofReplaced = $ENGINE->{'CGI'}->param('yearRoofReplaced'.$policyLocID);
                     if($yearRoofReplaced ne '' &&
                        ($yearRoofReplaced < 0
                        || !($yearRoofReplaced =~ /^\d{4}$/)))
                     {push(@{$errors{'yearRoofReplaced'.$policyLocID}},'Invalid year.');}
                     elsif($yearRoofReplaced > $currentYear)
                     {push(@{$errors{'yearRoofReplaced'.$policyLocID}},'Year cannot be in the future.');}
                     elsif($yearRoofReplaced ne '' && $yearRoofReplaced < 1900)
                     {push(@{$errors{'yearRoofReplaced'.$policyLocID}},'Year cannot be before 1900.');}
                     else
                     {$locationData->{'YEAR_ROOF_REPLACED'} = $ENGINE->{'CGI'}->param('yearRoofReplaced'.$policyLocID) || '';}

                     my $depreciationAmt = $ENGINE->{'CGI'}->param('depreciationAmt'.$policyLocID);
                     $depreciationAmt =~s/\$//g;
                     if((defined $depreciationAmt && $depreciationAmt ne '' && $depreciationAmt != 0 && !( $depreciationAmt =~/^[0-9]+(\.*[0-9][0-9])?$/))
                        || $depreciationAmt =~ /[A-Za-z]+/)
                     {push(@{$errors{'depreciationAmt'.$policyLocID}},'Invalid dollar amount.');}
                     else
                     {
                        if($depreciationAmt eq '')
                        {$depreciationAmt = undef;}
                        $locationData->{'DEPRECIATE_AMOUNT'} = $depreciationAmt;
                     }
                  }
                  push(@{$ENGINE->{'PropLiab'}->{'CLM_LOCATION'}},$locationData);
               }
            }

#            push(@{$ENGINE->{'PropLiab'}->{'CLM_PROPERTY_STAT'}},$propStatData);

            if($LineCode =~ /300|301|302|330|331|332|350|360|810|811|812|813|814|815|816|817|818/)
            {
                $liabStatData->{'CLM_COMMON_STAT_ID'} = $policyLocationID;
                push(@{$ENGINE->{'PropLiab'}->{'CLM_LIAB_STAT'}},$liabStatData);
            }
            else
            {
                push(@{$ENGINE->{'PropLiab'}->{'CLM_PROPERTY_STAT'}},$propStatData);
            }
         }
      }
#      elsif ($key =~ /^ilossLocID(\d+)$/ || $key =~ /^ilossLocID(new\d+)$/)
#      {
#         my $lossLocID = $1;
#         my $lossLocData = {};
#         $policyLocationID = $ENGINE->{'CGI'}->param('policyLocIDCS'.$lossLocID);

#         if (defined($policyLocationID) && $policyLocationID ne '')
#         {
#            $lossLocData->{'CLM_COMMON_STAT_ID'} = $policyLocationID;
#         }
#         else
#         {
#            $lossLocData->{'CLM_COMMON_STAT_ID'} = 'new'.0;
#         }

#         my $locationNum = 0;
#         my $unitNum = 0;
#         my $itemNum = 0;
#         my $lossLocSel = $ENGINE->{'CGI'}->param('lossLocList'.$lossLocID);
#         if ($LineCode =~ /110|111|112|113|120|300|301|302/)
#         {
#             $locationNum = $lossLocSel;
#             $unitNum = 0;
#         }
#         elsif($LineCode =~ /575/)
#         {
#             $locationNum = substr($lossLocSel,0,2);
##             $locationNum = $lossLocSel;
#             $unitNum = substr($lossLocSel,2,2);
##             print ' losslocsel '.$lossLocSel;
##             print ' locationnumL '.$locationNum;
#         }
#         elsif($LineCode =~ /810|811|812|813|815|816/)
#         {
#             $locationNum = substr($lossLocSel,0,4);
#             $unitNum = 0;
##             $unitNum = substr($lossLocSel,2,2);
#         }
#         elsif($LineCode =~ /105/)
#         {
#             $locationNum = substr($lossLocSel,0,2);
#             $unitNum = substr($lossLocSel,2,2);
##             $itemNum = substr($lossLocSel,4,2);
#         }


#         if (defined($lossLocSel) && $lossLocSel ne 'OT')
#         {
#            if (defined($lossLocSel) && $lossLocSel ne '')
#            {
##               print ' losslocid '.$lossLocID;
#               $lossLocData->{'LOCATION_ID'} = $lossLocID;
#               $lossLocData->{'LOCATION_NO'} = $locationNum;
#               $lossLocData->{'UNIT_NO'} = $unitNum;
##               $lossLocData->{'ITEM_NO'} = $itemNum;
#               $lossLocData->{'LOC_TYPE'} = 'LL';
#               push(@{$ENGINE->{'PropLiab'}->{'CLM_COMMON_STAT'}},$lossLocData);
#            }
#            else
#            {
#               push(@{$errors{'lossLocation'}},'Invalid Loss Location entered.'); $return=0;
#            }
#         }
#      }
      elsif($key =~ /^ipolLocID(\d+)$/ || $key =~ /^ipolLocID(new\d+)$/)
      {
         $z++;
         my $initiationPoint = $ENGINE->{'claimGeneral'}->{'INITIATION_POINT'};
         my $policyLocID = $1;
         my $policyLocData = {};
         my $otherLocData = {};
         my $otherPropStatData = {};
         my $otherLiabStatData = {};

         $policyLocationID = $ENGINE->{'CGI'}->param('policyLocIDCS'.$policyLocID);

         if (defined($policyLocationID) && $policyLocationID ne '')
         {
            $policyLocData->{'CLM_COMMON_STAT_ID'} = $policyLocationID;
            $otherLocData->{'CLM_COMMON_STAT_ID'} = $policyLocationID;
         }
         else
         {
            if($initiationPoint eq 'CV' && $z == 1 && $LineCode =~ /350|360/ && $holdCommonStatID > 0)
            {
                $policyLocData->{'CLM_COMMON_STAT_ID'} = $holdCommonStatID;
                $otherLocData->{'CLM_COMMON_STAT_ID'} = $holdCommonStatID;
                $otherLiabStatData->{'CLM_COMMON_STAT_ID'} = $holdCommonStatID;
            }
            else
            {
                $policyLocData->{'CLM_COMMON_STAT_ID'} = $policyLocID;
                $otherLocData->{'CLM_COMMON_STAT_ID'} = $policyLocID;
            }
         }

         if ($ManualOrWhat ne 'M')
         {
            my $policyLocSel = $ENGINE->{'CGI'}->param('polLocList'.$policyLocID);
            my $locationNum = 0;
            my $unitNum = 0;
            my $itemNum = 0;
#                my $lossLocSel = $ENGINE->{'CGI'}->param('lossLocList'.$lossLocID);
            if ($LineCode =~ /100|110|111|112|113|120|300|301|302|330|331|332/)
            {
                $locationNum = $policyLocSel;
                $unitNum = 0;
            }
            elsif($LineCode =~ /575|580|200|205|810|811|812|813|814|815|816|105/)
            {
                my @values = split('_',$policyLocSel);
                $locationNum = $values[0];
                $unitNum = $values[1];
            }
#            elsif($LineCode =~ /575|580|200|205/)
#            {
#            if($policyLocSel gt '')
#            {
#                    $locationNum = substr($policyLocSel,0,2);
#                    $unitNum = substr($policyLocSel,2,2);
#                }
#            }
#            elsif($LineCode =~ /810|811|812|813|814|815|816/)
#            {
#                if($policyLocSel gt '')
#                {
#                    $locationNum = substr($policyLocSel,0,4);
#                    $unitNum = substr($policyLocSel,4,4);
##                    $unitNum = substr($lossLocSel,2,2);
#                }
#            }
#            elsif($LineCode =~ /105/)
#            {
#                if($policyLocSel gt '')
#                {
#                    $locationNum = substr($policyLocSel,0,2);
#                    $unitNum = substr($policyLocSel,2,2);
##                    $itemNum = substr($policyLocSel,4,2);
#                }
#            }

#            print ' policylocsel '.$policyLocSel;
#            print ' locationnumP '.$locationNum;
            if (defined($policyLocSel) && $policyLocSel ne '')
            {
#               print ' policylocid '.$policyLocID;
               $policyLocData->{'LOCATION_ID'} = $policyLocID;
               $policyLocData->{'LOCATION_NO'} = $locationNum;
               $policyLocData->{'UNIT_NO'} = $unitNum;
#               $policyLocData->{'ITEM_NO'} = $itemNum;
               $policyLocData->{'LOC_TYPE'} = 'AC';
               push(@{$ENGINE->{'PropLiab'}->{'CLM_COMMON_STAT'}},$policyLocData);
            }
            else
            {
#               push(@{$errors{'polLocList'.$policyLocID}},'Invalid Policy Location entered.'); $return=0;
#               $policyLocData->{'errPolicyLocation'} = $errorDiv;
               $policyLocData->{'LOCATION_NO'} = '';
               $policyLocData->{'UNIT_NO'} = '';
               push(@{$ENGINE->{'PropLiab'}->{'CLM_COMMON_STAT'}},$policyLocData);
            }
         }
         else
         {
            my $policyLocSel = $ENGINE->{'CGI'}->param('polLocList'.$policyLocID);
            $holdLocNo++;
            my $othpolLocLocN = $holdLocNo;
            my $othpolLocUnitN = 0;
#            my $othpolLocLocN = $ENGINE->{'CGI'}->param('othPolLocLocN'.$policyLocID);
#            my $othpolLocUnitN = $ENGINE->{'CGI'}->param('othPolLocUnitN'.$policyLocID);
            my $othpolLocAdd1 = $ENGINE->{'CGI'}->param('othPolLocAdd1'.$policyLocID);
            my $othpolLocAdd2 = $ENGINE->{'CGI'}->param('othPolLocAdd2'.$policyLocID);
            my $othpolLocCity = $ENGINE->{'CGI'}->param('othPolLocCity'.$policyLocID);
            my $othpolLocState = $ENGINE->{'CGI'}->param('othPolLocState'.$policyLocID);
            my $othpolLocZip = $ENGINE->{'CGI'}->param('othPolLocZip'.$policyLocID);

            if (defined($othpolLocZip) && $othpolLocZip ne '')
            {
               $lossDate = $ENGINE->{'claimGeneral'}->{'LOSS_DATE_TIME'};
               if (length($lossDate) >= 10)
               {
                  $lossDate = substr($lossDate,0,4).substr($lossDate,5,2).substr($lossDate,8,2);
               }

               my $claimLineCode = $ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'};

               my %lineCodes = getIMTPolicyPrefix();
               for my $key (keys %lineCodes)
               {
                  if ($key eq $claimLineCode)
                  {
                     $claimLineCode = $lineCodes{$key};
                     if($claimLineCode eq 'BO')
                     { $claimLineCode = 'BOP'; }
                     if($claimLineCode eq 'GL')
                     { $claimLineCode = 'GLS'; }
                     if($claimLineCode eq 'CP')
                     { $claimLineCode = 'CPS'; }
                     if($claimLineCode eq 'HM')
                     { $claimLineCode = 'HO'; }
                     if($claimLineCode eq 'MB')
                     { $claimLineCode = 'HO'; }
                  }
               }

               my $zipQuery = $ENGINE->{'DBH'}->prepare(
                  'SELECT ZT.TERRITORY,
                          ZC.COUNTYCODE,
                          ZC.CITYCODE
                     FROM GENSUPDB.ZIPTERRZONE ZT
                     LEFT OUTER JOIN GENSUPDB.ZIPCOUNTYCITY ZC ON ZC.ZIPCODE = ZT.ZIPCODE
                    WHERE ZT.EFF_DATE IN
                             (SELECT MAX(EFF_DATE) AS MAX_DATE
                                FROM GENSUPDB.ZIPTERRZONE
                               WHERE ZIPCODE = ?
                                 AND LOB_CODE = ?)
                      AND ZC.EFF_DATE IN
                             (SELECT MAX(EFF_DATE) AS MAX_DATE
                                FROM GENSUPDB.ZIPCOUNTYCITY
                               WHERE ZIPCODE = ?)
                                 AND ZT.ZIPCODE = ?
                                 AND ZT.LOB_CODE = ?
                                 AND ? > ZT.EFF_DATE ') || $error->($ENGINE,'Zip Code data query prepare failed: '.$ENGINE->{'DBH'}->errstr);

               $zipQuery->execute($othpolLocZip,$claimLineCode,$othpolLocZip,$othpolLocZip,$claimLineCode,$lossDate) || $error->($ENGINE,'Zip Code data query execute failed: '.$ENGINE->{'DBH'}->errstr);

               my $zipResults = $zipQuery->fetchall_arrayref({});

               if(scalar(@$zipResults) > 0)
               {
                       for my $z (@$zipResults)
                       {
                          if (defined($z->{'TERRITORY'}) && $z->{'TERRITORY'} gt '')
                          {
                             $policyLocData->{'TERRITORY'} = $z->{'TERRITORY'};
                          }
                          if (defined($z->{'COUNTYCODE'}) && $z->{'COUNTYCODE'} gt '')
                          {
                             $policyLocData->{'COUNTY_CODE'} = $z->{'COUNTYCODE'};
                          }
                          if (defined($z->{'CITYCODE'}) && $z->{'CITYCODE'} gt '')
                          {
                             $policyLocData->{'CITY_SUBCNTY'} = $z->{'CITYCODE'};
                          }
                       }
               }
               else
               {
                       my $zipCityQuery = $ENGINE->{'DBH'}->prepare(
                          'SELECT COUNTYCODE,
                                  CITYCODE
                             FROM GENSUPDB.ZIPCOUNTYCITY
                             WHERE ZIPCODE = ?') || $error->($ENGINE,'Zip City Code data query prepare failed: '.$ENGINE->{'DBH'}->errstr);

                   $zipCityQuery->execute($othpolLocZip) || $error->($ENGINE,'Zip Code data query execute failed: '.$ENGINE->{'DBH'}->errstr);

                       my $zipCityResults = $zipCityQuery->fetchall_arrayref({});

                       for my $z (@$zipCityResults)
                       {
                          $policyLocData->{'TERRITORY'} = '';
                          if (defined($z->{'COUNTYCODE'}) && $z->{'COUNTYCODE'} gt '')
                          {
                             $policyLocData->{'COUNTY_CODE'} = $z->{'COUNTYCODE'};
                          }
                          if (defined($z->{'CITYCODE'}) && $z->{'CITYCODE'} gt '')
                          {
                             $policyLocData->{'CITY_SUBCNTY'} = $z->{'CITYCODE'};
                          }
                       }
               }
            }

            if(!defined($policyLocData->{'TERRITORY'}))
            { $policyLocData->{'TERRITORY'} = ' ' }
            if(!defined($policyLocData->{'COUNTY_CODE'}))
            { $policyLocData->{'COUNTY_CODE'} = ' ' }
            if(!defined($policyLocData->{'CITY_SUBCNTY'}))
            { $policyLocData->{'CITY_SUBCNTY'} = ' ' }

            if (defined($policyLocSel) && $policyLocSel eq 'OT')
            {
               $policyLocData->{'LOC_TYPE'} = 'AC';
               $otherLocData->{'LOC_TYPE'} = 'AC';
               $policyLocData->{'LOCATION_ID'} = $policyLocID;
               $otherLocData->{'LOCATION_ID'} = $policyLocID;

               if (defined($othpolLocLocN) && $othpolLocLocN !~ /[^0-9]/)
               {
                  $policyLocData->{'LOCATION_NO'} = $othpolLocLocN;
               }
               else
               {
                  push(@{$errors{'othPolLocLocN'}},'<li><div class="errorInfo">Invalid Other Policy Location entered.</div></li>'); $return=0;
                  $policyLocData->{'errPolLocN'} = '<li><div class="errorInfo">Invalid Other Policy Location entered.</div></li>';
                  $policyLocData->{'LOCATION_NO'} = $othpolLocLocN;
               }

               if (defined($othpolLocUnitN) && $othpolLocUnitN !~ /[^0-9]/)
               {
                  $policyLocData->{'UNIT_NO'} = $othpolLocUnitN;
               }
               else
               {
                  push(@{$errors{'othPolLocUnitN'}},'<li><div class="errorInfo">Invalid Other Policy Unit entered.</div></li>'); $return=0;
                  $policyLocData->{'errPolLocUnitN'} = '<li><div class="errorInfo">Invalid Other Policy Unit entered.</div></li>';
                  $policyLocData->{'UNIT_NO'} = $othpolLocUnitN;
               }

               if (defined($othpolLocZip) && $othpolLocZip ne '')
               {
                  $policyLocData->{'ZIP_CODE'} = $othpolLocZip;
               }
               else
               {
                  push(@{$errors{'othPolLocZip'}},'<li><div class="errorInfo">Invalid Other Policy Zip Code entered.</div></li>'); $return=0;
                  $policyLocData->{'errPolLocZip'} = '<li><div class="errorInfo">Invalid Other Policy Zip Code entered.</div></li>';
                  $policyLocData->{'ZIP_CODE'} = $othpolLocZip;
               }

               $policyLocData->{'MANUAL_AUTOMATIC'} = 'M';
               $otherLocData->{'LOC_TYPE'} = 'AC';

               if (defined($othpolLocAdd1) && $othpolLocAdd1 ne '')
               {
                  $otherLocData->{'ADDRESS1'} = uc($othpolLocAdd1);
               }
               else
               {
                  push(@{$errors{'othPolLocAdd1'}},'<li><div class="errorInfo">Invalid Other Policy Address 1 entered.</div></li>'); $return=0;
                  $otherLocData->{'errPolLocAdd1'} = '<li><div class="errorInfo">Invalid Other Policy Address 1 entered.</div></li>';
                  $otherLocData->{'ADDRESS1'} = $othpolLocAdd1;
               }

               if (defined($othpolLocAdd2) && $othpolLocAdd2 ne '')
               {
                  $otherLocData->{'ADDRESS2'} = uc($othpolLocAdd2);
               }
               else
               {
                  $otherLocData->{'ADDRESS2'} = ' ';
               }

               if (defined($othpolLocCity) && $othpolLocCity ne '')
               {
                  $otherLocData->{'CITY'} = uc($othpolLocCity);
               }
               else
               {
                  push(@{$errors{'othPolLocCity'}},'<li><div class="errorInfo">Invalid Other Policy City entered.</div></li>'); $return=0;
                  $otherLocData->{'errPolLocCity'} = '<li><div class="errorInfo">Invalid Other Policy City entered.</div></li>';
                  $otherLocData->{'CITY'} = $othpolLocCity;
               }

               if (defined($othpolLocState) && $othpolLocState ne '')
               {
                  $otherLocData->{'STATE'} = uc($othpolLocState);
               }
               else
               {
                  push(@{$errors{'othPolLocState'}},'<li><div class="errorInfo">Invalid Other Policy State entered.</div></li>'); $return=0;
                  $otherLocData->{'errPolLocState'} = '<li><div class="errorInfo">Invalid Other Policy State entered.</div></li>';
                  $otherLocData->{'STATE'} = $othpolLocState;
               }

               if (defined($othpolLocZip) && $othpolLocZip ne '')
               {
                  $otherLocData->{'ZIP1_5'} = $othpolLocZip;
                  if ($LineCode =~ /350|360/)
                  {
                           $policyLocData->{'ZIP1_5'} = '00000';
                  }
                  else
                  {
                          $policyLocData->{'ZIP1_5'} = $othpolLocZip;
                  }
               }


               push(@{$ENGINE->{'PropLiab'}->{'CLM_COMMON_STAT'}},$policyLocData);
               push(@{$ENGINE->{'PropLiab'}->{'CLM_LOCATION'}},$otherLocData);
#               push(@{$ENGINE->{'PropLiab'}->{'CLM_PROPERTY_STAT'}},$otherPropStatData);
               if($LineCode =~ /300|301|302|330|331|332|350|360|810|811|812|813|814|815|816|817|818/)
               {
                  push(@{$ENGINE->{'PropLiab'}->{'CLM_LIAB_STAT'}},$otherLiabStatData);
               }
               else
               {
                   push(@{$ENGINE->{'PropLiab'}->{'CLM_PROPERTY_STAT'}},$otherPropStatData);
               }

            }
            else
            {
               push(@{$errors{'policyLocation'}},'<li><div class="errorInfo">Please click \'Add Policy Location\' and add the Loss Location to proceed.</div></li>'); $return=0;
#               $policyLocData->{'errPolLocN'} = $errorDiv;
            }
         }
      }
      elsif ($key =~ /^delLossLoc(\d+)$/)
      {
         my $lossLocid = $1;
         my $lossLocData = {};
         $lossLocData->{'CLM_COMMON_STAT_ID'} = $lossLocid;
         $lossLocData->{'DELETE'} = 1;

         push(@{$ENGINE->{'PropLiab'}->{'CLM_COMMON_STAT'}},$lossLocData);
      }
      elsif ($key =~ /^delPolicyLoc(\d+)$/)
      {
         my $policyLocid = $1;
         my $commonStatQuery = $ENGINE->{'DBH'}->prepare(
            "SELECT LOCATION_NO, UNIT_NO
               FROM CLAIMDB.CLM_COMMON_STAT
              WHERE CLM_COMMON_STAT_ID = ?
                AND DATE_DELETED = \'9999-01-01 01:00:00.000000\'") || $error->($ENGINE,'Common Stat query prepare failed: '.$ENGINE->{'DBH'}->errstr);

         $commonStatQuery->execute($policyLocid) || $error->($ENGINE,'Common Stat query execute failed: '.$ENGINE->{'DBH'}->errstr);

         my $commonStatResults = $commonStatQuery->fetchall_arrayref({});
         my $policyLocData = {};
         $policyLocData->{'CLM_COMMON_STAT_ID'} = $policyLocid;
         $policyLocData->{'LOCATION_NO'} = $commonStatResults->[0]->{'LOCATION_NO'};
         $policyLocData->{'UNIT_NO'} = $commonStatResults->[0]->{'UNIT_NO'};
         $policyLocData->{'DELETE'} = 1;

         push(@{$ENGINE->{'PropLiab'}->{'CLM_COMMON_STAT'}},$policyLocData);

         if($LineCode !~ /350|360|550|303/)
         {
             $policyLocData->{'ASSIGNMENT_ID'} = $ENGINE->{'CGI'}->param('assignmentID' . $policyLocid);
             push(@{$ENGINE->{'PropLiab'}->{'CLM_ASSIGNMENT_FIELDS'}}, $policyLocData);
         }

         if ($ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'} ne 'P')
         {
             my $locationIDSel = $ENGINE->{'CGI'}->param('policyLocIDL'.$policyLocid);
             $policyLocData->{'LOCATION_ID'} = $locationIDSel;
             push(@{$ENGINE->{'PropLiab'}->{'CLM_LOCATION'}},$policyLocData);
             if($LineCode =~ /300|301|302|330|331|332|810|811|812|813|814|815|816|817|818/)
             {
                 push(@{$ENGINE->{'PropLiab'}->{'CLM_LIAB_STAT'}},$policyLocData);
             }
             else
             {
                 push(@{$ENGINE->{'PropLiab'}->{'CLM_PROPERTY_STAT'}},$policyLocData);
             }
         }
      }
      elsif ($key =~ /^delVarDataLoc(\d+)$/)
      {
         my $varDataLocid = $1;
         my $varDataLocData = {};
         $varDataLocData->{'CLM_VARDATA_ID'} = $varDataLocid;
         $varDataLocData->{'DELETE'} = 1;

         push(@{$ENGINE->{'PropLiab'}->{'CLM_VARDATA'}},$varDataLocData);
      }
      elsif ($key =~ /^delAssignLoc(\d+)$/)
      {
          my $policyLocid = $1;
          my $policyLocData = {};
          $policyLocData->{'CLM_COMMON_STAT_ID'} = $policyLocid;
          $policyLocData->{'ASSIGNMENT_ID'} = $ENGINE->{'CGI'}->param('delAssignLoc'.$policyLocid);
          $policyLocData->{'DELETE'} = 1;
          push(@{$ENGINE->{'PropLiab'}->{'CLM_ASSIGNMENT_FIELDS'}},$policyLocData);
      }
   }

   if($LineCode =~ /303|550/)
   {
       my $holdZip = '';
       if($holdLocNo == 0)
       {
           my $policyLocData = {};
           $policyLocData->{'CLM_COMMON_STAT_ID'} = 'new0';
           $holdLocNo++;
           my $othpolLocLocN = $holdLocNo;
           my $othpolLocUnitN = 0;
           $policyLocData->{'LOCATION_NO'} = $othpolLocLocN;
           $policyLocData->{'UNIT_NO'} = $othpolLocUnitN;
           $policyLocData->{'LOC_TYPE'} = 'AC';

               if(defined($ENGINE->{'CGI'}->param('copyZip1_5')) && $ENGINE->{'CGI'}->param('copyZip1_5') gt '')
               { $holdZip = $ENGINE->{'CGI'}->param('copyZip1_5'); }
               elsif(defined($ENGINE->{'CGI'}->param('IPzipnew0')) && $ENGINE->{'CGI'}->param('IPzipnew0') gt '')
               { $holdZip = $ENGINE->{'CGI'}->param('IPzipnew0'); }

               if(defined($holdZip) && $holdZip eq '')
               {
                   push(@{$errors{'zip'}},'<li><div class="errorInfo">Please enter an insured under Insureds and Claimants section.</div></li>'); $return=0;
               }
               $policyLocData->{'ZIP1_5'} = $holdZip;

               $lossDate = $ENGINE->{'claimGeneral'}->{'LOSS_DATE_TIME'};
               if (length($lossDate) >= 10)
               {
                  $lossDate = substr($lossDate,0,4).substr($lossDate,5,2).substr($lossDate,8,2);
               }

               my $claimLineCode = $ENGINE->{'claimGeneral'}->{'IMT_LINE_CODE'};

               my %lineCodes = getIMTPolicyPrefix();
               for my $key (keys %lineCodes)
               {
                  if ($key eq $claimLineCode)
                  {
                     $claimLineCode = $lineCodes{$key};
                     if($claimLineCode eq 'BO')
                     { $claimLineCode = 'BOP'; }
                     if($claimLineCode eq 'GL')
                     { $claimLineCode = 'GLS'; }
                     if($claimLineCode eq 'CP')
                     { $claimLineCode = 'CPS'; }
                     if($claimLineCode eq 'HM')
                     { $claimLineCode = 'HO'; }
                     if($claimLineCode eq 'MB')
                     { $claimLineCode = 'HO'; }
                  }
               }

               my $zipQuery = $ENGINE->{'DBH'}->prepare(
                  'SELECT ZT.TERRITORY,
                          ZC.COUNTYCODE,
                          ZC.CITYCODE
                     FROM GENSUPDB.ZIPTERRZONE ZT
                     LEFT OUTER JOIN GENSUPDB.ZIPCOUNTYCITY ZC ON ZC.ZIPCODE = ZT.ZIPCODE
                    WHERE ZT.EFF_DATE IN
                             (SELECT MAX(EFF_DATE) AS MAX_DATE
                                FROM GENSUPDB.ZIPTERRZONE
                               WHERE ZIPCODE = ?
                                 AND LOB_CODE = ?)
                      AND ZC.EFF_DATE IN
                             (SELECT MAX(EFF_DATE) AS MAX_DATE
                                FROM GENSUPDB.ZIPCOUNTYCITY
                               WHERE ZIPCODE = ?)
                                 AND ZT.ZIPCODE = ?
                                 AND ZT.LOB_CODE = ?
                                 AND ? > ZT.EFF_DATE ') || $error->($ENGINE,'Zip Code data query prepare failed: '.$ENGINE->{'DBH'}->errstr);

               $zipQuery->execute($holdZip,$claimLineCode,$holdZip,$holdZip,$claimLineCode,$lossDate) || $error->($ENGINE,'Zip Code data query execute failed: '.$ENGINE->{'DBH'}->errstr);

               my $zipResults = $zipQuery->fetchall_arrayref({});

               for my $z (@$zipResults)
               {
                  if (defined($z->{'TERRITORY'}) && $z->{'TERRITORY'} gt '')
                  {
                     $policyLocData->{'TERRITORY'} = $z->{'TERRITORY'};
                  }
                  if (defined($z->{'COUNTYCODE'}) && $z->{'COUNTYCODE'} gt '')
                  {
                     $policyLocData->{'COUNTY_CODE'} = $z->{'COUNTYCODE'};
                  }
                  if (defined($z->{'CITYCODE'}) && $z->{'CITYCODE'} gt '')
                  {
                     $policyLocData->{'CITY_SUBCNTY'} = $z->{'CITYCODE'};
                  }
               }
           if(!defined($policyLocData->{'TERRITORY'}))
           { $policyLocData->{'TERRITORY'} = ' ' }
           if(!defined($policyLocData->{'COUNTY_CODE'}))
           { $policyLocData->{'COUNTY_CODE'} = ' ' }
           if(!defined($policyLocData->{'CITY_SUBCNTY'}))
           { $policyLocData->{'CITY_SUBCNTY'} = ' ' }
           push(@{$ENGINE->{'PropLiab'}->{'CLM_COMMON_STAT'}},$policyLocData);
       }
   }

   my %keys = getTablePrimaryKeyName();

   push(@{$ENGINE->{'PropLiab'}->{'OLD_CLM_GENERAL'}},$ENGINE->{'claimGeneral'});

   if (!defined($ENGINE->{'PropLiab'}->{'CLM_GENERAL'}))
   {
      $ENGINE->{'PropLiab'}->{'CLM_GENERAL'} =[]
   }
   screenDataSync($ENGINE,$ENGINE->{'PropLiab'}->{'OLD_CLM_GENERAL'},$ENGINE->{'PropLiab'}->{'CLM_GENERAL'},$keys{'CLM_GENERAL'});

   my $vardataSelect = $ENGINE->{'DBH'}->prepare(
      'SELECT *
         FROM CLAIMDB.CLM_VARDATA
        WHERE CLAIM_ID = ?
          AND DATA_TYPE IN (\'DESCRIPT\',\'LOCATION\',\'LOSSSTOTH\')
          AND DATE_DELETED = \'9999-01-01 01:00:00.000000\'') || $error->($ENGINE);

   $vardataSelect->execute($claimid) || $error->($ENGINE);

   $ENGINE->{'PropLiab'}->{'OLD_CLM_VARDATA'} = $vardataSelect->fetchall_arrayref({});

   if (!defined($ENGINE->{'PropLiab'}->{'CLM_VARDATA'}))
   {
      $ENGINE->{'PropLiab'}->{'CLM_VARDATA'} =[];
   }
   screenDataSync($ENGINE,$ENGINE->{'PropLiab'}->{'OLD_CLM_VARDATA'},$ENGINE->{'PropLiab'}->{'CLM_VARDATA'},$keys{'CLM_VARDATA'});


   #adding the LINE_CNTR to clm_vardata if there is a screen error
   #as the sort won't work in the load screen function without
   #a value in this field.

   for my $x (@{$ENGINE->{'PropLiab'}->{'CLM_VARDATA'}})
   {
        if (!defined $x->{'LINE_CNTR'})
        {
            $x->{'LINE_CNTR'} = 1;
        }
        if (!defined $x->{'VARDATA'})
        {
            $x->{'VARDATA'} = '';
        }
   }


   my $propQuery = $ENGINE->{'DBH'}->prepare(
      'SELECT *
         FROM CLAIMDB.CLM_PROPERTY_STAT
        WHERE CLAIM_ID = ?
          AND DATE_DELETED = \'9999-01-01 01:00:00.000000\'') || $error->($ENGINE,'Property Stat query prepare failed: '.$ENGINE->{'DBH'}->errstr);

   $propQuery->execute($claimid) || $error->($ENGINE,'Property Stat query execute failed: '.$ENGINE->{'DBH'}->errstr);

   $ENGINE->{'PropLiab'}->{'OLD_CLM_PROPERTY_STAT'} = $propQuery->fetchall_arrayref({});

   if (!defined($ENGINE->{'PropLiab'}->{'CLM_PROPERTY_STAT'}))
   {
      $ENGINE->{'PropLiab'}->{'CLM_PROPERTY_STAT'} =[];
   }
   screenDataSync($ENGINE,$ENGINE->{'PropLiab'}->{'OLD_CLM_PROPERTY_STAT'},$ENGINE->{'PropLiab'}->{'CLM_PROPERTY_STAT'},$keys{'CLM_PROPERTY_STAT'});

#    for my $x (@{$ENGINE->{'PropLiab'}->{'CLM_PROPERTY_STAT'}})
#    {
#       for my $k (keys %$x)
#       {
#           print $k.'='.$x->{$k}.'<br/>';
#       }
#    }

   my $commStatQuery = $ENGINE->{'DBH'}->prepare(
      'SELECT *
         FROM CLAIMDB.CLM_COMMON_STAT
        WHERE CLAIM_ID = ?
          AND DATE_DELETED = \'9999-01-01 01:00:00.000000\'') || $error->($ENGINE,'Common Stat query prepare failed: '.$ENGINE->{'DBH'}->errstr);

   $commStatQuery->execute($claimid) || $error->($ENGINE,'Common Stat query execute failed: '.$ENGINE->{'DBH'}->errstr);

   $ENGINE->{'PropLiab'}->{'OLD_CLM_COMMON_STAT'} = $commStatQuery->fetchall_arrayref({});

   if (!defined($ENGINE->{'PropLiab'}->{'CLM_COMMON_STAT'}))
   {
      $ENGINE->{'PropLiab'}->{'CLM_COMMON_STAT'} =[];
   }
   screenDataSync($ENGINE,$ENGINE->{'PropLiab'}->{'OLD_CLM_COMMON_STAT'},$ENGINE->{'PropLiab'}->{'CLM_COMMON_STAT'},$keys{'CLM_COMMON_STAT'});

#    for my $x (@{$ENGINE->{'PropLiab'}->{'CLM_COMMON_STAT'}})
#    {
#       for my $k (keys %$x)
#       {
#           print $k.'='.$x->{$k}.'<br/>';
#       }
#    }

   my $locationQuery = $ENGINE->{'DBH'}->prepare(
      'SELECT *
         FROM CLAIMDB.CLM_LOCATION
        WHERE CLAIM_ID = ?
          AND DATE_DELETED = \'9999-01-01 01:00:00.000000\'') || $error->($ENGINE,'Property Stat query prepare failed: '.$ENGINE->{'DBH'}->errstr);

   $locationQuery->execute($claimid) || $error->($ENGINE,'Common Stat query execute failed: '.$ENGINE->{'DBH'}->errstr);

   $ENGINE->{'PropLiab'}->{'OLD_CLM_LOCATION'} = $locationQuery->fetchall_arrayref({});

   if (!defined($ENGINE->{'PropLiab'}->{'CLM_LOCATION'}))
   {
      $ENGINE->{'PropLiab'}->{'CLM_LOCATION'} =[];
   }
   screenDataSync($ENGINE,$ENGINE->{'PropLiab'}->{'OLD_CLM_LOCATION'},$ENGINE->{'PropLiab'}->{'CLM_LOCATION'},$keys{'CLM_LOCATION'});

#    for my $x (@{$ENGINE->{'PropLiab'}->{'CLM_LOCATION'}})
#    {
#       for my $k (keys %$x)
#       {
#           print $k.'='.$x->{$k}.'<br/>';
#       }
#    }

   my $liabQuery = $ENGINE->{'DBH'}->prepare(
      'SELECT *
         FROM CLAIMDB.CLM_LIAB_STAT
        WHERE CLAIM_ID = ?
          AND DATE_DELETED = \'9999-01-01 01:00:00.000000\'') || $error->($ENGINE,'Liability Stat query prepare failed: '.$ENGINE->{'DBH'}->errstr);

   $liabQuery->execute($claimid) || $error->($ENGINE,'Liability Stat query execute failed: '.$ENGINE->{'DBH'}->errstr);

   $ENGINE->{'PropLiab'}->{'OLD_CLM_LIAB_STAT'} = $liabQuery->fetchall_arrayref({});

   if (!defined($ENGINE->{'PropLiab'}->{'CLM_LIAB_STAT'}))
   {
      $ENGINE->{'PropLiab'}->{'CLM_LIAB_STAT'} =[];
   }
   screenDataSync($ENGINE,$ENGINE->{'PropLiab'}->{'OLD_CLM_LIAB_STAT'},$ENGINE->{'PropLiab'}->{'CLM_LIAB_STAT'},$keys{'CLM_LIAB_STAT'});

   my $assignmentQuery = $ENGINE->{'DBH'}->prepare(
      'SELECT *
         FROM CLAIMDB.CLM_ASSIGNMENT_FIELDS
        WHERE CLAIM_ID = ?') || $error->($ENGINE,'Assignment Fields query prepare failed: '.$ENGINE->{'DBH'}->errstr);

   $assignmentQuery->execute($claimid) || $error->($ENGINE,'Assignment Fields query execute failed: '.$ENGINE->{'DBH'}->errstr);

   $ENGINE->{'PropLiab'}->{'OLD_CLM_ASSIGNMENT_FIELDS'} = $assignmentQuery->fetchall_arrayref({});

   if (!defined($ENGINE->{'PropLiab'}->{'CLM_ASSIGNMENT_FIELDS'}))
   {
      $ENGINE->{'PropLiab'}->{'CLM_ASSIGNMENT_FIELDS'} =[];
   }
   screenDataSync($ENGINE,$ENGINE->{'PropLiab'}->{'OLD_CLM_ASSIGNMENT_FIELDS'},$ENGINE->{'PropLiab'}->{'CLM_ASSIGNMENT_FIELDS'},$keys{'CLM_ASSIGNMENT_FLD'});

#   my $bondQuery = $ENGINE->{'DBH'}->prepare(
#      'SELECT *
#         FROM CLAIMDB.CLM_BOND_INFO
#        WHERE CLAIM_ID = ?
#          AND DATE_DELETED = \'9999-01-01 01:00:00.000000\'') || $error->($ENGINE,'Bond Info query prepare failed: '.$ENGINE->{'DBH'}->errstr);

#   $bondQuery->execute($claimid) || $error->($ENGINE,'Bond Info query execute failed: '.$ENGINE->{'DBH'}->errstr);

#   $ENGINE->{'PropLiab'}->{'OLD_CLM_BOND_INFO'} = $bondQuery->fetchall_arrayref({});

#   if (!defined($ENGINE->{'PropLiab'}->{'CLM_BOND_INFO'}))
#   {
#      $ENGINE->{'PropLiab'}->{'CLM_BOND_INFO'} =[];
#   }
#   screenDataSync($ENGINE,$ENGINE->{'PropLiab'}->{'OLD_CLM_BOND_INFO'},$ENGINE->{'PropLiab'}->{'CLM_BOND_INFO'},$keys{'CLM_BOND_INFO'});
   if (!editPartiesData($ENGINE))
   {
      $return = 0;
   }

   if (!editAuthorityData($ENGINE))
   {
      $return = 0;
   }

   if (!editVehTotalLossData($ENGINE))
   {
      $return = 0;
   }

   if (!editOtherInfoData($ENGINE))
   {
      $return = 0;
   }

#   if (!editSubmitClaimData($ENGINE))  12/4/2015 - EMR - removed since has been previously commented out in Claims_Misc
#   {
#      $return = 0;
#   }

   if($return == 0)
   { $ENGINE->{'PropLiab'}->{'propLiabErrors'} = 'Y'; }

   if (defined($ENGINE->{'errors'}))
   {
      for my $key (keys %errors)
      {
         push(@{$ENGINE->{'errors'}->{$key}},@{$errors{$key}});
      }
   }
   else
   {
      $ENGINE->{'errors'} = \%errors;
   }

   return $return;
}


1;
