#!/usr/local/bin/perl
package Claims_Info;

require 5.000;
use strict;
use vars qw($VERSION @ISA @EXPORT_OK);

use Exporter;

@ISA = qw(Exporter);
@EXPORT_OK = qw(loadScreen);

$VERSION = '0.01';

use Claims_Misc qw(getHeader getMenu getFooter getClaimsCentral getClaimHeading getClaimDetails getTop getBottom createLabelHelp platformAuthError);
use Claims_NOTES qw(NOTESsave NOTEScreateTextArea NOTEScreateTextInput NOTESreadInput);
use Claims_Error qw(error);
use IMT::CommaFormatted qw(CommaFormatted);
use Common::Platform::Users::usersCommon qw(fetch_user_key_data);
        use Data::Dumper;

my $DETAILS = {'proc_details'=>[],'subro'=>'','paid'=>'','grossPay'=>'','reserve'=>'','mobileLossType'=>'','desc'=>'','otherLossDesc'=>''};

sub loadScreen
{
    my $ENGINE = shift;
    my $top = getTop($ENGINE);
    my $bottom = getBottom($ENGINE,'Claims_Info');

    my $loss_summary = getLossSummary($ENGINE);
    my $other_info = getOtherInfo($ENGINE);
    my $processing_details = getProcessingDetails($ENGINE);
    my $data = <<EOF;
$top
$loss_summary
$other_info
$processing_details
$bottom
</body>
</html>
EOF
#die Data::Dumper::Dumper($data);

    print <<EOF;
$top
$loss_summary
$other_info
$processing_details
$bottom
</body>
</html>
EOF

my $claimid = $ENGINE->{'claimGeneral'}->{'CLAIM_ID'};
my $policyNum = $ENGINE->{'claimGeneral'}->{'POLICY_NUMBER'};
#die Data::Dumper::Dumper($claimid,$policyNum,$ENGINE->{'SESSION'}->{'claimid'.$claimid},$ENGINE->{'claimGeneral'}->{'BRANCH'},$DETAILS->{'paid'},$DETAILS->{'grossPay'},$DETAILS->{'reserve'},$DETAILS->{'mobileLossType'},$DETAILS->{'desc'},$DETAILS->{'otherLossDesc'});
##mobile app plug
my $appOrigin = '';
if(defined($ENGINE->{'CGI'}->{'appOrigin'}))
{$appOrigin = lc($ENGINE->{'CGI'}->{'appOrigin'}->[0])||lc($ENGINE->{'CGI'}->{'param'}->{'appOrigin'}[0]);}
if ($appOrigin eq "android" || $appOrigin eq "ios" ){

        my %branches = (21=>'Glass Claim',
10=>'Home Office',
20=>'Des Moines',
30=>'Cedar Rapids',
40=>'Mason City',
50=>'Sioux Falls',
60=>'Sioux City',
70=>'Omaha',
22=>'Automated Proclaim Glass Claim',
12=>'Bond Claim',
75=>'Quad Cities',
00=>'Unknown');

                my $branchInfo = $branches{$ENGINE->{'claimGeneral'}->{'BRANCH'}} || 'Unknown';

        if($ENGINE->{'claimGeneral'}->{'BRANCH'} eq '00' && substr($ENGINE->{'claimGeneral'}->{'IMT_CLAIM_NO'},2,1) eq 'A')
          { $branchInfo = 'Des Moines'; }

my $hash_ref = {};

     $hash_ref->{ 'firstAdjusterFax' } = $ENGINE->{'SESSION'}->{'claimid'.$claimid}->{firstAdjusterFax};
     $hash_ref->{ 'firstAdjuster' } = $ENGINE->{'SESSION'}->{'claimid'.$claimid}->{firstAdjuster};
     $hash_ref->{ 'firstAdjusterPhone' } = $ENGINE->{'SESSION'}->{'claimid'.$claimid}->{firstAdjusterPhone};
     $hash_ref->{ 'firstAdjusterEmail' } = $ENGINE->{'SESSION'}->{'claimid'.$claimid}->{firstAdjusterEmail};
     $hash_ref->{ 'branch' } = $branchInfo;
     $hash_ref->{ 'paid' } = $DETAILS->{'paid'};
     $hash_ref->{ 'grossPaid' } = $DETAILS->{'grossPay'};
     $hash_ref->{ 'reserve' } = $DETAILS->{'reserve'};
     $hash_ref->{ 'typeOfLoss' } = $DETAILS->{'mobileLossType'};

     $hash_ref->{ 'lossDesc' } = $DETAILS->{'desc'};
          $hash_ref->{ 'notes' } = $DETAILS->{'otherLossDesc'};


 my $sth = $ENGINE->{'DBH'}->prepare('
        select  LINE_OF_BUSINESS,DATE_PROCESSED,char(DATE_PROCESSED,usa) as DATE_PROCESSED_USA,
                POLICY_EFF_DATE,char(POLICY_EFF_DATE,usa) as POLICY_EFF_DATE_USA,TYPE_OF_DOC,
                TOPIC_KEY
        from    GENSUPDB.MOBIUS
        where   POLICY_NUMBER = ?') || error($ENGINE->{'DBH'}->errstr);
    $sth->execute($policyNum) || error($ENGINE->{'DBH'}->errstr);
    my $mobius = $sth->fetchall_arrayref({});
    @$mobius = reverse sort{$a->{'DATE_PROCESSED'} cmp $b->{'DATE_PROCESSED'}} @$mobius;

  $hash_ref->{ 'decs' } = $mobius;

     use JSON;
 my $json = encode_json $hash_ref;
          print "<div id = mobileApp>";
          print $json;
           print "</div>";
##end mobile app plug
}

}

sub getLossSummary
{
    my $ENGINE = shift;
    my $claimid = $ENGINE->{'claimGeneral'}->{'CLAIM_ID'};

    my @procDetails = @{$DETAILS->{'proc_details'}};


    my $lossDate = $ENGINE->{'claimGeneral'}->{'LOSS_DATE_TIME'};
    my $claimStatus = $ENGINE->{'claimGeneral'}->{'CLAIM_STATUS'};
    my $DBlossDate = substr($lossDate,0,10);

    my $agentRestriction = 'AND LOSS_CODE NOT IN (\'79\',\'80\',\'81\',\'82\') AND TYPE <> \'R\'';
    if($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'Internal')
      { $agentRestriction = ''; }

    # Amount Paid
    my $paidQuery = $ENGINE->{'DBH'}->prepare('SELECT PAYMENT_AMT*100 AS PAYCENTS, RESERVED, TYPE, TRANSACTION_DATE, LOSS_CODE, VOID_STOP_PAY, CONTRIBUTION_IND
FROM CLAIMDB.CLM_CASH WHERE CLAIM_ID = ? '.$agentRestriction.' AND DATE_DELETED = \'9999-01-01 01:00:00.000000\'') || error($ENGINE,'Amount paid query prepare failed: '.$ENGINE->{'DBH'}->errstr);
    $paidQuery->execute($claimid) || error($ENGINE,'Amount paid query execute failed: '.$ENGINE->{'DBH'}->errstr);
    my $paidResults = $paidQuery->fetchall_arrayref({});
    my $paid = 0;
    my $subro = $DETAILS->{'subro'};
    my $reserved = 0;
    my $grossPay = 0;
    for my $p (@$paidResults)
    {
 #          if($p->{'TYPE'} ne 'G')
 #       { $p->{'PAYCENTS'} = -1* $p->{'PAYCENTS'}; }
        if(!($p->{'LOSS_CODE'} =~ /79|80|81|82/) && $p->{'TYPE'} ne 'R')
          { $paid += $p->{'PAYCENTS'}; }
#        elsif($p->{'TYPE'} eq 'B' && !($p->{'LOSS_CODE'} =~ /79|80|81|82/))
#         { $paid += $p->{'PAYCENTS'}; }

        if($p->{'TYPE'} eq 'B')
          { $subro = '<li><label>Subrogation </label><div><img src="c.png" style="vertical-align:middle" alt="Checked" /></div></li>'; }


        if($p->{'RESERVED'} eq 'Y' && $p->{'TYPE'} eq 'G' && !($p->{'LOSS_CODE'} =~ /79|80|81|82/))
          { $reserved -= $p->{'PAYCENTS'}; }
#        elsif($p->{'RESERVED'} eq 'Y')
#          { $reserved -= $p->{'PAYCENTS'}; }
        if($p->{'PAYCENTS'} != 0 && $p->{'VOID_STOP_PAY'} eq 'V' && $p->{'TRANSACTION_DATE'} ne '9999-01-01 01:00:00.000000')
          {
              if(substr($p->{'TRANSACTION_DATE'},11,15) eq '01:00:00.000000')
              { push(@procDetails, {'text'=>'Payment Voided','date'=>substr($p->{'TRANSACTION_DATE'},0,10),'rank'=>50}); }
              else
              { push(@procDetails, {'text'=>'Payment Voided','date'=>$p->{'TRANSACTION_DATE'},'rank'=>200}); }
          }
        elsif($p->{'PAYCENTS'} != 0 && $p->{'TRANSACTION_DATE'} ne '9999-01-01 01:00:00.000000')
#          { push(@procDetails, {'text'=>'Payment made','date'=>substr($p->{'TRANSACTION_DATE'},0,10),'rank'=>50}); }
          {
              if(substr($p->{'TRANSACTION_DATE'},11,15) eq '01:00:00.000000')
              {
                if($p->{'TYPE'} =~ /S|R|B|O/ || ($p->{'TYPE'} eq 'G' && $p->{'CONTRIBUTION_IND'} eq 'Y'))
                {push(@procDetails, {'text'=>'Payment Received','date'=>substr($p->{'TRANSACTION_DATE'},0,10),'rank'=>50});}
                else
                {push(@procDetails, {'text'=>'Payment Made','date'=>substr($p->{'TRANSACTION_DATE'},0,10),'rank'=>50});}
              }
              else
              {
                if($p->{'TYPE'} =~ /S|R|B|O/  || ($p->{'TYPE'} eq 'G' && $p->{'CONTRIBUTION_IND'} eq 'Y'))
                {push(@procDetails, {'text'=>'Payment Received','date'=>$p->{'TRANSACTION_DATE'},'rank'=>200});}
                else
                {push(@procDetails, {'text'=>'Payment Made','date'=>$p->{'TRANSACTION_DATE'},'rank'=>200});}
              }
          }
        if(defined($p->{'TYPE'}) && $p->{'TYPE'} eq 'G' && !($p->{'LOSS_CODE'} =~ /79|80|81|82/) && (!defined $p->{'CONTRIBUTION_IND'} || $p->{'CONTRIBUTION_IND'} ne 'Y'))
          { $grossPay += $p->{'PAYCENTS'}; }
    }
    $paid = '$'.CommaFormatted(sprintf('%0.2f',($paid/100)));
    $grossPay = '$'.CommaFormatted(sprintf('%0.2f',($grossPay/100)));

    # Reserve Amount
    my $reserve = '$0.00';
    my $reserveQuery = $ENGINE->{'DBH'}->prepare('SELECT RESERVE_AMT*100 AS RESCENTS, TRANSACTION_DATE, TYPE, LOSS_CODE
    FROM CLAIMDB.CLM_RESERVES WHERE CLAIM_ID = ? '.$agentRestriction.' AND DATE_DELETED = \'9999-01-01 01:00:00.000000\'') || error($ENGINE,'Amount paid query prepare failed: '.$ENGINE->{'DBH'}->errstr);
    $reserveQuery->execute($claimid) || error($ENGINE,'Amount paid query execute failed: '.$ENGINE->{'DBH'}->errstr);
    my $reserveResults = $reserveQuery->fetchall_arrayref({});
    my $reserveTotal = 0;
    for my $r (@$reserveResults)
    {
       if($r->{'TYPE'} eq 'G' && !($r->{'LOSS_CODE'} =~ /79|80|81|82/))
         { $reserveTotal += $r->{'RESCENTS'}; }
       if($r->{'TRANSACTION_DATE'} ne '9999-01-01 01:00:00.000000')
#          { push(@procDetails, {'text'=>'Reserve changed','date'=>substr($r->{'TRANSACTION_DATE'},0,10),'rank'=>40}); }
          {
              if(substr($r->{'TRANSACTION_DATE'},11,15) eq '01:00:00.000000')
              { push(@procDetails, {'text'=>'Reserve changed','date'=>substr($r->{'TRANSACTION_DATE'},0,10),'rank'=>40}); }
              else
              { push(@procDetails, {'text'=>'Reserve changed','date'=>$r->{'TRANSACTION_DATE'},'rank'=>200}); }
          }
    }
    if($reserveTotal ne 0)
      { $reserve = '$'.CommaFormatted(sprintf('%0.2f',(($reserveTotal + $reserved)/100))); }
    if($reserve eq '$0.00')
    {
        my $subroStatus = $ENGINE->{'claimGeneral'}->{'SUBRO_STATUS'} || '';
        my $salvStatus = $ENGINE->{'claimGeneral'}->{'SALV_STATUS'} || '';
        my $reinStatus = $ENGINE->{'claimGeneral'}->{'REINSURANCE_IND'} || '';
        my $recovStatus = $ENGINE->{'claimGeneral'}->{'CLOSE_RECOVERABLE'} || '';
        if($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'Internal' &&
           ($subroStatus eq 'P' || $salvStatus eq 'P' || $reinStatus eq 'P'))
        {
            $reserve = 'Pending ';
            if($subroStatus eq 'P')
              { $reserve .= 'Subr, '; }
            if($salvStatus eq 'P')
              { $reserve .= 'Salv, '; }
            if($reinStatus eq 'P')
              { $reserve .= 'Rein, '; }
            if($recovStatus eq 'P')
              { $reserve .= 'Recov, '; }
            chop($reserve);
            chop($reserve);
        }
        elsif($ENGINE->{'claimGeneral'}->{'PURGED_IND'} eq 'Y'
                || $ENGINE->{'claimGeneral'}->{'PURGED_IND'} eq 'R')
          { $reserve = 'Archived'; }
        elsif($claimStatus =~ /C/)
          { $reserve = 'Closed'; }
    }

    # Loss Description, Additional Loss Details
    my $descQuery = $ENGINE->{'DBH'}->prepare('SELECT VARDATA, LINE_CNTR, DATA_TYPE FROM CLAIMDB.CLM_VARDATA WHERE CLAIM_ID = ? AND DATA_TYPE IN (\'DESCRIPT\',\'DETAILS\') AND DATE_DELETED = \'9999-01-01 01:00:00.000000\'') || error($ENGINE,'Amount paid query prepare failed: '.$ENGINE->{'DBH'}->errstr);
    $descQuery->execute($claimid) || error($ENGINE,'Amount paid query execute failed: '.$ENGINE->{'DBH'}->errstr);
    my $descResults = $descQuery->fetchall_arrayref({});
    my @sortedDescResults = sort({$a->{'LINE_CNTR'} <=> $b->{'LINE_CNTR'}} @$descResults);
    my $desc = '';
    my $additionalLossDetails = '';
    for my $d (@sortedDescResults)
    {
        if($d->{'DATA_TYPE'} eq 'DESCRIPT')
          { $desc .= $d->{'VARDATA'}; }
        elsif($d->{'DATA_TYPE'} eq 'DETAILS')
          { $additionalLossDetails .= $d->{'VARDATA'}; }
    }
#    chop($desc);
#    chop($additionalLossDetails);
    if($additionalLossDetails ne '')
    {
     $additionalLossDetails = <<EOF;
<li><label>Additional Loss Details: </label>
    <div>
        <div class="collapse_toggle">
            <a class="showhide" href="#addtlDetails" onclick="toggleHelp(\'addtlDetails\');switchDetailInfo()" id="showLossDetail">View Details</a>
        </div>
        <div class="hidden" id="addtlDetails">
            $additionalLossDetails
        </div>
    </div>
</li>
EOF
    }

    # SDIP
    my $SDIPQuery = $ENGINE->{'DBH'}->prepare('SELECT C.SDIP_CODE, S.DEFINITION FROM CLAIMDB.CLM_SDIPS AS C LEFT JOIN CLAIMDB.SDIP_CODES AS S ON C.SDIP_CODE = S.SDIP_CODE WHERE C.CLAIM_ID = ? and DATE_DELETED = \'9999-01-01-01.00.00.000000\' AND S.EFFECTIVE <= ? AND S.OBSOLETE > ?') || error($ENGINE,'SDIP query prepare failed: '.$ENGINE->{'DBH'}->errstr);
    $SDIPQuery->execute($claimid,$DBlossDate,$DBlossDate) || error($ENGINE,'SDIP query execute failed: '.$ENGINE->{'DBH'}->errstr);
    my $SDIPResults = $SDIPQuery->fetchall_arrayref({});
    my $SDIP = '';
    for my $r (@$SDIPResults)
      { $SDIP .= '<li><label>SDIP</label><div> '.$r->{'SDIP_CODE'}.' '.($r->{'DEFINITION'} || '').'</div></li>'; }

    my $stormLoss = '';
    if(defined($ENGINE->{'claimGeneral'}->{'CLM_STORM_ID'}) && $ENGINE->{'claimGeneral'}->{'CLM_STORM_ID'} =~ /\w/)
    {
        my $stormQuery = $ENGINE->{'DBH'}->prepare('SELECT STORM_LONG_DESC, DATE_REPORTED, DATE_OCCURED FROM CLAIMDB.CLM_STORMS WHERE CLM_STORM_ID = ?') || error($ENGINE,'Storm query prepare failed: '.$ENGINE->{'DBH'}->errstr);
        $stormQuery->execute($ENGINE->{'claimGeneral'}->{'CLM_STORM_ID'}) || error($ENGINE,'Storm query execute failed: '.$ENGINE->{'DBH'}->errstr);
        my $stormResults = $stormQuery->fetchall_arrayref({});
        for my $s (@$stormResults)
          { $stormLoss .= '<li><label>Storm Loss</label><div> '.$s->{'STORM_LONG_DESC'}.'</div></li>'; }
    }

    # Type of Loss
    my $moblieLossType = '';
    my $typeOfLoss = '';
    if($ENGINE->{'claimGeneral'}->{'PROP_OR_LIAB'} eq 'P')
      { $typeOfLoss = 'PROPERTY'; }
    elsif($ENGINE->{'claimGeneral'}->{'PROP_OR_LIAB'} eq 'L')
      { $typeOfLoss = 'LIABILITY'; }
    elsif($ENGINE->{'claimGeneral'}->{'PROP_OR_LIAB'} eq 'B')
      { $typeOfLoss = 'PROPERTY & LIABILITY'; }
    $DETAILS->{'mobileLossType'} = $typeOfLoss;
    if($typeOfLoss ne '')
      { $typeOfLoss = '<li><label>Type of Loss: </label><div> '.$typeOfLoss.'</div></li>'; }

      my $paidReserve = '';
      my $net_reserve = createLabelHelp({
                                         'label' =>  'Net Reserve Amount: ',
                                         'help'  =>  'Net reserve is the total reserve minus paid out from reserve.',
                                         'data'  =>  $reserve,
                                         'id'    =>  'net_reserve',
                                     });

    if($paid eq $grossPay)
    {    my $net_gross = createLabelHelp({
                                            'label' =>  'Net/Gross Amount Paid: ',
                                            'help'  =>  'Gross paid is the sum of all payments paid out less adjusting expenses and legal fees.  Does not include Subrogation, Salvage, Contribution, or Reinsurance. Net paid is gross paid less salvage, subrogation and contribution.',
                                            'data'  =>  $paid,
                                            'id'    =>  'net_gross',
                                        });

      $paidReserve = <<EOF;
      <li>$net_gross</li>
      <li>$net_reserve</li>
EOF
}
    else
      {
        my $net_gross = createLabelHelp({
                                            'label' =>  'Net Amount Paid: ',
                                            'help'  =>  'Net paid is gross paid less salvage, subrogation, contribution and other.',
                                            'data'  =>  $paid,
                                            'id'    =>  'net_gross',
                                        });
        my $gross_paid = createLabelHelp({
                                            'label' =>  'Gross Amount Paid: ',
                                            'help'  =>  'Gross paid is the sum of all payments paid out less adjusting expenses and legal fees.  Does not include Subrogation, Salvage, Contribution, Reinsurance, or Other.',
                                            'data'  =>  $grossPay,
                                            'id'    =>  'gross_amt_paid',
                                        });


      $paidReserve = <<EOF;
      <li>$gross_paid</li>
      <li>$net_gross</li>
      <li>$net_reserve</li>
EOF
}

    my $loss_desc = createLabelHelp({
                                         'label' =>  'Loss Description: ',
                                         'help'  =>  'Description as reported.',
                                         'data'  =>  $desc,
                                         'id'    =>  'loss_desc',
                                     });
    $DETAILS->{'paid'} = $paid;
    $DETAILS->{'grossPay'} = $grossPay;
    $DETAILS->{'desc'} = $desc;
    $DETAILS->{'reserve'} = $reserve;
    $DETAILS->{'proc_details'} = \@procDetails;
    $DETAILS->{'subro'} = $subro;

    my $html = <<EOF;
<fieldset>
<h2>Loss Summary</h2>

<ul class="leftlabel_twocol">
  $paidReserve
  <li>$loss_desc</li>
  $additionalLossDetails
  $SDIP
  $stormLoss
  $typeOfLoss
  </ul>
</fieldset>
EOF

    return $html;
}

sub getOtherInfo
{
    my $ENGINE = shift;

    my $claimid = $ENGINE->{'claimGeneral'}->{'CLAIM_ID'};
    my $sessID = $ENGINE->{'SESSION'}->{'sessionID'};
    my $action = $ENGINE->{'ACTION'};

    my @procDetails = @{$DETAILS->{'proc_details'}};
    my $subro = $DETAILS->{'subro'} || '';

    my $suitFile = '';
    if($ENGINE->{'claimGeneral'}->{'SUIT_IND'} eq 'Y')
      { $suitFile = '<li><label>Suit File </label><img src="c.png" style="vertical-align:middle" alt="Checked" /></li>'; }

    my $restorePurgeMess = '';
    if ($ENGINE->{'claimGeneral'}->{'PURGED_IND'} eq 'R')
    {
             $restorePurgeMess = '<span style="color:red; font-weight:bold;">******Reinstate has been requested for this claim.  Full claim will appear on Thursday.</span>'
    }

    # Adjuster
    my $adjusterQuery = $ENGINE->{'DBH'}->prepare('SELECT CLM_REP_ASGN_ID, USER_KEY, DATE_ASSIGNED FROM CLAIMDB.CLM_REP_ASSIGNED AS C WHERE CLAIM_ID = ? AND C.DATE_REMOVED = \'9999-01-01 01:00:00.000000\'') || error($ENGINE,'Amount paid query prepare failed: '.$ENGINE->{'DBH'}->errstr);
    $adjusterQuery->execute($claimid) || error($ENGINE,'Amount paid query execute failed: '.$ENGINE->{'DBH'}->errstr);
    my $adjusterResults = $adjusterQuery->fetchall_arrayref({});
    my @sortedResults = sort({$a->{'CLM_REP_ASGN_ID'} <=> $b->{'CLM_REP_ASGN_ID'}} @$adjusterResults);
    #Parties query for Outside adjusters
    my $partyQuery = $ENGINE->{'DBH'}->prepare("SELECT PA.PARTY_ID,BUSINESS_NAME,DATE_ADDED,PR.CLM_PARTYROLE_ID,PR.ROLE FROM CLAIMDB.CLM_PARTIES PA LEFT OUTER JOIN CLAIMDB.CLM_PARTY_ROLES PR ON PR.PARTY_ID = PA.PARTY_ID
WHERE PA.CLAIM_ID = ? AND PR.ROLE IN ('OA') AND PA.DATE_DELETED = '9999-01-01 01:00:00.000000'") || error->($ENGINE,'Party query prepare failed: '.$ENGINE->{'DBH'}->errstr);
    $partyQuery->execute($claimid) || error->($ENGINE,'Party query execute failed: '.$ENGINE->{'DBH'}->errstr);
    my $partyResults = $partyQuery->fetchall_arrayref({});
    my $adjuster = '';
    my $outSideAdjuster = '';
    my $firstAdjusterName = 'Unknown';
    my $firstAdjusterEmail = '';
    my $repAssignedDate = '';
    my $outSideAdjDate = '';

    if(scalar(@sortedResults) > 0)
    {
        my $i = 0;
        for my $a (@sortedResults)
        {
            $i++;
            if($i < 2)
            {
                next;
            }
            else
            {
                    $repAssignedDate = $a->{'DATE_ASSIGNED'};
            #        push(@procDetails, {'text'=>'Claim assigned to adjuster','date'=>substr($repAssignedDate,0,10),'rank'=>30});
                    if(substr($repAssignedDate,11,15) eq '01:00:00.000000')
                    { push(@procDetails, {'text'=>'Claim assigned to adjuster','date'=>substr($repAssignedDate,0,10),'rank'=>30}); }
                    else
                    { push(@procDetails, {'text'=>'Claim assigned to adjuster','date'=>$repAssignedDate,'rank'=>200}); }

                    #$adjuster .= $a->{'FIRST_NAME'}.' '.$a->{'LAST_NAME'}.', ';
                    #call platform with user_key
                    my $user_key_data = fetch_user_key_data({
                      authorization => $ENGINE->{'AUTH'}->{'platform_access_token'},
                      user_key => $a->{'USER_KEY'},
                      max_attempts => 2
                    });

                    if ($user_key_data->{code} ne '200') {
                      $adjuster .= 'UNKNOWN, ';
                      my $auth_error_msg = 'Platform Auth fetch did not return a 200 code.';
                      platformAuthError($ENGINE,$user_key_data->{code},$auth_error_msg);
                    }
                    elsif(!defined($user_key_data->{content}->{data}->[0]->{attributes}->{first_name}) &&
                          !defined($user_key_data->{content}->{data}->[0]->{attributes}->{last_name}) )
                    {
                        $adjuster .= 'UNKNOWN, ';
                        my $auth_error_msg = 'Platform Authe fetch did not return a First and Last name.';
                        platformAuthError($ENGINE,$user_key_data->{code},$auth_error_msg);
                    }
                    else
                    {
                        $adjuster .=  uc($user_key_data->{content}->{data}->[0]->{attributes}->{first_name}).' '.uc($user_key_data->{content}->{data}->[0]->{attributes}->{last_name}).', ';
                    }


            }
        }
        chop($adjuster);
        chop($adjuster);
        if(scalar(@sortedResults) > 2)
          { $adjuster = '<li><label>Additional Adjusters</label> '.$adjuster.'</li>'; }
        elsif(scalar(@sortedResults) > 0 && $adjuster gt '')
          { $adjuster = '<li><label>Additional Adjuster</label> '.$adjuster.'</li>'; }
    }

    if(scalar(@$partyResults) > 0)
    {
        for my $pr (@$partyResults)
        {
            $outSideAdjDate = $pr->{'DATE_ADDED'};
            if(substr($outSideAdjDate,11,15) eq '01:00:00.000000')
            { push(@procDetails, {'text'=>'Claim assigned to outside adjuster','date'=>substr($outSideAdjDate,0,10),'rank'=>30}); }
            else
            { push(@procDetails, {'text'=>'Claim assigned to outside adjuster','date'=>$outSideAdjDate,'rank'=>200}); }

            $outSideAdjuster .= $pr->{'BUSINESS_NAME'}.', ';
        }
        chop($outSideAdjuster);
        chop($outSideAdjuster);
        if(scalar(@$partyResults) > 2)
          { $outSideAdjuster = '<li><label>Additional Outside Adjusters</label> '.$outSideAdjuster.'</li>'; }
        elsif(scalar(@$partyResults) > 0 && $outSideAdjuster gt '')
          { $outSideAdjuster = '<li><label>Additional Outside Adjuster</label> '.$outSideAdjuster.'</li>'; }
    }

    # Submitted By, Fatality
    my $claimPartyQuery = $ENGINE->{'DBH'}->prepare('SELECT P.BUSINESS_NAME,P.FIRST_NAME, P.LAST_NAME, P.FATALITY_IND, R.ROLE FROM CLAIMDB.CLM_PARTIES AS P LEFT JOIN CLAIMDB.CLM_PARTY_ROLES AS R ON P.PARTY_ID = R.PARTY_ID AND R.DATE_DELETED = \'9999-01-01 01:00:00.000000\' WHERE P.CLAIM_ID = ? AND P.DATE_DELETED = \'9999-01-01 01:00:00.000000\'') || error($ENGINE,'Claim parties query prepare failed: '.$ENGINE->{'DBH'}->errstr);
    $claimPartyQuery->execute($claimid) || error($ENGINE,'Claim parties query execute failed: '.$ENGINE->{'DBH'}->errstr);
    my $claimPartyResults = $claimPartyQuery->fetchall_arrayref({});
    my $fatality = '';
    my $submittedBy = '';
    for my $c (@$claimPartyResults)
    {
        if($c->{'FATALITY_IND'} eq 'Y')
          { $fatality = '<li><label>Fatality </label><div><img src="c.png" style="vertical-align:middle" alt="Checked" /></div></li>'; }
    }

    # Salvage
    my $salvageQuery = $ENGINE->{'DBH'}->prepare('SELECT CLAIM_ID FROM CLAIMDB.CLM_SALVAGE WHERE CLAIM_ID = ? AND DATE_DELETED = \'9999-01-01 01:00:00.000000\'') || error($ENGINE,'Salvage query prepare failed: '.$ENGINE->{'DBH'}->errstr);
    $salvageQuery->execute($claimid) || error($ENGINE,'Salvage query execute failed: '.$ENGINE->{'DBH'}->errstr);
    my $salvageResults = $salvageQuery->fetchall_arrayref({});
    my $salvage = '';
    if(scalar(@$salvageResults)>0)
      { $salvage = '<li><label>Salvage</label><div> <img src="c.png" style="vertical-align:middle" alt="Checked" /></div></li>'; }

    # Reinsurance
    my $rein = '';
    my $reinStat2 = $ENGINE->{'claimGeneral'}->{'REINSURANCE_IND'} || '';
    if($ENGINE->{'AUTH'}->{'IMTOnline_UserType'} eq 'Internal' &&
      ($reinStat2 eq 'P' || $reinStat2 eq 'C'))
            {
         $rein = '<li><label>Reinsurance</label><div> <img src="c.png" style="vertical-align:middle" alt="Checked" /></div></li>';
        }

    # Subro is determined above
    # Fatality is determined above
    # Suit File is determined above

    # Associated Claim
    my $assocClaimQuery = $ENGINE->{'DBH'}->prepare('SELECT ASSOC_CLAIM_NUMBER FROM CLAIMDB.CLM_ASSOC_CLAIMS WHERE CLAIM_ID = ? AND DATE_DELETED = \'9999-01-01 01:00:00.000000\'') || error($ENGINE,'Associated claims query prepare failed: '.$ENGINE->{'DBH'}->errstr);
    $assocClaimQuery->execute($claimid) || error($ENGINE,'Associated claims query execute failed: '.$ENGINE->{'DBH'}->errstr);
    my $assocClaimResults = $assocClaimQuery->fetchall_arrayref({});
    my $assocClaim = '';
    if(scalar(@$assocClaimResults)>0)
    {
        my $assocClaimIDQuery = $ENGINE->{'DBH'}->prepare('SELECT CLAIM_ID FROM CLAIMDB.CLM_GENERAL WHERE IMT_CLAIM_NO = ? AND DATE_DELETED = \'9999-01-01 01:00:00.000000\'') || error($ENGINE,'Associated claims query prepare failed: '.$ENGINE->{'DBH'}->errstr);
        $assocClaim = '<li><label>Associated Claim';
        if(scalar(@$assocClaimResults)>0)
          { $assocClaim .='s'; }
        $assocClaim .= '</label><div>';
        for my $a (@$assocClaimResults)
        {
            $assocClaimIDQuery->execute($a->{'ASSOC_CLAIM_NUMBER'}) || error($ENGINE,'Associated claims query execute failed: '.$ENGINE->{'DBH'}->errstr);
            my $assocClaimIDResults = $assocClaimIDQuery->fetchall_arrayref({});
            if(scalar(@$assocClaimIDResults)>0)
              { $assocClaim .= '<a href="'.$action.'?claimid='.$assocClaimIDResults->[0]->{'CLAIM_ID'}.'&amp;load=Claims_Info&amp;sessionID='.$sessID.'">'.$a->{'ASSOC_CLAIM_NUMBER'}.'</a>, '; }
            else
              { $assocClaim .= $a->{'ASSOC_CLAIM_NUMBER'}.', '; }
        }
        chop($assocClaim);
        chop($assocClaim);
        $assocClaim .= '</div></li> ';
    }

    #This code displays the Insured Contact Note entered in on the File Activity screen.
    my $notesQuery = $ENGINE->{'DBH'}->prepare('SELECT
        N.*,
        CHAR(TIME(N.CLM_NOTE_DATETIME),USA) AS TIME
    FROM
        CLAIMDB.CLM_NOTES AS N
    WHERE
        N.CLAIM_ID = ? AND
        NOTE_TYPE = ? AND
        SHOW_IN_LOG = ? AND
        CLM_NOTE_SCREEN = ? AND
        N.DATE_DELETED = \'9999-01-01 01:00:00.000000\'
    ORDER BY CLM_ACTIVITY_LOG_SEQ') || error($ENGINE,'Notes query prepare failed: '.$ENGINE->{'DBH'}->errstr);
    $notesQuery->execute($claimid,'A','Y','ACTTASK') || error($ENGINE,'Notes query execute failed: '.$ENGINE->{'DBH'}->errstr);
    my $notesResults = $notesQuery->fetchall_arrayref({});
    my @sortedNotesResults = sort({$a->{'CLM_NOTE_DATETIME'} cmp $b->{'CLM_NOTE_DATETIME'}} @$notesResults);

    my $holdDate = '';
    my $holdDisplayActivity = '';
    my $displayActivity = '';
    my $otherLossDesc = '';
    my $altRow = '';
    my $i = 0;
    my $displayActivityHeading = '';
    for my $n (@sortedNotesResults)
    {
            $i++;
            $altRow = '';
            if($i%2 == 0)
            {$altRow = 'class="altRow"';}
            $displayActivityHeading = '<li '.$altRow.'><label>Insured Contact Info:</label><div> ';
# adding the if > 1 will keep insured contact info from duplicating. sjs.107589
            if ($n->{'CLM_ACTIVITY_LOG_SEQ'} > 1)
            {
                    next;
            }
            my $userKey = $n->{'USER_KEY'};

        my $userName = '';
        my $user_key_data = fetch_user_key_data({
          authorization => $ENGINE->{'AUTH'}->{'platform_access_token'},
          user_key => $userKey,
          max_attempts => 2
        });

        if ($user_key_data->{code} ne '200') {
          $userName = 'UNKNOWN';
          my $auth_error_msg = 'Platform Auth fetch did not return a 200 code.';
          platformAuthError($ENGINE,$user_key_data->{code},$auth_error_msg);
        }
        elsif(!defined($user_key_data->{content}->{data}->[0]->{attributes}->{first_name}) &&
              !defined($user_key_data->{content}->{data}->[0]->{attributes}->{last_name}) )
        {
            $userName = 'UNKNOWN';
            my $auth_error_msg = 'Platform Authe fetch did not return a First and Last name.';
            platformAuthError($ENGINE,$user_key_data->{code},$auth_error_msg);
        }
        else
        {
            $userName =  uc($user_key_data->{content}->{data}->[0]->{attributes}->{first_name}).' '.uc($user_key_data->{content}->{data}->[0]->{attributes}->{last_name});
        }
#        if($userResults->[0]->{'FIRST_NAME'} )
#        {
#                $userName = ucfirst(lc($userResults->[0]->{'FIRST_NAME'})).' '.ucfirst(lc($userResults->[0]->{'LAST_NAME'}));
#        }

#        if(defined($n->{'TASK_USER_DONE'}) && $n->{'TASK_USER_DONE'} eq '9999-01-01 01:00:00.000000')
        $holdDate = substr($n->{'CLM_NOTE_DATETIME'},5,2).'/'.substr($n->{'CLM_NOTE_DATETIME'},8,2).'/'.substr($n->{'CLM_NOTE_DATETIME'},0,4).', '.$n->{'TIME'};
#        else
#        { $holdDate = substr($n->{'TASK_USER_DONE'},5,2).'/'.substr($n->{'TASK_USER_DONE'},8,2).'/'.substr($n->{'TASK_USER_DONE'},0,4).'/'.$n->{'TIME2'}; }
        $displayActivity .= $displayActivityHeading;
        $holdDisplayActivity = NOTEScreateTextArea($ENGINE,{'attributes'=>{'name'=>'displayEntryA','id'=>'displayEntryA','cols'=>'70','rows'=>'5'},
                                   'notes'=>\@sortedNotesResults,
                                   'keys'=>{'PRIVATE_OR_PUBLIC'=>'L','NOTE_TYPE'=>'A','SHOW_IN_LOG'=>'Y','CLM_NOTE_SCREEN'=>'ACTTASK','ALLOW_UPDATE'=>'N','CLM_NOTE_DATETIME'=>$n->{'CLM_NOTE_DATETIME'}}
                                   });
               $otherLossDesc = $holdDate.', BY: '.$userName.', ENTRY: '.$holdDisplayActivity; # for mobile app plug
        $DETAILS->{'otherLossdesc'} = $otherLossDesc;

        $displayActivity .= $holdDisplayActivity;
        $displayActivity .= '<span class="datetime">'.$holdDate.' - '.$userName.'</span>';
        $displayActivity =~ s/&AMP;/&amp;/g;
        $displayActivity =~ s/&LT;/&lt;/g;
        $displayActivity =~ s/<BR \/>/<br \/>/g;
        $displayActivity =~ s/&NBSP;/&nbsp;/g;
        $displayActivity .= '</div></li>';
    }

    $DETAILS->{'proc_details'} = \@procDetails;

    my $otherInfo = '';
    if($restorePurgeMess gt '' || $adjuster gt '' || $outSideAdjuster gt '' ||
        $submittedBy gt '' || $salvage gt '' || $subro gt '' || $rein gt '' ||
        $fatality gt '' || $suitFile gt '' || $assocClaim gt '' || $displayActivity gt ''){
        $otherInfo = <<EOF;
<div class="title">Other Information</div>
<div style="line-height:1.4em">
$restorePurgeMess
$adjuster
$outSideAdjuster
$submittedBy
$salvage
$subro
$rein
$fatality
$suitFile
$assocClaim
$displayActivity
</div>
EOF
}

    my $html = '';
    if($restorePurgeMess gt '' || $adjuster gt '' || $outSideAdjuster gt '' ||
        $submittedBy gt '' || $salvage gt '' || $subro gt '' || $rein gt '' ||
        $fatality gt '' || $suitFile gt '' || $assocClaim gt '' || $displayActivity gt ''){

        if($displayActivity ne ''){
            $displayActivity =  '<ul class="toplabel_onecol"  id="other_info_details_ul">'.$displayActivity.'</ul>';
        }
        my $class = '';
            if($restorePurgeMess eq '' && $adjuster eq '' && $outSideAdjuster eq '' &&
                $submittedBy eq '' && $salvage eq '' && $subro eq '' && $rein eq '' &&
                $fatality eq '' && $suitFile eq '' && $assocClaim eq '')
        {$class = 'hidden';}
    $html = <<EOF;
<fieldset>
    <h2>Other Information</h2>
        <div class="subsection other_info_details $class">
            <ul class="leftlabel_twocol">
                $restorePurgeMess
                $adjuster
                $outSideAdjuster
                $submittedBy
                $salvage
                $subro
                $rein
                $fatality
                $suitFile
                $assocClaim
            </ul>
        </div>
        $displayActivity
</fieldset>

EOF
}
    return $html;
}

sub getProcessingDetails
{
    my $ENGINE = shift;
    my $claimid = $ENGINE->{'claimGeneral'}->{'CLAIM_ID'};

    my @procDetails = @{$DETAILS->{'proc_details'}};

    my $date = $ENGINE->{'claimGeneral'}->{'SUBMIT_TO_IMT_DATE'};
    if($date ne '9999-01-01 01:00:00.000000')
#      { push(@procDetails,{'text'=>'Claim submitted to IMT','date'=>substr($date,0,10),'rank'=>1}); }
      { push(@procDetails,{'text'=>'Claim submitted to IMT','date'=>$date,'rank'=>1}); }

    $date = $ENGINE->{'claimGeneral'}->{'REPORTED_DATE'};
    if($date ne '9999-01-01 01:00:00.000000')
#      { push(@procDetails,{'text'=>'Claim reported date','date'=>substr($date,0,10),'rank'=>10}); }
      { push(@procDetails,{'text'=>'Claim reported date','date'=>$date,'rank'=>10}); }

    $date = $ENGINE->{'claimGeneral'}->{'CREATION_DATE'};
    if($date ne '9999-01-01 01:00:00.000000')
#      { push(@procDetails,{'text'=>'Claim entered date','date'=>substr($date,0,10),'rank'=>20}); }
      { push(@procDetails,{'text'=>'Claim entered date','date'=>$date,'rank'=>20}); }

    my $claimHistQuery = $ENGINE->{'DBH'}->prepare('SELECT D.AFTER, D.BEFORE, H.DATE_ENTERED, D.FIELDNAME FROM CLAIMDB.CLM_TRANS_DETAIL AS D
INNER JOIN CLAIMDB.CLM_TRANS_HIST AS H ON D.HISTORY_ID = H.HISTORY_ID
WHERE H.CLAIM_ID = ? AND D.TABLE_NAME = \'CLM_GENERAL\' AND D.FIELDNAME IN (\'CLOSING_CODE\',\'CLAIM_STATUS\')') || error($ENGINE,'Associated claims query prepare failed: '.$ENGINE->{'DBH'}->errstr);
    $claimHistQuery->execute($claimid) || error($ENGINE,'Associated claims query execute failed: '.$ENGINE->{'DBH'}->errstr);
    my $claimHistResults = $claimHistQuery->fetchall_arrayref({});

    for my $h (@$claimHistResults)
    {
#        if(!($h->{'BEFORE'} =~ /\w/) && $h->{'AFTER'} =~ /\w/)
        if(!($h->{'BEFORE'} =~ /^C/) && $h->{'AFTER'} =~ /^C/)
#          { push(@procDetails,{'text'=>'File closed','date'=>substr($h->{'DATE_ENTERED'},0,10),'rank'=>60}); }
          {
              if(substr($h->{'DATE_ENTERED'},11,15) eq '01:00:00.000000')
              { push(@procDetails,{'text'=>'Claim closed','date'=>substr($h->{'DATE_ENTERED'},0,10),'rank'=>60}); }
              else
              { push(@procDetails,{'text'=>'Claim closed','date'=>$h->{'DATE_ENTERED'},'rank'=>200}); }
          }
#        elsif($h->{'BEFORE'} =~ /\w/ && !($h->{'AFTER'} =~ /\w/))
        elsif($h->{'BEFORE'} =~ /^C/ && !($h->{'AFTER'} =~ /^C/))
#          { push(@procDetails,{'text'=>'File reopened','date'=>substr($h->{'DATE_ENTERED'},0,10),'rank'=>70}); }
          {
              if(substr($h->{'DATE_ENTERED'},11,15) eq '01:00:00.000000')
              { push(@procDetails,{'text'=>'Claim reopened','date'=>substr($h->{'DATE_ENTERED'},0,10),'rank'=>70}); }
              else
              { push(@procDetails,{'text'=>'Claim reopened','date'=>$h->{'DATE_ENTERED'},'rank'=>200}); }
          }
    }

    my $taskListQuery = $ENGINE->{'DBH'}->prepare("SELECT TASKS_ID, TASK_COMPLETE, TASK_USER_DONE FROM CLAIMDB.CLM_TASK_LIST
WHERE CLAIM_ID = ? AND TASKS_ID IN (1,2,8) AND TASK_COMPLETE <> '9999-01-01-01.00.00.000000' AND DATE_DELETED = '9999-01-01 01:00:00.000000'") || error($ENGINE,'Task List query prepare failed: '.$ENGINE->{'DBH'}->errstr);
    $taskListQuery->execute($claimid) || error($ENGINE,'Task List query execute failed: '.$ENGINE->{'DBH'}->errstr);
    my $taskListResults = $taskListQuery->fetchall_arrayref({});

    for my $tl (@$taskListResults)
    {
        if($tl->{'TASKS_ID'} =~ /1/)
#          { push(@procDetails,{'text'=>'First Contact Insured','date'=>substr($tl->{'TASK_COMPLETE'},0,10),'rank'=>80}); }
          {
              if($tl->{'TASK_USER_DONE'} ne '9999-01-01 01:00:00.000000')
              {
                  push(@procDetails,{'text'=>'First Contact Insured','date'=>$tl->{'TASK_USER_DONE'},'rank'=>200});
              }
              else
              {
                      if(substr($tl->{'TASK_COMPLETE'},11,15) eq '01:00:00.000000')
                      { push(@procDetails,{'text'=>'First Contact Insured','date'=>substr($tl->{'TASK_COMPLETE'},0,10),'rank'=>80}); }
                      else
                      { push(@procDetails,{'text'=>'First Contact Insured','date'=>$tl->{'TASK_COMPLETE'},'rank'=>200}); }
              }
          }
        elsif($tl->{'TASKS_ID'} =~ /2/)
#          { push(@procDetails,{'text'=>'First Contact Claimant','date'=>substr($tl->{'TASK_COMPLETE'},0,10),'rank'=>90}); }
          {
              if($tl->{'TASK_USER_DONE'} ne '9999-01-01 01:00:00.000000')
              {
                      push(@procDetails,{'text'=>'First Contact Claimant','date'=>$tl->{'TASK_USER_DONE'},'rank'=>200});
              }
              else
              {
                      if(substr($tl->{'TASK_COMPLETE'},11,15) eq '01:00:00.000000')
                      { push(@procDetails,{'text'=>'First Contact Claimant','date'=>substr($tl->{'TASK_COMPLETE'},0,10),'rank'=>90}); }
                      else
                      { push(@procDetails,{'text'=>'First Contact Claimant','date'=>$tl->{'TASK_COMPLETE'},'rank'=>200}); }
              }
          }
        elsif($tl->{'TASKS_ID'} =~ /8/)
#          { push(@procDetails,{'text'=>'Total Loss Valuation Insured','date'=>substr($tl->{'TASK_COMPLETE'},0,10),'rank'=>100}); }
          {
              if(substr($tl->{'TASK_COMPLETE'},11,15) eq '01:00:00.000000')
              { push(@procDetails,{'text'=>'Total Loss Valuation Insured','date'=>substr($tl->{'TASK_COMPLETE'},0,10),'rank'=>100}); }
              else
              { push(@procDetails,{'text'=>'Total Loss Valuation Insured','date'=>$tl->{'TASK_COMPLETE'},'rank'=>200}); }
          }
    }

    @procDetails = sort({$b->{'date'} cmp $a->{'date'}} @procDetails);
    @procDetails = sort({$b->{'rank'} <=> $a->{'rank'}} @procDetails);
#    @procDetails = sort({$b->{'date'} cmp $a->{'date'}} @procDetails);
    my $new_col = <<EOF;
<ul class="processing">
<li class="head"><span class="first">Description</span><span class="second">Completion Date</span></li>
EOF
    my $procDetailsFull = '<div class="detailsFull hidden" id="detailsFull">'.$new_col;
    my $procDetailsShort = '<div class="detailsShort" id="detailsShort">'.$new_col;
    my $length = scalar(@procDetails);
    my $half_length = int($length/2);
    $half_length++;
    if($length%2 != 0){
        $half_length++;
    }

    my $short_half = $half_length;
       $short_half = 6 if($length > 10);

    my $i = 0;
    my $k = 0;
    my $class = '';
    for my $p (@procDetails)
    {
        $i++;

        $k = 0 if ($i == $short_half);
        $k++;

        $class = '';
        if($k%2 == 0)
          { $class = ' class="altRow" '; }

        my $date = substr($p->{'date'},5,2).'/'.substr($p->{'date'},8,2).'/'.substr($p->{'date'},0,4);
        my $text = $p->{'text'};

        my $line = '<li '.$class.'><span class="first">'.$text.'</span><span class="second">'.$date.'</span></li>';

        if($i <= 10){
            if($i == $short_half){
                $procDetailsShort .= '</ul>'.$new_col;
            }
            $procDetailsShort .= $line;
        }
    }

    $i = 0;
    $k = 0;
    $class = '';
    for my $p (@procDetails)
    {
        $i++;

        $k = 0 if($i == $half_length);
        $k++;

        $class = '';
        if($k%2 == 0)
          { $class = ' class="altRow" '; }

        my $date = substr($p->{'date'},5,2).'/'.substr($p->{'date'},8,2).'/'.substr($p->{'date'},0,4);
        my $text = $p->{'text'};

        my $line = '<li '.$class.'><span class="first">'.$text.'</span><span class="second">'.$date.'</span></li>';

        if($i == $half_length){
            $procDetailsFull .= '</ul>'.$new_col;
        }
        $procDetailsFull .= $line;

    }

    $procDetailsShort .= '</ul></div>';
    $procDetailsFull .= '</ul></div>';

    my $view_details = '';
    if($length > 10){
        $view_details = '<div class="utilitybar"><a href="#processing" id="viewDetails" onclick="viewDetails()" >View All Details</a></div>';
    }

    my $html = <<EOF;
<fieldset>
<h2>Processing Details</h2>

$procDetailsShort
$procDetailsFull

$view_details
</fieldset>

EOF

    return $html;


}



sub editScreen
{

        #the only thing a user can do on the Summary screen at this point,
        #is request an Archived Claim to be Restored from History.  There
        #really are no edits associated but we need to know if the user pressed
        #the button indicating that the claim should be restored.
    my $ENGINE = shift;
    my %errors = ();
    my $return = 1;
    my $claimid = $ENGINE->{'claimGeneral'}->{'CLAIM_ID'};
    my $error = $ENGINE->{'error'};

    if (defined $ENGINE->{'CGI'}->param('transactionCode')
            && $ENGINE->{'CGI'}->param('transactionCode') eq 'restoreClaim')
    {
             #do nothing, this just means we are restoring the claim.
             #put in this section in case we need to add edits at some point
    }

    #load any errors
    $ENGINE->{'errors'} = \%errors;

    return $return;

}

sub saveScreen
{

    my $ENGINE = shift;
    my $error = $ENGINE->{'error'};

    my $sessID = $ENGINE->{'SESSION'}->{'sessionID'};
    my $action = $ENGINE->{'ACTION'};

    my $name = $ENGINE->{'AUTH'}->{'name'};
    my $claimid = $ENGINE->{'claimGeneral'}->{'CLAIM_ID'};
    my $userID = $ENGINE->{'claimGeneral'}->{'USER_KEY'};
    $ENGINE->{'wc_detail_jump'} = $ENGINE->{'CGI'}->param('wc_detail_jump');

    #call edits creen function.  Get into the inside of this
    #if statement if all edits are passed.  If they are not
    #all passed, the update section inside this if statement
    #is not processed.
    if(editScreen($ENGINE))
    {
            if (defined $ENGINE->{'CGI'}->param('transactionCode')
                    && $ENGINE->{'CGI'}->param('transactionCode') eq 'restoreClaim')
        {
                 my $generalUpdate = $ENGINE->{'DBH'}->prepare
                    ("UPDATE CLAIMDB.CLM_GENERAL
                            SET PURGED_IND = 'R'
                            WHERE CLAIM_ID = ?") || $error->($ENGINE);
                $generalUpdate->execute($claimid)
                        || $error->($ENGINE);
                $ENGINE->{'claimGeneral'}->{'PURGED_IND'} = 'R';
        }
    }

}

1;
